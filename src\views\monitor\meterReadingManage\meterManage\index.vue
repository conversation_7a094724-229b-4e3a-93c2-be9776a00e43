<!-- 表计管理 -->
<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <div class="search-from">
        <el-input v-model="queryForm.codeOrName" placeholder="设备名称/设备编码" clearable style="width: 200px"></el-input>
        <el-select v-model="queryForm.deviceType" placeholder="设备类型" clearable>
          <el-option v-for="item in deviceTypeList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
        </el-select>
        <el-select v-model="queryForm.recordType" placeholder="抄表类型" clearable>
          <el-option label="自动" :value="0"> </el-option>
          <el-option label="手动" :value="1"> </el-option>
        </el-select>
        <el-select v-model="queryForm.projectCode" placeholder="监测系统" clearable>
          <el-option v-for="item in sysList" :key="item.projectCode" :label="item.projectName" :value="item.projectCode"> </el-option>
        </el-select>
        <el-select ref="treeSelect" v-model="queryForm.serviceReginonCode" placeholder="服务区域" clearable @clear="handleClear">
          <el-option hidden :value="queryForm.serviceReginonCode" :label="areaName"> </el-option>
          <el-tree
            :data="serverSpaces"
            :props="serverDefaultProps"
            :load="serverLoadNode"
            lazy
            :expand-on-click-node="false"
            :check-on-click-node="true"
            @node-click="handleNodeClick"
          >
          </el-tree>
        </el-select>
        <el-select v-model="queryForm.departmentCode" placeholder="监测科室" clearable filterable>
          <el-option v-for="item in deptList" :key="item.id" :label="item.deptName" :value="item.id"></el-option>
        </el-select>
        <div style="display: inline-block">
          <el-button v-auth="'userManagement:reset'" type="primary" plain @click="resetForm">重置</el-button>
          <el-button v-auth="'userManagement:search'" type="primary" @click="searchForm">查询</el-button>
        </div>
      </div>
      <div class="batch-control">
        <el-button type="primary" icon="el-icon-plus" @click="handleRoleEvent('add')">新增</el-button>
        <el-button type="primary" :disabled="!multipleSelection.length" @click="handleRoleEvent('batchDel')">批量删除</el-button>
        <el-button type="primary" icon="el-icon-download" @click="handleRoleEvent('meterExportDevice')">导出设备</el-button>
        <el-popover class="upload" placement="right" trigger="hover">
          <el-button type="primary" icon="el-icon-download" style="width: 120px; margin-bottom: 10px" @click="handleRoleEvent('meterExportDeviceTmpl')">模板下载</el-button>
          <el-upload class="upload" action="" :show-file-list="false" accept=".xlsx, .xls" :http-request="importDevices">
            <el-button type="primary" icon="el-icon-upload2">批量导入</el-button>
          </el-upload>
          <template #reference>
            <el-button type="primary">导入设备</el-button>
          </template>
        </el-popover>
        <el-popover class="upload" placement="right" trigger="hover">
          <el-button type="primary" icon="el-icon-download" style="width: 120px; margin-bottom: 10px" @click="handleRoleEvent('meterExportRecordTmpl')">模板下载</el-button>
          <el-upload action="" :show-file-list="false" accept=".xlsx, .xls" :http-request="importMeter">
            <el-button type="primary" icon="el-icon-upload2">批量导入</el-button>
          </el-upload>
          <template #reference>
            <el-button type="primary">导入抄表记录</el-button>
          </template>
        </el-popover>
      </div>
    </div>
    <div slot="content" class="table-content">
      <TablePage
        ref="table"
        v-loading="tableLoading"
        :showPage="true"
        :tableColumn="tableColumn"
        :data="tableData"
        height="calc(100% - 40px)"
        :pageData="pageData"
        @pagination="paginationChange"
        @row-dblclick="handleRoleEvent('detail', $event)"
        @selection-change="handleSelectionChange"
      />
      <recordMeterDialog v-if="isRecordMeterDialog" :visible.sync="isRecordMeterDialog" :deviceData="activeItem" />
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import { transData, ListTree } from '@/util'
export default {
  name: 'meterManage',
  components: {
    recordMeterDialog: () => import('../components/recordMeterDialog.vue')
  },
  data() {
    return {
      isRecordMeterDialog: false,
      queryForm: {
        codeOrName: '',
        deviceType: '',
        recordType: '',
        projectCode: '',
        serviceReginonCode: '',
        departmentCode: ''
      },
      serverDefaultProps: {
        label: 'ssmName',
        isLeaf: 'leaf',
        children: 'children'
      },
      areaName: '', // 选中 下拉树的name
      spaces: [], // 空间位置
      serverSpaces: [], // 空间位置
      deviceTypeList: [], // 设备类型
      sysList: [], // 监测系统
      deptList: [], // 科室列表
      tableData: [],
      multipleSelection: [],
      pageData: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      tableLoading: false,
      tableColumn: [
        {
          type: 'selection',
          align: 'center'
        },
        // {
        //   prop: 'serialNumber',
        //   label: '序号',
        //   formatter: (scope) => {
        //     return (this.pageData.page - 1) * this.pageData.pageSize + scope.$index + 1
        //   }
        // },
        {
          prop: 'deviceCode',
          label: '设备编码'
        },
        {
          prop: 'deviceName',
          label: '设备名称'
        },
        {
          prop: 'deviceTypeName',
          label: '设备类型'
        },
        {
          prop: 'reginonName',
          label: '安装位置'
        },
        {
          prop: 'departmentName',
          label: '监测科室'
        },
        {
          prop: 'projectName',
          label: '监测系统'
        },
        {
          prop: 'recordType',
          label: '抄表类型',
          render: (h, row) => {
            return row.row.recordType == 0 ? '自动' : '手动'
          }
        },
        {
          width: 230,
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" style="color: #121F3E" onClick={() => this.handleRoleEvent('edit', row.row)}>
                  编辑
                </span>
                <span class="operationBtn-span" style="color: #fa403c" onClick={() => this.handleRoleEvent('del', row.row)}>
                  删除
                </span>
                <el-button type="text" disabled={row.row.recordType == 0} onClick={() => this.handleRoleEvent('recordMeter', row.row)}>
                  录入抄表
                </el-button>
                <el-button type="text" onClick={() => this.handleRoleEvent('detail', row.row)}>
                  抄表记录
                </el-button>
              </div>
            )
          }
        }
      ],
      activeItem: {}
    }
  },
  computed: {},
  watch: {
    isRecordMeterDialog(val) {
      if (!val) this.searchForm()
    }
  },
  created() {
    this.getSystemList()
    this.getDeptList()
    this.getTreelist()
    this.getDeviceTypeList()
    this.getMeterDevicePage()
  },
  methods: {
    handleRoleEvent(type, row = {}) {
      this.activeItem = row
      if (type == 'add' || type === 'edit') {
        // 新增/修改
        this.$router.push({
          path: 'meterManage/addDevice',
          query: {
            type,
            id: row?.id ?? ''
          }
        })
      } else if (type === 'detail') {
        // 抄表记录
        this.$router.push({
          path: 'meterManage/meterRecord',
          query: {
            id: row?.id ?? ''
          }
        })
      } else if (type == 'del' || type == 'batchDel') {
        // 删除
        this.$confirm('删除后将无法恢复，是否确定删除？', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let ids = type == 'del' ? [row?.id] : this.multipleSelection.map((item) => item.id)
          this.$api.meterDeleteDevice({ deviceIds: ids }).then((res) => {
            if (res.code == 200) {
              this.$message.success('删除成功')
              this.isLastPage(ids.length)
              this.getMeterDevicePage()
            } else {
              this.$message.error('删除失败')
            }
          })
        })
      } else if (type == 'meterExportDevice' || type == 'meterExportDeviceTmpl' || type == 'meterExportRecordTmpl') {
        let obj = {
          meterExportDevice: { key: 'meterExportDevice', name: '表计管理', params: { ...this.queryForm, pageSize: this.pageData.pageSize, page: this.pageData.page } },
          meterExportDeviceTmpl: { key: 'meterExportDevice', name: '表计管理模板', params: { projectCode: 'xxx' } },
          meterExportRecordTmpl: { key: 'meterExportRecordTmpl', name: '抄表记录模板', params: {} }
        }
        // 导出
        this.$api[obj[type].key](obj[type].params).then((res) => {
          let url = window.URL.createObjectURL(new Blob([res]), { type: 'application/vnd.ms-excel;charset=UTF-8' })
          let link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.setAttribute('download', `${obj[type].name}.xlsx`)
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link) // 下载完成移除元素
          window.URL.revokeObjectURL(url) // 释放掉blob对象
        })
      } else if (type == 'recordMeter') {
        // 录入抄表
        this.isRecordMeterDialog = true
      }
    },
    importMeter(file) {
      const fileData = new FormData()
      fileData.append('file', file.file)
      const params = {
        file: file.file
      }
      this.$api['meterImportRecord'](params).then((res) => {
        if (res.code == 200) {
          this.$message.success('导入成功')
          this.getMeterDevicePage()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 导入设备
    importDevices(file) {
      const fileData = new FormData()
      fileData.append('file', file.file)
      const params = {
        file: file.file
      }
      this.$api['meterImportDevice'](params).then((res) => {
        if (res.code == 200) {
          this.$message.success('导入成功')
          this.getMeterDevicePage()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    getMeterDevicePage() {
      let param = {
        ...this.queryForm,
        pageSize: this.pageData.pageSize,
        page: this.pageData.page
      }
      this.tableLoading = true
      this.$api
        .GetMeterDevicePage(param)
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.records
            this.pageData.total = res.data.total
          }
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    // 判断当前页是否是最后一页
    isLastPage(deleteNum) {
      let deleteAfterPage = Math.ceil((this.pageData.total - deleteNum) / this.pageData.pageSize)
      let currentPage = this.pageData.page > deleteAfterPage ? deleteAfterPage : this.pageData.page
      this.pageData.page = currentPage < 1 ? 1 : currentPage
    },
    // 获取设备类型
    getDeviceTypeList() {
      this.$api.GetDeviceTypeList().then((res) => {
        if (res.code == '200') {
          this.deviceTypeList = res.data
        }
      })
    },
    // 获取科室
    getDeptList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == '200') {
          this.deptList = res.data
        }
      })
    },
    // 获取监测系统
    getSystemList() {
      this.$api.GetMeterProjectList().then((res) => {
        if (res.code == 200) {
          this.sysList = res.data
        }
      })
    },
    // 获取服务空间树形结构
    getTreelist() {
      this.$api.getStructureTree({}).then((res) => {
        if (res.code == 200) {
          this.spaces = res.data
          // 增加 懒加载节点
          res.data.map((e) => {
            e.leaf = false
          })
          // 转换为树形结构数据
          this.serverSpaces = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    spaceTreeChange(value, resolve) {
      const child = ListTree(this.spaces, value.pid)
      child.push(value.id)
      let treeId = child.toString()
      let data = {
        current: 1,
        functionDictId: '',
        simCode: treeId,
        size: 999
      }
      this.$api.getSpaceInfoList(data).then((res) => {
        if (res.code == 200) {
          if (typeof resolve === 'function') {
            let treeNodeData = JSON.parse(JSON.stringify(res.data.records))
            treeNodeData.map((e) => {
              e.leaf = true
            })
            resolve(treeNodeData)
          }
        }
      })
    },
    // 空间数据清除
    handleClear() {
      this.queryForm.serviceReginonCode = ''
      this.areaName = ''
    },
    // 选择下拉树 数据
    handleNodeClick(data) {
      this.queryForm.serviceReginonCode = data.id
      this.areaName = data.ssmName
      this.$refs.treeSelect.blur()
    },
    // 下拉树懒加载
    serverLoadNode(node, resolve) {
      if (node.level == 0) {
        return resolve(node.data)
      } else if (node.level > 0 && node.level < 4) {
        return resolve(node.data.children)
      } else {
        return this.spaceTreeChange(node.data, resolve)
      }
    },
    // 查询
    searchForm() {
      this.pageData.page = 1
      this.getMeterDevicePage()
    },
    // 重置查询
    resetForm() {
      Object.assign(this.$data.queryForm, this.$options.data().queryForm)
      this.searchForm()
    },
    // 多选
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getMeterDevicePage()
    }
  }
}
</script>
<style lang="scss" scoped>
.control-btn-header {
  padding: 0px 10px 10px 10px !important;
  .search-from {
    & > div {
      margin-top: 10px;
      margin-right: 10px;
    }
  }
  .batch-control {
    display: flex;
    & > .el-button,
    .upload {
      margin-top: 10px;
      margin-right: 10px;
      margin-left: 0;
    }
  }
}
.table-content {
  margin-top: 15px;
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 10px;
}
</style>
<style lang="scss">
.operationBtn-span {
  margin-right: 10px;
}
</style>
