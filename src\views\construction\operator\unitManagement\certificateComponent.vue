<template>
  <div class="certificate-component">
    <el-row :gutter="10">
      <el-col :span="24" v-if="!disabled"><el-button type="primary" plain icon="el-icon-plus" @click="addCertificate">添加证照</el-button></el-col>
      <el-col :span="24" v-if="certificateList.length">
        <div class="zzBox" v-for="(item, index) in certificateList" :key="index">
          <div class="zzBox-left">
            <el-form ref="form" :model="item" label-width="120px" :disabled="disabled">
              <el-form-item label="证照名称" style="margin-bottom: 0px" :required="filterIsWrited(item.fileCode)">
                <el-select
                  v-model="item.fileCode"
                  placeholder="请选择活动区域"
                  style="width: 100%"
                  :disabled="disabled"
                  v-if="licenseNameFilter(item)"
                  clearable
                  @change="changeKey($event, index)"
                >
                  <el-option v-for="i in certificateOptions" :key="i.id" :label="i.fileName" :value="i.fileCode"></el-option>
                </el-select>
                <el-input v-else v-model="item.fileName" placeholder="" disabled></el-input>
              </el-form-item>
            </el-form>
          </div>
          <div class="zzBox-right">
            <el-upload
              :disabled="disabled"
              action=""
              list-type="list"
              :file-list="item.url"
              accept="*"
              :before-upload="beforeAvatarUpload"
              :http-request="(file) => httpCertificateRequset(file, index)"
              :on-remove="(file, fileList) => handleCertificateRemove(fileList, index)"
              :on-change="handleCertificateFileChange.bind(this, index)"
              :on-preview="handlePreview"
            >
              <div slot="tip" class="el-upload__tip">大小不超过20M</div>
              <el-button :disabled="disabled" size="medium" type="primary">点击上传</el-button>
            </el-upload>
          </div>
          <div class="zzBox-del" @click="delCertificate(index)" v-if="!disabled"><span class="el-icon-delete" style="color: #f53f3f"></span></div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
export default {
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    detail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      certificateList: [], // 证照添加列表
      certificateOptions: [] // 证照列表信息
    }
  },
  watch: {
    detail: {
      handler(val) {
        if (val) {
          if (val.fileAttachment) {
            let arr = JSON.parse(val.fileAttachment)
            arr.forEach((item) => {
              if (item.url) {
                item.url = JSON.parse(item.url).map((i) => {
                  return {
                    name: i.name,
                    url: this.$tools.imgUrlTranslation(i.url),
                    uploadPath: i.url
                  }
                })
              }
              item.id = Date.now()
            })
            this.certificateList = arr
          }
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.getCertificateList()
  },
  methods: {
    // 过滤是否可编辑
    licenseNameFilter(item) {
      if (!item) return false
      let obj = this.certificateOptions.find((i) => i.fileCode === item.fileCode)
      if (obj && String(obj.enableStatus) === '1') return true
      if (!obj && !item.id) return true
      return false
    },
    // 过滤是否必填
    filterIsWrited(val) {
      if (!val) return false
      let obj = this.certificateOptions.find((item) => item.fileCode === val)
      if (obj && obj.writed == 1) return true
      return false
    },
    // 证照名称选择事件
    changeKey(val, index) {
      if (val) {
        let obj = this.certificateOptions.find((item) => item.fileCode === val)
        if (obj) {
          this.certificateList[index].writed = obj.writed
          this.certificateList[index].fileName = obj.fileName
        }
      } else {
        this.certificateList[index].writed = 0
        this.certificateList[index].fileName = ''
      }
    },
    // 添加证照
    addCertificate() {
      let obj = {
        fileCode: '',
        fileName: '',
        writed: 0,
        url: []
      }
      this.certificateList.push(obj)
    },
    // 删除证照
    delCertificate(index) {
      this.certificateList.splice(index, 1)
    },
    // 证照上传
    async httpCertificateRequset(file, index) {
      let res = await this.$api.constructionUpload(__PATH.SPACE_API, 'SecurityLedger/upload', file)
      if (res.code === '200') {
        this.$message({
          message: '上传成功',
          type: 'success'
        })
        const fileInfo = this.certificateList[index].url.find((it) => it.uid === file.file.uid)
        if (fileInfo) {
          fileInfo.uploadPath = res.data.fileKey
        }
      } else {
        this.$message.error(res.message)
      }
    },
    // 证照上传删除
    handleCertificateRemove(fileList, index) {
      this.certificateList[index].url = fileList
    },
    // 证照上传 失踪同步文件列表
    handleCertificateFileChange(idx, file) {
      if (file.status === 'fail') {
        // 失败的文件，500ms后删除，视觉效果会好一些
        window.clearTimeout(this.$timerId)
        this.$timerId = window.setTimeout(() => {
          let index = -1
          do {
            index = this.certificateList[idx].url.findIndex((it) => it.status === 'fail')
            if (index > -1) {
              this.certificateList[idx].url.splice(index, 1)
            }
          } while (index > -1)
          this.$timerId = -1
        }, 500)
      } else {
        const index = this.certificateList[idx].url.findIndex((it) => it.uid === file.uid)
        if (index > -1) {
          this.certificateList[idx].url.splice(index, 1, file)
        } else {
          this.certificateList[idx].url.push(file)
        }
      }
    },
    // 图片上传大小校验
    beforeAvatarUpload(file) {
      const fileSize = file.size / 1024 / 1024 < 20
      if (!fileSize) {
        this.$message.error('上传图片大小不能超过 20MB!')
      }
      return fileSize
    },
    // 获取证照信息
    getCertificateList() {
      const params = {
        fileName: '',
        enableStatus: '',
        type: 1,
        page: 1,
        pageSize: 999999
      }
      this.$api
        .getConstructionCertificateList(params)
        .then((res) => {
          if (res.code === '200') {
            if (res.data.records && res.data.records.length) {
              this.certificateOptions = res.data.records.filter((item) => item.initialed == 2 && item.enableStatus == 1)
            }
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '查询失败'))
    },
    handlePreview(file) {
      window.open(file.url)
    }
  }
}
</script>
<style lang="scss" scoped>
.certificate-component {
  height: 100%;
  padding: 10px;
  box-sizing: border-box;
}
.zzBox {
  margin: 15px 0px;
  display: flex;
  .zzBox-left {
    width: 400px;
    flex-shrink: 0;
  }
  .zzBox-right {
    width: 350px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    margin-left: 20px;
  }
  .zzBox-del {
    margin-left: 20px;
    font-size: 26px;
    cursor: pointer;
  }
}
/* 隐藏提示 */
::v-deep .el-upload-list__item.is-success.focusing .el-icon-close-tip {
  display: none !important;
}
</style>