<script>
import tableListMixin from '@/mixins/tableListMixin'
export default {
  name: 'DictionaryList',
  components: {
    DictionaryValueEdit: () => import('./components/DictionaryValueEdit.vue')
  },
  mixins: [tableListMixin],
  data() {
    return {
      currentKey: '',
      // 当前选中的节点对应的字典类型
      currentDictType: '',
      treeSearchKeyWord: '',
      searchForm: {
        name: ''
      },
      treeData: [],
      treeLoadingStatus: false,
      tableData: [],
      tableLoadingStatus: false,
      dialog: {
        show: false,
        id: '',
        readonly: false, // 查看模式
        parentIds: [] // 指定的父节点ID
      },
      currentImage: '' // 当前预览的图片
    }
  },
  computed: {
    OperateType() {
      return {
        Edit: 'edit',
        Delete: 'delete',
        Create: 'create',
        View: 'view',
        Child: 'child',
        Status: 'status'
      }
    }
  },
  watch: {
    treeSearchKeyWord(val) {
      this.$refs.treeRef.filter(val)
    }
  },
  mounted() {
    this.getTreeData()
  },
  methods: {
    // 获取树数据
    getTreeData() {
      this.treeLoadingStatus = true
      this.$api
        .dictGetTypeList()
        .then((res) => {
          if (res.code === '200') {
            this.treeData = res.data
            const [first] = this.treeData
            if (first) {
              this.currentKey = first.id
              this.currentDictType = first.dictType
              this.onSearch()
              this.$nextTick(() => {
                this.$refs.treeRef.setCurrentKey(first.id)
              })
            }
          } else {
            throw '获取字典类型失败'
          }
        })
        .catch((msg) => this.$message.error(msg))
        .finally(() => {
          this.treeLoadingStatus = false
        })
    },
    // 当树点击被点击时
    onTreeNodeClick(data) {
      if (data.id !== this.currentKey) {
        this.currentKey = data.id
        this.currentDictType = data.dictType
        this.onReset()
      }
    },
    // 树节点过滤
    filterNode(value, data) {
      if (!value) return true
      return data.dictName.includes(value)
    },
    // 获取列表数据
    getDataList() {
      this.tableLoadingStatus = true
      const params = {
        pageSize: this.pagination.size,
        pageNo: this.pagination.current,
        dictName: this.searchForm.name,
        dictType: this.currentDictType
      }
      this.$api
        .dictQueryByPage(params)
        .then((res) => {
          if (res.code === '200') {
            this.tableData = res.data.list
            this.pagination.total = res.data.count
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取列表数据失败'))
        .finally(() => {
          this.tableLoadingStatus = false
        })
    },
    // 表单搜索按钮点击
    onSearch() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 表单重置按钮点击
    onReset() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    // 列表操作相关的事件绑定
    onOperate(command, row) {
      switch (command) {
        case this.OperateType.Delete:
          // 删除非启用的字典数据
          if (row.dictPreset === '1') {
            this.$message.error('预设的字典不允许删除！')
          } else if (row.dictState === '1') {
            this.$message.error('启用的字典不允许删除！')
          } else {
            this.$confirm('删除后将无法恢复，是否确定删除？', '信息提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消'
            }).then(() => this.doDelete(row.id))
          }
          break
        case this.OperateType.Child:
          // 指定父级字典ID  需要转换成number
          const ids = (row.parentIds || '')
            .split(',')
            .concat(row.id)
            .filter((it) => !!it)
            .map((it) => Number(it))
          this.dialog.parentIds = ids
          this.dialog.id = ''
          this.dialog.show = true
          this.dialog.readonly = false
          break
        case this.OperateType.Status:
          const targetStatus = row.dictState === '0' ? '1' : '0'
          this.doUpdateStatus(row.id, targetStatus)
          break
        default:
          this.dialog.parentIds = []
          this.dialog.id = row?.id || ''
          this.dialog.readonly = command === this.OperateType.View
          this.dialog.show = true
          break
      }
    },
    // 修改状态
    doUpdateStatus(id, status) {
      const params = {
        id,
        dictState: status,
        userId: this.$store.state.user.userInfo.user.staffId,
        userName: this.$store.state.user.userInfo.user.staffName
      }
      this.tableLoadingStatus = true
      this.$api
        .dictEnableOrDisable(params)
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('已更新状态')
            this.getDataList()
          } else {
            throw res.message || '设置状态失败'
          }
        })
        .catch((msg) => msg && this.$message.error(msg))
        .finally(() => (this.tableLoadingStatus = false))
    },
    // 删除一行数据
    doDelete(id) {
      this.tableLoadingStatus = true
      const params = {
        id,
        userId: this.$store.state.user.userInfo.user.staffId,
        userName: this.$store.state.user.userInfo.user.staffName
      }
      this.$api
        .dictDeleteById(params)
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('已删除')
            this.getDataList()
          } else {
            throw res.message || '删除失败'
          }
        })
        .catch((msg) => msg && this.$message.error(msg))
        .finally(() => (this.tableLoadingStatus = false))
    },
    // 点击图片更换预览地址
    onImageClick(e) {
      this.currentImage = e.target.currentSrc ?? ''
    }
  }
}
</script>
<template>
  <PageContainer class="dictionary-list">
    <template #content>
      <div class="dictionary-list__left">
        <div class="dictionary-list__left__header">
          <el-input v-model="treeSearchKeyWord" placeholder="输入关键字搜索" suffix-icon="el-icon-search"></el-input>
        </div>
        <el-tree
          ref="treeRef"
          node-key="id"
          :data="treeData"
          :current-node-key="currentKey"
          :props="{ label: 'dictName' }"
          :filter-node-method="filterNode"
          default-expand-all
          highlight-current
          class="dictionary-list__tree"
          @node-click="onTreeNodeClick"
        ></el-tree>
      </div>
      <div class="dictionary-list__right">
        <div class="dictionary-list__right__header">
          <el-form ref="formRef" :model="searchForm" class="dictionary-list__search" inline @submit.native.prevent="onSearch">
            <el-form-item prop="name">
              <el-input v-model="searchForm.name" clearable filterable placeholder="搜索名称"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSearch">查询</el-button>
              <el-button type="primary" plain @click="onReset">重置</el-button>
            </el-form-item>
          </el-form>
          <div class="dictionary-list__right__actions">
            <el-button type="primary" icon="el-icon-plus" :disabled="!currentKey" @click="onOperate(OperateType.Create)">添加字典值 </el-button>
          </div>
        </div>
        <div class="dictionary-list__table">
          <el-table v-loading="tableLoadingStatus" height="100%" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
            <el-table-column label="名称" prop="dictName" show-overflow-tooltip></el-table-column>
            <el-table-column label="编码" prop="dictCode" width="100px"></el-table-column>
            <el-table-column label="颜色" prop="colour" width="120px">
              <template #default="{ row }">
                <span v-if="row.colour" class="dictionary-list__table__color" :style="{ backgroundColor: row.colour }"></span>
                <span>{{ row.colour || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="图片" prop="image" width="100px" class-name="dictionary-list__table__col-img">
              <template #default="{ row }">
                <el-image v-if="row.pictureUrl" :src="$tools.imgUrlTranslation(row.pictureUrl)" :preview-src-list="[currentImage]" fit="cover" @click="onImageClick"></el-image>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="上级字典" prop="parent" :formatter="(row) => row.parentName || '-'" show-overflow-tooltip></el-table-column>
            <el-table-column label="状态" prop="dictState" width="100px">
              <template #default="{ row }">
                <span class="dictionary-list__tag" :class="`dictionary-list__tag--${row.dictState}`">
                  {{ row.dictState === '1' ? '启用' : '停用' }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="预设" prop="dictPreset" width="100px" :formatter="(row) => (row.dictPreset === '1' ? '是' : '否')"></el-table-column>
            <el-table-column label="操作" width="150px">
              <template #default="{ row }">
                <el-button type="text" @click="onOperate(OperateType.View, row)">查看</el-button>
                <el-button type="text" :disabled="row.dictPreset === '1'" @click="onOperate(OperateType.Edit, row)"> 编辑 </el-button>
                <el-dropdown :disabled="row.dictPreset === '1'" @command="onOperate($event, row)">
                  <el-button :disabled="row.dictPreset === '1'" type="text" style="margin-left: 10px">更多</el-button>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="OperateType.Child">增加下级</el-dropdown-item>
                    <el-dropdown-item :command="OperateType.Status">{{ row.dictState === '0' ? '启用' : '停用' }} </el-dropdown-item>
                    <el-dropdown-item :command="OperateType.Delete" class="text-red">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination
          class="dictionary-list__pagination"
          :current-page="pagination.current"
          :page-sizes="pagination.pageSizeOptions"
          :page-size="pagination.size"
          :layout="pagination.layoutOptions"
          :total="pagination.total"
          @size-change="paginationSizeChange"
          @current-change="paginationCurrentChange"
        >
        </el-pagination>
      </div>
      <!--字典值编辑-->
      <DictionaryValueEdit :visible.sync="dialog.show" v-bind="dialog" :dict-type="currentDictType" @success="getDataList" />
    </template>
  </PageContainer>
</template>
<style scoped lang="scss">
.dictionary-list {
  ::v-deep(> .container-content) {
    display: flex;
    background-color: #fff;
  }
  &__left {
    width: 280px;
    border-right: solid 1px #eee;
    display: flex;
    flex-flow: column nowrap;
    &__header {
      padding: 16px;
      line-height: 40px;
    }
    &::v-deep(.el-tree) {
      height: calc(100% - 64px);
      overflow: auto;
      padding: 0 16px 16px 16px;
      .el-tree-node__content {
        line-height: 36px;
        height: 36px;
      }
      .el-tree-node.is-current > .el-tree-node__content {
        color: #3562db;
        background: #e6effc;
      }
    }
  }
  &__right {
    flex: 1;
    overflow: hidden;
    padding: 16px;
    display: flex;
    flex-flow: column nowrap;
  }
  &__right__header {
    display: flex;
    justify-content: space-between;
  }
  &__right__actions {
    line-height: 40px;
  }
  &__table {
    margin-top: -10px;
    flex: 1;
  }
  &__pagination {
    margin-top: 10px;
  }
  &__tag {
    // 停用
    &--0 {
      --color: #f64646;
    }
    // 启用
    &--1 {
      --color: #00b42a;
    }
    &:before {
      content: '';
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
      height: 4px;
      width: 4px;
      border-radius: 4px;
      background-color: var(--color);
    }
  }
  &__table__color {
    display: inline-block;
    height: 16px;
    width: 16px;
    vertical-align: text-top;
    margin-right: 4px;
  }
  ::v-deep(.dictionary-list__table__col-img) {
    padding: 0;
    .cell {
      padding: 4px;
    }
    .el-image {
      height: 38px;
      .el-image__error {
        background-color: transparent;
      }
    }
  }
}
</style>
