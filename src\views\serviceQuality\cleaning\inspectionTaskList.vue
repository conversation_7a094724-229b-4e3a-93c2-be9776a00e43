<template>
  <PageContainer>
    <div slot="content" ref="contentRef">
      <div class="back" @click="goBack">
        <i class="el-icon-arrow-left"></i>
        <span>巡检任务列表</span>
      </div>
      <el-table v-loading="tableLoading" :data="tableData" border style="width: 100%;" height="88%" @row-click="handleClick">
        <el-table-column label="序号" type="index" width="50"> </el-table-column>
        <el-table-column prop="taskName" show-overflow-tooltip label="任务名称" width="200">
          <template slot-scope="scope">
            <span style="color: #3562db;">{{ scope.row.taskName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="planName" show-overflow-tooltip label="所属计划" width="150"> </el-table-column>
        <el-table-column label="周期类型" width="80" align="center">
          <template slot-scope="scope">
            <span>{{ cycleTypeFn(scope.row.cycleType) }}</span>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="应巡日期">
          <template slot-scope="scope">
            <span>{{ moment(scope.row.taskStartTime).format('YYYY-MM-DD') }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="distributionTeamName" show-overflow-tooltip label="巡检部门"></el-table-column>
        <el-table-column prop="planPersonName" show-overflow-tooltip label="巡检人员"></el-table-column>
        <el-table-column prop="totalCount" label="应巡点数" width="90" align="center"></el-table-column>
        <el-table-column prop="hasCount" label="实巡点数" width="90" align="center"></el-table-column>
        <el-table-column label="完成状态" width="90" align="center">
          <template slot-scope="scope">
            <div>
              <span v-if="scope.row.taskStatus == '1'" style="color: #f64848;"> 未完成 </span>
              <span v-if="scope.row.taskStatus == '2'" style="color: #73c245;"> 已完成 </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80">
          <template slot-scope="scope">
            <div class="operation">
              <el-link type="primary" style="margin-right: 10px;" @click="goDetails(scope.row)">详情</el-link>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="pageNo"
        :page-sizes="[15, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
    </div>
  </PageContainer>
</template>
<script>
import moment from 'moment'
import store from '@/store/index'
export default {
  name: 'InspectionTaskList',
  data() {
    return {
      moment,
      workNum: '', // 工单号
      flowCode: '', // 工单状态
      feedbackFlag: '', // 跟踪状态
      showTimeType: '1', // 申报时间
      dateVal: '', // 自定义时间
      tableData: [], // 表格数据
      total: 0, // 总条数
      pageNo: 1, // 当前页
      pageSize: 15, // 每页条数
      tableLoading: false, // 表格加载状态
      countData: {},
      workOrderDetailCenterShow: false,
      detailObj: {},
      dialogTitle: '',
      free1: '',
      teamId: '',
      dateType: '',
      chartType: '',
      taskStatus: '',
      // 周期类型
      cycleTypeList: [
        {
          cycleType: 8,
          label: '单次'
        },
        {
          cycleType: 6,
          label: '每日'
        },
        {
          cycleType: 0,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 5,
          label: '全年'
        }
      ]
    }
  },
  created() {
    this.teamId = this.$route.query.teamId
    this.dateType = this.$route.query.dateType
    this.chartType = this.$route.query.chartType
    this.taskStatus = this.$route.query.taskStatus
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        distributionTeamId: this.teamId,
        dateType: this.dateType,
        taskStatus: this.taskStatus,
        planTypeId: 'cleaning',
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      this.tableLoading = true
      this.$api.getTaskMaintaninList(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.data.list
        this.total = res.data.sum
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.pageNo = val
      this.getList()
    },
    handleClick(row) {
      this.detailObj = row
      this.dialogTitle = `综合维修（${this.detailObj.flowtype}）`
      this.workOrderDetailCenterShow = true
    },
    workOrderDetailCloseDialog() {
      this.workOrderDetailCenterShow = false
    },
    goBack() {
      this.$router.go(-1)
    },
    // 周期类型
    cycleTypeFn(cycleType) {
      const item = this.cycleTypeList.find((i) => i.cycleType == cycleType)
      return item.label
    },
    goDetails(row) {
      this.$router.push({
        path: '/comInspectionManagement/taskManagement/taskDetail',
        query: {
          taskId: row.id,
          row
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.container-content > div {
  height: 100%;
  background-color: #fff;
  padding: 16px;
  margin-top: 16px;
}

.el-table {
  margin-bottom: 12px;
}

::v-deep .el-date-editor .el-range__icon {
  line-height: 24px;
}

::v-deep .el-dialog {
  height: 80vh;
  margin-top: 10vh;
}

::v-deep .el-dialog__wrapper {
  overflow: hidden;
}

::v-deep .el-dialog__body {
  height: 93%;
}

::v-deep .el-table__row:hover {
  cursor: pointer;
}

.back {
  color: #121f3e;
  cursor: pointer;
  margin-bottom: 16px;
}
</style>
