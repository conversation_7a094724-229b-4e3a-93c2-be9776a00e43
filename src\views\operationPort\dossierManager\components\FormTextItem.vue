<template>
  <div class="FormTextItem">
    <span class="formLabel">{{ title }}：</span>
    <template v-if="type === 'text'">
      <span v-showtipPlus="value"></span>
    </template>
    <template v-if="type === 'textarea'">
      <span v-showtipPlus="value ? value.replace(/\n|\r\n/g, '<br>').replace(/ /g, ' &nbsp') : ''"></span>
    </template>
    <template v-if="type === 'slot'">
      <slot></slot>
    </template>
  </div>
</template>
<script>
export default {
  props: {
    title: String,
    value: [String, Number, null, undefined],
    type: {
      type: String,
      default: 'text'
    }
  }
}
</script>
<style lang="scss" scoped>
.FormTextItem {
  font-size: 14px;
  margin-bottom: 24px;
  display: flex;
  .seizeASeat {
    color: #8c8c8c;
  }
}
</style>
