<template>
  <PageContainer>
    <div slot="content" class="elevator-content">
      <div ref="largeScreenMonitoring" class="largeScreenMonitoring">
        <div class="ability-icon">
          <i v-if="!isFullScreen" class="screen-icon el-icon-edit" @click="allMoreOper"></i>
          <i class="screen-icon" :class="isFullScreen ? 'exit-full-screen' : 'full-screen'" @click="fullScreen('largeScreenMonitoring')"></i>
        </div>
        <div class="largeScreen_content">
          <dashboard
            v-if="dashExampleShow"
            id="dashExample"
            :style="{ width: editExampleShow ? 'calc(100% - 220px)' : '100%', height: editExampleShow ? dashExampleHeight : '100%' }"
          >
            <dash-layout v-bind="dlayout" :debug="false">
              <template v-for="(item, index) in dlayout.items">
                <dash-item v-if="item.status == 1" v-show="item.status == 1" v-bind.sync="dlayout.items[index]" :key="item.componentName" @resizeEnd="resizeEnd">
                  <component
                    :is="item.componentName"
                    :ref="item.componentName"
                    :item="item"
                    :query="query"
                    :socketData="socketelevatorMsgs"
                    @dataChange="chartDataChange"
                    @barClick="barClick"
                    @childEvent="childEvent"
                  ></component>
                </dash-item>
              </template>
            </dash-layout>
          </dashboard>
          <div id="editExample" :style="{ width: editExampleShow ? '215px' : '0' }">
            <div class="round-box">
              <div class="editExample-title">
                <div>添加模块</div>
                <p>请点击卡片修改显示模块</p>
              </div>
              <div class="editExample-content">
                <div
                  v-for="(item, index) in dlayout.items"
                  :key="index"
                  :class="{ 'editExample-content-item': true, 'active-editExample-item': activeExample.includes(item.componentName) }"
                  @click="addStaging(item)"
                >
                  <span>{{ item.componentTitle }}</span>
                </div>
              </div>
              <div class="editExample-footer">
                <el-button type="primary" plain :disabled="hasStatusFalg" @click="cancelStaging">取消</el-button>
                <el-button type="primary" @click="saveStaging">保存</el-button>
              </div>
            </div>
          </div>
        </div>
        <el-dialog v-if="drawer" :close-on-click-modal="false" custom-class="detailDialog main" width="50%" :visible.sync="drawer">
          <template slot="title">
            <span class="dialog-title">{{ `${barData.name}${barData.title}` }}</span>
          </template>
          <div class="drawer-content">
            <div id="floorAlarm" ref="floorAlarm"></div>
          </div>
        </el-dialog>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import { mapGetters } from 'vuex'
import { Dashboard, DashLayout, DashItem } from 'vue-responsive-dash'
import { monitorTypeList } from '@/util/dict.js'
import * as echarts from 'echarts'
import moment from 'moment'
moment.locale('zh-cn')
// 引入当前所有组件
const require_module = import.meta.globEager('./overflowComponents/*.vue')
const components = {}
for (const file_name in require_module) {
  components[file_name.slice(21, -4)] = require_module[file_name].default
}
export default {
  name: 'elevatorOverview',
  components: {
    Dashboard,
    DashLayout,
    DashItem,
    ...components
  },
  data() {
    return {
      requestHttp: __PATH.VUE_IEMC_ELEVATOR_API,
      // PollingDialogVisible: false, // 轮播设置弹框
      // pollingTime: localStorage.getItem('polingTime') || 20, // 摄像机轮询默认20s
      // pollingTimer: null, // 轮播摄像机定时器
      // pollingIndex: 0, // 当前轮播摄像机的序号
      // videoList: [], // 摄像机列表
      isFullScreen: false, // 是否全屏
      projectCode: monitorTypeList.find((item) => item.projectName == '电梯监测').projectCode, // 电梯code
      dlayout: {
        breakpoint: 'xs',
        numberOfCols: 24,
        items: []
      },
      reservedItems: [],
      dashExampleShow: true, // 工作台显示隐藏
      editExampleShow: false, // 编辑工作台显示隐藏
      activeExample: [], // 当前激活的工作台
      query: {
        type: 1
      },
      drawer: false,
      barData: {}
    }
  },
  computed: {
    ...mapGetters({
      socketelevatorMsgs: 'socket/socketelevatorMsgs'
    }),
    hasStatusFalg() {
      return this.dlayout.items.filter((e) => e.status === 0).length === this.dlayout.items.length
    },
    dashExampleHeight() {
      // 宽度缩小220px 高度同比例缩小 暂缓
      return '100%'
    }
  },
  // 监听侧边栏缩放 动态改变echarts图表大小
  watch: {
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          let echartsDom = [
            'workingTime',
            'workingFloor',
            'elevatorBrand',
            'serviceLife',
            'workingFloorDistance',
            'openDoorNum',
            'alarmTypeAnalysis',
            'evevatorRepeatOpen',
            'elevatorTrapped',
            'seizingFloor',
            'heatAlarm'
          ]
          echartsDom.forEach((item) => {
            if (this.$refs[item]) {
              this.$refs[item][0].echartsResize()
            }
          })
        })
      },
      deep: true
    }
  },
  created() {},
  mounted() {
    this.getWorktopManageList()
    window.onresize = () => {
      // 可视区域的高度
      const clientHeight = document.documentElement.clientHeight || document.body.clientHeight
      // screen是window的属性方法，window.screen可省略window，指的是窗口
      this.isFullScreen = screen.height == clientHeight
    }
    // this.$once('hook:beforeDestroy', () => {
    //   clearInterval(this.pollingTimer)
    //   this.pollingTimer = null
    // })
  },
  methods: {
    chartDataChange(val) {
      this.query = {
        ...this.query,
        ...val
      }
    },
    // 获取报警类型数据
    getAlarmTypeAnalysisData() {
      this.$api
        .getReasonStatisticPie(
          {
            projectCode: this.projectCode,
            parameterId: this.barData.id,
            type: this.type
          },
          this.requestHttp
        )
        .then((res) => {
          if (res.code == 200) {
            if (res.data.length > 0) {
              this.alarmTypeAnalysisShow = true
              let that = this
              setTimeout(() => {
                that.setAlarmTypeAnalysisEcharts(res.data)
              }, 50)
            } else {
              this.alarmTypeAnalysisShow = false
            }
          }
        })
    },
    // 子组件传递事件
    childEvent(event) {
      if (this.editExampleShow) return
      if (event.type === 'routerPush') {
        this.$router.push({
          path: event.path,
          query: event.query
        })
      }
    },
    // 缩放结束事件
    resizeEnd() {
      const resizeList = [
        'workingTime',
        'workingFloor',
        'elevatorBrand',
        'serviceLife',
        'workingFloorDistance',
        'openDoorNum',
        'alarmTypeAnalysis',
        'evevatorRepeatOpen',
        'elevatorTrapped',
        'seizingFloor',
        'heatAlarm'
      ]
      resizeList.forEach((item) => {
        if (this.$refs[item]) {
          this.$refs[el][0].echartsResize()
        }
      })
    },
    // 获取工作台管理列表
    getWorktopManageList() {
      this.editExampleShow = false
      this.$store.commit('settings/dragSidebarCollapse', false)
      this.$api.getWorktopManageList({ userId: this.$store.state.user.userInfo.user.staffId, menuType: 13 }).then((res) => {
        if (res.code == 200) {
          const data = res.data
          const items = []
          data.forEach((item, index) => {
            items.push({
              id: item.id,
              componentName: item.componentName,
              componentTitle: item.componentTitle,
              x: item.x,
              y: item.y,
              width: item.width,
              height: item.height,
              dragAllowFrom: '.drag_class',
              resizable: false,
              draggable: false,
              path: item.path,
              status: item.status
            })
          })
          this.dashExampleShow = false
          this.$nextTick(() => {
            this.dashExampleShow = true
            this.dlayout.items = items
            this.reservedItems = JSON.parse(JSON.stringify(items))
          })
        }
      })
    },
    // 编辑事件
    allMoreOper() {
      this.dlayout.items.map((e) => {
        e.resizable = true
        e.draggable = true
      })
      this.activeExample = this.dlayout.items.filter((e) => e.status === 1).map((e) => e.componentName)
      this.editExampleShow = true
      this.$store.commit('settings/toggleSidebarCollapse', true)
      this.$store.commit('settings/dragSidebarCollapse', true)
      this.$nextTick(() => {
        this.resizeEnd()
      })
    },
    // 添加/减少模块
    addStaging(item) {
      if (this.activeExample.includes(item.componentName)) {
        this.activeExample.splice(this.activeExample.indexOf(item.componentName), 1)
        this.dlayout.items.map((e) => {
          if (e.componentName === item.componentName) {
            e.status = 0
          }
        })
      } else {
        this.activeExample.push(item.componentName)
        this.dlayout.items.map((e) => {
          if (e.componentName === item.componentName) {
            e.status = 1
          }
        })
      }
    },
    // 取消编辑工作台模块
    cancelStaging() {
      this.editExampleShow = false
      this.dashExampleShow = false
      this.$nextTick(() => {
        this.dashExampleShow = true
        this.dlayout.items = JSON.parse(JSON.stringify(this.reservedItems))
        this.$store.commit('settings/dragSidebarCollapse', false)
      })
    },
    // 保存工作台模块
    saveStaging() {
      const items = this.dlayout.items
      const params = []
      items.forEach((item) => {
        params.push({
          id: item.id,
          componentName: item.componentName,
          componentTitle: item.componentTitle,
          path: item.path,
          status: item.status,
          x: item.x,
          y: item.y,
          width: item.width,
          height: item.height
        })
      })
      this.$api.saveWorktopManage({ jsonList: JSON.stringify(params), userId: this.$store.state.user.userInfo.user.staffId, menuType: 13 }).then((res) => {
        if (res.code == 200) {
          this.$message.success('保存成功')
          this.getWorktopManageList()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 全屏事件
    fullScreen(dom) {
      this.isFullScreen = !this.isFullScreen
      const full = this.$refs[dom]
      if (this.isFullScreen) {
        if (full.RequestFullScreen) {
          full.RequestFullScreen()
          // 兼容Firefox
        } else if (full.mozRequestFullScreen) {
          full.mozRequestFullScreen()
          // 兼容Chrome, Safari and Opera等
        } else if (full.webkitRequestFullScreen) {
          full.webkitRequestFullScreen()
          // 兼容IE/Edge
        } else if (full.msRequestFullscreen) {
          full.msRequestFullscreen()
        }
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen()
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen()
        } else if (document.webkitCancelFullScreen) {
          document.webkitCancelFullScreen()
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen()
        }
      }
    },
    // 柱状图点击事件
    barClick(data) {
      console.log(data)
      this.barData = data
      if (data.type === 1) {
        this.barData.title = ''
        this.$nextTick(() => {
          this.getAlarmTypeEvevatorList()
        })
      } else {
        this.$nextTick(() => {
          this.getElevatorFloorList()
        })
      }
      this.drawer = true
    },
    // 获取抽屉图表数据
    getElevatorFloorList() {
      let data = {
        projectCode: this.projectCode,
        parameterIds: this.barData.id,
        surveyCode: this.barData.code
      }
      this.$api.getFloorAlarmData(data, this.requestHttp).then((res) => {
        if (res.code === '200') {
          let nameList = []
          let valueList = []
          res.data.forEach((item) => {
            nameList.push(item.surveyName)
            valueList.push(item.count)
          })
          setTimeout(() => {
            this.setRankingBarEcharts(nameList, valueList)
          }, 100)
        }
      })
    },
    array2obj(array, key) {
      var resObj = {}
      for (var i = 0; i < array.length; i++) {
        resObj[array[i][key]] = array[i]
      }
      return resObj
    },
    // 报警类型饼图
    getAlarmTypeEvevatorList() {
      let startTime = ''
      let endTime = ''
      switch (this.query.type) {
        case 1:
          startTime = moment().startOf('month').format('YYYY-MM-DD')
          endTime = moment().endOf('month').format('YYYY-MM-DD')
          break
        case 2:
          startTime = moment().isoWeekday(1).format('YYYY-MM-DD')
          endTime = moment().isoWeekday(7).format('YYYY-MM-DD')
          break
        case 3:
          startTime = moment().format('YYYY-MM-DD')
          endTime = startTime
          break
        default:
          break
      }
      this.$api
        .getReasonStatisticPie(
          {
            projectCode: this.projectCode,
            startTime: startTime,
            endTime: endTime,
            parameterId: this.barData.parameterId
          },
          this.requestHttp
        )
        .then((res) => {
          if (res.code == 200) {
            if (res.data.length > 0) {
              this.alarmTypeAnalysisShow = true
              let that = this
              setTimeout(() => {
                that.setAlarmTypeAnalysisEcharts(res.data)
              }, 50)
            }
          }
        })
    },
    // 报警类型数据echarts
    setAlarmTypeAnalysisEcharts(data) {
      const getchart = echarts.init(this.$refs['floorAlarm'])
      const nameList = Array.from(data, (item) => item.name)
      const sum = data.reduce((per, cur) => per + cur.value, 0)
      var objData = this.array2obj(data, 'name')
      let option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          type: 'scroll',
          orient: 'vertical',
          top: '10',
          x: '75%',
          hoverLink: true, // 启用图例项和相应系列项的hover联动
          pageIconColor: '#5188fc', // 激活的分页按钮颜色
          pageIconInactiveColor: '#c2c6ce', // 没激活的分页按钮颜色
          pageTextStyle: {
            color: '#FFF' // 设置图例数字的颜色
          },
          data: nameList,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          formatter: function (name) {
            return '{c|' + objData[name].percent + '}{a|' + objData[name].value + '   ' + name + '}'
          },
          textStyle: {
            rich: {
              a: {
                align: 'center',
                fontSize: 13,
                color: 'rgba(255,255,255,1)'
              },
              c: {
                align: 'center',
                width: 70,
                fontSize: 15,
                color: '#00C2FF'
              }
            }
          }
        },
        series: [
          {
            type: 'pie',
            roundCap: true,
            radius: ['56%', '72%'],
            center: ['30%', '50%'],
            hoverAnimation: true,
            label: {
              normal: {
                show: false,
                position: 'center',
                // formatter: '{value|{d}' + '%' + '}\n{label|{b}}',
                formatter: function (data) {
                  return '{value|' + objData[data.name].percent + '}\n{label|' + data.name + '}'
                },
                rich: {
                  value: {
                    padding: 5,
                    align: 'center',
                    verticalAlign: 'middle',
                    fontSize: 24,
                    fontWeight: '600'
                  },
                  label: {
                    align: 'center',
                    verticalAlign: 'middle',
                    color: '#A3A9AD',
                    fontWeight: '400',
                    fontSize: 14
                  }
                }
              },
              emphasis: {
                show: true,
                textStyle: {
                  fontSize: '12',
                  color: '#fff'
                }
              }
            },
            itemStyle: {
              borderWidth: 3,
              borderColor: '#09141e'
            },
            data: data
          },
          {
            name: '',
            type: 'pie',
            startAngle: 80,
            radius: ['54%'],
            hoverAnimation: false,
            center: ['30%', '50%'],
            tooltip: {
              show: false
            },
            itemStyle: {
              color: 'rgba(10, 25, 40, 1)',
              borderWidth: 1,
              borderColor: '#1E2C39'
            },
            data: [sum]
          },
          {
            name: '',
            type: 'pie',
            startAngle: 80,
            radius: ['48%'],
            hoverAnimation: false,
            center: ['30%', '50%'],
            tooltip: {
              show: false
            },
            itemStyle: {
              color: 'rgba(21, 36, 50, 1)'
            },
            data: [sum]
          },
          {
            name: '',
            type: 'pie',
            startAngle: 80,
            radius: ['77%', '78%'],
            hoverAnimation: false,
            center: ['30%', '50%'],
            tooltip: {
              show: false
            },
            itemStyle: {
              normal: {
                color: 'rgba(10, 25, 40, .1)',
                borderColor: '#1E2C39',
                borderWidth: 1
              }
            },
            data: [sum]
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 电梯4个状态数据echarts
    setRankingBarEcharts(nameList, valueList) {
      valueList = valueList.reverse()
      nameList = nameList.reverse()
      const getchart = echarts.init(this.$refs['floorAlarm'])
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '6%',
          right: '15%',
          top: '6%',
          bottom: '5%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'value',
            position: 'top',
            splitNumber: 3,
            splitLine: {
              lineStyle: {
                color: '#283849',
                type: 'dashed'
              }
            },
            axisLabel: {
              textStyle: {
                color: '#6A737C'
              },
              fontSize: 11,
              interval: 30000,
              hideOverlap: true // 隐藏互相遮挡的文本标签
            }
          }
        ],
        yAxis: [
          {
            type: 'category',
            data: nameList,
            axisTick: { show: false },
            axisLine: {
              lineStyle: {
                color: '#283849',
                type: 'dashed'
              }
            },
            axisLabel: {
              textStyle: {
                color: '#6A737C'
              },
              margin: 8,
              interval: 0,
              hideOverlap: true, // 隐藏互相遮挡的文本标签
              formatter: function (val) {
                var strs = val.split('') // 字符串数组
                var str = ''
                // strs循环 forEach 每五个字符增加换行符
                strs.forEach((item, index) => {
                  if (index % 6 === 0 && index !== 0) {
                    str += '\n'
                  }
                  str += item
                })
                return str
              }
            }
          }
        ],
        series: [
          {
            type: 'bar',
            barWidth: 8,
            sort: false,
            data: valueList,
            itemStyle: {
              normal: {
                color: '#3562db'
                // color: function (params) {
                //   // 首先定义一个数组
                //   var colorList = ['#2784E9', '#F4DB67']
                //   if (params.dataIndex % 2 == 0) {
                //     return colorList[0]
                //   } else {
                //     return colorList[1]
                //   }
                // }
                // color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                //   {
                //     offset: 0,
                //     color: color[0]
                //   },
                //   {
                //     offset: 1,
                //     color: color[1]
                //   }
                // ])
              }
            }
          }
        ]
        // dataZoom: [
        //   {
        //     type: 'slider',
        //     show: true,
        //     orient: 'vertical',
        //     // 设置组件控制的y轴
        //     yAxisIndex: 0,
        //     right: 4,
        //     start: 100,
        //     // end: 0,
        //     width: 8,
        //     // borderRadius: 0,
        //     borderColor: 'transparent',
        //     fillerColor: '#6580b8', // 滑动块的颜色
        //     backgroundColor: 'transparent', // 两边未选中的滑动条区域的颜色
        //     // 是否显示detail，即拖拽时候显示详细数值信息
        //     showDataShadow: false,
        //     showDetail: false,
        //     zoomLock: true,
        //     // 控制手柄的尺寸
        //     // handleSize: 12,
        //     // 是否在 dataZoom-silder 组件中显示数据阴影。数据阴影可以简单地反应数据走势。
        //     filterMode: 'filter',
        //     handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
        //     handleStyle: {
        //       color: '#6580b8',
        //       borderColor: '#6580b8'
        //     },
        //     maxValueSpan: 6,
        //     minValueSpan: 6,
        //     brushSelect: false
        //   },
        //   {
        //     type: 'inside',
        //     // show: false,
        //     yAxisIndex: [0],
        //     zoomOnMouseWheel: false, // 关闭滚轮缩放
        //     moveOnMouseWheel: true, // 开启滚轮平移
        //     moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
        //   }
        // ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.elevator-content {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  .largeScreenMonitoring {
    width: 100%;
    height: 100%;
    // overflow: auto;
    background: url('~@/assets/images/elevator/elevator-bg.png') no-repeat;
    background-size: 100% 100%;
    .ability-icon {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding-top: 10px;
      padding-right: 10px;
      .screen-icon {
        display: inline-block;
        width: 15px;
        height: 15px;
        margin-left: 10px;
        cursor: pointer;
        color: #fff;
      }
      .full-screen {
        background: url('~@/assets/images/elevator/full-screen.png') no-repeat;
        background-size: 100% 100%;
      }
      .exit-full-screen {
        background: url('~@/assets/images/elevator/exit-full-screen.png') no-repeat;
        background-size: 100% 100%;
      }
    }
    .largeScreen_content {
      width: 100%;
      height: calc(100% - 30px);
      display: flex;
    }
    .pie-decoration {
      position: absolute;
      height: 50%;
      aspect-ratio: 1;
      background: url('~@/assets/images/elevator/pie-decoration.png') no-repeat;
      background-size: 100% 100%;
    }
    .card_box_short_bg {
      background: url('~@/assets/images/elevator/card-title-short-bg.png') no-repeat;
      background-size: 100% 100%;
    }
  }
}
// 主要按钮
::v-deep .el-button--primary,
.el-button--default {
  color: #fff;
  border: none;
  background: url('~@/assets/images/elevator/button-primary.png') no-repeat;
  background-size: 100% 100%;
  font-weight: 400;
  font-size: 14px;
  padding: 8px 22px;
  font-family: PingFangSC-Regular;
  &:hover,
  &:focus {
    color: #7cd0ff;
    font-family: PingFangSC-Regular;
    background: url('~@/assets/images/elevator/button-primary-hover.png') no-repeat;
    background-size: 100% 100%;
    font-weight: 500;
  }
  &.is-disabled {
    background: url('~@/assets/images/elevator/button-primary-hover.png') no-repeat;
    background-size: 100% 100%;
    font-weight: 500;
    &:hover {
      background: url('~@/assets/images/elevator/button-primary-hover.png') no-repeat;
      background-size: 100% 100%;
      font-weight: 500;
    }
  }
}
// 副要按钮
::v-deep .el-button--primary.is-plain,
.el-button--default.is-plain {
  color: #fff;
  background: url('~@/assets/images/elevator/button-primary-plain.png') no-repeat;
  background-size: 100% 100%;
  font-size: 14px;
  font-family: PingFangSC-Regular;
  padding: 8px 22px;
  &:hover,
  &:focus {
    color: #fff;
    background: url('~@/assets/images/elevator/button-primary-plain-hover.png') no-repeat;
    background-size: 100% 100%;
    font-family: PingFangSC-Regular-Blod, PingFang SC;
  }
}
#dashExample {
  height: 100%;
  // background: #f5f7f9;
  overflow-y: auto;
  overflow-x: hidden;
  // firefox隐藏滚动条
  scrollbar-width: none;
  // chrome隐藏滚动条
  &::-webkit-scrollbar {
    display: none;
  }
}
#editExample {
  // height: 100%;
  // background-color: #fff;
  box-shadow: 10px 0 10px -10px #c7c7c7;
  box-shadow: 10px 0 10px -10px #c7c7c7;
  padding: 16px 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: linear-gradient(180deg, #0a1e31 0%, #09141e 100%);
  .round-box {
    width: 100%;
    height: 100%;
    background-color: #08305d0a;
    border-radius: 10px;
    padding: 10px 0;
    display: flex;
    flex-direction: column;
  }
  .editExample-title {
    > div {
      font-size: 16px;
      font-family: PingFangSC-Regular-Blod;
      color: #fff;
      margin: 0 0 12px 15px;
      height: 22px;
      line-height: 22px;
    }
    > p {
      height: 18px;
      font-size: 12px;
      line-height: 18px;
      margin: 0 0 12px 15px;
      color: #999;
    }
  }
  .editExample-content {
    flex: 1;
    // height: calc(100% - 50px);
    overflow-y: auto;
    padding: 10px 20px;
    .editExample-content-item {
      cursor: pointer;
      width: 100%;
      box-sizing: border-box;
      height: 40px;
      line-height: 40px;
      margin-bottom: 16px;
      border-radius: 4px;
      padding: 0 10px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      background-color: #0f203d;
      text-align: center;
      color: #fff;
      font-size: 13px;
      font-family: 'PingFang SC-Regular', 'PingFang SC';
      &:hover {
        // color: #3562db;
        // background: rgba(53, 98, 219, 0.2);
        box-shadow: 0 4px 12px 0 rgb(17 19 23 / 10%);
      }
    }
    .active-editExample-item {
      background-color: #0e2a5c !important;
      color: #fff !important;
      border-color: #4e78cf !important;
    }
  }
  .editExample-footer {
    text-align: right;
    padding: 5px 15px;
  }
}
::v-deep .detailDialog {
  background: url('~@/assets/images/elevator/dialog-bg.png') no-repeat;
  background-size: 100% 100%;
  .el-dialog__header {
    color: #fff;
    padding: 0;
    padding-top: 30px;
    padding-left: 40px;
    // padding-bottom: 20px;
  }
  .el-dialog__headerbtn {
    top: 30px;
    right: 30px;
  }
  .el-dialog__body {
    height: 650px;
    max-height: 650px;
    overflow-y: auto;
    .drawer-content {
      width: 100%;
      height: 100%;
      #floorAlarm {
        width: 90%;
        height: 100%;
        z-index: 2;
      }
    }
  }
}
</style>
