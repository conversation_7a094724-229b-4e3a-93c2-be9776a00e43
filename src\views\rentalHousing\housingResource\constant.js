/**
 * 房间状态枚举值
 */
export const HouseStatusType = {
  /** 闲置中 */
  FREE: '0',
  /** 待入住 */
  READY: '1',
  /** 使用中 */
  USING: '2',
  /** 已下架 */
  OFF: '3',
  /** 已超期 */
  OVERDUE: '4'
}
/**
 * 房源状态字典
 */
export const HouseStatus = [
  { value: HouseStatusType.FREE, label: '闲置中' },
  { value: HouseStatusType.READY, label: '待入住' },
  { value: HouseStatusType.USING, label: '使用中' },
  { value: HouseStatusType.OFF, label: '已下架' },
  { value: HouseStatusType.OVERDUE, label: '已超期' }
]
/**
 * 退租类型
 */
export const LeaveType = {
  // 到期
  END: '1',
  // 中途退
  BREAK: '2'
}
/**
 * 退租类型字典
 */
export const LeaveTypeList = [
  { value: LeaveType.END, label: '到期退租' },
  { value: LeaveType.BREAK, label: '中途退租' }
]
/**
 * 楼层命名规则类型
 */
export const FloorNameType = {
  // L开头
  L: '1',
  // F结尾
  F: '2',
  // 中文规则
  ZH: '3'
}
/**
 * 楼层命名规则选项
 */
export const FloorNameOptions = [
  { value: FloorNameType.L, label: 'L+数字' },
  { value: FloorNameType.F, label: '数字+F' },
  { value: FloorNameType.ZH, label: '中文规则' }
]
