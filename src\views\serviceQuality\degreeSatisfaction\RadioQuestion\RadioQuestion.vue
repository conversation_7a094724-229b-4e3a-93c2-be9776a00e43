<template>
  <div class="main-container">
    <el-form ref="radioForm" :model="formItem" :rules="rules" label-width="100px">
      <div style="background-color: #fff;">
        <el-row style="display: flex;">
          <el-col :span="1.5" class="table-label">
            <i class="is-require">*</i>
            <span class="cgangeStyle">标题 :</span>&nbsp;
          </el-col>
          <el-col :span="21">
            <el-form-item prop="name" label-width="0">
              <el-input v-model="formItem.name" type="textarea" rows="1" style="width: 40%;"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <div style="display: flex; padding-right: 10px;">
          <div class="table-label">
            <span class="cgangeStyle">选项：</span>
          </div>
          <el-table :data="formItem.optionsData">
            <el-table-column label="操作" width="100" align="center">
              <template slot-scope="scope">
                <span style="cursor: pointer; padding: 0 5px; color: #3562db;" @click="addOption">新增</span>
                <span v-if="scope.$index != 0" style="cursor: pointer; color: red; padding: 0 5px;" @click.prevent="removeOption(scope.row)">删除</span>
              </template>
            </el-table-column>
            <el-table-column label="选项" align="center">
              <template slot-scope="scope">
                <el-input v-model.trim="scope.row.name" show-word-limit placeholder="请输入选项" :maxlength="50" :readonly="readonly"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="可输入" align="center" width="120px">
              <template slot-scope="scope">
                <el-checkbox :checked="scope.row.isInput === 1" @change="handleCheckBoxChange($event, scope.row, 'isInput')"></el-checkbox>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <el-row style="display: flex; margin-top: 10px; background-color: #fff; align-items: center;">
        <!-- <el-col :span="3">
          <el-form-item label-width="0" style="margin: 0;">
            <el-checkbox v-model="isSetSore">设置分数</el-checkbox>
          </el-form-item>
        </el-col> -->
        <el-col :span="3">
          <el-form-item label-width="0" style="margin: 0;">
            <el-checkbox v-model="isSetMust">是否必填</el-checkbox>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="6">
          <el-form-item label="选项排序" style="margin: 0;">
            <el-select v-model="formItem.rowCount" placeholder="请选排序方式">
              <el-option label="横排" value="5"></el-option>
              <el-option label="竖排" value="1"></el-option>
              <el-option label="每行2个" value="2"></el-option>
              <el-option label="每行3个" value="3"></el-option>
              <el-option label="每行4个" value="4"></el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
      </el-row>
    </el-form>
  </div>
</template>

<script>
import notice from '../utils/notice'
import utils from '../utils/utils'
import checkCentralControl from '../check/checkCentralControl'
export default {
  props: {
    questionSubject: {
      type: Object
    }
  },
  data() {
    return {
      readonly: false,
      arr: [],
      formItem: {
        name: '',
        optionsData: [
          { name: '', key: this.guid(), score: 0, isInput: 0, isDelete: 0 } // isDelete是否删除  0 否 1是; isInput 是否可输入 0否 1是
        ],
        isMust: 0, // 是否必填 1是 0否
        score: 0, // 是否设置分数  1是 0否
        rowCount: '1' // 排序方式 一行显示几个  1.竖排 2.一排2个 3.一排3个 4.一排4个 5.横排
      },
      rules: checkCentralControl.getCheckConfig('readio') // 获取表单校验对象  (单选校验)
    }
  },
  computed: {
    isSetSore: {
      set(value) {
        this.formItem.score = value ? 1 : 0
        this.resetOptionScore()
      },
      get() {
        return this.formItem.score === 1
      }
    },
    isSetMust: {
      set(value) {
        this.formItem.isMust = value ? 1 : 0
      },
      get() {
        return this.formItem.isMust === 1
      }
    }
  },
  mounted() {
    if (this.questionSubject.id) {
      const formData = {
        ...this.questionSubject,
        rowCount: this.questionSubject.rowCount + ''
      }
      this.formItem = JSON.parse(JSON.stringify(formData))
      // 添加copy数组
      this.arr = JSON.parse(JSON.stringify(this.formItem.optionsData))
    }
    notice.$emit('initChildComponent', this, '单选题')
    notice.$on('handleSubmit', (component) => {
      if (this === component) {
        this.submitForm()
      }
    })
  },
  methods: {
    guid() {
      return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1)
    },
    resetOptionScore() {
      this.formItem.optionsData.forEach((item) => {
        item.score = 0
      })
    },
    handleCheckBoxChange(val, option, item) {
      console.log(val, option, item, 'val, option, item')
      option[item] = val ? 1 : 0
    },
    submitForm() {
      this.$refs['radioForm'].validate((valid) => {
        if (valid) {
          this.saveQuestionSubject()
        } else {
          return false
        }
      })
    },
    deleteArrKey(arrMain, arrFrom) {
      for (var a = 0; a < arrMain.length; a++) {
        for (var b = 0; b < arrFrom.length; b++) {
          if (arrMain[a].id == arrFrom[b].id || (arrMain[a].key && arrMain[a].key == arrFrom[b].key)) {
            arrFrom.splice(b, 1)
          }
        }
      }
    },
    saveQuestionSubject(callBack) {
      this.deleteArrKey(this.formItem.optionsData, this.arr)
      // 合并数组
      var arrCopy = this.arr.concat(this.formItem.optionsData)
      let len = this.formItem.optionsData.filter((item) => item.name == '')
      if (len.length) {
        return this.$message.error('请检查选项是否填写完整')
      }
      const params = {
        id: this.questionSubject.id ? this.questionSubject.id : '',
        type: 'radio',
        name: this.formItem.name,
        isMust: this.formItem.isMust,
        score: this.formItem.score,
        rowCount: this.formItem.rowCount,
        pvqId: localStorage.getItem('questId'),
        options: JSON.stringify(arrCopy),
        userId: this.$store.state.user.userInfo.userId
      }

      if (params.id == '') {
        this.$api.saveQuestion(params).then((res) => {
          if (res.status == 200) {
            this.$message({
              message: res.message,
              type: 'success'
            })
            notice.$emit('closeQuestionDialog', this)
          }
        })
      } else {
        console.log(params.options, 'paramsparamsparamsparams')
        this.$api.updateQuestion(params).then((res) => {
          if (res.status == 200) {
            this.$message({
              message: res.message,
              type: 'success'
            })
            notice.$emit('closeQuestionDialog', this)
          }
        })
      }
    },
    // 删除选项成功之后重置表单数据
    resetOptionData(data) {
      this.formItem = { ...data, rowCount: this.questionSubject.rowCount + '' }
    },
    // 删除存储数组
    arrCopyCheck(item) {
      for (var a = 0; a < this.arr.length; a++) {
        if (this.arr[a].id === item.id) {
          this.arr[a].isDelete = 1
        }
      }
    },
    // 删除数组中的项
    deleteArr(item, arrFlag, itemFlag) {
      for (var a = 0; a < this.formItem.optionsData.length; a++) {
        if (this.formItem.optionsData[a][arrFlag] === item[itemFlag]) {
          this.formItem.optionsData.splice(a, 1)
        }
      }
    },
    // 移除选项，移除时调用后台接口
    removeOption(item) {
      // 编辑时删除选项
      if (this.questionSubject.id) {
        if (item.id) {
          this.arrCopyCheck(item)
          this.deleteArr(item, 'id', 'id')
        } else {
          this.deleteArr(item, 'key', 'key')
        }
      } else {
        // 新建时删除选项
        var index = this.formItem.optionsData.indexOf(item)
        if (index !== -1) {
          this.formItem.optionsData.splice(index, 1)
        }
      }
    },
    // 添加选项
    addOption() {
      this.formItem.optionsData.push({
        name: '',
        key: this.guid(),
        score: 0,
        isInput: 0,
        isDelete: 0
      })
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
.main-container {
  width: 100%;

  textarea {
    resize: none;
  }

  .el-input__inner {
    width: 100%;
  }

  .el-dialog__body {
    padding-bottom: 20px;
  }

  .table-label {
    text-align: left;
    box-sizing: border-box;
    padding-left: 15px;
    margin-top: 10px;
  }

  .is-require {
    position: absolute;
    top: 0;
    left: 0%;
    color: red;
    padding-top: 12px;
  }

  .operator-container {
    text-align: center;
    line-height: 40px;

    .row-option {
      cursor: pointer;
      display: inline-block;
      width: 16px;
      height: 16px;
      vertical-align: middle;
      background-size: 100% 100%;
    }
  }

  .el-input .el-input__inner {
    padding: 0 0 0 10px;
  }

  .el-select .el-input__inner {
    padding: 0 10px;
  }

  .el-checkbox {
    margin-left: 15px;
  }
}
</style>
