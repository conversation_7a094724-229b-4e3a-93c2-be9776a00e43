<template>
  <PageContainer v-loading="pageLoading" footer>
    <div slot="content" class="content">
      <div class="title">{{ $route.query.type !== 'add' ? $route.query.name : '新增' }}</div>
      <div class="baseInfo">
        <!-- 基础信息 -->
        <div class="info-title">
          <div class="label_line"><span class="line"></span><span class="label">基础信息</span></div>
        </div>
        <div class="baseInfo-content">
          <el-form ref="form" :model="form" :rules="rules" label-width="140px" inline>
            <el-form-item label="网格名称" prop="gridName">
              <el-input v-model="form.gridName" maxlength="10" :disabled="$route.query.type === 'detail'" placeholder="请输入网格名称"></el-input>
            </el-form-item>
            <el-form-item label="责任部门" prop="responsibleDepartId">
              <el-select
                v-model="form.responsibleDepartId"
                :disabled="$route.query.type === 'detail'"
                style="width: 100%"
                popper-class="fireproof-popperClass"
                placeholder="请选择责任部门"
                filterable
                @change="deptChange"
              >
                <el-option v-for="item in deptList" :key="item.id" :label="item.deptName" :value="item.id"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="责任人" prop="responsiblePersonId">
              <el-input v-if="$route.query.type === 'detail'" v-model="form.responsiblePersonName" disabled></el-input>
              <el-select
                v-else
                v-model="form.responsiblePersonId"
                popper-class="fireproof-popperClass"
                style="width: 100%"
                placeholder="请选择责任人"
                @change="(e) => userchange(e, 'personNumber', 'responsiblePersonName')"
              >
                <el-option v-for="item in userList" :key="item.id" :label="item.staffName" :value="item.id"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="责任人电话" prop="personNumber">
              <el-input v-model="form.personNumber" maxlength="11" :disabled="$route.query.type === 'detail'" placeholder="请输入责任人电话"></el-input>
            </el-form-item>
            <el-form-item label="安全责任人" prop="safePersonId">
              <el-input v-if="$route.query.type === 'detail'" v-model="form.safePersonName" disabled></el-input>
              <el-select
                v-else
                v-model="form.safePersonId"
                popper-class="fireproof-popperClass"
                style="width: 100%"
                placeholder="请选择安全责任人"
                @change="(e) => userchange(e, 'safePersonNumber', 'safePersonName')"
              >
                <el-option v-for="item in userList" :key="item.id" :label="item.staffName" :value="item.id"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="安全责任人电话" prop="safePersonNumber">
              <el-input v-model="form.safePersonNumber" maxlength="11" :disabled="$route.query.type === 'detail'" placeholder="请输入安全责任人电话"></el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="space">
        <!-- 包含空间 -->
        <div class="info-title hasBtn">
          <div class="label_line"><span class="line"></span><span class="label">包含空间</span></div>
          <el-button v-if="$route.query.type !== 'detail'" type="primary" icon="el-icon-plus" @click="showDeptDialog">添加空间</el-button>
        </div>
        <div class="space-content">
          <el-table v-loading="tableLoading" border :data="list" style="width: 100%" height="360">
            <el-table-column prop="spaceName" label="空间名称" />
            <el-table-column prop="modelCode" label="空间编码" />
            <el-table-column prop="localCode" label="本地编码" />
            <el-table-column prop="regionName" label="所在位置" />
            <el-table-column v-if="$route.query.type !== 'detail'" label="操作" width="80">
              <template slot-scope="scope">
                <span style="color: #ff4d4f; cursor: pointer" @click="remove(scope.row, scope.$index)">移除</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="bottom-pagination" style="height: 40px">
            <el-pagination
              :page-sizes="[15, 30, 20, 100]"
              :page-size="pagination.pageSize"
              :current-page="pagination.pageNo"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            >
            </el-pagination>
          </div>
        </div>
      </div>
      <deptDialog v-if="isDeptDialog" :visible.sync="isDeptDialog" title="添加空间" :spaceList="allList" @selectDept="selectDept" />
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="back">返回</el-button>
      <el-button v-if="$route.query.type !== 'detail'" type="primary" @click="submitForm">确认</el-button>
    </div>
  </PageContainer>
</template>
<script>
import { transData } from '@/util'
import deptDialog from './deptDialog.vue'
export default {
  name: 'addFireproof',
  components: { deptDialog },
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      const titleArr = {
        add: '新增',
        edit: '编辑',
        detail: '查看'
      }
      to.meta.title = titleArr[to.query.type] + '防火分区'
    }
    next()
  },
  async beforeRouteLeave(to, from, next) {
    if (!['fireproof'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      form: {
        gridName: '',
        responsibleDepartId: '',
        responsibleDepartName: '',
        responsiblePersonId: '',
        responsiblePersonName: '',
        personNumber: '',
        safePersonId: '',
        safePersonName: '',
        safePersonNumber: '',
        gridArea: '',
        regionList: []
      },
      rules: {
        gridName: [{ required: true, message: '请输入网格名称', trigger: 'blur' }],
        responsibleDepartId: [{ required: true, message: '请选择责任部门', trigger: 'change' }],
        responsiblePersonId: [{ required: true, message: '请选择责任人', trigger: 'change' }],
        personNumber: [
          { required: true, message: '请输入责任人电话', trigger: 'change' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        safePersonId: [{ required: true, message: '请选择安全责任人', trigger: 'change' }],
        safePersonNumber: [
          { required: true, message: '请输入安全责任人电话', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ]
      },
      userList: [],
      deptList: [],
      tableLoading: false,
      allList: [],
      list: [],
      isDeptDialog: false,
      total: 0,
      pagination: {
        pageSize: 15,
        pageNo: 1
      },
      pageLoading: false
    }
  },
  watch: {
    'form.responsibleDepartId': {
      handler(val) {
        if (val) {
          this.getUserList(val)
        }
      },
      immediate: true
    }
  },
  mounted() {
    if (!this.$store.state.keepAlive.list.includes('fireproof')) {
      this.initEvent()
    }
  },
  activated() {
    this.pagination = {
      pageSize: 15,
      pageNo: 1
    }
    this.total = 0
    this.initEvent()
  },
  methods: {
    initEvent() {
      this.getDept()
      // const data = this.$options.data()
      // delete data.rules
      // Object.assign(this.$data, data)
      if (this.$route.query.type !== 'add') {
        this.getDetail()
      } else {
        this.allList = []
        this.list = []
      }
      this.$refs.form.clearValidate()
      this.$refs.form.resetFields()
    },
    // 获取防火分区详情
    getDetail() {
      const params = {
        id: this.$route.query.id,
        page: 1,
        pageSize: 9999
      }
      this.pageLoading = true
      this.$api.fireproofDetail(params).then((res) => {
        if (res.code === '200') {
          this.form = res.data
          this.form.regionList = res.data.regionList
          this.allList = res.data.regionList
          this.total = res.data.total
          this.list = this.allList.slice(0, this.pagination.pageSize)
        }
        this.pageLoading = false
      })
    },
    // 获取部门
    getDept() {
      this.$api.getSelectedDept({}).then((res) => {
        if (res.code == '200') {
          this.deptList = res.data
        }
      })
    },
    // 责任部门点击
    deptChange(val) {
      this.userList = []
      this.form.responsibleDepartName = this.deptList.find((el) => el.id === val).deptName
      this.getUserList(val)
    },
    // 获取部门下人员list
    getUserList(dept) {
      let params = {
        current: 1,
        size: 9999,
        sex: '',
        pmId: '',
        postId: '', // 岗位
        stationStatus: '',
        officeId: dept
      }
      this.$api.staffList(params).then((res) => {
        if (res.code === 200) {
          this.userList = res.data.records
        }
      })
    },
    // 责任人选择
    userchange(val, phone, name) {
      this.form[phone] = this.userList.find((el) => el.id === val).mobile || ''
      this.form[name] = this.userList.find((el) => el.id === val).staffName
    },
    remove(row, $index) {
      // 移除
      this.list.splice($index, 1)
      let index = this.allList.findIndex((el) => el.modelCode === row.modelCode)
      this.allList.splice(index, 1)
    },
    // 选择科室
    selectDept(value) {
      let newArr = value.map((item) => {
        return {
          regionCode: item.parentSpaceIds, // 所在位置code
          regionName: item.parentSpaceName, // 所在位置
          modelCode: item.modelCode, // 空间编码
          spaceName: item.ssmName, // 空间名称
          localCode: item.localSpaceCode, // 本地编码
          spaceId: item.id // 空间id
        }
      })
      // 使用 Map 来存储唯一的 modelCode
      let map = new Map()
      // 将 allList 和 value 合并
      let arr = this.allList.concat(newArr)
      // 遍历合并后的数组，将每个对象的 modelCode 作为键存储在 Map 中
      arr.forEach((item) => {
        map.set(item.spaceId, item)
      })
      // 将 Map 的值转换回数组
      this.allList = Array.from(map.values())
      this.list = this.allList.slice(0, this.pagination.pageSize)
      this.total = this.allList.length
    },
    showDeptDialog() {
      this.isDeptDialog = true
    },
    handleSizeChange(pageSize) {
      this.pagination.pageNo = 1
      this.pagination.pageSize = pageSize
      this.list = this.allList.slice((this.pagination.pageNo - 1) * this.pagination.pageSize, this.pagination.pageNo * this.pagination.pageSize)
    },
    handleCurrentChange(current) {
      this.pagination.pageNo = current
      this.list = this.allList.slice((this.pagination.pageNo - 1) * this.pagination.pageSize, this.pagination.pageNo * this.pagination.pageSize)
    },
    // 提交
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          let fn = this.$route.query.id ? 'updateFireproof' : 'addFireproof'
          let header = {}
          if (this.$route.query.id) {
            header = {
              'operation-type': 2,
              'operation-id': this.$route.query.id,
              'operation-name': this.form.gridName
            }
          } else {
            header = {
              'operation-type': 1
            }
          }
          this.form.regionList = this.allList
          this.$api[fn](this.form, header).then((res) => {
            if (res.code === '200') {
              this.$message.success('添加成功')
              this.back()
            }
          })
        }
      })
    },
    back() {
      this.$router.go(-1)
    }
  }
  // onDistorted() {
  //   this.form.resetField()
  // },
  // onDeactivated() {
  //   this.form.resetField()
  // }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  background: #fff;
  .title {
    height: 40px;
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #333333;
    line-height: 40px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    border-bottom: 1px solid #dcdfe6;
    margin-bottom: 24px;
    padding-left: 16px;
  }
  .info-title {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    .label_line {
      display: flex;
      align-items: center;
      .line {
        width: 8px;
        height: 14px;
        background: #3562db;
        border-top-right-radius: 14px;
        border-bottom-right-radius: 14px;
        margin-right: 4px;
      }
      .label {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 15px;
        color: #333333;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
  }
  .hasBtn {
    justify-content: space-between;
  }
  .baseInfo {
    padding: 0 24px;
    ::v-deep .el-form--inline {
      width: 70%;
      .el-form-item {
        width: 45%;
      }
      .el-form-item__content {
        width: calc(100% - 140px);
      }
    }
  }
  .space {
    padding: 0 24px;
  }
}
</style>
<style lang="scss">
.fireproof-popperClass {
  width: 442px;
}
</style>
