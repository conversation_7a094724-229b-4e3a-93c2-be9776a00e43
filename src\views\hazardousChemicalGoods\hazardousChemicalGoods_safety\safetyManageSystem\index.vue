<template>
  <PageContainer>
    <div slot="content" class="page_containner">
      <el-tabs v-model="active">
        <el-tab-pane v-for="(item, index) in tabList" :key="index" :label="item.label" :name="item.value"></el-tab-pane>
      </el-tabs>
      <allDoc v-if="active == '1'" />
      <recycleBin v-if="active == '2'" />
    </div>
  </PageContainer>
</template>
<script>
import allDoc from './allDoc.vue'
import recycleBin from './recycleBin.vue'
export default {
  name: 'safetyManageSystem',
  components: {
    allDoc,
    recycleBin
  },
  data() {
    return {
      active: '1',
      tabList: [
        {
          label: '全部文档',
          value: '1'
        },
        {
          label: '回收站',
          value: '2'
        }
      ]
    }
  },
}
</script>
<style lang="scss" scoped>
.page_containner {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 0 16px;
}
</style>
