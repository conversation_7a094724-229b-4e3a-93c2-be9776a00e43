<template>
  <PageContainer>
    <div v-if="rows.length" slot="content" class="container">
      <div class="title">监测总览</div>
      <!-- 监测总览 -->
      <el-row v-if="rows.length" v-loading="loading.overview" :gutter="16" class="overview">
        <el-col v-for="(item, index) in rows" :key="index" :xl="rows.length < 4 ? 24 / rows.length : 6" :lg="8" :md="12" :sm="12" :xs="24">
          <div class="item">
            <div class="semicircle">{{ item.sysName }}</div>
            <div class="chart_date" :style="{ 'justify-content': rows.length < 4 ? 'center' : 'space-between' }">
              <div class="chart_date_left">
                <!-- <img :src="monitorItemImg[item.projectCode]['on']" :alt="item.projectName" /> -->
                <img src="@/assets/images/monitor/on_flrb.png" />
                <div class="eq_data">
                  <span>设备总数</span>
                  <div class="eq_data_p">{{ item.devicesNum }}</div>
                </div>
              </div>
              <div class="chart_date_right">
                <echarts
                  :ref="`overviewStatistics${index}`"
                  :domId="`overviewStatistics${index}`"
                  :onLegendClickBool="true"
                />
                  <!-- @onClickChart="(data) => onClickChart(data)"
                  @onClickChartLegend="(data) => onClickChart(data)" -->
              </div>
            </div>
          </div>
        </el-col>
      </el-row>

      <div class="call_the_police">
        <div class="semicircle">报警统计（件）</div>
        <div class="control-btn-header">
          <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 0 }" plain @click="timeTypeChange(0)">今日</el-button>
          <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 1 }" plain @click="timeTypeChange(1)">本月</el-button>
          <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 2 }" plain @click="timeTypeChange(2)">本年</el-button>
          <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 3 }" plain style="margin-right: 10px;" @click="timeTypeChange(3)">自定义</el-button>
          <el-date-picker
            v-model="requestInfo.dataRange"
            type="daterange"
            unlink-panels
            :disabled="requestInfo.timeType != 3"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            clearable
          />
          <div style="display: inline-block;">
            <el-button type="primary" plain @click="resetForm">重置</el-button>
            <el-button type="primary" @click="submit">查询</el-button>
          </div>
        </div>
        <!-- <TimeQuery ref="timeQuery" @submit="submit" /> -->
        <!-- :xs="24" :md="24" :lg="12" :xl="8" -->
        <el-row class="display_graphics">
          <el-col v-loading="loading.PoliceInfoLoading" class="display_graphics_left" :xs="12" :md="12" :lg="10" :xl="8">
            <div class="cardContent-left">
              <div v-for="item in alarmStatisticsList" :key="item.title" class="left-item">
                <p class="item-title">{{ item.title }}</p>
                <p class="item-value" :style="{ color: item.color }">{{ item.value || 0 }}<span>个</span></p>
                <img class="item-icon" :src="item.icon" :alt="item.title" />
              </div>
            </div>
            <div class="callThePolice">
              <echarts
                :ref="`callThePolice`"
                :domId="`callThePolice`"
                :onLegendClickBool="true"
                :isMonitor="false"
                @onClickChartLegend="(data) => jumpAlarmRecord(data.data.projectCode, queryParams.dateRange)"
                @onClickChart="(data) => jumpAlarmRecord(data.data.projectCode, queryParams.dateRange)"
              />
            </div>
          </el-col>
          <el-col v-loading="loading.PoliceTrendLoading" class="display_graphics_right" :xs="24" :md="24" :lg="14" :xl="16">
            <el-table ref="table" :resizable="false" border :data="tableData" height="calc(100% - 45px)" style="width: 100%">
              <el-table-column label="序号" type="index" width="50" />
              <el-table-column prop="alarmStartTime" label="报警时间" width="170"></el-table-column>
              <el-table-column prop="alarmObjectName" label="报警对象名称" width="120" show-overflow-tooltip></el-table-column>
              <el-table-column prop="iphPoliceLevel" label="严重等级" width="110">
                <span slot-scope="scope" class="alarmLevel" :style="{ background: alarmLevelItem[scope.row.alarmLevel].color }">
                  {{ alarmLevelItem[scope.row.alarmLevel].text }}
                </span>
              </el-table-column>
              <el-table-column prop="incidentName" label="事件类型" show-overflow-tooltip></el-table-column>
              <el-table-column prop="menuName" label="类型" show-overflow-tooltip></el-table-column>
              <el-table-column prop="alarmSpaceName" label="位置" show-overflow-tooltip></el-table-column>
              <el-table-column prop="alarmStatus" label="处理状态" width="120">
                <div slot-scope="scope" class="alarmStatus" :style="{ color: scope.row.alarmStatus == 0 ? '#FA403C' : scope.row.alarmStatus == 1 ? '#fd9434' : '#999' }">
                  <span class="alarmStatusIcon" :style="{ background: scope.row.alarmStatus == 0 ? '#FA403C' : scope.row.alarmStatus == 1 ? '#ff9435' : '#999' }"></span>
                  {{ scope.row.alarmStatus == 0 ? '未处理' : scope.row.alarmStatus == 1 ? '处理中' : '已关闭' }}
                </div>
              </el-table-column>
            </el-table>
            <div class="card-content-footer" style="margin-top: 8px">
              <el-pagination
                :current-page="pagination.current"
                :page-sizes="pagination.pageSizeOptions"
                :page-size="pagination.size"
                :layout="pagination.layoutOptions"
                :total="pagination.total"
                @size-change="paginationSizeChange"
                @current-change="paginationCurrentChange"
              >
              </el-pagination>
            </div>
          </el-col>
        </el-row>
      </div>
      <OffLineDialog ref="offLineDialog"></OffLineDialog>
      <NormalDialog ref="normalDialog"></NormalDialog>
    </div>
  </PageContainer>
</template>

<script>
import alarmStatistics from '@/assets/images/monitor/alarm-statistics.png'
import alarmAlarm from '@/assets/images/monitor/alarm-pending.png'
import alarmDoing from '@/assets/images/monitor/alarm-doing.png'
import mixins from '../components/overviewPublic/mixins/chartMixin.js'
import tableListMixin from '@/mixins/tableListMixin.js'
import OffLineDialog from '../components/overviewPublic/offLineDialog.vue'
import NormalDialog from '../components/overviewPublic/normalDialog.vue'
import TimeQuery from '../components/overviewPublic/timeQuery.vue'
import { monitorItemImg, monitorTypeList } from '@/util/dict.js'
import { auth } from '@/util'
import moment from 'moment'
export default {
  name: 'airCooledOverview',
  components: {
    OffLineDialog, // 详情弹窗
    NormalDialog // 正常
  },
  mixins: [mixins, tableListMixin],
  props: {},
  data() {
    return {
      // 风冷热泵监测
      projectCode: monitorTypeList.find((item) => item.projectName == '风冷热泵监测').projectCode,
      requestInfo: {
        dataRange: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')], // 时间范围
        timeType: 0 // 0: 当天 1: 本月 2: 本年 3: 自定义)
      },
      alarmStatisticsList: [
        {
          title: '报警统计',
          color: '#3562DB',
          icon: alarmStatistics,
          value: 0
        },
        {
          title: '未处理',
          color: '#FA403C',
          icon: alarmAlarm,
          value: 0
        },
        {
          title: '处理中',
          color: '#FF9435',
          icon: alarmDoing,
          value: 0
        }
      ],
      monitorItemImg,
      // 设备
      rows: [],
      Xdisplay: [
        { value: 0, label: '24小时', disabled: true, fn: (diffDays) => diffDays <= 0 },
        { value: 1, label: '日', disabled: false, fn: (diffDays) => diffDays > 0 },
        { value: 2, label: '月', disabled: false, fn: (diffDays) => diffDays > 0 }
        // {value: 1, label: '日', disabled: false, fn: (diffDays) => diffDays > 0 && diffDays < 30 * 7 },
        // {value: 2, label: '月', disabled: false, fn: (diffDays) => diffDays > 30 }
      ],
      hourOrMouth: 0,
      // 报警统计 需要的projectCode拼接字段
      projectSplic: '',
      // diffDays: null
      // 报警数
      policeInfo: {},
      // 报警数日期描述
      dateText: null,
      // 入参
      queryParams: {},
      loading: {
        overview: false,
        PoliceInfoLoading: false,
        PoliceTrendLoading: false
      },
      tableData: [],
      alarmLevelItem: {
        0: { text: '通知', color: '#3562DB' },
        1: { text: '一般', color: '#3562DB' },
        2: { text: '紧急', color: '#FF9435' },
        3: { text: '重要', color: '#FA403C' }
      }
    }
  },
  computed: {
    // 显示一行还是两行
    gridTemplateRows() {
      let rowsNum = Math.ceil(this.rows.length / 4)
      return rowsNum
    }
  },
  mounted() {
    this.getCountMonitoringNum()
    this.submit()
  },
  methods: {
    // 日期选择
    timeTypeChange(type) {
      let newObj = {
        0: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        1: [moment().startOf('month').format('YYYY-MM-DD'), moment().endOf('month').format('YYYY-MM-DD')],
        2: [moment().startOf('year').format('YYYY-MM-DD'), moment().endOf('year').format('YYYY-MM-DD')],
        3: []
      }
      this.requestInfo.dataRange = newObj[type]
      this.requestInfo.timeType = type
    },
    // 跳转报警中心
    jumpAlarmRecord(projectSplic, dateRange) {
      this.$router.push({
        name: 'AlarmRecordIndex',
        query: {
          projectCode: projectSplic,
          dataRange: dateRange
        }
      })
    },
    // 颜色样式
    colorType(type) {
      let colorObj = {
        0: '#FA403C',
        1: '#00BC6D',
        2: ''
      }
      return colorObj[type]
    },
    getCountMonitoringNum() {
      this.loading.overview = true
      this.$api.countMonitoringNumByMenuCode({projectCode: this.projectCode}).then(res => {
        if (res.code == 200) {
          this.rows = res.data
          this.$nextTick(() => {
            this.getEchartsData()
            this.loading.overview = false
          })
        }
      })
    },
    /**
     * 批量pie图点击
     */
    onClickChart(data) {
      // console.log('data==========', data)
      if (data.data.name == '正常') {
        this.$refs.normalDialog.getEchartData(data.data)
      } else {
        this.$refs.offLineDialog.getEchartData(data.data, this.queryParams)
      }
    },
    getEcharPieBig(arr) {
      let obj
      let data = []
      let total = 0
      arr.forEach((ele) => {
        // 测试数据
        // let a = Math.round(Math.random() * 99)
        total += ele.policeCount
        // 测试数据
        // total += a
        obj = {
          name: ele.projectName,
          projectCode: ele.projectCode,
          value: ele.policeCount,
          // 测试数据
          // value: a,
          proportion: '',
          ringRatio: ele.ringRatio,
          ringRatioStatus: ele.ringRatioStatus
        }
        data.push(obj)
      })
      this.$refs.callThePolice.init(this.alarmChartData(data))
    },
    // 饼图
    alarmChartData(arr) {
      const nameList = Array.from(arr, (item) => item.menuName)
      let total = arr.reduce((p, v) => {
        return p + v.policeCount
      }, 0)
      let option
      if (arr.length) {
        option = {
          tooltip: {
            trigger: 'item'
          },
          title: {
            text: '报警设备类型统计',
            left: 'center',
            textStyle: {
              fontSize: 18,
              fontWeight: 500
            }
          },
          legend: {
            orient: 'vertical',
            type: 'scroll',
            top: '35%',
            right: '10%',
            bottom: 1,
            itemWidth: 15,
            itemHeight: 15,
            // itemGap: 15,
            pageButtonPosition: 'start',
            data: nameList,
            formatter: (name) => {
              let item = arr.find(v => v.menuName == name)
              return `${name}  ${((item.policeCount / total) * 100).toFixed(2)}%  ${item.policeCount}个`
            }
          },
          series: [
            {
              type: 'pie',
              roseType: 'radius',
              radius: ['35%', '50%'],
              center: ['40%', '50%'],
              data: arr.map(item => {
                return {name: item.menuName, value: item.policeCount}
              }),
              label: {
                normal: {
                  show: false
                }
              }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    },
    // 报警趋势
    getDataList() {
      let params = {
        startTime: this.requestInfo.dataRange[0],
        endTime: this.requestInfo.dataRange[1],
        projectCode: this.projectCode,
        timeType: this.requestInfo.timeType,
        pageNo: this.pagination.current,
        pageSize: this.pagination.size
      }
      this.$api.GetAllAlarmRecord(params).then((res) => {
        this.loading.PoliceTrendLoading = false
        if (res.code == 200) {
          this.tableData = res.data ? res.data.records : []
          this.pagination.total = res.data ? res.data.total : 0
        } else {
          this.tableData = []
          this.pagination.total = 0
        }
      }).catch((err) => {
        this.loading.PoliceTrendLoading = false
      })
    },
    //
    getEcharLineBig(data) {
      this.$refs.alarmTrend.init(this.setLineChart(data, []))
    },
    // 重置查询表单
    resetForm() {
      this.requestInfo = {
        dataRange: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        timeType: 0
      }
      this.submit()
    },
    echartsResize() {
      setTimeout(() => {
        if (this.$refs.callThePolice) {
          this.$refs.callThePolice.chartResize()
        }
      }, 250)
    },
    /**
     * 查询 | 调接口
     */
    submit() {
      if (!this.requestInfo.dataRange.length) {
        this.$message.warning('请选择时间段！')
        return
      }
      let data = {
        startTime: this.requestInfo.dataRange[0],
        endTime: this.requestInfo.dataRange[1],
        projectCode: this.projectCode,
        timeType: this.requestInfo.timeType
      }
      this.$api.GetAirRunPolice(data).then((res) => {
        if (res.code == 200) {
          this.alarmStatisticsList[0].value = res.data?.total
          this.alarmStatisticsList[1].value = res.data?.noDealCount
          this.alarmStatisticsList[2].value = res.data?.isDealCount
          this.$nextTick(() => {
            this.$refs.callThePolice.init(this.alarmChartData(res.data ? res.data.policeList : []))
            // this.$refs.alarmChart.init(this.alarmChartData(res.data ? res.data.policeList : []))
            this.echartsResize()
          })
        }
      })
      this.getDataList()
    },
    getEchartsData() {
      console.log('this.rows======', this.rows)
      this.rows.forEach((ele, index) => {
        let data = [
          { name: '正常', value: ele.onLineNum, projectCode: ele.projectCode, sysName: ele.sysName },
          { name: '离线', value: ele.offLineNum, projectCode: ele.projectCode, sysName: ele.sysName }
        ]
        this.$nextTick(() => {
          console.log('this.$refs[`overviewStatistics${index}`]==========', this.$refs)
          this.$refs[`overviewStatistics${index}`][0].init(this.setBatchPieChart('个', data, [], ['#3562DB', '#CBCED3', '#FF6461']))
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.aLine {
  flex-direction: column;

  .police_proportion_box {
    margin-top: 20px;
    align-items: center;
  }
}

.twoLines {
  align-items: center;

  .police_proportion_box {
    flex-direction: column;
  }
}

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: scroll;
  overflow-x: hidden;
}

.semicircle::before {
  content: ' ';
  display: inline-block;
  width: 7px;
  height: 13px;
  background: #3562db;
  border-radius: 0 7px 7px 0;
  opacity: 1;
  margin-right: 4px;
}

.title {
  font-size: 14px;
  font-family: 'PingFang SC-Medium', 'PingFang SC';
  font-weight: 500;
  color: #121f3e;
  line-height: 14px;
  margin-bottom: 16px;
}

.item,
.call_the_police {
  background: white;
  padding: 15px;
}

.overview {
  .item {
    background: white;
    padding: 15px;
    margin-bottom: 10px;

    .semicircle {
      font-size: 15px;
      display: flex;
      align-items: center;
    }

    .chart_date {
      display: flex;
      justify-content: space-between;
      margin: 10px 0;
      height: 80px;

      &_left {
        display: flex;
        height: 100%;
        margin-right: 20px;

        img {
          height: 60px;
          width: 60px;
          margin: auto;
          margin-right: 15px;
        }

        .eq_data {
          display: flex;
          flex-direction: column;
          justify-content: center;

          span {
            font-size: 15px;
            line-height: 18px;
          }

          &_p {
            width: 90px;
            font-size: 30px;
            font-weight: bold;
            line-height: 36px;
          }

          &_p::after {
            content: '个';
            font-size: 15px;
            font-weight: 400;
            color: #ccced3;
            line-height: 18px;
          }
        }
      }

      &_right {
        width: 170px;
        height: 100%;
      }
    }

    .distribution {
      display: flex;
      justify-content: center;
      align-items: center;
      background: #faf9fc;
      border-radius: 4px;
      padding: 10px 0;

      span {
        font-size: 15px;
        line-height: 15px;
        color: #121f3e;
        margin-right: 8px;
      }

      .distribution_p {
        font-size: 18px;
        font-family: Arial-Bold, Arial;
        font-weight: bold;
        color: #121f3e;
      }

      .distribution_p::after {
        content: '个';
        font-size: 14px;
        font-weight: 400;
        margin-left: 2px;
        color: #ccced3;
      }
    }
  }
}

.call_the_police {
  flex: 1;
  display: flex;
  flex-direction: column;
  .control-btn-header {
    padding: 6px 6px 16px 16px;

    & > div {
      margin-right: 10px;
      margin-top: 10px;
    }

    .btn-item {
      border: 1px solid #3562db;
      color: #3562db;
      font-family: none;
    }

    .btn-active {
      color: #fff;
      background: #3562db;
    }
  }
  .display_graphics {
    flex: 1;
    // overflow: auto;
    // display: flex;
    &_left {
      // width: 28%;
      // margin-right: 1%;
      height: 100%;
      // flex: 1;
      display: flex;
      flex-direction: column;
      .cardContent-left {
        display: flex;
        // flex-direction: column;
        justify-content: center;
        width: 100%;

        p {
          margin: 0;
        }

        .left-item {
          flex: 1;
          padding: 14px 22px;
          background: #faf9fc;
          border-radius: 4px;
          margin-bottom: 7px;
          position: relative;
          margin-right: 16px;

          .item-title {
            font-size: 15px;
            font-weight: 500;
            color: #121f3e;
          }

          .item-value {
            margin-top: 4px;
            font-size: 30px;

            & > span {
              font-size: 15px;
              font-weight: 500;
              color: #ccced3;
            }
          }

          .item-icon {
            position: absolute;
            right: 22px;
            bottom: 14px;
            width: 40px;
            height: 40px;
          }
        }

        & :last-child {
          margin-right: 0;
        }
      }
      .callThePolice {
        flex: 1;
        width: 100%;
        margin-top: 20px;
      }
    }

    &_right {
      position: relative;
      height: 100%;
      // width: 72%;
      .location_sele {
        position: absolute;
        right: 0;
        top: 0;
        z-index: 50;

        .el-select {
          width: 90px;
        }
      }
    }
  }
  .display_graphics_right {
    padding-left: 16px;
    .alarmLevel {
      padding: 3px 6px;
      border-radius: 4px;
      color: #fff;
      line-height: 14px;
    }

    .alarmStatus {
      position: relative;
      display: inline-block;
      padding-left: 12px;

      .alarmStatusIcon {
        display: inline-block;
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 8px;
        height: 8px;
        border-radius: 100%;
      }
    }
  }
}

.piece::after {
  content: '件';
  font-size: 14px;
  font-family: 'PingFang SC-Medium', 'PingFang SC';
  font-weight: 500;
  margin-left: 5px;
  color: #ccced3;
}
// 不知名问题  疑似被其他组件css冲掉  后期解决
::v-deep .el-input__icon {
  height: unset !important;
}
</style>
