<!-- 运行监测 -->
<template>
  <ContentCard :title="item.componentTitleShow" :scrollbarHover="true" :cstyle="{ width: '100%', height: '100%' }"
    class="drag_class" :hasMoreOper="['edit']"
    @more-oper-event="(val) => $emit('all-more-Oper', val, item.componentName, item.chartType)">
    <div slot="content" style="width: 100%; height: 100%;">
      <div class="control-btn-header">
        <el-input v-model="filters.planName" style="width: 230px" placeholder="预案名称"></el-input>
        <el-select v-model="filters.plancategory" placeholder="请选择预案类型" style="width: 230px" filterable clearable>
          <el-option v-for="(item, index) in plancategoryList" :key="index" :label="item.name"
            :value="item.value"></el-option>
        </el-select>
        <el-select v-model="filters.alarmTypes" placeholder="请选择报警类型" style="width: 230px" filterable clearable
          @change="alarmTypeSelect">
          <el-option v-for="(item, index) in alarmTypeList" :key="index" :label="item.name"
            :value="item.id"></el-option>
        </el-select>
        <div style="display: inline-block">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="searchForm">查询</el-button>
        </div>
      </div>
      <div>
        <TablePage ref="table" v-loading="tableLoading" :showPage="true" :tableColumn="tableColumn" height="270"
          :data="tableData" :pageData="pagination" @pagination="paginationChange" :pageProps="pageProps" />
      </div>
    </div>
  </ContentCard>
</template>

<script lang="jsx">
export default {
  name: 'yjya',
  props: {
    item: {
      type: Object,
      default: () => { }
    },
    systemCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableColumn: [
        {
          prop: '',
          label: '序号',
          formatter: (scope) => {
            return (this.pagination.page - 1) * this.pagination.pageSize + scope.$index + 1
          },
          width: 60
        },
        {
          prop: 'planName',
          label: '预案名称'
        },
        {
          prop: 'planType',
          label: '预案分类',
          formatter: (scope) => {
            return scope.row.planType ? this.planTypeList.find((v) => v.thirdSystemCode == scope.row.planType)?.thirdSystemName : ''
          }
        },
        {
          prop: 'planCategory',
          label: '预案类型',
          formatter: (row) => {
            switch (row.row.planCategory) {
              case '0':
                return '智能预案';
              case '1':
                return '常规预案';
              case '2':
                return '废除预案';
              default:
                return ''; // 处理未定义的情况
            }
          }
        },
        {
          prop: 'alarmTypeName',
          label: '报警类型',
        },
        {
          prop: 'spaceTypeName',
          label: '空间类型'
        },
        {
          prop: 'versionNo',
          label: '版本号',
        },
        {
          prop: 'operation',
          label: '操作',
          width: 100,
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" onClick={() => this.handleListEvent(row.row)}>
                  详情
                </span>
              </div>
            )
          }
        }
      ],
      filters: {
        planName: "",//预案名称
        plancategory: "",
        alarmTypes: [],
      },
      alarmTypeList: [],
      plancategoryList: [
        {
          value: "0", name: "智能预案"
        },
        {
          value: "1", name: "常规预案"
        },
        {
          value: "2", name: "废除预案"
        },
      ],
      pagination: {
        pageSize: 15,
        page: 1,
        total: 0
      },
      pageProps: {
        page: 'page',
        pageSize: 'pageSize',
        total: 'total'
      },
      tableData: [],
      tableLoading: false,
      alarmType: [],
      planTypeList: []
    }
  },
  mounted() {
    this.getAlarmThirdSystemList()
  },
  methods: {
    getAlarmThirdSystemList() {
      this.$api.getAlarmThirdSystemList().then((res) => {
        if (res.code == 200) {
          this.planTypeList = res.data
        }
      })
    },
    alarmTypeSelect(value) {
      this.alarmType = []
      this.alarmType.push(value)
    },
    // 报警类型
    getAlarmTypeByAlarmObjectId(id) {
      let data = {
        alarmObjectId: id
      }
      this.$api.getAlarmTypeByAlarmObjectId(data).then((res) => {
        if (res.code == 200) {
          this.alarmTypeList = res.data
          this.alarmType = res.data.map(item => item.id)
          this.pagination = {
            pageSize: 15,
            page: 1,
            total: 0
          }
          this.yjyaData()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 品类table列表
    yjyaData() {
      this.tableLoading = true
      let data = {
        pageSize: this.pagination.pageSize,
        page: this.pagination.page,
        planName: this.filters.planName,//预案名称
        plancategory: this.filters.plancategory,
        alarmTypes: this.alarmType,
      }
      this.$api
        .getConfigPageByTypes(data)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data.records
            this.pagination.total = res.data.total
          } else if (res.message) {
            this.tableData = []
            this.pagination.total = 0
            this.$message.error(res.message)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    // 分页
    paginationChange(pagination) {
      Object.assign(this.pagination, pagination)
      this.yjyaData()
    },
    // 重置查询表单
    resetForm() {
      this.filters = {
        planName: "",//预案名称
        plancategory: "",
        alarmTypes: [],
      }
      this.pagination = {
        pageSize: 15,
        page: 1,
        total: 0
      }
      this.yjyaData()
    },
    searchForm() {
      this.yjyaData()
    },
    // 详情
    handleListEvent(row) {
      this.$router.push({
        path: '/planManage/planList/planDetails',
        query: {
          type: 'detail',
          planType: row.planCategory,
          id: row?.id ?? ''
        }
      })
    },
  }
}
</script>
<style lang="scss" scoped>
.data-btns {
  position: absolute;
  right: 7%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &>span {
    width: 44px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    margin: 0 4px;
    background-color: #f6f5fa;
    font-size: 14px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    border: none;
    border-radius: 2px;
    color: #7f848c;
    cursor: pointer;
  }

  .active-btn {
    background-color: #e6effc !important;
    color: #3562db !important;
    border-color: #e6effc !important;
  }
}

.operation-list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
}

.control-btn-header {
  padding: 0;
  margin-bottom: 20px;

  &>div {
    margin-right: 10px;
    margin-top: 10px;
  }

  .btn-item {
    border: 1px solid #3562db;
    color: #3562db;
    font-family: none;
  }

  .btn-active {
    color: #fff;
    background: #3562db;
  }
}
</style>
