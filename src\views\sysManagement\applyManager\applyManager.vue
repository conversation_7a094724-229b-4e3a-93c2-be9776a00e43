<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <div class="search-from">
        <el-input v-model="searchFrom.shortName" placeholder="应用名称" clearable style="width: 200px;"></el-input>
        <div style="display: inline-block;">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="searchForm">查询</el-button>
          <el-button type="primary" icon="el-icon-plus" @click="control('add')">新增</el-button>
        </div>
        <el-image-viewer v-if="showViewer" :on-close="() => showViewer = false" :url-list="iconPathList" />
      </div>
    </div>
    <div slot="content" class="table-content">
      <TablePage
        ref="table"
        v-loading="tableLoading"
        :showPage="true"
        :tableColumn="tableColumn"
        :data="tableData"
        height="calc(100% - 40px)"
        :pageData="pageData"
        :pageProps="pageProps"
        @pagination="paginationChange"
        @row-dblclick="control('detail', $event)"
      />
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
export default {
  name: 'userManagement',
  components: {
    ElImageViewer
  },
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    // 进入页面时，先将当前页面的 name 信息存入 keep-alive 全局状态
    setTimeout(() => {
      next((vm) => {
        vm.$store.commit('keepAlive/add', names)
      })
    }, 0)
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['AddApp'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      tableLoading: false,
      showViewer: false,
      iconPathList: [], // 图片列表
      searchFrom: {
        shortName: '' // 应用名称
      },
      tableColumn: [
        {
          prop: '',
          label: '序号',
          formatter: (scope) => {
            return (this.pageData.current - 1) * this.pageData.size + scope.$index + 1
          }
        },
        {
          prop: 'shortName',
          label: '应用简称'
        },
        {
          prop: 'fullName',
          label: '应用全称'
        },
        {
          prop: 'path',
          label: '应用地址'
        },
        {
          prop: 'type',
          label: '应用类型',
          formatter: (scope) => {
            if (scope.row.type == 1) {
              return '子系统'
            } else if (scope.row.type == 2) {
              return '子模块 '
            } else if (scope.row.type == 3) {
              return '外部应用'
            } else {
              return ''
            }
          }
        },
        {
          prop: 'icon',
          label: '图标',
          render: (h, row) => {
            return (
              <span style="color: #3562DB; cursor: pointer;"  onClick={() => this.viewImage(row.row)}>点击查看</span>
            )
          }
        },
        {
          width: 200,
          prop: 'remark',
          label: '简介'
        },
        {
          width: 100,
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" style="color: #121F3E" onClick={() => this.control('edit', row.row)}>编辑</span>
                <span class="operationBtn-span" style="color: #fa403c" onClick={() => this.control('del', row.row)}>删除</span>
              </div>
            )
          }
        }
      ],
      tableData: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      pageProps: {
        page: 'current',
        pageSize: 'size',
        total: 'total'
      }
    }
  },
  watch: {

  },
  activated() {
    this.getApplicationList()
  },
  mounted() {
    this.getApplicationList()
  },
  methods: {
    // 查看图片
    viewImage(row) {
      let {activeIcon, defaultIcon} = JSON.parse(row.icon)
      let images = []
      if (!activeIcon && !defaultIcon) {
        this.$message.info('当前没有可预览的图片')
        return
      }
      if (activeIcon) images.push(this.$tools.imgUrlTranslation(activeIcon))
      if (defaultIcon) images.push(this.$tools.imgUrlTranslation(defaultIcon))
      this.iconPathList = images
      this.showViewer = true
    },
    // 查询
    searchForm() {
      this.getApplicationList()
    },
    // 重置查询
    resetForm() {
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.searchForm()
    },
    control(type, row) {
      if (['add', 'edit', 'detail'].includes(type)) {
        this.$router.push({
          path: '/applyManager/addApp',
          query: {
            type,
            id: row?.id ?? ''
          }
        })
      } else if (type == 'del') {  // 删除
        this.$confirm('删除后将无法恢复，是否确定删除？', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api.RemoveApp({appId: row.id}, {'operation-type': 3, 'operation-id': row.id, 'operation-name': row.appName}).then(res => {
            if (res.code == 200) {
              this.$message({ message: '应用删除成功', type: 'success'})
              this.isLastPage(1)
              this.searchForm()
            } else {
              this.$message({ message: res.msg, type: 'error'})
            }
          })
        })
      }
    },
    // 获取应用列表
    getApplicationList() {
      let param = {
        ...this.searchFrom,
        pageSize: this.pageData.size,
        page: this.pageData.current
      }
      this.tableLoading = true
      this.$api.GetApplicationList(param).then(res => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.pageData.total = res.data.total
        }
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    // 判断当前页是否是最后一页
    isLastPage (deleteNum) {
      let deleteAfterPage = Math.ceil((this.pageData.total - deleteNum) / this.pageData.size)
      let currentPage = this.pageData.current > deleteAfterPage ? deleteAfterPage : this.pageData.current
      this.pageData.current = currentPage < 1 ? 1 : currentPage
    },
    paginationChange(pagination) {
      // this.pageData.size = pagination.pageSize
      // this.pageData.current = pagination.page
      Object.assign(this.pageData, pagination)
      this.getApplicationList()
    }
  }
}
</script>
<style lang="scss" scoped>
.control-btn-header {
  padding: 10px !important;

  .search-from {
    & > div {
      margin-right: 10px;
    }
  }
}

.table-content {
  margin-top: 15px;
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 10px;
}
</style>
<style lang="scss">
.operationBtn-span {
  margin-right: 10px;
}
</style>
