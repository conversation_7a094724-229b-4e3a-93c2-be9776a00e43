<template>
  <div style="height: 100%;">
    <!-- <sinoPanel title="巡检记录详情" type="list" :isClose="true" style="background-color: rgb(245, 246, 251)" @close="$router.go(-1)">
      <template slot="content">
        <div class="backBar" @click="$router.go(-1)">
          <span class="icon el-icon-arrow-left"></span>
          <span class="header-title">巡检点详情</span>
        </div>
        
      </template>
    </sinoPanel> -->
    <div class="topFilter">
      <div class="backBar">
        <span style="cursor: pointer;" @click="$router.go(-1)">
          <i class="el-icon-arrow-left"></i>
          巡检点详情
        </span>
      </div>
    </div>
    <div class="content_box">
      <div class="list_content">
        <div style="display: flex; align-items: center;">
          <div class="listTitle">
            <div class="line"></div>
            <span style="color: #5b5b5b; font-weight: 600;">巡检点</span>
          </div>
          <div style="color: #909399;">{{ taskPoint.taskPointName }}</div>
        </div>
      </div>
      <div class="list_content">
        <div class="listTitles">
          <div class="line"></div>
          <div class="titleBox"><span>巡检内容</span></div>
        </div>
        <div class="listCon">
          <!-- 专业 -->
          <el-table v-if="tasksType == '1'" :data="tableData" border stripe :cell-style="{ padding: '0' }" :header-cell-style="{ background: '#f2f4fbd1' }" style="width: 80%;">
            <el-table-column type="index" label="序号" width="100" align="center"></el-table-column>
            <el-table-column prop="detailName" show-overflow-tooltip label="巡检项目" align="center"></el-table-column>
            <el-table-column prop="content" show-overflow-tooltip label="巡检要点" align="center"> </el-table-column>
            <el-table-column prop="contentStandard" show-overflow-tooltip label="巡检内容" align="center"> </el-table-column>
          </el-table>
          <!-- 日常 -->
          <el-table v-if="tasksType == '0'" :data="tableData" border stripe :cell-style="{ padding: '0' }" :header-cell-style="{ background: '#f2f4fbd1' }" style="width: 80%;">
            <el-table-column type="index" label="序号" width="100" align="center"></el-table-column>
            <el-table-column prop="detailName" show-overflow-tooltip label="巡检内容" align="center"></el-table-column>
            <el-table-column prop="standardRequirements" show-overflow-tooltip label="标准要求" align="center"> </el-table-column>
            <el-table-column prop="inspectionBasis" show-overflow-tooltip label="巡检依据" align="center"> </el-table-column>
            <el-table-column prop show-overflow-tooltip label="巡检结果" align="center">
              <template slot-scope="scope">
                <span v-if="scope.row.normal == '0'">正常</span>
                <span v-if="scope.row.normal == '1'">异常</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <!-- 巡检执行 -->
      <div class="list_content">
        <div class="listTitles">
          <div class="line"></div>
          <div class="titleBox"><span>巡检执行</span></div>
        </div>
        <div class="listCon">
          <div class="centerCon">
            <div>
              <div class="label">巡检情况：</div>
              <span class="labelVal">{{ excute.carryOutFlag == '0' ? '未巡' : excute.carryOutFlag == '1' ? '已巡' : '' }}</span>
            </div>
            <div>
              <span class="label">执行人员：</span>
              <!-- <span class="labelVal">{{ excute.distributionTeamName || excute.planPersonName }}</span> -->
              <!-- <span class="labelVal">{{ excute.planPersonName ? filterDepartment(excute.distributionTeamName) + ',' + excute.planPersonName : filterDepartment(excute.distributionTeamName) }}</span> -->
              <span class="labelVal">{{ excute.implementPersonName }}</span>
            </div>
            <div>
              <div class="label">实际巡检时间：</div>
              <span class="labelVal">{{ excute.excuteTime }}</span>
            </div>
            <div>
              <div class="label">定位状态：</div>
              <span class="labelVal">{{ excute.spyScan }}</span>
            </div>
          </div>
        </div>
      </div>
      <!-- 巡检结果 -->
      <div class="list_content">
        <div class="listTitles">
          <div class="line"></div>
          <div class="titleBox"><span>巡检结果</span></div>
        </div>
        <div class="listCon">
          <div class="centerCon">
            <div style="width: 100%;">
              <div class="label">巡检结果：</div>
              <span class="labelVal">{{ result.state == '2' ? '合格' : result.state == '3' ? '不合格' : result.state == '4' ? '异常报修' : '' }}</span>
            </div>
            <div style="width: 100%;">
              <span class="label" style="flex: 1;">巡检情况说明：</span>
              <span style="flex: 5;">{{ result.desc }}</span>
            </div>
            <!-- <div style="width:100%;">
                          <span class="label">语音：</span>
                          <span v-if="!result.callerTapeUrl">暂无</span>
                          <div v-else>
                              <audio style="height:40px;" :src="result.callerTapeUrl" preload="true" controls="controls" ref="player"></audio>
                          </div>
                      </div> -->
            <div style="width: 100%; display: flex;">
              <span class="label">图片：</span>
              <div v-if="result.attachmentUrlList !== '[]'" class="resultImgBox">
                <div v-for="(item, index) in result.attachmentUrlList" :key="index">
                  <el-image style="max-height: 120px; max-width: 100px; margin-right: 5px;" :src="item" fit="scale-down" :preview-src-list="[item]"></el-image>
                </div>
              </div>
              <div v-else>暂无</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script type="text/ecmascript-6">
export default {
  name: 'feedbackRecord',
  components: {},
  data() {
    return {
      tasksType: null,
      query: '',
      tableData: [],
      taskPoint: '',
      excute: '',
      result: '',
      tableColumns: [],
      dialogTableVisible: false,
      bigImgUrl: ''
    }
  },
  mounted() {
    this.query = this.$route.query
    this._findRecordDetails() // 获取详情
  },
  methods: {
    // 点击查看大图
    showImgUrl(imgUrl) {
      this.dialogTableVisible = true
      this.bigImgUrl = imgUrl
    },

    _findRecordDetails() {
      this.$api.findRecordDetails({ id: this.query.id }).then((res) => {
        if (res.code == 200) {
          this.taskPoint = res.data.taskPoint
          this.tasksType = res.data.project.equipmentTypeId
          this.tableData = res.data.project.projectdetailsReleaseList
          if (this.tableData && this.tableData.length) {
            this.tableData.forEach((val, index) => {
              if (val.isNum == '3') {
                val.radioTextArr = JSON.parse(val.termJson)
              }
            })
          }
          this.excute = res.data.excute
          res.data.result.attachmentUrlList.forEach(i => this.$tools.imgUrlTranslation(i))
          this.result = res.data.result
        }
      })
    },
    // 科室去重
    filterDepartment(department) {
      const nameArr = department ? department.split(',') : ''
      const teamName = Array.from(new Set(nameArr))
      return teamName.toString()
    }
  }
}
</script>
<style lang="scss" scoped>
.topFilter {
  margin: 15px 15px 0;
  padding: 15px;
  width: calc(100% - 30px);
  height: 70px;
  background-color: #fff;

  .backBar {
    color: #121f3e;
    height: 30px;
    border-bottom: 1px solid #dcdfe6;
  }
}

.backBar {
  height: 20px;
}

.backBar:hover {
  cursor: pointer;
}

.listTitle,
.listTitles {
  display: flex;
  align-items: center;
  margin-right: 30px;

  .line {
    width: 5px;
    height: 25px;
    background: #5188fc;
    margin-right: 8px;
  }
}

.resultImgBox {
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  img {
    width: 80px;
    height: 80px;
    margin-left: 10px;
  }
}

.listCon {
  margin: 25px 40px;
}

.centerCon {
  width: 50%;
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  >div {
    display: flex;
    align-items: center;
    width: 310px;
    margin-bottom: 15px;
  }

  .label {
    width: 105px;
    color: #5b5b5b;
    font-weight: 500;
  }

  .labelVal {
    width: calc(100% - 105px);
    color: #909399;
  }
}

:deep(.el-table .el-table__cell) {
  padding: 5px 0 !important;
}

.titleBox {
  width: 80%;
  height: 25px;
  line-height: 25px;
  background: #eee;
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #5b5b5b;

  >span {
    padding-left: 8px;
  }
}

.list_content {
  margin-bottom: 30px;
  font-size: 14px;
}

:deep(.el-tabs__nav-wrap.is-left::after) {
  height: 0 !important;
}

:deep(.el-tabs__active-bar) {
  height: 0 !important;
}

:deep(.el-tabs__nav) {
  display: flex;
  flex-direction: column;
  align-items: center;
}

:deep(.el-tabs__header) {
  float: none !important;
}

:deep(.is-active) {
  background: #eee;
  border-radius: 4px;
  width: 180px;
  height: 35px;
  line-height: 35px;
  text-align: center !important;
}

:deep(.el-tabs__item.is-active) {
  color: #5188fc;
}

:deep(.el-tabs__item:hover) {
  color: #5188fc;
}

.content {
  display: flex;
}

.pagination {
  bottom: 20px !important;
}

.special_box {
  // width:100%;
  height: 100%;
  display: flex;
}

.content_box {
  overflow-y: auto;
  font-size: 14px;
  height: calc(100% - 80px);
  margin: 0 15px;
  padding: 20px 25px 25px;
  background: #fff;
  display: flex;
  flex-direction: column;
}

.top_content {
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-bottom: 25px;

  >div {
    width: 45%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .label {
      margin-right: 10px;
    }

    .labelVal {
      color: #909399;
    }
  }
}

.el-input {
  width: 200px;
}

.viewButton {
  color: #5188fc;
  cursor: pointer;
}

.green_line {
  display: inline-block;
  width: 4px;
  height: 16px;
  background: #5188fc;
  margin-right: 10px;
  vertical-align: middle;
}

.toptip {
  box-sizing: border-box;
  padding-left: 26px;
  height: 50px;
  width: 100%;
  line-height: 50px;
  text-align: left;
  border-bottom: 1px solid rgb(216 222 231 / 100%);
}

.left_content {
  margin: 20px 20px 0 35px;
}

.topTitle {
  display: flex;
  align-items: center;
  padding-bottom: 20px;
  border-bottom: 1px solid #ddd;
}

.line {
  width: 4px;
  height: 16px;
  background-color: #5188fc;
  margin-right: 10px;
}

.left {
  width: 268px;
  min-width: 14%;
  height: 97%;
  border-radius: 10px;
  background-color: #fff;
  margin: 10px;
  margin-top: 10px;

  .middle_tools {
    margin-top: 10px !important;
  }
}

.right {
  position: relative;
  width: calc(100% - 300px);
  // flex: 1;
  // width: 56%;
  height: 97%;
  text-align: left;
  background-color: #fff;
  padding-left: 30%;
  // float: left;
  box-sizing: border-box;
  padding: 20px 10px 10px;
  margin: 10px 10px 10px 0;
  margin-left: 288px;
  border-radius: 10px;

  .top_filters {
    input,
    select {
      margin-right: 15px;
      width: 200px;
      height: 40px;
    }

    .search_button {
      background-color: #5188fc;
      color: #fff;
    }

    .search_button:hover {
      opacity: 0.7;
    }
  }

  .middle_tools {
    margin-bottom: 10px;
  }
}

.deleteButton {
  color: #f43530;
  cursor: pointer;
}

@media screen and (max-width: 1600px) {
  .pagination {
    position: absolute;
    bottom: 0;
    right: 15px;
  }

  .personDialog .el-dialog {
    height: 545px;
  }

  .toptip {
    height: 35px;
    line-height: 35px;
  }

  .left_content {
    height: 415px;
  }

  .left .middle_tools {
    line-height: 36px;
    box-shadow: 0 -4px 4px 0 #f3f4f8;
    box-shadow: 0 -4px 4px 0 #f3f4f8;
  }
}
</style>