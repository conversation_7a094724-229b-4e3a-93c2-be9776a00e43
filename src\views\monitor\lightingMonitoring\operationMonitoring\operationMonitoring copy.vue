<template>
  <PageContainer title type="list" :isClose="false" @setWebsocket="setWebsocket">
    <template slot="content">
      <div class="sino-all-content">
        <div class="top">
          <div class="toptip">
            <span class="green_line"></span>
            运行总览
          </div>
          <div class="top_right">
            <!-- 控制模块离线数 -->
            <div class="overview">
              <div class="overview_data">
                <div class="overview_data_title">控制模块离线数</div>
                <div class="overview_data_num notlinkBg">
                  <span class="notlink"></span><i>{{ loopNum.offlineTotal }}</i>
                </div>
                <!-- <div class="overview_data_title">模块故障数</div>
                <div class="overview_data_num warnBg">
                  <span class="warn"></span><i>{{loopNum.moduleFailureTotal}}</i>
                </div> -->
                <div class="overview_data_title">回路控返不一致</div>
                <div class="overview_data_num warnBg">
                  <span class=""><i class="el-icon-warning-outline"></i></span><i>{{ loopNum.controlReturnTotal }}</i>
                </div>
              </div>
              <div class="overview_mode">
                <span>今日运行模式</span>
                <div class="pattern_box active_pattern">
                  <img style="width: 30px; height: 30px;" :src="todayPattern.patternActiveSrc" alt="" />
                  <span>{{ todayPattern.patternName }}</span>
                </div>
              </div>
            </div>
            <el-radio-group v-model="automatic" size="mini" style="margin: auto 0;" @change="automaticChange">
              <el-radio-button label="1">自动</el-radio-button>
              <el-radio-button label="2"
                >手动<span v-if="second !== null">({{ second }}s)</span></el-radio-button
              >
            </el-radio-group>
          </div>
        </div>
        <transition name="fade">
          <div v-show="fadeShow" class="top_echart">
            <div class="top_overview">
              <div class="p-box">
                <div
                  class="progressBox"
                  :style="{
                    width: (loopNum.openTotal / (Number(loopNum.openTotal) + Number(loopNum.closeTotal) + Number(loopNum.unknownTotal))) * 100 + '%'
                  }"
                >
                  <span
                    >开启&nbsp;&nbsp;<span class="span-font">{{ loopNum.openTotal }}</span></span
                  >
                  <el-progress :percentage="100" color="#FFC050" :format="format"></el-progress>
                </div>
                <div
                  class="progressBox"
                  :style="{
                    width: (loopNum.closeTotal / (Number(loopNum.openTotal) + Number(loopNum.closeTotal) + Number(loopNum.unknownTotal))) * 100 + '%'
                  }"
                >
                  <span
                    >关闭&nbsp;&nbsp;<span class="span-font">{{ loopNum.closeTotal }}</span></span
                  >
                  <el-progress :percentage="100" color="#868895" :format="format"></el-progress>
                </div>
                <div
                  class="progressBox"
                  :style="{
                    width: (loopNum.unknownTotal / (Number(loopNum.openTotal) + Number(loopNum.closeTotal) + Number(loopNum.unknownTotal))) * 100 + '%'
                  }"
                >
                  <span v-if="loopNum.unknownTotal != 0"
                    >未知&nbsp;&nbsp;<span class="span-font">{{ loopNum.unknownTotal }}</span></span
                  >
                  <el-progress :percentage="100" color="#00D06A" :format="format"></el-progress>
                </div>
              </div>
              <div class="loop_overview">
                <div>
                  <span>回路总数（条）</span>
                  <i>{{ loopNum.outPutTotal }}</i>
                </div>
                <div>
                  <span>模块总数（个）</span>
                  <i>{{ loopNum.actuatorTotal }}</i>
                </div>
              </div>
            </div>
            <div class="echart_content">
              <div v-if="fadeShow" id="monitoringEchart"></div>
            </div>
          </div>
        </transition>
        <div
          class="middle_type"
          :style="{
            height: fadeShow ? 'calc(50% - 55px)' : 'calc(100% - 55px)'
          }"
        >
          <el-tabs v-model="activeTab" style="padding-bottom: 10px;" @tab-click="handleTabsClick">
            <el-tab-pane label="按控制模块管理" name="modular"></el-tab-pane>
            <el-tab-pane label="按分组管理" name="group"></el-tab-pane>
            <el-tab-pane label="按回路管理" name="loop"></el-tab-pane>
            <el-tab-pane label="按服务空间管理" name="area"></el-tab-pane>
          </el-tabs>
          <i v-if="fadeShow" title="展开" class="el-icon-top fadeBtn" @click="fadeChange"></i>
          <i v-else title="关闭" class="el-icon-bottom fadeBtn" @click="fadeChange"></i>
          <div class="tab-content">
            <div class="search-form">
              <div>
                <el-select
                  v-if="activeTab == 'modular' || activeTab == 'loop'"
                  v-model="searchForm.constructionId"
                  class="sino_sdcp_input mr15"
                  clearable
                  placeholder="请选择设备服务建筑"
                  @change="searchFromChange(false)"
                >
                  <el-option v-for="item in buildingOption" :key="item.id" :label="item.ssmName" :value="item.id"></el-option>
                </el-select>
                <el-select
                  v-if="activeTab == 'modular' || activeTab == 'loop'"
                  v-model="searchForm.floorId"
                  class="sino_sdcp_input mr15"
                  clearable
                  placeholder="请选择设备服务楼层"
                  @change="searchFromChange(false)"
                >
                  <el-option v-for="item in floorOption" :key="item.id" :label="item.ssmName" :value="item.id"></el-option>
                </el-select>
                <el-select
                  v-if="activeTab == 'group' || activeTab == 'area'"
                  v-model="searchForm.switchStatus"
                  class="sino_sdcp_input mr15"
                  clearable
                  placeholder="请选择开关状态"
                  @change="searchFromChange(false)"
                >
                  <el-option v-for="item in statusOption" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
                <el-select
                  v-if="activeTab == 'area'"
                  v-model="searchForm.spaceTypeId"
                  class="sino_sdcp_input mr15"
                  clearable
                  filterable
                  placeholder="请选择空间类型"
                  @change="searchFromChange(false)"
                >
                  <el-option v-for="item in areaOption" :key="item.id" :label="item.dictName" :value="item.id"></el-option>
                </el-select>
                <el-input
                  v-if="activeTab == 'group'"
                  v-model.trim="searchForm.groupName"
                  clearable
                  placeholder="搜索分组名称"
                  class="sino_sdcp_input mr15"
                  @input="debounceFun"
                ></el-input>
                <!-- @blur="searchFromChange(false)" -->
              </div>
              <el-input
                v-if="activeTab == 'modular' || activeTab == 'loop'"
                v-model.trim="searchForm.surveryName"
                clearable
                :placeholder="activeTab == 'modular' ? '搜索执行器名称' : '搜索回路名称'"
                class="sino_sdcp_input mr15"
                @input="debounceFun"
              ></el-input>
              <el-input
                v-if="activeTab == 'area'"
                v-model.trim="searchForm.spaceName"
                clearable
                placeholder="搜索空间名称"
                class="sino_sdcp_input mr15"
                @input="debounceFun"
              ></el-input>
            </div>
            <div style="height: 40px; margin: 5px 0;">
              <el-button v-if="automatic == 1" type="primary" @click="btnConfig('3')">一键强开</el-button>
              <el-button v-if="automatic == 1" type="primary" @click="btnConfig('4')">一键强关</el-button>
              <el-button v-if="automatic == 2" type="primary" @click="btnConfig('1')">一键打开</el-button>
              <el-button v-if="automatic == 2" type="primary" @click="btnConfig('0')">一键关闭</el-button>
            </div>
            <div class="equ-content" :style="{ overflow: activeTab == 'modular' ? 'auto' : 'hidden' }">
              <div v-if="activeTab == 'modular'" class="modular_manage">
                <div v-for="(item, index) in modularManageData" :key="index" class="rectangle_box">
                  <div class="box_title">
                    <el-checkbox
                      ref="checkBoxRef"
                      :value="item.checked"
                      name="checkBoxId"
                      :true-label="item.actuatorId + '_true'"
                      :false-label="item.actuatorId + '_false'"
                      :checked="item.checked"
                      @change="changeCheckBox"
                    >
                    </el-checkbox>
                    <span>{{ item.localName.length ? item.localName.toString() : item.localName }}</span>
                    <div v-if="item.state == 0" class="legend-right-box linkBg"><span class="link"></span><i>在线</i></div>
                    <div v-else class="legend-right-box notlinkBg"><span class="notlink"></span><i>离线</i></div>
                  </div>
                  <div class="box_content">
                    <div v-for="(itemD, idx) in item.child" :key="idx" class="box_content_one">
                      <div>{{ itemD.loopName }}</div>
                      <div class="light_bg">
                        <span :class="itemD.outputStatus == '1' ? 'onlight' : 'offlight'"><div></div></span>
                      </div>
                      <div>
                        <el-switch
                          v-if="automatic == 2 || manualControlList.includes(itemD.actuatorId + '_' + itemD.outPutId)"
                          v-model="itemD.outputStatus"
                          active-text="开"
                          inactive-text="关"
                          active-value="1"
                          inactive-value="0"
                          :disabled="itemD.forceSwitch == 3 || itemD.forceSwitch == 4"
                          @change="loopStatusChange($event, itemD)"
                        >
                        </el-switch>
                        <span v-if="automatic == 1 && manualControlList.includes(itemD.actuatorId + '_' + itemD.outPutId)">({{ controlSecond }}s)</span>
                        <el-popover
                          v-if="automatic == 1 && !manualControlList.includes(itemD.actuatorId + '_' + itemD.outPutId)"
                          popper-class="poper_btns"
                          placement="top"
                          width="auto"
                          trigger="click"
                        >
                          <div class="switch_btns">
                            <div v-for="(hasItem, h) in btnGroupColors" :key="h">
                              <!-- 四个状态渲染 不为自己的其他三个  并且手动状态只对自动状态开放 -->
                              <el-button
                                v-if="hasItem.state != itemD.forceSwitch && !((itemD.forceSwitch == 3 || itemD.forceSwitch == 4) && hasItem.state == 6)"
                                type="primary"
                                :style="{
                                  backgroundColor: hasItem.color,
                                  color: '#fff!important',
                                  border: 'none'
                                }"
                                @click="btnConfig(hasItem.state, itemD)"
                                >{{ hasItem.name }}</el-button
                              >
                            </div>
                          </div>
                          <el-button
                            slot="reference"
                            type="primary"
                            style="margin: 0;"
                            :style="{ backgroundColor: btnGroupColors.find((e) => e.state == (itemD.forceSwitch ?? '5'))?.color, color: '#fff!important', border: 'none' }"
                            >{{ btnGroupColors.find((e) => e.state == (itemD.forceSwitch ?? '5'))?.name }}</el-button
                          >
                        </el-popover>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else class="other_manage">
                <el-table
                  :key="activeTab"
                  ref="tableList"
                  v-loading="tableLoading"
                  :data="tableData"
                  :border="true"
                  stripe
                  height="calc(100% - 30px)"
                  :cell-style="{ padding: '8px' }"
                  @selection-change="handleSelectionChange"
                >
                  <el-table-column type="selection" width="55" align="center"></el-table-column>
                  <el-table-column v-for="(item, index) in tableTitle" :key="index" :prop="item.value" :label="item.label" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <span v-if="item.value == 'spaceType'">{{ getSpaceTypeToId(scope.row.spaceType) }}</span>
                      <span v-else>{{ scope.row[item.value] }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" min-width="150">
                    <template slot-scope="scope">
                      <!-- loop -->
                      <div v-if="automatic == 1 && activeTab == 'loop' && !manualControlList.includes(scope.row.actuatorId + '_' + scope.row.outPutId)" class="operationBtn">
                        <span v-if="scope.row.forceSwitch != 3" @click="btnConfig('3', scope.row)">强开</span>
                        <span v-if="scope.row.forceSwitch != 4" @click="btnConfig('4', scope.row)">强关</span>
                        <span class="operation_line"> | </span>
                        <span v-if="scope.row.forceSwitch != 5" @click="btnConfig('5', scope.row)">自动</span>
                        <span @click="btnConfig('6', scope.row)">手动</span>
                      </div>
                      <div v-if="(automatic == 2 || manualControlList.includes(scope.row.actuatorId + '_' + scope.row.outPutId)) && activeTab == 'loop'" class="operationBtn">
                        <span v-if="scope.row.outputStatus != 1" @click="loopStatusChange('1', scope.row)">开启</span>
                        <span v-if="scope.row.outputStatus != 0" @click="loopStatusChange('0', scope.row)">关闭</span>
                        <span v-if="automatic == 1 && manualControlList.includes(scope.row.actuatorId + '_' + scope.row.outPutId)">({{ controlSecond }}s)</span>
                      </div>
                      <!-- group -->
                      <div v-if="automatic == 1 && activeTab == 'group' && !manualControlList.includes(scope.row.groupId)" class="operationBtn">
                        <span @click="btnConfig('3', scope.row)">强开</span>
                        <span @click="btnConfig('4', scope.row)">强关</span>
                        <span class="operation_line"> | </span>
                        <span @click="btnConfig('5', scope.row)">自动</span>
                        <span @click="btnConfig('6', scope.row)">手动</span>
                      </div>
                      <div v-if="(automatic == 2 || manualControlList.includes(scope.row.groupId)) && activeTab == 'group'" class="operationBtn">
                        <span @click="btnConfig('1', scope.row)">开启</span>
                        <span @click="btnConfig('0', scope.row)">关闭</span>
                        <span v-if="automatic == 1 && manualControlList.includes(scope.row.groupId)">({{ controlSecond }}s)</span>
                      </div>
                      <!-- area -->
                      <div v-if="automatic == 1 && activeTab == 'area' && !manualControlList.includes(scope.row.spaceId)" class="operationBtn">
                        <span @click="btnConfig('3', scope.row)">强开</span>
                        <span @click="btnConfig('4', scope.row)">强关</span>
                        <span class="operation_line"> | </span>
                        <span @click="btnConfig('5', scope.row)">自动</span>
                        <span @click="btnConfig('6', scope.row)">手动</span>
                      </div>
                      <div v-if="(automatic == 2 || manualControlList.includes(scope.row.spaceId)) && activeTab == 'area'" class="operationBtn">
                        <span @click="btnConfig('1', scope.row)">开启</span>
                        <span @click="btnConfig('0', scope.row)">关闭</span>
                        <span v-if="automatic == 1 && manualControlList.includes(scope.row.spaceId)">({{ controlSecond }}s)</span>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
                <el-pagination
                  class="pagination"
                  :current-page="paginationData.page"
                  :page-sizes="[15, 30, 50, 100]"
                  :page-size="paginationData.pageSize"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="paginationData.total"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                ></el-pagination>
              </div>
            </div>
          </div>
        </div>
        <template v-if="automaticChangeDialogShow">
          <automaticChangeDialog
            :automaticChangeDialogShow="automaticChangeDialogShow"
            :dialogData="automaticDialogData"
            @closeAutomaticDialog="closeAutomaticDialog"
            @automaticSubmit="automaticSubmit"
          />
        </template>
        <template v-if="controlReportDialogShow">
          <controlReportDialog :controlReportDialogShow="controlReportDialogShow" :dialogData="controlDialogData" @closeControlDialog="closeControlDialog" />
        </template>
        <!-- <audio
          class="audio"
          controls
          ref="audio"
          src="http://ihcrssjt.logimis.com:10001/ipsm/9d4249e0d3fa42c2b326429210e81e31.mp3?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=minioadmin%2F20220908%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20220908T025328Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=2bf278d8994c32e3dab45498aea88fba8ed412d6e2ebbf047941ece2f863c1dc"
          ></audio> -->
      </div>
    </template>
  </PageContainer>
</template>
<script type="text/ecmascript-6">
// import { objectExpression } from 'babel-types'
import * as echarts from 'echarts'
import moment from 'moment'
import sunshine_weather_active from '@/assets/images/lightingMonitoring/sunshine_weather_active.png'
import automaticChangeDialog from '../components/automaticChangeDialog.vue'
import controlReportDialog from '../components/controlReportDialog.vue'
import Dict from '../components/dict.js'
export default {
  name: 'filledRecord',
  components: {
    automaticChangeDialog,
    controlReportDialog
  },
  data() {
    return {
      automatic: '1',
      controlSecond: null, // 控制模块手动操作倒计时
      second: null, // 手动操作倒计时
      timerSecond: null, // 定时器
      todayPattern: {
        patternName: '晴天模式',
        patternActiveSrc: sunshine_weather_active
      },
      patternTypeIconList: Dict.patternTypeIconList,
      checkBoxGroup: [], // 控制器选中
      selectionData: [], // table选中
      automaticChangeDialogShow: false, // 自动切换弹窗
      automaticDialogData: {}, // 自动切换弹窗数据
      controlReportDialogShow: false, // 控制报表弹窗显示
      controlDialogData: {}, // 控制报表弹窗数据
      btnGroupColors: [
        {
          name: '强开',
          color: '#34B253',
          value: 'forcedOpen',
          state: '3'
        },
        {
          name: '强关',
          color: '#5188FC',
          value: 'forcedClose',
          state: '4'
        },
        {
          name: '自动',
          color: '#8671DE',
          value: 'auto',
          state: '5'
        },
        {
          name: '手动',
          color: '#FFBB52',
          value: 'manual',
          state: '6'
        }
      ],
      fadeShow: true, // 淡入淡出
      loopNum: {
        openTotal: 0,
        closeTotal: 0,
        unknownTotal: 0,
        outPutTotal: 0,
        actuatorTotal: 0,
        offlineTotal: 0,
        moduleFailureTotal: 0,
        controlReturnTotal: 0
      },
      activeTab: 'modular',
      buildingOption: [],
      floorOption: [],
      areaOption: [],
      statusOption: [
        {
          id: '1',
          name: '开启'
        }, {
          id: '0',
          name: '关闭'
        }
      ],
      eventStatus: true, // 查询节流
      searchForm: {
        switchStatus: '',
        constructionId: '',
        floorId: '',
        surveryName: '',
        groupName: '',
        spaceName: '',
        spaceTypeId: '',
        type: 1
      },
      tableData: [],
      tableLoading: false,
      tableTitle: [],
      tableGroupTitle: [
        { label: '分组名称', value: 'groupName' },
        { label: '回路数量', value: 'loopsNum' },
        { label: '开关状态', value: 'state' }
      ],
      tableLoopTitle: [
        { label: '回路名称', value: 'loopsName' },
        { label: '状态', value: 'state' }
      ],
      tableAreaTitle: [
        { label: '基础空间名称', value: 'spaceName' },
        { label: '空间类型', value: 'spaceType' },
        { label: '支路数量', value: 'loopsNum' },
        { label: '状态', value: 'state' }
      ],
      paginationData: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      modularManageData: [ // 模块管理数据
      ],
      tabType: ['modular', 'group', 'loop', 'area'],
      manualControlList: [], // 控制模块手动选中列表
      inputTimer: null// 节流定时器
    }
  },
  mounted() {
    this.getStructureTree()
    this.getAreaTypeList()
    this.getMonitorStatusList()
    this.searchFromChange()
    // this.setWebsocket()
  },
  methods: {
    format() {
      return ''
    },
    setWebsocket() {
      let projectCode = sessionStorage.getItem('websocket')
        ? JSON.parse(sessionStorage.getItem('websocket')).projectCode
        : ''
      // 匹配设备相同的接收消息刷新页面
      if (projectCode == '00f4ad80e5c04809aae72c7470e8be28') {
        // 收到消息刷新页面
        console.log('照明匹配成功')
        this.searchFromChange(true)
      }
    },
    // 手动自动切换
    automaticChange(val) {
      this.automatic = val
      this.manualControlList = []// 手动的列表置空
      // 手动状态开始倒计时
      const flag = val == 2
      if (flag) { // 监听鼠标移动事件
        document.addEventListener('mousemove', this.windowAddEvent)
      } else { // 取消监听mousemove
        // document.removeEventListener("mousemove", this.windowAddEvent)
        this.windowRemoveEvent()
      }
      // 切换 数据重新加载
      this.searchFromChange()
    },
    windowAddEvent() {
      console.log('监听鼠标移动事件')
      let that = this
      // that.second = 60
      let second = 60
      if (that.automatic == 2) {
        that.second = second
      } else {
        that.controlSecond = second
      }
      // 清除定时器
      clearInterval(that.timerSecond)
      that.timerSecond = setInterval(() => {
        // that.second--
        second--
        if (that.automatic == 2) {
          that.second = second
        } else {
          that.controlSecond = second
        }
        if (second == 0) {
          that.windowRemoveEvent()
        }
      }, 600)
    },
    windowRemoveEvent() {
      console.log('取消监听鼠标移动事件')
      document.removeEventListener('mousemove', this.windowAddEvent)
      this.second = null
      this.controlSecond = null
      clearInterval(this.timerSecond)
      this.automatic = 1
      this.manualControlList = []// 手动的列表置空
    },
    // 强开强关 开启关闭
    btnConfig(type, row) {
      // console.log(type);
      const viewType = this.tabType.indexOf(this.activeTab) + 1
      // 有值代表单独操作
      if (row) {
        // 自动转其他类型需要提交弹窗(控制模块/回路模块)
        if (row.forceSwitch == 5) {
          this.automaticDialogData = this.btnGroupColors.find(e => e.state == type)
          Object.assign(this.automaticDialogData, {
            ...row,
            state: this.automaticDialogData.state,
            viewType: viewType
          })
          this.automaticChangeDialogShow = true
        } else { // 强开强关转其他 分组和空间操作
          if (viewType == 1 || viewType == 3) { // 控制 回路 单控
            this.$confirm('此操作将改变回路运行状态, 是否继续?', '提示', {
              cancelButtonClass: 'el-button--primary is-plain',
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              let params = {
                type: viewType,
                actuatorId: row.actuatorId,
                outputNum: row.outPutId,
                outputStatus: type == 3 ? '1' : (type == 4 ? '0' : (type == 5 ? null : type)),
                forceSwitch: (type == 1 || type == 0) ? null : type
              }
              this.controlSubmit(params, 'alone')
            })
          } else if (viewType == 2 || viewType == 4) { // 分组群控 建筑群控
            if (type == 6) { // 切换手动
              this.automaticDialogData = this.btnGroupColors.find(e => e.state == type)
              Object.assign(this.automaticDialogData, {
                ...row,
                state: this.automaticDialogData.state,
                viewType: viewType
              })
              this.automaticChangeDialogShow = true
            } else { // 强开强关开启关闭
              this.$confirm('此操作将改变回路运行状态, 是否继续?', '提示', {
                cancelButtonClass: 'el-button--primary is-plain',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                let params = {
                  type: viewType,
                  // groupId: row.groupId,
                  outputStatus: type == 3 ? '1' : (type == 4 ? '0' : (type == 5 ? null : type)),
                  forceSwitch: (type == 1 || type == 0) ? null : type
                }
                if (viewType == 2) {
                  Object.assign(params, {
                    groupId: row.groupId
                  })
                } else {
                  Object.assign(params, {
                    serviceSpaceId: row.spaceId
                  })
                }
                if (type == '5') {
                  this.controlSubmit(params, 'alone')
                } else {
                  this.controlSubmit(params, 'group')
                }
              })
            }
          } else { //
          }
        }
      } else { // 没值代表批量操作
        let params = {}
        // 执行器
        if (viewType == 1) {
          if (!this.checkBoxGroup.length) return this.$message.warning('请选中数据后再进行批量操作！')
          const groupControl = this.checkBoxGroup.map(item => {
            let sameData = this.modularManageData.find(e => e.actuatorId == item)
            if (sameData.child.length) {
              let dealData = sameData.child.map((item) => {
                return item.actuatorId + '_' + item.outPutId
              })
              return dealData
            }
          })
          // console.log(groupControl.flat());
          params = {
            type: viewType,
            groupControl: groupControl.flat().toString(),
            outputStatus: type == 3 ? '1' : (type == 4 ? '0' : (type == 5 ? null : type)), // 强开同时开启 自动忽略穿5
            forceSwitch: (type == 1 || type == 0) ? null : type
          }
        } else {
          if (!this.selectionData.length) return this.$message.warning('请选中数据后再进行批量操作！')
          if (viewType == 2) { // 分组
            let dealData = Array.from(this.selectionData, ({ groupId }) => groupId)
            params = {
              type: viewType,
              groupId: dealData.toString(),
              outputStatus: type == 3 ? '1' : (type == 4 ? '0' : (type == 5 ? null : type)), // 强关同时关闭
              forceSwitch: (type == 1 || type == 0) ? null : type
            }
          } else if (viewType == 3) { // 回路
            console.log(this.selectionData)
            let dealData = this.selectionData.map((item) => {
              return item.actuatorId + '_' + item.outPutId
            })
            params = {
              type: viewType,
              groupControl: dealData.toString(),
              outputStatus: type == 3 ? '1' : (type == 4 ? '0' : (type == 5 ? null : type)), // 强关同时关闭
              forceSwitch: (type == 1 || type == 0) ? null : type // 开关转为自动
            }
          } else { // 建筑空间
            let dealData = Array.from(this.selectionData, ({ spaceId }) => spaceId)
            params = {
              type: viewType,
              serviceSpaceId: dealData.toString(),
              outputStatus: type == 3 ? '1' : (type == 4 ? '0' : (type == 5 ? null : type)), // 强关同时关闭
              forceSwitch: (type == 1 || type == 0) ? null : type
            }
          }
        }
        this.controlSubmit(params, 'group')
      }
    },
    // 获取选中执行器id
    changeCheckBox(val) {
      let [data, flag] = val.split('_')
      // 没有增加 有了删除
      if (flag == 'true') {
        this.checkBoxGroup.push(data)
      } else {
        this.checkBoxGroup.splice(this.checkBoxGroup.indexOf(data), 1)
      }
      this.modularManageData.map(item => {
        item.checked = this.checkBoxGroup.some(e => e == item.actuatorId)
      })
      this.$forceUpdate()
      console.log(this.checkBoxGroup)
    },
    handleTabsClick() {
      console.log('tab切换--', this.activeTab)
      this.checkBoxGroup = [] // 选中重置(控制模块)
      this.selectionData = [] // 选中重置(table选中)
      this.manualControlList = [] // 手动控制列表重置
      this.windowRemoveEvent() // 解除手动状态的监听
      const column = [
        {
          name: 'group',
          value: 'tableGroupTitle'
        }, {
          name: 'loop',
          value: 'tableLoopTitle'
        }, {
          name: 'area',
          value: 'tableAreaTitle'
        }]
      const tabType = ['modular', 'group', 'loop', 'area']
      // 获取表格列
      this.tableTitle = this.activeTab === 'modular' ? [] : this[column.filter(e => e.name == this.activeTab)[0].value]
      // 获取选中tab的类型
      Object.assign(this.searchForm, {
        switchStatus: '',
        constructionId: '',
        floorId: '',
        surveryName: '',
        groupName: '',
        spaceName: '',
        spaceTypeId: '',
        type: tabType.indexOf(this.activeTab) + 1
      })
      Object.assign(this.paginationData, {
        page: 1,
        pageSize: 15,
        total: 0
      })
      this.searchFromChange(false)
    },
    // 获取空间 建筑 楼层信息
    getStructureTree() {
      this.$api.getStructureTree({}).then((res) => {
        const data = res.data
        if (data && data.length) {
          this.buildingOption = data.filter((e) => e.ssmType == 3)
          this.floorOption = data.filter((e) => e.ssmType == 4)
        }
      })
    },
    // 获取空间类型
    getAreaTypeList() {
      this.$api.valveTypeList({ typeValue: 'SP' }).then((res) => {
        const data = res.data
        if (data && data.length) {
          this.areaOption = data
          // console.log(this.areaOption)
        }
      })
    },
    // 获取总览数据
    getMonitorStatusList() {
      let startTime = moment().format('YYYY-MM-DD') + ' 00:00:00'
      let endTime = moment().format('YYYY-MM-DD') + ' 24:00:00'
      this.$api.getCountRunToDayState({}).then((res) => {
        if (res.code == '200') {
          // 遍历对象res.data
          let data = res.data[1]
          // Object.assign(data, {
          //   '2022-09-16 23:59': null
          // })
          console.log(data)
          let {sunRise, sunSet} = res.data[0]
          let weatherPattern = res.data[2].weatherPattern
          // 今日运行模式
          this.todayPattern = {
            patternActiveSrc: this.patternTypeIconList.find(
              (item) => item.patternCode == weatherPattern.patternType
            )?.patternActiveSrc,
            patternName: weatherPattern.patternName
          }
          sunRise = moment().format('YYYY-MM-DD') + ' ' + sunRise + ':00'
          sunSet = moment().format('YYYY-MM-DD') + ' ' + sunSet + ':00'
          let dataArray = []
          let seriesData = []
          // 生成时间数组 和 时间对应数据的数组
          for (let key in data) {
            dataArray.push(key)
            // console.log(data[key]);
            seriesData.push({
              actuallyClosed: data[key]?.[0]?.actuallyClosed ?? 0,
              actuallyOpen: data[key]?.[0]?.actuallyOpen ?? 0,
              loopSum: data[key]?.[0]?.loopSum ?? 0,
              planClose: data[key]?.[0]?.planClose ?? 0,
              planOpen: data[key]?.[0]?.planOpen ?? 0
            })
          }
          this.$nextTick(() => {
            this.getMonitorEchart(startTime, endTime, dataArray, seriesData, sunRise, sunSet)
          })
        }
      })
    },
    getMonitorEchart(startTime, endTime, dataArray, seriesData, sunRise, sunSet) {
      const getchart = echarts.init(document.getElementById('monitoringEchart'))
      getchart.resize()
      const legendData = [
        '回路总数',
        '实际开启',
        '实际关闭',
        // "未知",
        '计划开启',
        '计划关闭'
      ]
      const legendField = [
        'loopSum',
        'actuallyOpen',
        'actuallyClosed',
        // 'unknown',
        'planOpen',
        'planClose'
      ]
      const legendColor = [
        '#5188FC',
        '#FFC265',
        '#868894',
        // "#59CC75",
        '#FFC265',
        '#868894'
      ]
      const legendLineType = [
        'solid',
        'solid',
        // "solid",
        'solid',
        'dashed',
        'dashed'
      ]
      const seriesObj = []
      for (let i = 0; i < legendData.length; i++) {
        seriesObj.push({
          name: legendData[i],
          type: 'line',
          step: 'start',
          data: [],
          symbol: 'none',
          lineStyle: {
            color: legendColor[i],
            width: 2,
            type: legendLineType[i]
          }
        })
        for (let j = 0; j < dataArray.length; j++) {
          // var num = parseInt(Math.random(1000) * 1000 + 1);
          // console.log(num, seriesData[j][legendField[i]]);
          seriesObj[i].data.push([dataArray[j], seriesData[j][legendField[i]]])
        }
      }
      seriesObj[0].markArea = {
        itemStyle: {
          color: 'rgba(255, 173, 177, 0.4)'
        },
        data: [
          [
            {
              name: '日出',
              xAxis: sunRise
            },
            {
              xAxis: moment(sunRise).add (2, 'minutes').format('YYYY-MM-DD HH:mm:ss')
            }
          ],
          [
            {
              name: '日落',
              xAxis: sunSet
            },
            {
              name: '日落',
              xAxis: moment(sunSet).add (2, 'minutes').format('YYYY-MM-DD HH:mm:ss')
            }
          ]
        ]
      }
      // console.log(seriesObj[0].markArea.data);
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          // icon: "circle",
          data: legendData
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'time',
          interval: 60 * 60 * 1000, // 固定x轴时间间隔 间隔24小时，也就是一天
          min: startTime, // 开始时间时间戳
          max: endTime, // 结束时间时间戳 如果实际的最大日期不确定，也可以不设定这个属性
          boundaryGap: false,
          nameLocation: 'start',
          // x轴的字
          axisLabel: {
            show: true,
            showMinLabel: true,
            showMaxLabel: true,
            formatter: function (value, index) {
              // 格式化成月/日，只在第一个刻度显示年份
              var date = new Date(value)
              return date.getHours() + ':00'
            }
          },
          splitLine: {
            show: false
          },
          axisTick: {
            show: false // 不显示坐标轴刻度线
          },
          label: {}
        },
        yAxis: {
          type: 'value',
          // show: false,
          splitLine: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false // 不显示坐标轴刻度线
          }
        },
        series: seriesObj
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 查询事件
    searchFromChange(flag = true) { // 是否需要重载总览数据
      // console.log('查询参数--', this.searchForm);
      const params = {
        ...this.searchForm,
        ...this.paginationData
      }
      delete params.total
      this.tableLoading = true
      this.$api.groupOperationMonitoring(params).then(res => {
        const data = res.data
        this.tableLoading = false
        if (this.activeTab === 'modular') {
          // console.log(data)
          this.modularManageData = data ?? []
          this.modularManageData.map(item => {
            item.checked = false
          })
          this.$forceUpdate()
        } else {
          this.tableData = data.list ?? []
          this.paginationData.total = data.count
        }
      })
      flag ? this.searchCountOverview() : ''
      flag ? this.getMonitorStatusList() : ''
      var that = this
      if (!that.eventStatus) {
        return
      }
      that.eventStatus = false
      setTimeout(() => {
        try {
          that.getEquipmentRealTimeData()
        } catch (error) {
        }
        that.eventStatus = true
      }, 1500)
    },
    // 查询顶部总览
    searchCountOverview() {
      this.$api.countOverview().then(res => {
        if (res.code == 200) {
          // console.log(res);
          this.loopNum = res.data
        }
      })
    },
    // table 选中事件
    handleSelectionChange() {
      this.selectionData = this.$refs.tableList.selection
    },
    // 获取 实时设备数据
    getEquipmentRealTimeData() {
      // console.log('data');
    },
    // 回路执行器 回路单控（手动/ 自动） 改变 auto判断自动手动
    loopStatusChange(val, item, auto) {
      console.log('switch--', val, item)
      // this.$confirm('此操作将改变回路运行状态, 是否继续?', '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning'
      // }).then(() => {
      const params = {
        type: this.tabType.indexOf(this.activeTab) + 1,
        actuatorId: item.actuatorId,
        outputNum: item.outPutId,
        outputStatus: val == 3 ? '1' : (val == 4 ? '0' : (val == 5 ? null : val)),
        forceSwitch: (val == 1 || val == 0) ? null : val
      }
      this.controlSubmit(params, 'alone')
      // })
    },
    // 提交控制 type 单控或者群控
    controlSubmit(params, type) {
      this.tableLoading = true
      console.log('params--', params)
      this.$api.lightOpenOrClose(params).then(res => {
        this.tableLoading = false
        // console.log(res);
        if (res.code == 200) {
          if (type == 'alone') {
            if (res.data.code == 200) {
              this.$message.success(res.data.returnMap[0].outputStatus)
            } else {
              this.$message.warning(res.data.returnMap[0].outputStatus)
            }
            this.searchFromChange()
          } else {
            res.data.date = moment(res.data.date).format('HH:mm:ss')
            this.controlDialogData = res.data
            this.controlReportDialogShow = true
          }
        } else {
          this.$message.warning(res.message)
          this.searchFromChange()
        }
      })
    },
    // 输入框节流
    debounceFun() {
      if (this.inputTimer) {
        clearTimeout(this.inputTimer)
      }
      this.inputTimer = setTimeout(() => {
        this.inputTimer = null
        this.searchFromChange(false)
      }, 1000)
    },
    handleSizeChange(val) {
      this.paginationData.page = 1
      this.paginationData.pageSize = val
      this.searchFromChange(false)
    },
    handleCurrentChange(val) {
      this.paginationData.page = val
      this.searchFromChange(false)
    },
    getSpaceTypeToId(id) {
      const obj = this.areaOption.find((e) => e.id == id)
      return obj ? obj.dictName : ''
    },
    closeAutomaticDialog(data) {
      // this.automaticSubmit(data)
      this.automaticChangeDialogShow = false
    },
    closeControlDialog() {
      this.controlReportDialogShow = false
      this.checkBoxGroup = [] // 选中重置
      this.selectionData = []
      this.searchFromChange()
    },
    // 切换控制提交
    automaticSubmit(data) {
      // 切换为手动的list
      if (data.state == 6) {
        if (data.viewType == 1 || data.viewType == 3) {
          this.manualControlList.push(data.actuatorId + '_' + data.outPutId)
        } else if (data.viewType == 2) {
          this.manualControlList.push(data.groupId)
        } else if (data.viewType == 4) {
          this.manualControlList.push(data.spaceId)
        }
        // 开启监听60s
        this.controlSecond = 60
        document.addEventListener('mousemove', this.windowAddEvent)
      } else { // 强开强关
        if (data.viewType == 1 || data.viewType == 3) {
          this.loopStatusChange(data.state, data, true)
        } else if (data.viewType == 2 || data.viewType == 4) {
          let params = {
            type: data.viewType,
            // groupId: data.groupId,
            outputStatus: data.state == 3 ? '1' : (data.state == 4 ? '0' : data.state),
            forceSwitch: data.state
          }
          if (data.viewType == 2) {
            Object.assign(params, {
              groupId: data.groupId
            })
          } else {
            Object.assign(params, {
              serviceSpaceId: data.spaceId
            })
          }
          this.controlSubmit(params, 'group')
        }
      }
      this.automaticChangeDialogShow = false
    },
    // 改变高度
    fadeChange() {
      this.fadeShow = !this.fadeShow
      if (this.fadeShow) {
        setTimeout(() => {
          this.getMonitorStatusList()
        }, 300)
      }
    }
  }
}
</script>
<style src="@/assets/styles/sinomis-ui.css" scoped></style>
<style lang="scss" scoped>
.sino-all-content {
  height: calc(100% - 10px);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border-radius: 0;
  background-color: #f5f5fa;

  .top {
    border-radius: 10px;
    height: 48px;
    line-height: 48px;
    background-color: #fff;
    display: flex;

    .toptip {
      border-bottom: none;
      width: auto;
      font-size: 16px;
      font-weight: 400;
    }

    .top_right {
      flex-grow: 1;
      display: flex;
      justify-content: space-between;
      padding: 0 20px;
      box-sizing: border-box;

      .overview {
        display: flex;
        flex-grow: 1;
        justify-content: space-between;
        padding: 0 20px;
        box-sizing: border-box;

        .overview_data {
          display: flex;
          flex-grow: 1;

          .overview_data_title {
            font-size: 14px;
            font-family: "HarmonyOS_Sans_SC";
            color: #2a2a2a;
          }

          .overview_data_num {
            width: 90px;
            height: 36px;
            line-height: 36px;
            background: #f2f2f2;
            border-radius: 5px;
            margin: auto 10px;
            display: flex;
            margin-right: 20px;

            span {
              display: inline-block;
              width: 27px;
              height: 27px;
              margin: auto 10px 5px;
              margin-right: 5px;
            }

            i {
              font-size: 22px;
              font-family: Roboto-Medium, Roboto;
              font-weight: 500;
            }

            .notlink {
              background: url("~@/assets/images/lightingMonitoring/notlink.png") no-repeat;
              background-size: 100% 100%;
            }

            .warn {
              background: url("~@/assets/images/lightingMonitoring/warn.png") no-repeat;
              background-size: 100% 100%;
            }
          }

          .notlinkBg {
            background-color: #f2f2f2;
            color: #606266;
          }

          .warnBg {
            background: #ffebeb;
            color: #ff4848;
          }
        }

        .overview_mode {
          display: flex;

          .pattern_box {
            box-sizing: border-box;
            height: 38px;
            line-height: 38px;
            padding: 0 30px;
            border-radius: 24px;
            margin: auto 15px;
            cursor: pointer;

            img {
              vertical-align: middle;
              margin-right: 10px;
            }

            span {
              font-size: 14px;
              font-family: "HarmonyOS_Sans_SC";
              color: #333;
            }
          }

          .active_pattern {
            background: #fffae8;
            border: 1px solid #ffe8ab;
          }
        }
      }
    }
  }

  .top_echart {
    width: 100%;
    height: calc(50% - 10px);
    box-sizing: border-box;
    background-color: #fff;
    // padding: 20px 26px;
    border-radius: 10px;

    .top_overview {
      height: 19%;
      border-bottom: 1px solid #ededf3;
      display: flex;

      .p-box {
        width: 40%;
        margin: auto;
        display: flex;
        padding: 0 50px;

        .progressBox {
          // flex: 1;
          .span-font {
            font-size: 14px;
            color: #000;
          }

          & > span {
            white-space: nowrap;
            font-size: 12px;
            font-family: PingFang-SC-Medium, PingFang-SC;
          }
        }
      }

      .loop_overview {
        flex: 1;
        display: flex;
        padding-left: 50px;

        div {
          height: 40px;
          line-height: 40px;
          margin: auto 20px;
        }

        span {
          font-size: 14px;
          font-family: "HarmonyOS_Sans_SC";
          color: #2a2a2a;
        }

        i {
          margin: 0 15px;
          font-size: 24px;
          font-family: Roboto-Medium, Roboto;
          font-weight: 500;
          color: #5188fc;
          vertical-align: bottom;
        }
      }
    }

    .echart_content {
      height: calc(81%);
      width: 100%;
      padding: 15px 20px;

      #monitoringEchart {
        width: 100%;
        height: 100%;
      }
    }
  }

  .middle_type {
    width: 100%;
    // height: calc(50% - 55px);
    box-sizing: border-box;
    background-color: #fff;
    padding: 3px;
    border-radius: 10px;
    position: relative;
    transition: height 0.3s linear;
    overflow: hidden;

    .fadeBtn {
      position: absolute;
      top: 10px;
      right: 40px;
      font-size: 25px;
      cursor: pointer;
    }

    .tab-content {
      height: calc(100% - 55px);
      padding: 0 10px;

      .search-form {
        display: flex;
        justify-content: space-between;
      }

      .equ-content {
        height: calc(100% - 90px);
        padding-top: 6px;
        // overflow: auto;
        > div {
          width: 100%;
        }

        .modular_manage {
          display: flex;
          flex-wrap: wrap;

          .rectangle_box {
            background: #fff;
            box-shadow: 0 2px 7px 0 #e4e4ec;
            border-radius: 10px 4px 4px;
            margin: 0 10px 10px 0;

            .box_title {
              height: 45px;
              display: flex;
              line-height: 45px;
              justify-content: space-between;
              padding: 0 15px;
              box-sizing: border-box;
              border-bottom: 1px solid #d8dee7;

              .notlinkBg {
                background-color: #f2f2f2;
                color: #606266;
              }

              .linkBg {
                background-color: #e4f2e6;
                color: #34b253;
              }

              .legend-right-box {
                width: 50px;
                height: 18px;
                border-radius: 2px;
                margin: auto 0;
                display: flex;
                margin-left: 15px;

                span {
                  display: inline-block;
                  width: 18px;
                  height: 18px;
                  margin-right: 2px;
                  line-height: 18px;
                  padding: 2px;
                }

                i {
                  font-size: 13px;
                  line-height: 22px;
                }

                .notlink {
                  background: url("~@/assets/images/lightingMonitoring/notlink.png") no-repeat;
                  background-size: 100% 100%;
                }

                .link {
                  background: url("~@/assets/images/lightingMonitoring/link.png") no-repeat;
                  background-size: 100% 100%;
                }
              }
            }

            .box_content {
              display: flex;
              padding: 10px 15px;

              .box_content_one {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                min-width: 120px;
                height: 130px;
                text-align: center;
                flex: 1;
                padding: 12px 10px;
                box-sizing: border-box;

                > div {
                  width: 100%;
                }

                .light_bg {
                  // width: 26px;
                  // height: 34px;
                  > span {
                    display: inline-block;
                    width: 26px;
                    height: 34px;
                    position: relative;

                    div {
                      position: absolute;
                      bottom: 0;
                      right: -10px;
                      width: 6px;
                      height: 6px;
                      border-radius: 50%;
                      background: #34b253;
                    }
                  }

                  .onlight {
                    background: url("~@/assets/images/lightingMonitoring/onlight.png") no-repeat;
                    background-size: 100% 100%;

                    > div {
                      background: #34b253;
                    }
                  }

                  .offlight {
                    background: url("~@/assets/images/lightingMonitoring/offlight.png") no-repeat;
                    background-size: 100% 100%;

                    > div {
                      background: #ff4848;
                    }
                  }
                }

                &:hover {
                  background: rgb(246 249 255 / 84%);
                  border-radius: 4px;
                  border: 1px solid #5089fc;
                }
              }
            }
          }
        }

        .other_manage {
          height: 100%;

          .pagination {
            height: 30px !important;
            padding-top: 2px !important;
          }

          .operationBtn {
            span {
              font-size: 14px;
              font-family: "HarmonyOS_Sans_SC_Medium";
              color: #5188fc;
              margin: auto 10px;
              cursor: pointer;
            }

            .operation_line {
              color: #d8dee7;
            }
          }
        }
      }
    }
  }
}
// .fade-enter, .fade-leave-to {
//   opacity: 0;opacityopacityopacityopacityopacityopacityopacityopacityopacityopacityopacityopacityopacityopacityopacityopacityopacityopacityopacityopacityopacityopacityopacityopacityopacityopacityopacityopacityopacityopacityopacityopacityopacityopacityopacityopacityopacityopacityopacity
// }
// .fade-enter-active, .fade-leave-active {
//   transition: all 0.5s;transitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransitiontransition
// }
</style>
<style lang="scss">
.progressBox {
  .el-progress {
    margin-top: 7px;
  }

  .el-progress-bar {
    padding-right: 2px;
    border-radius: 0;

    .el-progress-bar__outer {
      border-radius: 0;
      background: center;
      height: 10px !important;
    }

    .el-progress-bar__inner {
      border-radius: 0;
    }
  }
}

.middle_type {
  .el-tabs__nav-wrap {
    padding-left: 15px;
  }

  .el-tabs__nav-wrap::after {
    height: 1px;
    background-color: #d8dee7;
  }

  .el-switch__label {
    color: #2d2d2d;
  }

  .el-switch__label.is-active {
    color: #424242;
  }

  .el-switch__core::after {
    width: 24px;
    height: 24px;
    top: -3px;
    left: -5px;
    box-shadow: 0 2px 7px 0 #a4a4b7;
  }

  .el-table thead {
    height: 40px !important;
    line-height: 40px !important;
  }

  .pagination {
    // height: auto!important;
    padding-top: 15px !important;
  }
}

.poper_btns {
  max-height: 200px !important;
  overflow: hidden !important;
}

.switch_btns {
  display: flex;
  flex-wrap: wrap;

  div {
    // width: 50%;
  }

  .el-button {
    // width: 25%;
    margin: 5px 10px !important;
    border: none !important;
  }
}
</style>
