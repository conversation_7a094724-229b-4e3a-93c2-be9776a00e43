<template>
  <el-dialog
    v-dialogDrag
    class="component housing-edit"
    :title="title"
    width="500px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    custom-class="model-dialog"
    destroy-on-close
    @close="onDialogClosed"
  >
    <div class="dialog-submit">
      <div class="room-box">
        <div class="room-box-title">分配房屋</div>
        <div class="room-box-inpit">
          <div class="box-input">{{ formInfo.houseName }}</div>
          <div class="box-div" @click="handReselect">重新选择</div>
        </div>
      </div>
      <div class="room-box roomBox">
        <div class="room-box-title">类型</div>
        <div class="room-box-inpit">
          <div class="box-input">{{ echoInfo.processName }}</div>
        </div>
      </div>
      <div class="room-box roomBox">
        <div class="room-box-title">员工名称</div>
        <div class="room-box-inpit">
          <div class="box-input">{{ echoInfo.name }}</div>
        </div>
      </div>
      <div class="room-box roomBox">
        <div class="room-box-title">工号</div>
        <div class="room-box-inpit">
          <div class="box-input">{{ echoInfo.userEmployeeId }}</div>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button type="primary" plain @click="onDialogClosed">取消</el-button>
      <el-button type="primary" @click="onSubmit">确认</el-button>
    </template>
  </el-dialog>
</template>
<script>
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    reselectRoomInfo: {
      type: Object,
      default: () => {}
    },
    echoInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      title: '分配房屋',
      rules: {
        spaceNames: [{ required: true, message: '请选择分配房屋', trigger: 'blur' }]
      },
      formInfo: {
        houseName: '',
        typeName: '',
        employeeName: '',
        jobNumber: ''
      }
    }
  },
  watch: {
    reselectRoomInfo: {
      handler(newVal, oldVal) {
        this.$nextTick(() => {
          this.formInfo.houseName = newVal.houseName
        })
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {},
  methods: {
    onDialogClosed() {
      this.$emit('submitDialogclose')
    },
    onSubmit() {
      if (!this.formInfo.houseName) {
        return this.$message.error('请先选择分配房屋!')
      }
      this.$emit('submitDialog')
    },
    handReselect() {
      this.$emit('reselect')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-submit {
  padding: 15px;
  box-sizing: border-box;
  width: 100%;
  background: #fff;
  .room-box {
    height: 32px;
    display: flex;
    align-items: center;
    .room-box-title {
      font-size: 15px;
      width: 65px;
      flex-shrink: 0;
      padding-left: 5px;
      position: relative;
      text-align: right;
      margin-right: 10px;
    }
    .room-box-title::after {
      content: '*';
      position: absolute;
      top: 0px;
      left: -5px;
      color: #ff1919;
    }
    .room-box-inpit {
      flex-shrink: 0;
      flex: 1;
      display: flex;
      justify-content: space-between;
      .box-input {
        width: calc(100% - 85px);
        flex-shrink: 0;
      }
      .box-div {
        width: 70px;
        text-align: center;
        color: #3664dd;
        cursor: pointer;
      }
    }
  }
  .roomBox {
    margin-top: 15px;
    .room-box-title::after {
      content: '';
      color: #fff;
    }
  }
}
</style>