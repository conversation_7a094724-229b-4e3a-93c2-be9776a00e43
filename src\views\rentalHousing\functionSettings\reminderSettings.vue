<template>
  <el-container v-loading="pageLoading" class="config-container">
    <!-- 即将超期提醒设置 -->
    <el-card shadow="hover" class="config-section">
      <div class="content">
        <div class="left-content">
          <h2>即将超期提醒设置</h2>
          <div class="checkbox-group">
            <span>开关状态：</span>
            <span :class="soonOverdueReminderInfo.upcomingReminderStatus == 1 ? 'span-active' : soonOverdueReminderInfo.upcomingReminderStatus === '0' ? 'span-close' : ''">
              {{ soonOverdueReminderInfo.upcomingReminderStatus | switchStatusFilters }}
            </span>
          </div>
          <div class="checkbox-group">
            <span>距离预计退租日期前：</span>
            <span class="span-style">{{ soonOverdueReminderInfo.upcomingDaysBeforeRentEnd || '' }}</span>
            <span>天进行提醒，短信发送频率：每天</span>
            <span class="span-style">{{
              soonOverdueReminderInfo.upcomingSmsSendTime
                ? `${soonOverdueReminderInfo.upcomingSmsSendTime > 9 ? soonOverdueReminderInfo.upcomingSmsSendTime : '0' + soonOverdueReminderInfo.upcomingSmsSendTime}:00`
                : ''
            }}</span>
            <span>发送</span>
          </div>
          <div class="checkbox-group">
            <span>短信发送至租户本人手机号，并发送短信给</span>
            <span class="span-style">{{ personnelOptionsFilter(soonOverdueReminderInfo.upcomingMembers) }}</span>
            <span>和</span>
            <span class="span-style">{{ soonOverdueReminderInfo.upcomingSmsReceivers || '' }}</span>
          </div>
        </div>
        <div class="right-content">
          <el-button type="primary" @click="configureSection('soonOverdue')">配置</el-button>
        </div>
      </div>
    </el-card>
    <!-- 超期提醒设置 -->
    <el-card shadow="hover" class="config-section">
      <div class="content">
        <div class="left-content">
          <h2>超期提醒设置</h2>
          <div class="checkbox-group">
            <span>开关状态：</span>
            <span :class="overdueReminderInfo.overdueReminderStatus == 1 ? 'span-active' : overdueReminderInfo.overdueReminderStatus === '0' ? 'span-close' : ''">
              {{ overdueReminderInfo.overdueReminderStatus | switchStatusFilters }}
            </span>
          </div>
          <div class="checkbox-group">
            <span>超期后提醒，短信发送频率：每天</span>
            <span class="span-style">{{
              overdueReminderInfo.overdueSmsSendTime
                ? `${overdueReminderInfo.overdueSmsSendTime > 9 ? overdueReminderInfo.overdueSmsSendTime : '0' + overdueReminderInfo.overdueSmsSendTime}:00`
                : ''
            }}</span>
            <span>发送</span>
          </div>
          <div class="checkbox-group">
            <span>短信发送至租户本人手机号，并发送短信给</span>
            <span class="span-style">{{ personnelOptionsFilter(overdueReminderInfo.overdueMembers) }}</span>
            <span>和</span>
            <span class="span-style">
              {{ overdueReminderInfo.overdueSmsReceivers || '' }}
            </span>
          </div>
        </div>
        <div class="right-content">
          <el-button type="primary" @click="configureSection('overdue')">配置</el-button>
        </div>
      </div>
    </el-card>
    <!-- 配置弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="28%" :before-close="handleCloseDialog" :close-on-click-modal="false">
      <div class="content-dialog">
        <div class="status-div">
          <span>开关状态</span>
          <el-switch v-model="switchStatus" active-color="#13ce66"> </el-switch>
        </div>
        <div v-if="saveType == 'soonOverdue'" class="status-div">
          <div>
            <span>距离预计退租日期前</span>
            <el-input v-model="dayNum" style="width: 100px" placeholder="请输入"></el-input>
            <span>天进行提醒，短信发送频率：</span>
          </div>
          <div style="margin-bottom: 10px">
            <!-- <el-select v-model="timeNodes" style="width: 150px" clearable placeholder="请选择">
              <el-option v-for="item in daysOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select> -->
            <span>每天</span>
            <el-time-select v-model="time" style="width: 120px; margin: 0px 5px" clearable :picker-options="pickerOptions" placeholder="选择时间"> </el-time-select>
            <span>发送</span>
          </div>
        </div>
        <div v-if="saveType == 'overdue'" class="status-div">
          <div style="margin-bottom: 10px">
            <span>超期后提醒，短信发送频率：</span>
            <!-- <el-select v-model="timeNodes" style="width: 150px" clearable placeholder="请选择">
              <el-option v-for="item in daysOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select> -->
            <span>每天</span>
            <el-time-select v-model="time" style="width: 120px; margin: 0px 5px" clearable :picker-options="pickerOptions" placeholder="选择时间"> </el-time-select>
            <span>发送</span>
          </div>
        </div>
        <div class="status-div">
          <div>
            <span>短信发送至租户本人手机号，并发送短信给</span>
          </div>
          <div>
            <el-select v-model="personnel" style="width: 100%" clearable filterable multiple placeholder="请选择成员，可多选" @change="handleSelectPersonnel">
              <el-option v-for="item in personnelOptions" :key="item.id" :label="item.staffName" :value="item.id"> </el-option>
            </el-select>
          </div>
          <div style="margin-bottom: 10px"><span>和</span></div>
          <div>
            <el-input v-model="phoneArr" class="phone-input" clearable placeholder="可填写多个手机号，用','隔开"></el-input>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="handleCloseDialog">取消</el-button>
        <el-button :loading="saveBtnLoding" :disabled="saveBtnLoding" type="primary" @click="saveDialog">确认</el-button>
      </span>
    </el-dialog>
  </el-container>
</template>
<script>
export default {
  filters: {
    switchStatusFilters(val) {
      if (val === '' || val === null) return ''
      return val == '1' ? '开启' : '关闭'
    }
  },
  data() {
    return {
      dialogTitle: '即将超期提醒设置',
      pageLoading: false,
      dialogVisible: false,
      saveBtnLoding: false,
      switchStatus: false,
      saveType: 'soonOverdue',
      pickerOptions: {
        start: '00:00',
        end: '23:59',
        step: '01:00'
      },
      daysOptions: [],
      soonOverdueReminderInfo: {},
      overdueReminderInfo: {},
      personnelOptions: [],
      timeNodes: '1',
      dayNum: '',
      time: '',
      personnel: [],
      phoneArr: '',
      id: ''
    }
  },
  mounted() {
    this.getLersonnelList()
    this.getReminderSettings()
  },
  methods: {
    personnelOptionsFilter(val) {
      if (!val) return ''
      let ids = val.split(',')
      let name = ''
      ids.forEach((e, index) => {
        this.personnelOptions.forEach((i) => {
          if (i.id == e) {
            name += index == ids.length - 1 ? i.staffName : i.staffName + ','
          }
        })
      })
      return name
    },
    configureSection(section) {
      switch (section) {
        case 'soonOverdue':
          this.dialogTitle = '即将超期提醒设置'
          this.switchStatus = this.soonOverdueReminderInfo.upcomingReminderStatus == 1
          this.dayNum = this.soonOverdueReminderInfo.upcomingDaysBeforeRentEnd || ''
          this.time = this.soonOverdueReminderInfo.upcomingSmsSendTime ? `${this.soonOverdueReminderInfo.upcomingSmsSendTime}:00` : ''
          this.personnel = this.soonOverdueReminderInfo.upcomingMembers ? this.soonOverdueReminderInfo.upcomingMembers.split(',') : ''
          this.phoneArr = this.soonOverdueReminderInfo.upcomingSmsReceivers || ''
          break
        case 'overdue':
          this.switchStatus = this.overdueReminderInfo.overdueReminderStatus == 1
          this.dayNum = ''
          this.time = this.overdueReminderInfo.overdueSmsSendTime ? `${this.overdueReminderInfo.overdueSmsSendTime}:00` : ''
          this.personnel = this.overdueReminderInfo.overdueMembers ? this.overdueReminderInfo.overdueMembers.split(',') : ''
          this.phoneArr = this.overdueReminderInfo.overdueSmsReceivers || ''
          this.dialogTitle = '超期提醒设置'
          break
      }
      this.saveType = section
      this.dialogVisible = true
    },
    saveDialog() {
      const userInfo = this.$store.state.user.userInfo.user
      let params = {
        userId: userInfo.id,
        userName: userInfo.staffName,
        id: this.id
      }
      if (this.saveType == 'overdue') {
        params.overdueMembers = this.personnel && this.personnel.length ? this.personnel.join(',') : ''
        params.overdueReminderStatus = this.switchStatus ? '1' : '0'
        params.overdueSmsReceivers = this.phoneArr
        params.overdueSmsSendFrequency = '1'
        params.overdueSmsSendTime = this.time ? this.time.split(':')[0] : ''
      }
      if (this.saveType == 'soonOverdue') {
        params.upcomingDaysBeforeRentEnd = this.dayNum ? Number(this.dayNum) : ''
        params.upcomingMembers = this.personnel && this.personnel.length ? this.personnel.join(',') : ''
        params.upcomingReminderStatus = this.switchStatus ? '1' : '0'
        params.upcomingSmsReceivers = this.phoneArr
        params.upcomingSmsSendFrequency = '1'
        params.upcomingSmsSendTime = this.time ? this.time.split(':')[0] : ''
      }
      if (params.overdueSmsSendTime) {
        params.overdueSmsSendTime = Number(params.overdueSmsSendTime) > 9 ? params.overdueSmsSendTime : `${Number(params.overdueSmsSendTime)}`
      }
      if (params.upcomingSmsSendTime) {
        params.upcomingSmsSendTime = Number(params.upcomingSmsSendTime) > 9 ? params.upcomingSmsSendTime : `${Number(params.upcomingSmsSendTime)}`
      }
      this.saveBtnLoding = true
      this.$api.rentalHousingApi.saveOrUpdateReminderSettings(params).then((res) => {
        this.saveBtnLoding = false
        if (res.code == 200) {
          this.$message.success(res.message)
          this.handleCloseDialog()
          this.getReminderSettings()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleCloseDialog() {
      this.timeNodes = ''
      this.dayNum = ''
      this.time = ''
      this.personnel = []
      this.phoneArr = ''
      this.saveBtnLoding = false
      this.dialogVisible = false
    },
    /** 获取提醒设置详情 */
    getReminderSettings() {
      let params = {}
      this.pageLoading = true
      this.$api.rentalHousingApi.getReminderSettingsDetails(params).then((res) => {
        this.pageLoading = false
        if (res.code == 200) {
          let {
            overdueMembers,
            overdueReminderStatus,
            overdueSmsReceivers,
            overdueSmsSendFrequency,
            overdueSmsSendTime,
            upcomingDaysBeforeRentEnd,
            upcomingMembers,
            upcomingReminderStatus,
            upcomingSmsReceivers,
            upcomingSmsSendFrequency,
            upcomingSmsSendTime
          } = res.data
          this.soonOverdueReminderInfo = {
            upcomingDaysBeforeRentEnd,
            upcomingMembers,
            upcomingReminderStatus,
            upcomingSmsReceivers,
            upcomingSmsSendFrequency,
            upcomingSmsSendTime
          }
          this.overdueReminderInfo = {
            overdueMembers,
            overdueReminderStatus,
            overdueSmsReceivers,
            overdueSmsSendFrequency,
            overdueSmsSendTime
          }
          this.id = res.data.id || ''
          this.$forceUpdate()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    /** 人员选择事件 */
    handleSelectPersonnel(val) {},
    // 获取人员列表
    getLersonnelList() {
      let params = {
        current: 1,
        size: 9999,
        sex: '',
        pmId: '',
        postId: '', // 岗位
        stationStatus: '',
        officeId: ''
      }
      this.$api.staffList(params).then((res) => {
        if (res.code == 200) {
          this.personnelOptions = res.data.records
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.config-container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.config-section {
  padding: 20px;
  border: 1px solid #e4e7ed; /* 设置黑色边框 */
  border-radius: 5px;
}
.content {
  display: flex;
  align-items: flex-start; /* 垂直对齐到顶部 */
  justify-content: space-between; /* 内容两端对齐 */
}
.left-content {
  flex: 1; /* 左侧占用较大空间 */
  display: flex;
  flex-direction: column; /* 纵向排列标题和内容 */
}
.right-content {
  display: flex;
  justify-content: flex-end; /* 按钮右对齐 */
}
.checkbox-group {
  display: flex;
  align-items: center;
  margin-bottom: 10px; /* 控制多选框之间的间距 */
  flex-wrap: wrap;
  .span-style {
    height: 22px;
    color: #ff9435;
    padding: 0px 10px;
    display: inline-block;
  }
  .span-active {
    color: #00b42a;
    background: #e8ffea;
    border-radius: 4px 4px 4px 4px;
  }
  .span-close {
    color: #96989a;
    background: #e5e6eb;
    border-radius: 4px 4px 4px 4px;
  }
}
.checkbox-group:first-child {
  display: block; /* 独占一行 */
}
/* 新增的样式 */
.config-text {
  color: #409eff; /* 配置按钮的颜色 */
}
.status-div:first-child {
  margin-bottom: 8px;
}
.status-div {
  div:first-child {
    margin-bottom: 8px;
  }
}
.el-switch {
  margin-left: 10px;
}
.el-date-editor {
  width: 150px;
  margin: 0px 5px;
}
.el-input:not(.phone-input, .el-date-editor) {
  width: 100px;
  margin: 0px 10px;
}
</style>
