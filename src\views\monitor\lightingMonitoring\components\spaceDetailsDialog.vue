<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    :title="selectRoom.localSpaceName"
    width="40%"
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="content">
      <div style="background: #fff; padding: 0 0 16px 0">
        <el-button type="primary" @click="allOperation(1)">一键开启</el-button>
        <el-button type="primary" @click="allOperation(0)">一键关闭</el-button>
      </div>
      <div style="height: 400px">
        <TablePage ref="table" v-loading="tableLoading" :showPage="false" :tableColumn="tableColumn" :data="tableData" height="100%" @selection-change="handleSelectionChange" />
      </div>
    </div>
    <!-- <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="confirm">确 定</el-button>
    </span> -->
  </el-dialog>
</template>
<script lang="jsx">
export default {
  name: 'spaceDetailsDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    projectCode: {
      type: String,
      default: ''
    },
    selectRoom: {
      type: Object,
      default: () => {
        return { localSpaceName: '空间明细' }
      }
    }
  },
  data() {
    return {
      controlLoading: false,
      tableLoading: false,
      tableData: [],
      tableColumn: [
        {
          type: 'selection',
          align: 'center',
          selectable: (row) => {
            return row.id !== 1
          }
        },
        {
          prop: 'actuatorOutPutName',
          label: '回路名称'
        },
        {
          prop: 'lightingStatus',
          label: '开关状态',
          formatter: (scope) => {
            return scope.row.lightingStatus == 1 ? '开启' : '关闭'
          }
        },
        {
          width: 100,
          prop: 'operation',
          label: '控制',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" style="color: #3562db" onClick={() => this.switchOperation(1, row.row)}>
                  开启
                </span>
                <span class="operationBtn-span" style="color: #3562db" onClick={() => this.switchOperation(0, row.row)}>
                  关闭
                </span>
                {/* <span class="operationBtn-span" style="color: #3562db" onClick={() => this.switchOperation(3, row.row)}>强开</span>
                <span class="operationBtn-span" style="color: #3562db" onClick={() => this.switchOperation(4, row.row)}>强关</span> */}
              </div>
            )
          }
        }
      ]
    }
  },
  mounted() {
    this.getSpaceLightingCircuit()
  },
  methods: {
    getSpaceLightingCircuit() {
      let params = {
        spaceId: this.selectRoom.id,
        // spaceId: '1621407939359162370',
        floorId: this.selectRoom.floorId,
        projectCode: this.projectCode
      }
      this.tableLoading = true
      this.$api.GetSpaceLightingCircuit(params).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.tableData = res.data.lightingBySpace
        }
      })
    },
    allOperation(status) {
      const params = {
        type: 4, // 空间模式控制
        forceSwitch: null,
        outputStatus: status,
        serviceSpaceId: this.selectRoom.id
      }
      this.changeLoopStates(params)
    },
    switchOperation(status, item) {
      const params = {
        type: 3, // 回路模式控制
        forceSwitch: status === 3 || status === 4 ? status : null, // 强开强关为3,4 开启关闭为null
        outputStatus: status === 3 ? 1 : status === 4 ? 0 : status, // 强开为1, 强关为0, 开启为1, 关闭为0
        actuatorId: item.actuatorId,
        outputNum: item.outPutId
        // groupControl: item.surveyCode
      }
      this.changeLoopStates(params)
    },
    changeLoopStates(params) {
      this.$confirm('此操作将改变回路运行状态, 是否继续?', '提示', {
        cancelButtonClass: 'el-button--primary is-plain',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tableLoading = true
        this.$api.lightOpenOrClose(params).then((res) => {
          if (res.code === '200') {
            this.$message.success('操作成功！')
            this.getSpaceLightingCircuit()
          } else {
            this.tableLoading = false
            this.$message.warning(res.message)
          }
        }).catch(() => {
          this.tableLoading = false
        })
      })
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    },
    confirm() {},
    // 多选
    handleSelectionChange(val) {
      this.multipleSelection = val
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  padding: 16px !important;
  background: #fff;
}
.model-dialog {
  padding: 0 !important;
}
::v-deep .el-dialog__body {
  padding: 0 !important;
}
</style>
<style lang="scss">
.operationBtn-span {
  margin-right: 10px;
}
</style>
