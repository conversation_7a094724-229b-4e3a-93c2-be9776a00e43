<template>
  <div class="heard">
    <div class="course_title">培训任务</div>
    <div class="train_content">
      <div class="course_nav" v-for="(item, index) in trainList" :key="index">
        <div class="courer_name">
          <div class="titleImg"><img src="../../../../assets/images/icon_train.png" alt=""></div>
          <div>{{ item.name }}</div>
        </div>
        <div>
          <div class="trainTime">培训时间：{{ item.startTime }} - {{ item.endTime }}</div>
        </div>
        <div>
          <div>培训老师：<span>{{ item.teacherName }}</span></div>
        </div>
        <div>
          <div class="trainTime">培训地点：{{ item.address }}</div>
        </div>
        <div class="operation">
          <i class="el-icon-delete Iconx" @click="deleteTrain(item, index)"></i>
        </div>
      </div>
    </div>
    <div>
      <el-button type="primary" @click="addTrain">添加培训计划</el-button>
    </div>
    <el-drawer :visible.sync="drawer" :with-header="true" size="65%" :show-close="true">
      <div slot="title" class="coursrDrawer">选择培训</div>
      <div class="deawer_conter">
        <div class="deawer_heard">
          <el-input v-model="formInline.name" placeholder="请输入课程名称" style="width: 200px; margin-right:10px;"></el-input>
          <el-cascader ref="myCascader" v-model="formInline.deptId" :options="deptList" :props="deptTree"
            placeholder="请选择所属部门" clearable style="width: 200px; margin-right: 10px"></el-cascader>
          <el-input v-model="formInline.teacherName" placeholder="请输入培训老师"
            style="width: 200px; margin-right:10px;"></el-input>
          <el-date-picker style="width: 200px; margin-right:10px;" v-model="timeLine" type="daterange"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
          </el-date-picker>
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="search">查询</el-button>
        </div>
        <div class="table_conter">
          <el-table ref="multipleTable" :data="tableData" tooltip-effect="dark" style="width: 100%"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" fixed="left" align="center"></el-table-column>
            <el-table-column prop="name" label="培训名称" align="center"></el-table-column>
            <el-table-column prop="deptName" label="所属单位" show-overflow-tooltip align="center"></el-table-column>
            <el-table-column prop="teacherName" label="培训老师" show-overflow-tooltip align="center"></el-table-column>
            <el-table-column label="培训时间" show-overflow-tooltip align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.startTime }} - {{ scope.row.endTime }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="address" label="培训地点" show-overflow-tooltip align="center"></el-table-column>
          </el-table>
        </div>
      </div>
      <div class="derwer_footer">
        <div class="derwer_footerSum"> 已选择 <span class="sumNumber">{{ tableSum }}</span> 个课程</div>
        <div>
          <el-button type="primary" plain @click="cancel">取消</el-button>
          <el-button type="primary" @click="submit">添加</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import moment from "moment";
export default {
  props: {
    trainTaskList: {
      type: Array,
      default: []
    },
    startTime: {
      type: String,
      default: '',
    },
    endTime: {
      type: String,
      default: '',
    }
  },
  data() {
    return {
      formInline: {
        name: '',
        deptId: '',
        teacherName: '',
        startTime: '',
        endTime: ''
      },
      deptTree: {
        children: "children",
        label: "teamName",
        value: "id",
        checkStrictly: true,
        emitPath: false,
      },
      timeLine: [],
      drawer: false,
      tableData: [],
      multipleSelection: [],
      tableSum: '0',
      trainList: [],
      deptList: []
    };
  },
  created() {
    this.getdeptList()
    this.getTrainList()
    this.trainList = this.trainTaskList
  },
  methods: {
    // 获取组织
    getdeptList() {
      this.$api.getDeptListLaboratory({}).then((res) => {
        this.deptList = this.$tools.transData(
          res.data.list,
          "id",
          "parentId",
          "children"
        );
      });
    },
    // 培训列表
    getTrainList(tages = false) {
      let data = {
        current: 1,
        size: 9999,
        startTime: this.startTime,
        endTime: this.endTime,
        ...this.seachForm,
      }
      this.$api.trainPlanNewList(data).then(res => {
        if (res.code == 200) {
          this.tableData = res.data.list
          if (tages) {
            setTimeout(() => {
              this.tableData.forEach((row) => {
                const matchedIndex = this.trainList.findIndex(item => item.id === row.id)
                this.$refs.multipleTable.toggleRowSelection(row, matchedIndex != -1)
              })
            })
          }
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    // 添加培训计划
    addTrain() {
      this.drawer = true
      this.$nextTick(() => {
        this.getTrainList(true)
      })
    },
    handleSelectionChange(val) {
      this.tableSum = val.length
      this.multipleSelection = val;
    },
    // 查看
    detail() { },
    // 删除
    deleteTrain(item, index) {
      this.trainList.splice(index, 1)
    },
    // 取消关闭弹窗
    cancel() {
      this.drawer = false
    },
    // 提交
    submit() {
      this.trainList = this.multipleSelection
      this.$refs.multipleTable.clearSelection()
      this.drawer = false
    },
    // 查询
    search() {
      this.getTrainList()
    },
    // 重置
    resetForm() {
      this.formInline = {
        name: '',
        deptId: '',
        teacherName: '',
        startTime: '',
        endTime: ''
      }
      this.timeLine = []
    },
  },
};
</script>
<style lang="scss" scoped>
.heard {
  height: 100%;
}

.train_content {
  height: calc(100% - 70px);
  overflow: auto;
  margin-top: 16px;
}

.course_nav {
  height: 64px;
  background: #FAF9FC;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  line-height: 64px;
  padding: 0px 15px;
  font-size: 14px;
  margin: 15px 0px;
}

.course_title {
  margin-top: 8px;
  font-weight: 600;
  color: #333333;
  font-size: 16px;
}

.courer_name {
  display: flex;
  width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.trainTime {
  width: 400px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.operation {
  display: flex;
  align-items: center;
  color: #3562DB;
}

.titleImg {
  margin-top: 7px;
  margin-right: 10px;
}

.Iconx {
  margin-left: 20px;
}

.coursrDrawer {
  color: #333333;
  font-weight: 500;
  font-size: 18px;
}

::v-deep .el-drawer__body {
  overflow: hidden;
}

::v-deep .el-drawer__header {
  height: 56px;
  line-height: 56px !important;
  padding-bottom: 20px;
  border-bottom: 1px solid #DCDFE6;
  box-shadow: -4px 0 4px 0 rgba(203, 205, 220, 0.24);
}

.deawer_conter {
  padding: 0px 20px;
}

.deawer_heard {
  width: 100%;
  margin-bottom: 16px;
  // display: flex;
}

.derwer_footer {
  width: 100%;
  height: 56px;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0px 0px 12px 3px rgba(0, 0, 0, 0.1);
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  position: absolute;
  bottom: 0px;
  left: 0px;
  display: flex;
  justify-content: space-between;
  line-height: 56px;
  padding: 0px 20px;
}

.derwer_footerSum {
  font-size: 14px;
  font-weight: 400;
  color: #7F848C;

  .sumNumber {
    font-size: 16px;
    font-weight: 500;
    color: #333333;
  }
}

.table_conter {
  height: 600px;
}
</style>