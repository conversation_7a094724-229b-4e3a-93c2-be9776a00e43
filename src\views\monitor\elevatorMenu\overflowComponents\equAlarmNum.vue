<template>
  <div class="top_count drag_class">
    <div class="vertical_count">
      <img src="@/assets/images/monitor/ic-vertical.png" alt="" />
      <div class="count_num">
        <div class="count_title">直梯数量</div>
        <span>
          <span>{{ elevatorList[0].connectNum }}</span>
          <span> /{{ elevatorList[0].totalNum }} </span>
        </span>
      </div>
    </div>
    <div class="escalator_count">
      <img src="@/assets/images/monitor/ic-escalator.png" alt="" />
      <div class="count_num">
        <div class="count_title">扶梯数量</div>
        <span>
          <span>{{ elevatorList[1].connectNum }}</span>
          <span>/{{ elevatorList[1].totalNum }}</span>
        </span>
      </div>
    </div>
    <div class="warn_count" @click="jumpPage">
      <img src="@/assets/images/elevator/warn-alert.png" alt="" />
      <div class="count_num cursor">
        <div class="count_title">报警数量</div>
        <span>{{ elevatorStatisticsData.alarmCount }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import moment from 'moment'
import { monitorTypeList } from '@/util/dict.js'
export default {
  data() {
    return {
      requestHttp: __PATH.VUE_IEMC_ELEVATOR_API,
      projectCode: monitorTypeList.find((item) => item.projectName == '电梯监测').projectCode, // 电梯code
      elevatorList: [
        {
          type: '1',
          connectNum: 0,
          totalNum: 0
        },
        {
          type: '2',
          connectNum: 0,
          totalNum: 0
        }
      ],
      elevatorStatisticsData: {
        alarmCount: 0
      },
      timer: null
    }
  },
  mounted() {
    this.getElevatorStatistics()
    this.scheduledTasks()
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
  },
  methods: {
    scheduledTasks() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
      this.timer = setInterval(() => {
        this.getElevatorStatistics()
      }, 30000)
    },
    // 传递父组件事件 跳转页面
    jumpPage() {
      this.$emit('childEvent', {
        type: 'routerPush',
        path: '/allAlarm/allAlarmIndex'
      })
    },
    // 获取电梯类型数据 左上角统计 及电梯健康分布数据
    getElevatorStatistics() {
      const params = {
        projectCode: this.projectCode,
        startTime: moment().date(1).format('YYYY-MM-DD'),
        endTime: moment().format('YYYY-MM-DD')
      }
      this.$api.getElevatorStatistics(params, this.requestHttp).then((res) => {
        if (res.code === '200') {
          this.elevatorStatisticsData = res.data
          const { list, recordList, ...othersData } = res.data
          // 直梯扶梯数量渲染
          this.elevatorList.map((e) => {
            const filterData = list.find((item) => item.elevatorType === e.type)
            if (filterData) {
              e.connectNum = filterData.connectNum
              e.totalNum = filterData.totalNum
            }
          })
          // 其他统计项渲染
          this.elevatorStatisticsData = othersData
          // 电梯健康分布渲染
          // this.healthLevelData.map((e) => {
          //   const filterData = recordList?.find((item) => item.record === e.levelType) ?? {}
          //   e.levelNum = filterData?.num ?? 0
          // })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.top_count {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  width: 100%;
  padding: 10px;
  .vertical_count,
  .escalator_count {
    background: url('~@/assets/images/elevator/elevator-type-bg.png') no-repeat;
    background-size: 100% 100%;
  }
  .vertical_count,
  .escalator_count,
  .warn_count {
    width: calc(32% - 15px);
    height: 70%;
    // border-radius: 10px;
    display: flex;
    padding: 0 20px;
    align-items: center;
    img {
      width: 30px;
      height: 30px;
    }
    .cursor {
      cursor: pointer;
    }
    .count_num {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-left: 10px;
      .count_title {
        font-size: 14px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        font-weight: 500;
        color: #fff;
      }
      span {
        font-size: 20px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        font-weight: 500;
        color: #fff;
        span {
          color: #ffd661;
        }
      }
    }
  }
}
</style>
