<!-- 报警分析 -->
<template>
  <ContentCard :title="item.componentTitle + '（单）'" :scrollbarHover="true" :hasIcon="false" :cstyle="{ height: '100%' }" class="drag_class"  :hasMoreOper="['more', 'edit']" @more-oper-event="(val) => $emit('all-more-Oper', val, 'alarmAnalysis')">
    <div slot="content" class="alarm-main">
      <div class="main-left">
        <div v-for="item in alarmStatisticsList" :key="item.title" class="left-item" :style="{width: item.width, marginBottom: item.marginBottom, marginRight: item.marginRight}">
          <p class="item-title">{{ item.title }}</p>
          <p class="item-value">{{ item.value || 0 }}<span>个</span></p>
          <img class="item-icon" :src="item.icon" :alt="item.title" />
        </div>
      </div>
      <div class="main-right">
        <echarts ref="alarmChart" domId="alarmChart" />
      </div>
    </div>
  </ContentCard>
</template>

<script>
import alarmStatistics from '@/assets/images/monitor/alarm-statistics.png'
import alarmAlarm from '@/assets/images/monitor/alarm-pending.png'
import alarmDoing from '@/assets/images/monitor/alarm-doing.png'
import moment from 'moment'
export default {
  name: 'alarmAnalysis',
  props: {
    item: {
      type: Object,
      default: () => {}
    },
    projectCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      alarmStatisticsList: [
        {
          title: '报警统计',
          icon: alarmStatistics,
          width: '100%',
          value: 0,
          marginBottom: '10px',
          marginRight: '0px'
        },
        {
          title: '未处理',
          icon: alarmAlarm,
          width: 'calc(50% - 5px)',
          value: 0,
          marginBottom: '0px',
          marginRight: '10px'
        },
        {
          title: '处理中',
          icon: alarmDoing,
          width: 'calc(50% - 5px)',
          value: 0,
          marginBottom: '0px',
          marginRight: '0px'
        }
      ]
    }
  },
  computed: {

  },
  watch: {
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          this.echartsResize()
        })
      },
      deep: true
    },
    // dragSidebarCollapse
    '$store.state.settings.dragSidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          this.echartsResize()
        })
      },
      deep: true
    },
    projectCode: {
      handler(val, oldVal) {
        this.getAlarmAnalysis()
      },
      deep: true
    }
  },
  mounted() {
    this.getAlarmAnalysis()
  },
  methods: {
    // 获取报警分析数据
    getAlarmAnalysis() {
      let data = {
        // startTime: moment().startOf('month').format('YYYY-MM-DD'),
        // endTime: moment().endOf('month').format('YYYY-MM-DD'),
        projectCode: this.projectCode
      }
      this.$api.GetAirRunPolice(data).then((res) => {
        if (res.code == 200) {
          this.alarmStatisticsList[0].value = res.data?.total
          this.alarmStatisticsList[1].value = res.data?.noDealCount
          this.alarmStatisticsList[2].value = res.data?.isDealCount
          this.$nextTick(() => {
            this.$refs.alarmChart.init(this.alarmChartData(res.data ? res.data.policeList : []))
            this.echartsResize()
          })
        }
      })
    },
    echartsResize() {
      setTimeout(() => {
        if (this.$refs.alarmChart) {
          this.$refs.alarmChart.chartResize()
        }
      }, 250)
    },
    alarmChartData(arr) {
      const nameList = Array.from(arr, (item) => item.menuName)
      let total = arr.reduce((p, v) => {
        return p + v.policeCount
      }, 0)
      let option
      if (arr.length) {
        option = {
          tooltip: {
            trigger: 'item'
          },
          title: {
            text: '报警设备类型统计',
            left: 'center',
            textStyle: {
              fontSize: 18,
              fontWeight: 500
            }
          },
          legend: {
            orient: 'vertical',
            type: 'scroll',
            top: '35%',
            right: '10%',
            bottom: 1,
            itemWidth: 15,
            itemHeight: 15,
            // itemGap: 15,
            pageButtonPosition: 'start',
            data: nameList,
            formatter: (name) => {
              let item = arr.find(v => v.menuName == name)
              return `${name}  ${((item.policeCount / total) * 100).toFixed(2)}%  ${item.policeCount}个`
            }
          },
          series: [
            {
              type: 'pie',
              roseType: 'radius',
              radius: ['35%', '50%'],
              center: ['40%', '50%'],
              data: arr.map(item => {
                return {name: item.menuName, value: item.policeCount}
              }),
              label: {
                normal: {
                  show: false
                }
              }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    }
  }
}

</script>

<style lang="scss" scoped>
.alarm-main {
  height: 100%;
  width: 100%;
  display: flex;

  p {
    margin: 0;
  }

  .main-left {
    width: 50%;
    display: flex;
    flex-wrap: wrap;
    align-content: center;

    .left-item {
      height: 140px;
      padding: 24px 24px 30px;
      background: #faf9fc;
      border-radius: 4px;
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .item-title {
        font-size: 15px;
        font-weight: 500;
        color: #121f3e;
      }

      .item-value {
        height: 36px;
        font-size: 40px;
        font-weight: bold;
        color: #121f3e;
        line-height: 36px;

        & > span {
          margin-left: 4px;
          font-size: 15px;
          font-weight: 500;
          color: #ccced3;
        }
      }

      .item-icon {
        position: absolute;
        right: 24px;
        bottom: 24px;
        width: 40px;
        height: 40px;
      }
    }
  }

  .main-right {
    width: 50%;
  }
}
</style>
