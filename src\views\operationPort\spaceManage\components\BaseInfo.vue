<template>
  <div>
    <el-row>
      <el-col :span="8">
        <div class="item">
          <span class="item-title">资产编码</span>
          <span class="item-content">{{ detailsInfo.assetsNumber || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">财务编码</span>
          <span class="item-content">{{ detailsInfo.financeNum || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">所在区域</span>
          <span class="item-content">{{ detailsInfo.areaName || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">管理员</span>
          <span class="item-content">{{ detailsInfo.adminName || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">计量单位</span>
          <span class="item-content">{{ detailsInfo.unit || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">注册证号</span>
          <span class="item-content">{{ detailsInfo.registrationNum || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">启用日期</span>
          <span class="item-content">{{ detailsInfo.buyDate || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">资产来源</span>
          <span class="item-content">{{ detailsInfo.sourceName || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">资产状态</span>
          <span class="item-content">{{ detailsInfo.statusName || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">保修日期</span>
          <span class="item-content">{{ detailsInfo.warrantyContractStartTime&&detailsInfo.warranties?detailsInfo.warranties+'~'+detailsInfo.warrantyContractStartTime: '--' }}</span>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="item">
          <span class="item-title">资产名称</span>
          <span class="item-content">{{ detailsInfo.assetsName || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">规格型号</span>
          <span class="item-content">{{ detailsInfo.model || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">存放位置</span>
          <span class="item-content">{{ detailsInfo.storageLocation || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">合同编码</span>
          <span class="item-content">{{ detailsInfo.contractNum || '--' }}</span>
        </div>
        <!-- <div class="item">
          <span class="item-title">维保到期</span>
          <span class="item-content">{{ detailsInfo.warrantyExpiresDateLine || '--' }}</span>
        </div> -->
        <div class="item">
          <span class="item-title">入库日期</span>
          <span class="item-content">{{ detailsInfo.addTime || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">出库日期</span>
          <span class="item-content">{{ detailsInfo.outTime || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">资产用途</span>
          <span class="item-content">{{ detailsInfo.assrtsUseName || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">使用状态</span>
          <span class="item-content">{{ detailsInfo.useStatusName || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">采购合同</span>
          <span class="item-content">{{ detailsInfo.purchaseContractName || '--' }}</span>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="item">
          <span class="item-title">68编码</span>
          <span class="item-content">{{ detailsInfo.num68 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">出厂编码</span>
          <span class="item-content">{{ detailsInfo.snNumber || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">使用科室</span>
          <span class="item-content">{{ detailsInfo.useDepartmentName || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">发票号</span>
          <span class="item-content">{{ detailsInfo.invoiceNum || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">安装日期</span>
          <span class="item-content">{{ detailsInfo.installTime || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">验收日期</span>
          <span class="item-content">{{ detailsInfo.examineTime || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">成本核算</span>
          <span class="item-content">{{ detailsInfo.costAccountingName || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">是否进口</span>
          <span class="item-content">{{ detailsInfo.isImportName || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">保修合同</span>
          <span class="item-content">{{ detailsInfo.warrantyContractName || '--' }}</span>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
export default {
  name: 'BaseInfo',
  props: ['detailsInfo'],
  data() {
    return {}
  },
  mounted() {
    console.log(this.detailsInfo)
  },
  methods: {}
}
</script>

<style scoped lang="scss">
  .item {
    margin-bottom: 12px;
    font-size: 14px;
    display: flex;
    // height: 20px;
    // line-height: 20px;
    .item-title {
      color: #909399;
      min-width: 110px;
    }
    .item-content {
      color: #121f3e;
    }
  }
</style>
