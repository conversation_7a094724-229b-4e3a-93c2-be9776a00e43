<template>
  <div>
    <div class="content_item">
      <div class="typeTabs">
        <div class="query_row">
          <div class="query_left">
            <div class="control-btn-header">
              <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 'day' }" plain
                @click="timeTypeChange('day')">今日</el-button>
              <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 'week' }" plain
                @click="timeTypeChange('week')">本周</el-button>
              <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 'month' }" plain
                @click="timeTypeChange('month')">本月</el-button>
              <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 'year' }" plain
                @click="timeTypeChange('year')">本年</el-button>
              <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 'custom' }" plain
                style="margin-right: 10px" @click="timeTypeChange('custom')">自定义</el-button>
              <el-date-picker v-model="requestInfo.dataRange" type="daterange" unlink-panels
                :disabled="requestInfo.timeType != 'custom'" range-separator="至" start-placeholder="开始日期"
                end-placeholder="结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd" clearable @change="timeListData" />
              <el-select v-model="selectDeviceFactoryCode" filterable placeholder="请选择设备" @change="selectChange"
                style="width: 180px;">
                <el-option v-for="(item, index) in bindingIotAssetsList" :key="index" :label="item.assetsName"
                  :value="item.factoryCode"> </el-option>
              </el-select>
              <el-select v-model="selectAssetsProperties" filterable placeholder="请选择参数"
                :clearable="activeTab === 1 ? true : false" style="width: 180px;">
                <el-option v-for="(item, index) in bindingIotAssetsPropertiesList" :key="index"
                  :label="item.metadataName" :value="item.metadataTag"> </el-option>
              </el-select>
              <div style="display: inline-block">
                <el-button type="primary" plain @click="resetForm">重置</el-button>
                <el-button type="primary" @click="searchForm">查询</el-button>
              </div>
            </div>
          </div>
          <div class="query_right">
            <div style="margin-right: 20px" v-if="activeTab === 1">
              <el-button type="primary" @click="exportExcel">导出</el-button>
            </div>
            <div class="pattern-item" @click="handleClick(0)"
              :style="{ background: activeTab === 0 ? '#3562DB' : '#F6F5FA' }">
              <span :style="{ color: activeTab === 0 ? '#fff' : '#666' }">图形</span>
            </div>
            <div class="pattern-item" @click="handleClick(1)"
              :style="{ background: activeTab === 1 ? '#3562DB' : '#F6F5FA' }">
              <span :style="{ color: activeTab === 1 ? '#fff' : '#666' }">列表</span>
            </div>
          </div>
        </div>
        <div v-if="activeTab === 1" class="my_tabel">
          <TablePage ref="table" v-loading="tableLoading" :showPage="true" :tableColumn="tableColumn" :data="tableData"
            height="440" :pageData="pageData" @pagination="paginationChange" />
        </div>
        <div v-else-if="activeTab === 0" class="my_chart">
          <ContentCard :title="selectAssetsPropertiesObj?.metadataName" :style="{ height: '100%' }">
            <div slot="content" style="width: 100%; height: 100%; display: flex">
              <echarts v-if="hasChart" ref="alarmAnalysisTrend" domId="alarmAnalysisTrend" width="100%" height="100%" />
              <div v-if="!hasChart"
                style="width: 100%; height: 100%; display: flex; flex-direction: column;  align-items: center; justify-content: center; padding: 50px;">
                <img src="@/assets/images/newMonitor/no-chat.png" />
                <span style="color: #909399;">暂无数据</span>
              </div>
            </div>
          </ContentCard>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import moment from 'moment'
import { monitorTypeList } from '@/util/dict.js'
import * as echarts from 'echarts'

import no_chat from '@/assets/images/newMonitor/no-chat.png'
moment.locale('zh-cn')
export default {
  name: 'monitorDetails',
  props: {
    historicalData: {
      type: Object,
      default() {
        return {}
      }
    },
    detailsData: {
      type: Object,
      default() {
        return {}
      }
    },
  },
  data() {
    return {
      selectDeviceFactoryCode: '',
      selectAssetsProperties: '',
      selectAssetsPropertiesObj: undefined,
      loading: true,
      requestInfo: {
        projectCode: '',
        dataRange: [moment().format('YYYY-MM-DD 00:00:00'), moment().format('YYYY-MM-DD 23:59:59')], // 时间范围
        timeType: 'day' // 1: 当天 2: 本月 3: 本年 4: 自定义
      },
      hasChart: false,
      activeTab: 1, // 图形列表切换   0 图形  1 列表
      tableColumn: [
        {
          prop: '',
          label: '序号',
          formatter: (scope) => {
            return (this.pageData.page - 1) * this.pageData.pageSize + scope.$index + 1
          },
          width: 60
        },
        {
          prop: 'timestamp',
          label: '日期时间',
          formatter: (row) => {
            return moment(row.row.timestamp).format('YYYY-MM-DD HH:mm:ss')
          },
          width: 200
        },
        {
          prop: 'assetsName',
          label: '设备名称',
          width: 200
        },
        {
          prop: 'propertiesText',
          label: '监测参数'
        }
      ],
      tableData: [],
      pageData: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      tableLoading: false,
      bindingIotAssetsList: [],
      bindingIotAssetsPropertiesList: [],
      filter: false,
    }
  },
  computed: {},
  watch: {
    selectAssetsProperties: {
      handler(val) {
        if (val) {
          this.selectAssetsPropertiesObj = this.bindingIotAssetsPropertiesList.find(item => {
            return item.metadataTag === val
          })
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.historicalData) {
        this.selectDeviceFactoryCode = this.historicalData.factoryCode
        this.selectAssetsProperties = this.historicalData.metadataTag
        this.selectAssetsPropertiesObj = this.historicalData
        this.getCustomIotAssets(this.$route.query.id)
      } else {
        this.selectAssetsProperties = ''
        this.getCustomIotAssets(this.$route.query.id)
      }
      this.requestInfo.projectCode = this.$route.query.projectCode
      moment.updateLocale('en', {
        week: {
          dow: 1 // 设置一周的第一天为周一
        }
      });
    });

  },
  methods: {
    selectChange(val) {
      this.selectAssetsProperties = ''
      this.getCustomIotAssetsProperties(this.$route.query.id, val, this.filter)
    },
    // Tab切换
    handleClick(type) {
      if (type === 1) {
        this.filter = false
      } else {
        // this.getPropertiesHistoryData()
        this.filter = true
      }
      this.selectAssetsProperties = ''
      this.activeTab = type
      this.getCustomIotAssets(this.$route.query.id)
    },
    // 获取被监测设备绑定列表
    getCustomIotAssets(id) {
      this.$api.getCustomIotAssets(id).then((res) => {
        if (res.code === '200') {
          this.bindingIotAssetsList = res.data
          this.selectDeviceFactoryCode = this.bindingIotAssetsList[0].factoryCode
          this.getApplicationList(this.selectDeviceFactoryCode, this.selectAssetsProperties)
          this.getCustomIotAssetsProperties(this.$route.query.id, this.selectDeviceFactoryCode, this.filter)
        }
      })
    },
    getCustomIotAssetsProperties(id, code, filter) {
      this.$api.getCustomIotAssetsProperties(id, code, filter).then((res) => {
        if (res.code === '200') {
          this.bindingIotAssetsPropertiesList = res.data
          if (this.bindingIotAssetsPropertiesList.length) {
            if (Object.keys(this.historicalData).length === 0 && this.activeTab === 0) {
              this.selectAssetsProperties = this.bindingIotAssetsPropertiesList[0].metadataTag
            }
          } else {
            this.selectAssetsProperties = ''
          }
          this.selectAssetsPropertiesObj = this.bindingIotAssetsPropertiesList.find(item => {
            return item.metadataTag === this.selectAssetsProperties
          })
          if (this.activeTab === 0) {
            this.getPropertiesHistoryData()
          }

        }
      })
    },
    timeListData(val) {
      this.requestInfo.dataRange[0] = val[0] + ' 00:00:00'
      this.requestInfo.dataRange[1] = val[1] + ' 23:59:59'
    },
    // 获取历史数据列表
    getApplicationList(id, code) {
      let param = {
        pageSize: this.pageData.pageSize,
        pageIndex: this.pageData.page - 1,
        terms: [
          {
            column: 'timestamp$BTW',
            value: [moment(this.requestInfo.dataRange[0]).valueOf(), moment(this.requestInfo.dataRange[1]).valueOf()]
          }
        ]
      }
      this.tableLoading = true
      if (code === undefined) {
        code = ''
      }
      this.$api
        .getQueryPropertiesRow(id, code, '', param)
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.data
            this.pageData.total = res.data.total
          }
          this.tableLoading = false
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getApplicationList(this.selectDeviceFactoryCode, this.selectAssetsProperties)
    },
    // 导出
    exportExcel() {
      let param = {
        pageSize: this.pageData.pageSize,
        pageIndex: this.pageData.page - 1,
        terms: [
          {
            column: 'timestamp$BTW',
            value: [moment(this.requestInfo.dataRange[0]).valueOf(), moment(this.requestInfo.dataRange[1]).valueOf()]
          }
        ]
      }
      if (this.selectAssetsProperties === undefined) {
        this.selectAssetsProperties = ''
      }
      this.$api.getCustomIotExport(this.selectDeviceFactoryCode, this.selectAssetsProperties, this.detailsData.assetsName, param).then((res) => {
        let url = window.URL.createObjectURL(new Blob([res]), { type: 'application/vnd.ms-excel;charset=UTF-8' })
        let link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', '历史数据.xlsx')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link) // 下载完成移除元素
        window.URL.revokeObjectURL(url) // 释放掉blob对象
      })
    },
    // 日期选择
    timeTypeChange(type) {
      let newObj = {
        'day': [moment().format('YYYY-MM-DD 00:00:00'), moment().format('YYYY-MM-DD 23:59:59')],
        'week': [moment().startOf('week').format('YYYY-MM-DD 00:00:00'), moment().endOf('week').format('YYYY-MM-DD 23:59:59')],
        'month': [moment().startOf('month').format('YYYY-MM-DD 00:00:00'), moment().endOf('month').format('YYYY-MM-DD 23:59:59')],
        'year': [moment().startOf('year').format('YYYY-MM-DD 00:00:00'), moment().endOf('year').format('YYYY-MM-DD 23:59:59')],
        'custom': []
      }
      this.requestInfo.dataRange = newObj[type]
      this.requestInfo.timeType = type
      this.pageData.pageSize = 15
      this.pageData.page = 1
    },
    // 重置查询表单
    resetForm() {
      this.requestInfo = {
        dataRange: [moment().format('YYYY-MM-DD 00:00:00'), moment().format('YYYY-MM-DD 23:59:59')],
        timeType: 'day'
      }
      if (this.activeTab === 1) {
        this.selectAssetsProperties = ''
      }
      this.selectAssetsPropertiesObj = undefined
      this.searchForm()
    },
    getPropertiesHistoryData() {
      setTimeout(() => {
        const payload = {
          monitorDeviceId: this.$route.query.id,
          deviceId: this.selectAssetsPropertiesObj ? this.selectAssetsPropertiesObj.factoryCode : '',
          endTime: moment(this.requestInfo.dataRange[1]).format('YYYY-MM-DD 23:59:59'),
          metadataTag: this.selectAssetsPropertiesObj ? this.selectAssetsPropertiesObj.metadataTag : '',
          product: this.selectAssetsPropertiesObj ? this.selectAssetsPropertiesObj.product : '',
          startTime: moment(this.requestInfo.dataRange[0]).format('YYYY-MM-DD 00:00:00'),
          dateType: this.requestInfo.timeType
        }
        this.$api.getPropertiesHistoryData(payload).then((res) => {
          if (res.code === 200) {
            this.hasChart = true
            setTimeout(() => {
              this.appendChartData(res.result)
            }, 30)
          } else {
            this.hasChart = false
          }
        })
      }, 30)
    },
    // 图表分页
    appendChartData(data) {
      data.grid = {
        left: '0%',
        right: '0%',
        bottom: '1%',
        top: '12%',
        containLabel: true
      }
      data.dataZoom = [
        {
          type: 'slider',
          start: 0,
          end: 100,
          xAxisIndex: [0],
          height: 8,
        },
        {
          type: 'slider',
          start: 0,
          end: 100,
          xAxisIndex: [0],
          height: 8,
        }
      ]
      if (data && data.xAxis.data.length > 0) {
        data.xAxis.data.length > 10 ? data.dataZoom[0].end = 30 : data.dataZoom[0].end = 100
      }
      data.xAxis.axisLabel = {
        formatter: function (value) {
          const dateTime = value.split(" ");
          if (dateTime[1] !== undefined) {
            return dateTime[0] + '\n' + dateTime[1];
          } else {
            return value;
          }
          // Date on top, time on bottom
        },
        margin: 7, // 设置为 0 以使标签更靠近轴
        padding: [5, 0, 0, 6], // 设置为 0 以消除额外的内边距
      }
      this.$refs.alarmAnalysisTrend.init(data)
    },
    // 查询
    searchForm() {
      if (!this.requestInfo.dataRange.length) {
        this.$message.warning('请选择时间段！')
        return
      }
      if (this.activeTab === 1) {
        this.getApplicationList(this.selectDeviceFactoryCode, this.selectAssetsProperties)
      } else if (this.activeTab === 0) {
        this.getPropertiesHistoryData()
      }
    }
  }
}
</script>
<style ang="scss" scoped>
.content_item {
  width: 100%;
  height: 100%;

  .control-btn-header {
    padding: 0;

    &>div {
      margin-right: 10px;
      margin-top: 10px;
    }

    .btn-item {
      border: 1px solid #3562db;
      color: #3562db;
      font-family: none;
    }

    .btn-active {
      color: #fff;
      background: #3562db;
    }
  }

  .typeTabs {
    height: 100%;
  }

  .query_row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 16px 0;
    padding: 0 16px;

    .query_left {
      display: flex;
      align-items: center;

      .up_down {
        display: flex;
        margin-right: 6px;

        .jump_date {
          cursor: pointer;
          margin-left: 16px;
          font-size: 14px;
          color: #3562db;
        }

        .no_clicl {
          cursor: not-allowed;
          opacity: 0.5;
          pointer-events: none;
          color: #414653;
        }
      }

      .re_btn {
        margin-left: 10px;
      }
    }

    .query_right {
      display: flex;
      margin-top: 9px;

      .pattern-item {
        cursor: pointer;
        font-size: 15px;
        padding: 5px 15px;
        background: #f6f5fa;
        width: 60px;

        .pattern-icon {
          font-size: 16px;
          margin-right: 6px;
        }
      }
    }
  }

  .btn_row {
    padding: 0 16px;
    background: #fff;
    border-radius: 4px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .harmonicOrder {
      display: flex;
      align-items: center;

      span {
        font-size: 14px;
      }
    }
  }

  .choice_row {
    margin: 16px 0;
    padding: 0 16px;
  }

  .my_tabel {
    padding: 0 16px;
    height: calc(100% - 230px);
  }

  .my_chart {
    padding: 0 16px;
    height: 390px;
  }
}
</style>
