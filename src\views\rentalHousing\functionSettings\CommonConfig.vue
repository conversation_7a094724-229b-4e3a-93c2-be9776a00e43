<template>
  <el-container class="config-container" v-loading="pageLoading">
    <!-- 房源展示配置 -->
    <el-card shadow="hover" class="config-section">
      <div class="content">
        <div class="left-content">
          <h2>房源展示配置</h2>
          <div class="checkbox-group">
            <el-checkbox v-model="showRoomStatus" disabled>展示房源状态</el-checkbox>
          </div>
          <div class="checkbox-group">
            <el-checkbox-group v-model="roomConfig">
          <el-checkbox v-for="(item, index) in housingResourcesConfig" disabled :label="item.val" :key="index">{{ item.text }}</el-checkbox>
        </el-checkbox-group>
          </div>
        </div>
        <div class="right-content">
          <el-button type="primary" @click="configureSection('roomDisplay')">配置</el-button>
        </div>
      </div>
    </el-card>

    <!-- 租金构成配置 -->
    <el-card shadow="hover" class="config-section">
      <div class="content">
        <div class="left-content">
          <h2>租金构成配置</h2>
          <p class="config-text">{{ rentConfigDescription }}</p>
          <!-- 确保这两行是分开的 -->
        </div>
        <div class="right-content">
          <el-button type="primary" @click="configureSection('rentConfig')" style="display: none">配置</el-button>
        </div>
      </div>
    </el-card>

    <!-- 账单规则 -->
    <el-card shadow="hover" class="config-section">
      <div class="content">
        <div class="left-content">
          <h2>账单规则</h2>
          <p class="config-text">{{ billingRule }}</p>
        </div>
        <div class="button-right">
          <el-button type="primary" @click="configureSection('billingRules')" style="display: none">配置</el-button>
        </div>
      </div>
    </el-card>
    <!-- 房源展示配置弹窗 -->
    <el-dialog title="房源展示配置" :visible.sync="housingResourcesDialog" width="20%" :before-close="handleCloseDialog" :close-on-click-modal="false">
      <div class="content-dialog">
        <el-checkbox v-model="checkAll" @change="handleCheckAllChange">展示房源状态</el-checkbox>
        <div style="margin: 15px 0"></div>
        <el-checkbox-group v-model="checkedHousingResourcesConfig">
          <el-checkbox v-for="(item, index) in housingResourcesConfig" :label="item.val" :key="index" :disabled="!checkAll">{{ item.text }}</el-checkbox>
        </el-checkbox-group>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="handleCloseDialog">取消</el-button>
        <el-button :loading="saveBtnLoding" :disabled="saveBtnLoding" type="primary" @click="saveDialog">确认</el-button>
      </span>
    </el-dialog>
  </el-container>
</template>

<script>
export default {
  data() {
    return {
      pageLoading: false,
      saveBtnLoding: false,
      housingResourcesDialog: false,
      showRoomStatus: false,
      roomConfig: [],
      checkAll: false,
      rentConfigDescription: '租金单价*房屋平米',
      billingRule: '按月生成，单月单账单',
      housingResourcesConfig: [{text: '闲置中房源',val: '0'}, {text: '使用中房源',val: '1'}],
      checkedHousingResourcesConfig: [],
      id: '',
    }
  },
  mounted() {
    this.getCommonSettingDetails()
  },
  methods: {
    configureSection(section) {
      switch (section) {
        case 'roomDisplay':
          this.housingResourcesDialog = true
          this.checkAll = this.showRoomStatus
          this.checkedHousingResourcesConfig = this.roomConfig
          break
        case 'rentConfig':
          this.$message.warning('功能开发中!')
          break
        case 'billingRules':
          this.$message.warning('功能开发中!')
          break
      }
    },
    /** 房源展示配置提交 */
    saveDialog(){
      let params = {
        id: this.id,
        showHouseStatus: this.checkAll ? 1 : 0,
        showVacantInuse: this.checkedHousingResourcesConfig && this.checkedHousingResourcesConfig.length ? this.checkedHousingResourcesConfig.join(',') : ''
      }
      this.saveBtnLoding = true
      this.$api.rentalHousingApi.saveOrUpdateCommonSetting(params).then(res=>{
        this.saveBtnLoding = false
        if(res.code == 200){
          this.$message.success(res.message)
          this.handleCloseDialog()
          this.getCommonSettingDetails()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    /** 是否展示房源状态 */
    handleCheckAllChange(val) {
      if(!val){
        this.checkedHousingResourcesConfig = []
      }
    },
    handleCloseDialog() {
      this.saveBtnLoding = false
      this.checkAll = false
      this.checkedHousingResourcesConfig = []
      this.housingResourcesDialog = false
    },
    /** 获取通用设置 */
    getCommonSettingDetails(){
      let params = {}
      this.pageLoading = true
      this.$api.rentalHousingApi.getCommonSetting(params).then(res=>{
        this.pageLoading = false
        if(res.code == 200){
          this.id = res.data.id || ''
          this.showRoomStatus = res.data.showHouseStatus == '1' ? true : false
          this.roomConfig = res.data.showVacantInuse ? res.data.showVacantInuse.split(',') : []
        } else {
          this.$message.error(res.message)
        }
      })
    }
  }
}
</script>

<style scoped>
.config-container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.config-section {
  padding: 20px;
  border: 1px solid #e4e7ed; /* 设置黑色边框 */
  border-radius: 5px;
}

.content {
  display: flex;
  align-items: flex-start; /* 垂直对齐到顶部 */
  justify-content: space-between; /* 内容两端对齐 */
}

.left-content {
  flex: 1; /* 左侧占用较大空间 */
  display: flex;
  flex-direction: column; /* 纵向排列标题和内容 */
}

.right-content {
  display: flex;
  justify-content: flex-end; /* 按钮右对齐 */
}

.checkbox-group {
  display: flex;
  align-items: center;
  margin-bottom: 10px; /* 控制多选框之间的间距 */
}

.checkbox-group:first-child {
  display: block; /* 独占一行 */
}
/* 新增的样式 */
.config-text {
  color: #409eff; /* 配置按钮的颜色 */
}
</style>
