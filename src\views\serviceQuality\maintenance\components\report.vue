<template>
  <!-- 报表统计页 -->
  <div style="height: 100%">
    <div class="dom-content-top">
      <div><img src="../../../../assets/images/report.png" alt="" /><span @click="exportExcel">导出</span></div>
    </div>
    <div class="sino_content">
      <div class="sino_content_top" style="padding-top: 15px">
        <template v-if="reportType == '5'">
          <!-- 工单类型选择 -->
          <el-select
            v-model="workTypeCodeList"
            placeholder="请选择工单类型"
            clearable
            filterable
            style="background: #fff; margin-right: 10px"
            @change="onChangeWork"
            @clear="onClearWork"
          >
            <el-option v-for="item in workTypeArr" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <!-- 服务事项选择 -->
          <el-cascader
            v-model="serviceItemCodeArr"
            :options="processedServiceItems"
            :props="serviceItemProps"
            :show-all-levels="false"
            collapse-tags
            clearable
            size="mini"
            placeholder="请选择服务事项"
            filterable
            style="background: #fff; width: 300px; margin-right: 10px"
            @change="handleCascaderChange"
          ></el-cascader>
        </template>
        <el-button class="sino-button-sure buttonStyle radio-button mb-10" :class="clickType == '1' ? 'activeButton' : ''" @click="radioChange('1')">今日</el-button>
        <el-button class="sino-button-sure buttonStyle radio-button mb-10" :class="clickType == '2' ? 'activeButton' : ''" @click="radioChange('2')">本周</el-button>
        <el-button class="sino-button-sure buttonStyle radio-button mb-10" :class="clickType == '3' ? 'activeButton' : ''" @click="radioChange('3')">本月</el-button>
        <el-button class="sino-button-sure buttonStyle radio-button mb-10" :class="clickType == '4' ? 'activeButton' : ''" @click="radioChange('4')">本年</el-button>
        <el-button class="sino-button-sure buttonStyle radio-button mb-10" :class="clickType == '5' ? 'activeButton' : ''" @click="radioChange('5')">自定义</el-button>
        <el-date-picker
          v-if="params.btnType == '5'"
          v-model="timeLine"
          type="datetimerange"
          range-separator="~"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          picker-options="pickerOptions"
          size="mini"
          style="background: #fff; margin: 0 8px"
          :clearable="false"
          :default-time="['00:00:00', '23:59:59']"
          @change="dateChange"
        >
        </el-date-picker>
        <el-button class="sino-button-sure searchButtonStyle green-button mb-10" @click="searchData">查询</el-button>
      </div>
      <div class="sino_content_right">
        <div class="sino_table">
          <el-table
            v-if="showTable"
            id="out-table"
            v-loading="tableLoading"
            :data="tableData"
            :header-cell-style="{ padding: '8px' }"
            :span-method="reportType == '8' ? objectSpanMethod : () => {}"
            border
            size="mini"
            stripe
            style="width: 80%; margin-left: 10%; border-top: none; border-left: none; border-right: none"
          >
            <el-table-column align="center" :label="reportNmae">
              <el-table-column align="center" :label="timeLine[0] + '—' + timeLine[1]">
                <el-table-column v-if="tableData.length > 0" align="center" label="序号" type="index" width="100"> </el-table-column>
                <el-table-column v-for="(item, index) in targetColumn" :key="index" align="center" :prop="item.prop" :label="item.label">
                  <template slot-scope="scope">
                    <div>
                      <span v-if="reportType == '0' && item.prop == 'repairAmount'" style="color: #5482ee; cursor: pointer" @click="handle0_0(scope.row)">{{
                        scope.row[item.prop]
                      }}</span>
                      <span v-else-if="reportType == '1' && item.prop == 'amount'" style="color: #5482ee; cursor: pointer" @click="handle1_0(scope.row)">{{
                        scope.row[item.prop]
                      }}</span>
                      <span v-else-if="reportType == '4' && item.prop == 'allCount'" style="color: #5482ee; cursor: pointer" @click="handle4_0(scope.row)">{{
                        scope.row[item.prop]
                      }}</span>
                      <span v-else-if="reportType == '4' && item.prop == 'finish'" style="color: #5482ee; cursor: pointer" @click="handle4_1(scope.row)">{{
                        scope.row[item.prop]
                      }}</span>
                      <span v-else-if="reportType == '6' && item.prop == 'deptCount'" style="color: #5482ee; cursor: pointer" @click="handle6_0(scope.row)">{{
                        scope.row[item.prop]
                      }}</span>
                      <span
                        v-else-if="reportType == '6' && index > 0 && index < targetColumn.length - 2"
                        style="color: #5482ee; cursor: pointer"
                        @click="handle6_1(targetColumn[index], scope.row)"
                        >{{ scope.row[item.prop] }}</span
                      >
                      <span v-else-if="reportType == '5' && item.prop == 'amount'" style="color: #5482ee; cursor: pointer" @click="handle5_0(scope.row)">{{
                        scope.row[item.prop]
                      }}</span>
                      <span v-else-if="reportType == '3' && item.prop == 'serviceCount'" style="color: #5482ee; cursor: pointer" @click="handle3_0(scope.row)">{{
                        scope.row[item.prop]
                      }}</span>
                      <span v-else-if="reportType == '7' && item.prop == 'serviceCount'" style="color: #5482ee; cursor: pointer" @click="handle7_0(scope.row)">{{
                        scope.row[item.prop]
                      }}</span>
                      <span v-else-if="reportType == '7' && item.prop == 'finishCount'" style="color: #5482ee; cursor: pointer" @click="handle7_1(scope.row)">{{
                        scope.row[item.prop]
                      }}</span>
                      <span v-else>{{ scope.row[item.prop] }}</span>
                    </div>
                  </template>
                </el-table-column>
              </el-table-column>
            </el-table-column>
          </el-table>
          <p
            v-if="(reportType == '4' && tableData.length > 0) || (reportType == '7' && tableData.length > 0)"
            style="width: 80%; margin: 0 auto; text-align: center; font: bold 14px SimSun"
          >
            {{ chartsName }}
          </p>
          <div
            v-if="(reportType == '4' && tableData.length > 0) || (reportType == '7' && tableData.length > 0)"
            id="charts"
            style="height: 320px; width: 80%; margin: 0 auto"
          ></div>
          <p v-if="reportType == '7' && tableData.length > 0" style="width: 80%; margin: 0 auto; text-align: center; font: bold 14px SimSun">{{ chartsName1 }}</p>
          <div v-if="reportType == '7' && tableData.length > 0" id="charts1" style="height: 320px; width: 80%; margin: 0 auto"></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import * as XLSX2 from 'xlsx'
import * as echarts from 'echarts'
import XLSX from 'xlsx-js-style'
import Axios from 'axios'
import moment from 'moment'
export default {
  name: 'StatisticalReport',
  props: ['reportTypePara'],
  data() {
    return {
      userInfo: {},
      designateDeptCode: [],
      designateDeptList: [],
      dialogVisible: false,
      exportext: 0,
      pickerOptions: {},
      tableLoading: false,
      reportList: [],
      defaultProps: {
        children: 'children',
        label: 'reportName',
        value: 'id'
      },
      timeLine: [],
      treeClickData: '',
      queryType: 'day',
      tableData: [],
      headerArr: [],
      treeNode: [],
      expanded: [],
      checkedData: '',
      workTypeList: [], // 工单类型
      workTypeCodeArr: [], // 工单类型集合
      clickType: '',
      params: {
        startTime: '',
        endTime: ''
        // btnType: ''
      },
      // 当前列类型
      targetColumn: [],
      reportType: '0', // 报表类型
      reportNmae: '科室维修成本报表', // 报表名称
      chartsName: '班组服务数量分布', // echarts名称
      chartsName1: '服务满意度分布',
      deptObj: [], // 服务部门合并（一级）
      typeObj: [], // 工单类型合并（二级）
      showTable: true,
      serviceItemCodeArr: [], // 服务事项
      serviceItemList: [], // 服务事项列表
      processedServiceItems: [], // 处理后的服务事项列表（带禁用状态）
      workTypeArr: [], // 工单类型列表
      workTypeCodeList: '', // 工单类型
      serviceItemProps: {
        value: 'id',
        label: 'name',
        children: 'children',
        multiple: true,
        checkStrictly: true
      },
      selectedLevel: '' // 当前选中的层级
    }
  },
  computed: {},
  watch: {
    reportType() {
      console.log('报表类型', this.reportType)
    },
    tableData: {
      handler(newVal, oldVal) {
        this.showTable = false
        this.$nextTick(() => {
          this.showTable = true
        })
      }
    }
  },
  created() {
    // if (localStorage.getItem('userInfo')) {
    //   this.userInfo = JSON.parse(localStorage.getItem('userInfo'))
    // } else {
    //   this.userInfo = JSON.parse(this.$route.query.userInfo)
    //   localStorage.setItem('userInfo', JSON.stringify(this.userInfo))
    // }
    this.reportType = this.reportTypePara
    //  this.getPlanTaskList();
    // 时间初始化
    this.clickType = '3'
    this.timeLine = []
    this.radioChange('3')
    const self = this
    this.reportType = '0'
    this.getDepartmentCost()
    // this.reportType = '7'
    // self.reportNmae = '一站式服务综合统计报表'
    // self.chartsName = '一站式服务分布'
    // self.getIntegratedReport()
    // self.getIntegratedReportDegree()
    window.addEventListener('message', function (event) {
      var data = event.data
      // 以下内容为处理业务和调用当前页面的函数方法
      if (data.module == 'reportType') {
        self.reportType = data.params
        if (data.params == '0') {
          self.reportNmae = '科室维修成本报表'
          self.getDepartmentCost()
        } else if (data.params == '1') {
          self.reportNmae = '维修区域报表'
          self.getMaintenanceLocaltion()
        } else if (data.params == '2') {
          self.reportNmae = '维修耗材消耗统计'
          self.getCostStatistical()
        } else if (data.params == '3') {
          self.reportNmae = '服务工人任务量报表'
          self.getWorkersTasks()
        } else if (data.params == '4') {
          self.reportNmae = '后勤服务班组统计报表'
          self.chartsName = '班组服务数量分布'
          self.getTeamTasks()
        } else if (data.params == '5') {
          self.reportNmae = '服务事项详细分类统计报表'
          self.getWorkTypeDict()
          self.getMaintenanceMatters()
        } else if (data.params == '6') {
          self.reportNmae = '科室报修量统计报表'
          self.getMaintenanceCount()
        } else if (data.params == '7') {
          self.reportNmae = '一站式服务综合统计报表'
          self.chartsName = '一站式服务分布'
          self.getIntegratedReport()
          self.getIntegratedReportDegree()
        } else if (data.params == '8') {
          self.reportNmae = '后勤服务绩效报表'
          self.getCwaStatistics()
        }
      }
    })
  },
  mounted() {
    // this.getAlldata()
    // this.getTableData()
    // this.getQueryCondition();
    // this.getTeams();
  },
  beforeDestroy() {
    document.styleSheets[0].deleteRule(0)
  },
  methods: {
    onClearWork() {
      this.processedServiceItems = []
      this.serviceItemCodeArr = []
      this.selectedLevel = ''
    },
    getWorkTypeDict() {
      this.$api
        .findWorkTypeDict({})
        .then((res) => {
          if (res.code == 200) {
            this.workTypeArr = res.data
          } else {
            this.$message.error(res.message)
          }
        })
        .catch((err) => {
          this.$message.error(err)
        })
    },
    onChangeWork(id) {
      // 重置服务事项选择和选中层级
      this.onClearWork()
      this.getServiceItemDict(id)
    },
    getServiceItemDict(id) {
      if (!id) return
      this.$api
        .findEnableTreeData({ workTypeCode: id })
        .then((res) => {
          if (res.code == 200 && res.data) {
            this.serviceItemList = res.data
            this.processServiceItems()
          }
          if (res.code != 200) {
            this.$message.error(res.message)
          }
        })
        .catch((err) => {
          this.$message.error(err)
        })
    },
    // 处理服务事项数据，根据选中层级设置禁用状态
    processServiceItems() {
      // 深拷贝原始数据
      const processedItems = JSON.parse(JSON.stringify(this.serviceItemList))
      // 如果有选中层级，设置禁用状态
      if (this.selectedLevel) {
        this.setDisabledStatus(processedItems, parseInt(this.selectedLevel))
      }
      this.processedServiceItems = processedItems
    },
    // 递归设置禁用状态
    setDisabledStatus(items, selectedLevel) {
      if (!items || !Array.isArray(items)) return
      for (const item of items) {
        // 设置当前节点的禁用状态
        if (parseInt(item.level) !== selectedLevel) {
          item.disabled = true
        }
        // 递归处理子节点
        if (item.children && item.children.length > 0) {
          this.setDisabledStatus(item.children, selectedLevel)
        }
      }
    },
    // 处理级联选择器变化，实现同级多选但禁止跨级选择
    handleCascaderChange(value) {
      // 如果清空了选择，重置选中层级
      if (!value || value.length === 0) {
        this.selectedLevel = ''
        this.processServiceItems()
        return
      }
      // 获取第一个选中项的层级
      const firstSelectedId = value[0][value[0].length - 1]
      const firstNode = this.findNodeById(this.serviceItemList, firstSelectedId)
      if (!firstNode) return
      // 如果是首次选择或者层级变化，更新层级并重新处理数据
      if (!this.selectedLevel || this.selectedLevel !== firstNode.level) {
        this.selectedLevel = firstNode.level
        this.processServiceItems()
        // 过滤掉不同层级的选项
        const filteredValue = value.filter((path) => {
          const nodeId = path[path.length - 1]
          const node = this.findNodeById(this.serviceItemList, nodeId)
          return node && node.level === this.selectedLevel
        })
        // 如果有被过滤掉的选项，更新选中值
        if (filteredValue.length !== value.length) {
          this.$nextTick(() => {
            this.serviceItemCodeArr = filteredValue
          })
        }
      }
    },
    // 根据ID查找节点及其层级
    findNodeById(nodes, id, parent = null) {
      if (!nodes || !Array.isArray(nodes)) return null
      for (const node of nodes) {
        if (node.id === id) {
          // 返回节点并包含父级信息
          return { ...node, parent }
        }
        if (node.children && node.children.length > 0) {
          const found = this.findNodeById(node.children, id, node)
          if (found) return found
        }
      }
      return null
    },
    // 获取选中的服务事项ID
    getSelectedServiceItemIds() {
      if (!this.serviceItemCodeArr || this.serviceItemCodeArr.length === 0) {
        return ''
      }
      // 提取每个选中路径的最后一个ID（实际选中的节点ID）
      const selectedIds = this.serviceItemCodeArr.map((path) => path[path.length - 1])
      return selectedIds.join(',')
    },
    handleTime() {
      if (this.params.btnType == '1') {
        return '1'
      }
      if (this.params.btnType == '2') {
        return '6'
      }
      if (this.params.btnType == '3') {
        return '2'
      }
      if (this.params.btnType == '4') {
        return '3'
      }
      if (this.params.btnType == '5') {
        return '4'
      }
    },
    handle0_0(row) {
      console.log(row)
      let showTimeType = this.handleTime()
      this.$router.push({
        path: '/maintenanceService/workOrderTable',
        query: {
          typeName: 'bydept',
          showTimeType,
          sourcesDept: row.sourcesDept,
          startTime: this.params.startTime,
          endTime: this.params.endTime
        }
      })
    },
    handle1_0(row) {
      let showTimeType = this.handleTime()
      this.$router.push({
        path: '/maintenanceService/workOrderTable',
        query: {
          typeName: 'byRegion',
          showTimeType,
          regionId: row.regionId,
          startTime: this.params.startTime,
          endTime: this.params.endTime
        }
      })
    },
    handle4_0(row) {
      let showTimeType = this.handleTime()
      this.$router.push({
        path: '/maintenanceService/workOrderTable',
        query: {
          typeName: 'byteam',
          showTimeType,
          designateDeptCode: row.deptCode,
          startTime: this.params.startTime,
          endTime: this.params.endTime
        }
      })
    },
    handle4_1(row) {
      let showTimeType = this.handleTime()
      this.$router.push({
        path: '/maintenanceService/workOrderTable',
        query: {
          typeName: 'byteam',
          showTimeType,
          designateDeptCode: row.deptCode,
          startTime: this.params.startTime,
          endTime: this.params.endTime,
          flowcode: '5'
        }
      })
    },
    handle6_0(row) {
      let showTimeType = this.handleTime()
      this.$router.push({
        path: '/maintenanceService/workOrderTable',
        query: {
          typeName: 'bydept',
          showTimeType,
          sourcesDept: row.deptCode,
          startTime: this.params.startTime,
          endTime: this.params.endTime
        }
      })
    },
    handle6_1(obj, row) {
      let workTypeCode = obj.prop.split('_')[1]
      console.log(obj, row)
      if (row[obj.prop] == 0) {
        return false
      }
      let showTimeType = this.handleTime()
      this.$router.push({
        path: '/maintenanceService/workOrderTable',
        query: {
          typeName: 'bydeptPro',
          showTimeType,
          sourcesDept: row.deptCode,
          startTime: this.params.startTime,
          endTime: this.params.endTime,
          workTypeCode: workTypeCode
        }
      })
    },
    handle5_0(row) {
      let showTimeType = this.handleTime()
      this.$router.push({
        path: '/maintenanceService/workOrderTable',
        query: {
          typeName: 'byitem',
          showTimeType,
          itemServiceCode: row.itemCode,
          startTime: this.params.startTime,
          endTime: this.params.endTime
        }
      })
    },
    handle3_0(row) {
      let showTimeType = this.handleTime()
      this.$router.push({
        path: '/maintenanceService/workOrderTable',
        query: {
          typeName: 'byWorker',
          showTimeType,
          designatePersonCode: row.persionCode,
          startTime: this.params.startTime,
          endTime: this.params.endTime
        }
      })
    },
    handle7_0(row) {
      let showTimeType = this.handleTime()
      this.$router.push({
        path: '/maintenanceService/workOrderTable',
        query: {
          typeName: 'byWorkType',
          showTimeType,
          workTypeCode: row.workTypeCode,
          startTime: this.params.startTime,
          endTime: this.params.endTime
        }
      })
    },
    handle7_1(row) {
      let showTimeType = this.handleTime()
      this.$router.push({
        path: '/maintenanceService/workOrderTable',
        query: {
          typeName: 'byWorkTypeFinish',
          showTimeType,
          workTypeCode: row.workTypeCode,
          startTime: this.params.startTime,
          endTime: this.params.endTime
        }
      })
    },
    changeType(type) {
      let self = this
      this.reportType = type
      if (type == '0') {
        self.reportNmae = '科室维修成本报表'
        self.getDepartmentCost()
      } else if (type == '1') {
        self.reportNmae = '维修区域报表'
        self.getMaintenanceLocaltion()
      } else if (type == '2') {
        self.reportNmae = '维修耗材消耗统计'
        self.getCostStatistical()
      } else if (type == '3') {
        self.reportNmae = '服务工人任务量报表'
        self.getWorkersTasks()
      } else if (type == '4') {
        self.reportNmae = '后勤服务班组统计报表'
        self.chartsName = '班组服务数量分布'
        self.getTeamTasks()
      } else if (type == '5') {
        self.reportNmae = '服务事项详细分类统计报表'
        self.getWorkTypeDict()
        self.getMaintenanceMatters()
      } else if (type == '6') {
        self.reportNmae = '科室报修量统计报表'
        self.getMaintenanceCount()
      } else if (type == '7') {
        self.reportNmae = '一站式服务综合统计报表'
        self.chartsName = '一站式服务分布'
        self.getIntegratedReport()
        self.getIntegratedReportDegree()
      } else if (type == '8') {
        self.reportNmae = '后勤服务绩效报表'
        self.getCwaStatistics()
      }
    },
    getAlldata() {
      Promise.all([this.getQueryCondition(), this.getTeams()]).then((values) => {
        this.getTableData()
      })
    },
    handleNodeClick(data, node) {
      this.treeClickData = data
      this.getTableData()
    },
    getTableData() {
      this.tableLoading = true
      let data = {
        taskBeginTime: this.timeLine[0],
        taskEndTime: this.timeLine[1],
        type: this.workTypeCodeArr.length > 0 ? this.workTypeCodeArr.toString() : '',
        deptCode: this.designateDeptCode.length > 0 ? this.designateDeptCode.toString() : ''
      }
      this.$api.getCwaStatistics(data).then((res) => {
        if (res.code == 200) {
          let list = res.data.list
          list = list.filter((e) => e.matterCode != '' && e.matterCode != undefined && e.matterCode != null)
          // this.tableData = list;
          this.rowSpanEvent()
        }
      })
      this.tableLoading = false
    },
    handleCheckChange(data, checked, indeterminate) {
      this.treeNode = this.$refs.treeRef.getCheckedNodes()
      // console.log(data, checked, indeterminate);
    },
    // 处理百分比数据，将字符串转换为小数
    processPercentData() {
      // 检查哪些列是百分比列
      const percentProps = []
      this.targetColumn.forEach((col) => {
        if (
          col.label.includes('百分比') ||
          col.prop === 'rate' ||
          col.prop === 'deptRate' ||
          col.prop === 'itemRate' ||
          col.prop === 'finishRate' ||
          col.prop === 'returnRate' ||
          col.prop === 'itemTBRate' ||
          col.prop === 'itemHBRate' ||
          col.prop === 'serviceTB' ||
          col.prop === 'serviceHB'
        ) {
          percentProps.push(col.prop)
        }
      })
      // 临时修改表格数据中的百分比值，使其在导出时正确显示
      const tableRows = document.querySelectorAll('#out-table tbody tr')
      tableRows.forEach((row) => {
        const cells = row.querySelectorAll('td')
        cells.forEach((cell, index) => {
          // 跳过序号列
          if (index > 0 && index <= this.targetColumn.length) {
            const prop = this.targetColumn[index - 1].prop
            if (percentProps.includes(prop)) {
              const text = cell.textContent.trim()
              if (text && text.includes('%')) {
                // 临时修改单元格内容为小数形式
                const originalValue = cell.innerHTML
                const percentValue = parseFloat(text.replace('%', '')) / 100
                cell.setAttribute('data-original', originalValue)
                cell.innerHTML = percentValue
              }
            }
          }
        })
      })
    },
    // 恢复百分比数据显示
    restorePercentData() {
      const tableRows = document.querySelectorAll('#out-table tbody tr')
      tableRows.forEach((row) => {
        const cells = row.querySelectorAll('td')
        cells.forEach((cell) => {
          const originalValue = cell.getAttribute('data-original')
          if (originalValue) {
            cell.innerHTML = originalValue
            cell.removeAttribute('data-original')
          }
        })
      })
    },
    // elementTable导出
    exportExcel() {
      // 处理表格数据中的百分比值
      this.processPercentData()
      // 生成Excel表格
      var wb = XLSX2.utils.table_to_sheet(document.querySelector('#out-table')) // mytable为表格的id名
      // 恢复表格数据显示
      this.restorePercentData()
      if (!wb['!merges'] || this.tableData.length == 0) {
        this.$message.warning('无法导出：报表无数据')
        return
      }
      for (var i = 0; i < 11; i++) {
        wb['!cols'][i] = { wpx: 130 }
      }
      // 检查哪些列是百分比列
      const percentColumns = []
      this.targetColumn.forEach((col, index) => {
        if (
          col.label.includes('百分比') ||
          col.prop === 'rate' ||
          col.prop === 'deptRate' ||
          col.prop === 'itemRate' ||
          col.prop === 'finishRate' ||
          col.prop === 'returnRate' ||
          col.prop === 'itemTBRate' ||
          col.prop === 'itemHBRate' ||
          col.prop === 'serviceTB' ||
          col.prop === 'serviceHB'
        ) {
          percentColumns.push(index + 1) // 因为第一列是序号，所以+1
        }
      })
      console.log(wb)
      // 样式的文档地址
      // https://www.npmjs.com/package/xlsx-style
      for (const key in wb) {
        if (key[0] != '!' && wb[key].v !== '') {
          if (key == 'A1' || key == 'A2' || key == 'A3' || key == 'B3' || key == 'C3' || key == 'D3' || key == 'E3' || key == 'F3' || key == 'G3' || key == 'H3' || key == 'I3') {
            wb[key].s = {
              font: {
                // 字体设置
                name: 'SimSun',
                sz: 14,
                bold: true,
                color: {
                  rgb: '000000' // 十六进制，不带#
                }
              },
              alignment: {
                // 文字居中
                horizontal: 'center',
                vertical: 'center',
                wrap_text: true
              }
            }
            if (key == 'A3' || key == 'B3' || key == 'C3' || key == 'D3' || key == 'E3' || key == 'F3' || key == 'G3' || key == 'H3' || key == 'I3') {
              wb[key].s = {
                font: {
                  // 字体设置
                  name: 'SimSun',
                  sz: 14,
                  bold: true,
                  color: {
                    rgb: '000000' // 十六进制，不带#
                  }
                },
                alignment: {
                  // 文字居中
                  horizontal: 'center',
                  vertical: 'center',
                  wrap_text: true
                },
                border: {
                  // 设置边框
                  top: { style: 'thin' },
                  bottom: { style: 'thin' },
                  left: { style: 'thin' },
                  right: { style: 'thin' }
                }
              }
            }
          } else {
            // 判断是否是百分比列
            const colLetter = key.replace(/[0-9]/g, '')
            const colIndex = colLetter.charCodeAt(0) - 'A'.charCodeAt(0)
            wb[key].s = {
              font: {
                // 字体设置
                sz: 13,
                bold: false,
                color: {
                  rgb: '000000' // 十六进制，不带#
                }
              },
              alignment: {
                // 文字居中
                horizontal: 'center',
                vertical: 'center',
                wrap_text: true
              },
              border: {
                // 设置边框
                top: { style: 'thin' },
                bottom: { style: 'thin' },
                left: { style: 'thin' },
                right: { style: 'thin' }
              }
            }
            // 为百分比列设置特殊格式
            if (percentColumns.includes(colIndex)) {
              wb[key].s.numFmt = '0.00%' // 设置百分比格式
              // 确保数值是小数而不是字符串
              if (typeof wb[key].v === 'string' && wb[key].v.includes('%')) {
                // 如果是字符串且包含%，转换为小数
                wb[key].v = parseFloat(wb[key].v.replace('%', '')) / 100
              }
            }
          }
        }
      }
      var data = this.addRangeBorder(wb['!merges'], wb) // 合并项添加边框
      var filedata = this.sheet2blob(data)
      // this.openDownloadDialog(filedata, this.$route.item ? this.reportNmae + '.xlsx' : "未命名.xlsx")
      this.openDownloadDialog(filedata, this.reportNmae + '.xlsx')
    },
    sheet2blob(sheet, sheetName) {
      sheetName = sheetName || 'sheet1'
      var workbook = {
        SheetNames: [sheetName],
        Sheets: {}
      }
      workbook.Sheets[sheetName] = sheet // 生成excel的配置项
      var wopts = {
        bookType: 'xlsx', // 要生成的文件类型
        bookSST: false, // 是否生成Shared String Table，官方解释是，如果开启生成速度会下降，但在低版本IOS设备上有更好的兼容性
        type: 'binary'
      }
      var wbout = XLSX.write(workbook, wopts)
      var blob = new Blob([s2ab(wbout)], {
        type: 'application/octet-stream'
      }) // 字符串转ArrayBuffer
      function s2ab(s) {
        var buf = new ArrayBuffer(s.length)
        var view = new Uint8Array(buf)
        for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xff
        return buf
      }
      return blob
    },
    addRangeBorder(range, ws) {
      let arr = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
      // 检查哪些列是百分比列
      const percentColumns = []
      this.targetColumn.forEach((col, index) => {
        if (
          col.label.includes('百分比') ||
          col.prop === 'rate' ||
          col.prop === 'deptRate' ||
          col.prop === 'itemRate' ||
          col.prop === 'finishRate' ||
          col.prop === 'returnRate' ||
          col.prop === 'itemTBRate' ||
          col.prop === 'itemHBRate' ||
          col.prop === 'serviceTB' ||
          col.prop === 'serviceHB'
        ) {
          percentColumns.push(index + 1) // 因为第一列是序号，所以+1
        }
      })
      range.forEach((item) => {
        let startColNumber = Number(item.s.r)
        let endColNumber = Number(item.e.r)
        let startRowNumber = Number(item.s.c)
        let endRowNumber = Number(item.e.c)
        const test = { ...ws[arr[startRowNumber] + (startColNumber + 1)] } // 深拷贝原始单元格样式
        for (let col = startColNumber; col <= endColNumber; col++) {
          for (let row = startRowNumber; row <= endRowNumber; row++) {
            const cellKey = arr[row] + (col + 1)
            ws[cellKey] = { ...test } // 使用深拷贝，避免引用问题
            // 为百分比列设置特殊格式
            if (percentColumns.includes(row)) {
              if (!ws[cellKey].s) ws[cellKey].s = {}
              ws[cellKey].s.numFmt = '0.00%' // 设置百分比格式
            }
          }
        }
      })
      return ws
    },
    openDownloadDialog(url, saveName) {
      if (typeof url == 'object' && url instanceof Blob) {
        url = URL.createObjectURL(url) // 创建blob地址
      }
      var aLink = document.createElement('a')
      aLink.href = url
      aLink.download = saveName || '' // HTML5新增的属性，指定保存文件名，可以不要后缀，注意，file:///模式下不会生效
      var event
      if (window.MouseEvent) event = new MouseEvent('click')
      else {
        event = document.createEvent('MouseEvents')
        event.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null)
      }
      aLink.dispatchEvent(event)
    },
    radioChange(val) {
      // this.queryType = val;
      // if (val != "userDefined") {
      //   this.getTableData();
      // }
      this.clickType = val
      if (val == '1') {
        this.timeLine = [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
        this.params.btnType = '1'
        this.params.startTime = moment().format('YYYY-MM-DD')
        this.params.endTime = moment().format('YYYY-MM-DD')
      } else if (val == '2') {
        this.timeLine = [
          moment().week(moment().week()).startOf('week').add(1, 'd').format('YYYY-MM-DD'),
          moment().week(moment().week()).endOf('week').add(1, 'd').format('YYYY-MM-DD')
        ]
        this.params.btnType = '2'
        this.params.startTime = moment().week(moment().week()).startOf('week').add(1, 'd').format('YYYY-MM-DD')
        this.params.endTime = moment().week(moment().week()).endOf('week').add(1, 'd').format('YYYY-MM-DD')
      } else if (val == '3') {
        this.timeLine = [moment().month(moment().month()).startOf('month').format('YYYY-MM-DD'), moment().month(moment().month()).endOf('month').format('YYYY-MM-DD')]
        this.params.btnType = '3'
        this.params.startTime = moment().month(moment().month()).startOf('month').format('YYYY-MM-DD')
        this.params.endTime = moment().month(moment().month()).endOf('month').format('YYYY-MM-DD')
      } else if (val == '4') {
        this.timeLine = [moment().year(moment().year()).startOf('year').format('YYYY-MM-DD'), moment().year(moment().year()).endOf('year').format('YYYY-MM-DD')]
        this.params.btnType = '4'
        this.params.startTime = moment().year(moment().year()).startOf('year').format('YYYY-MM-DD')
        this.params.endTime = moment().year(moment().year()).endOf('year').format('YYYY-MM-DD')
      } else if (val == '5') {
        this.params.btnType = '5'
      }
    },
    dateChange(val) {
      this.params.startTime = val[0]
      this.params.endTime = val[1]
      this.clickType = '5'
      this.params.btnType = '5'
    },
    searchData() {
      // this.getTableData();
      if (this.reportType == '0') {
        this.getDepartmentCost()
      } else if (this.reportType == '1') {
        this.getMaintenanceLocaltion()
      } else if (this.reportType == '2') {
        this.getCostStatistical()
      } else if (this.reportType == '3') {
        this.getWorkersTasks()
      } else if (this.reportType == '4') {
        this.getTeamTasks()
      } else if (this.reportType == '5') {
        this.getMaintenanceMatters()
      } else if (this.reportType == '6') {
        this.getMaintenanceCount()
      } else if (this.reportType == '7') {
        this.getIntegratedReport()
        this.getIntegratedReportDegree()
      } else if (this.reportType == '8') {
        this.getCwaStatistics()
      }
    },
    getMockTableData() {
      this.tableData = [
        {
          id: 1,
          deptName: '维修二部',
          workTypeName: '应急保洁',
          matterName: '电梯维修',
          workCount: '10',
          individualScore: '10',
          totalScort: '80'
        },
        {
          id: 1,
          deptName: '维修一部',
          workTypeName: '应急保洁',
          matterName: '应急保洁',
          workCount: '10',
          individualScore: '10',
          totalScort: '80'
        },
        {
          id: 1,
          deptName: '维修三部',
          workTypeName: '应急保洁',
          matterName: '应急保洁',
          workCount: '10',
          individualScore: '10',
          totalScort: '80'
        },
        {
          id: 1,
          deptName: '维修一部',
          workTypeName: '综合维修',
          matterName: '电梯维修2',
          workCount: '10',
          individualScore: '10',
          totalScort: '80'
        },
        {
          id: 1,
          deptName: '维修二部',
          workTypeName: '应急保洁',
          matterName: '应急保洁',
          workCount: '10',
          individualScore: '10',
          totalScort: '80'
        },
        {
          id: 1,
          deptName: '维修一部',
          workTypeName: '综合维修',
          matterName: '应急保洁1',
          workCount: '10',
          individualScore: '10',
          totalScort: '80'
        },
        {
          id: 1,
          deptName: '维修一部',
          workTypeName: '应急保洁',
          matterName: '应急保洁2',
          workCount: '10',
          individualScore: '10',
          totalScort: '80'
        },
        {
          id: 1,
          deptName: '维修二部',
          workTypeName: '综合维修',
          matterName: '综合维修',
          workCount: '10',
          individualScore: '10',
          totalScort: '80'
        },
        {
          id: 1,
          deptName: '维修一部',
          workTypeName: '应急保洁',
          matterName: '应急保洁',
          workCount: '10',
          individualScore: '10',
          totalScort: '80'
        },
        {
          id: 1,
          deptName: '维修二部',
          workTypeName: '综合维修',
          matterName: '应急保洁1',
          workCount: '10',
          individualScore: '10',
          totalScort: '80'
        }
      ]
      this.rowSpanEvent()
    },
    // 表格合并
    rowSpanEvent() {
      // 排序
      this.tableData.sort(function (item1, item2) {
        if (item1.deptName == item2.deptName) {
          return item2.workTypeName.localeCompare(item1.workTypeName, 'zh-CN')
        }
        return item2.deptName.localeCompare(item1.deptName, 'zh-CN')
      })
      // 合并
      let spanArr = []
      let spanArrType = []
      for (let i = 0; i < this.tableData.length; i++) {
        const element = this.tableData[i]
        // 第一层合并
        let index = spanArr.length > 0 ? spanArr.findIndex((e) => e.deptName == element.deptName) : -1
        // 第二层合并
        let indexType = spanArrType.length > 0 ? spanArrType.findIndex((e) => e.workTypeName == element.workTypeName) : -1
        if (index == -1) {
          spanArr.push({
            index: i,
            deptName: element.deptName
          })
          this.tableData[i].rowSpan = 1
          this.tableData[i].newSort = spanArr.length // 序号
          this.tableData[i].rowSpanTotal = this.tableData[i].individualScore ? Number(this.tableData[i].individualScore) : this.tableData[i].individualScore
          // 2
          spanArrType = []
          spanArrType.push({
            index: i,
            workTypeName: element.workTypeName
          })
          this.tableData[i].rowSpanType = 1
        } else {
          this.tableData[i].rowSpan = 0
          this.tableData[spanArr[index].index].rowSpan++
          this.tableData[spanArr[index].index].rowSpanTotal += this.tableData[i].individualScore ? Number(this.tableData[i].individualScore) : 0
          // this.tableData[i].rowSpanTotal = this.tableData[i].totalScort
          // 2
          if (indexType == -1) {
            spanArrType.push({
              index: i,
              workTypeName: element.workTypeName
            })
            this.tableData[i].rowSpanType = 1
          } else {
            this.tableData[i].rowSpanType = 0
            this.tableData[spanArrType[indexType].index].rowSpanType++
          }
        }
      }
      console.log(spanArr, spanArrType)
    },
    // 列合并
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      // console.log(row, column, rowIndex, columnIndex);
      if (columnIndex == 0 || columnIndex == 1 || columnIndex == 6) {
        const rowSpan = row.rowSpan
        const colSpan = rowSpan > 0 ? 1 : 0
        return {
          rowspan: rowSpan,
          colspan: colSpan
        }
      } else if (columnIndex == 2) {
        const rowSpanType = row.rowSpanType
        const colSpanType = rowSpanType > 0 ? 1 : 0
        return {
          rowspan: rowSpanType,
          colspan: colSpanType
        }
      }
    },
    /**
     * 获取工单类型、紧急程度、申报属性、申报人
     */
    getQueryCondition() {
      return this.$api.getQueryCondition({}).then((res) => {
        let list = res.data.workTypes
        this.workTypeCodeArr = Array.from(list, ({ workTypeCode }) => workTypeCode)
        this.workTypeList = res.data.workTypes
        return res
      })
    },
    /**
     * 获取服务部门（班组）
     */
    getTeams() {
      return this.$api.getOutsourcedTreeData({}).then((res) => {
        if (res.code == 200 && res.data.length > 0) {
          let list = res.data.filter((e) => e.level == 3)
          // 数组对象去重
          let obj = {}
          list = list.reduce((cur, next) => {
            obj[next.deptId] ? '' : (obj[next.deptId] = true && cur.push(next))
            return cur
          }, [])
          this.designateDeptCode = Array.from(list, ({ deptId }) => deptId)
          this.designateDeptList = list
        }
        return res
      })
    },
    // 维修区域报表
    getMaintenanceLocaltion() {
      this.tableLoading = true
      let params = JSON.stringify(this.params)
      params = JSON.parse(params)
      if (params.btnType != '5') {
        // 删除params中的startTime和endTime
        delete params.startTime
        delete params.endTime
      }
      if (params.btnType == '5') {
        params.accurate = true
      }
      this.$api
        .getMaintenanceLocaltion(params)
        .then((res) => {
          if (res.code == '200') {
            this.tableLoading = false
            this.targetColumn = [
              {
                prop: 'regionName',
                label: '楼宇名称'
              },
              {
                prop: 'amount',
                label: '报修总量'
              },
              {
                prop: 'rate',
                label: '百分比'
              }
            ]
            this.tableData = res.regionResult
            if (this.tableData.length == 0) {
              document.styleSheets[0].insertRule('.el-table::before {display: none}', 0)
            }
          }
        })
        .catch((err) => {
          this.tableLoading = false
          document.styleSheets[0].insertRule('.el-table::before {display: none}', 0)
        })
    },
    // 科室维修成本报表
    getDepartmentCost() {
      this.tableLoading = true
      let params = JSON.stringify(this.params)
      params = JSON.parse(params)
      if (params.btnType != '5') {
        // 删除params中的startTime和endTime
        delete params.startTime
        delete params.endTime
      }
      if (params.btnType == '5') {
        params.accurate = true
      }
      this.$api
        .getDepartmentCost(params)
        .then((res) => {
          if (res.code == '200') {
            this.tableLoading = false
            this.tableData = res.data
            this.targetColumn = [
              {
                prop: 'sourcesDeptName',
                label: '科室名称'
              },
              {
                prop: 'repairAmount',
                label: '报修总量'
              },
              {
                prop: 'cost',
                label: '费用（元）'
              }
            ]
          }
          if (this.tableData.length == 0) {
            document.styleSheets[0].insertRule('.el-table::before {display: none}', 0)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    // 维修耗材消耗统计
    getCostStatistical() {
      this.tableLoading = true
      let params = JSON.stringify(this.params)
      params = JSON.parse(params)
      if (params.btnType != '5') {
        // 删除params中的startTime和endTime
        delete params.startTime
        delete params.endTime
      }
      if (params.btnType == '5') {
        params.accurate = true
      }
      this.$api
        .getCostStatistical(params)
        .then((res) => {
          if (res.code == '200') {
            this.tableLoading = false
            this.tableData = res.data
            this.targetColumn = [
              {
                prop: 'depName',
                label: '耗材名称'
              },
              {
                prop: 'depTwoName',
                label: '耗材小类'
              },
              {
                prop: 'depOneName',
                label: '耗材大类'
              },
              {
                prop: 'amount',
                label: '耗材消耗数量'
              },
              {
                prop: 'price',
                label: '单价（元）'
              },
              {
                prop: 'allPrice',
                label: '总价（元）'
              }
            ]
          }
          if (this.tableData.length == 0) {
            document.styleSheets[0].insertRule('.el-table::before {display: none}', 0)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    // 工人任务量统计
    getWorkersTasks() {
      this.tableLoading = true
      let params = JSON.stringify(this.params)
      params = JSON.parse(params)
      if (params.btnType != '5') {
        // 删除params中的startTime和endTime
        delete params.startTime
        delete params.endTime
      }
      if (params.btnType == '5') {
        params.accurate = true
      }
      this.$api
        .getWorkersTasks(params)
        .then((res) => {
          this.tableLoading = false
          if (res.code == '200') {
            this.targetColumn = [
              {
                prop: 'persionName',
                label: '姓名'
              },
              {
                prop: 'deptName',
                label: '所在班组'
              },
              {
                prop: 'serviceCount',
                label: '服务总量'
              },
              {
                prop: 'finishCount',
                label: '已完工'
              },
              {
                prop: 'finishTotalTime',
                label: '工时总计'
              },
              {
                prop: 'finishAvgTime',
                label: '平均完工时间（小时）'
              },
              {
                prop: 'evaluate',
                label: '满意度'
              }
            ]
            this.tableData = res.data
          }
          if (this.tableData.length == 0) {
            document.styleSheets[0].insertRule('.el-table::before {display: none}', 0)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    // 班组工作量报表
    getTeamTasks() {
      this.tableLoading = true
      let params = JSON.stringify(this.params)
      params = JSON.parse(params)
      if (params.btnType != '5') {
        // 删除params中的startTime和endTime
        delete params.startTime
        delete params.endTime
      }
      if (params.btnType == '5') {
        params.accurate = true
      }
      this.$api
        .getTeamTasks(params)
        .then((res) => {
          if (res.code == '200') {
            this.tableLoading = false
            this.targetColumn = [
              {
                prop: 'deptName',
                label: '班组名称'
              },
              {
                prop: 'allCount',
                label: '服务总量'
              },
              {
                prop: 'finish',
                label: '已完工'
              },
              {
                prop: 'finishRate',
                label: '完工率'
              },
              {
                prop: 'returnRate',
                label: '返修率'
              },
              {
                prop: 'finishTime',
                label: '平均完工时间（小时）'
              },
              {
                prop: 'evaluate',
                label: '满意度'
              }
            ]
            this.tableData = res.data
            this.initOption()
          }
          if (this.tableData.length == 0) {
            document.styleSheets[0].insertRule('.el-table::before {display: none}', 0)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    // 班组服务数量分布echarts
    initOption() {
      const chartsWarp = echarts.init(document.getElementById('charts'))
      const chartsData = []
      this.tableData.forEach((i) => {
        const item = {
          value: i.allCount,
          name: i.deptName
        }
        chartsData.push(item)
      })
      console.log(chartsData)
      const option = {
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            type: 'pie',
            radius: '70%',
            label: {
              show: true,
              formatter: '{b}{c}件 {d}%'
            },
            data: chartsData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      chartsWarp.setOption(option)
    },
    // 根据选中的层级动态调整列表展示项
    adjustColumns() {
      // 基础列（所有层级都有的列）
      const baseColumns = [
        {
          prop: 'amount',
          label: '服务总量'
        },
        {
          prop: 'itemTBRate',
          label: '同比'
        },
        {
          prop: 'itemHBRate',
          label: '环比'
        },
        {
          prop: 'itemRate',
          label: '所占比重'
        }
      ]
      // 根据选中层级添加不同的服务事项列
      if (this.selectedLevel === '1') {
        // 一级服务事项
        this.targetColumn = [
          {
            prop: 'itemTypeName',
            label: '一级服务事项'
          },
          ...baseColumns
        ]
      } else if (this.selectedLevel === '2') {
        // 二级服务事项
        this.targetColumn = [
          {
            prop: 'itemTypeName', // 假设API返回了父级事项名称
            label: '一级服务事项'
          },
          {
            prop: 'itemDetailName',
            label: '二级服务事项'
          },
          ...baseColumns
        ]
      } else if (this.selectedLevel === '3') {
        // 三级服务事项
        this.targetColumn = [
          {
            prop: 'itemTypeName', // 假设API返回了顶级事项名称
            label: '一级服务事项'
          },
          {
            prop: 'itemDetailName', // 假设API返回了父级事项名称
            label: '二级服务事项'
          },
          {
            prop: 'itemName',
            label: '服务事项'
          },
          ...baseColumns
        ]
      } else {
        // 默认列
        this.targetColumn = [
          {
            prop: 'itemName',
            label: '服务事项'
          },
          ...baseColumns
        ]
      }
    },
    // 维修事项报表
    getMaintenanceMatters() {
      this.tableLoading = true
      let params = JSON.stringify(this.params)
      params = JSON.parse(params)
      if (params.btnType != '5') {
        // 删除params中的startTime和endTime
        delete params.startTime
        delete params.endTime
      }
      if (params.btnType == '5') {
        params.accurate = true
      }
      // 添加工单类型和服务事项筛选条件
      const data = {
        ...params,
        workTypeCode: this.workTypeCodeList || '', // 工单类型
        olgItemTypeIds: this.getSelectedServiceItemIds(), // 服务事项
        level: this.selectedLevel || ''
      }
      this.$api
        .getMaintenanceMatters(data)
        .then((res) => {
          if (res.code == '200') {
            this.tableLoading = false
            // 处理返回的数据，确保包含所需字段
            let serviceData = res.serviceData || []
            // 如果选择了服务事项，并且返回的数据中没有包含所需的字段，则添加这些字段
            if (this.selectedLevel && serviceData.length > 0) {
              // 获取选中的服务事项信息
              const selectedIds = this.getSelectedServiceItemIds().split(',')
              // 根据选中的层级处理数据
              if (this.selectedLevel === '2') {
                // 为二级服务事项添加一级服务事项名称
                serviceData = serviceData.map((item) => {
                  // 查找当前项对应的二级服务事项
                  const selectedItem = this.findNodeById(this.serviceItemList, item.id || item.itemId)
                  if (selectedItem && selectedItem.parent) {
                    // 添加父级事项名称
                    item.parentItemName = selectedItem.parent.name
                  }
                  return item
                })
              } else if (this.selectedLevel === '3') {
                // 为三级服务事项添加一级和二级服务事项名称
                serviceData = serviceData.map((item) => {
                  // 查找当前项对应的三级服务事项
                  const selectedItem = this.findNodeById(this.serviceItemList, item.id || item.itemId)
                  if (selectedItem && selectedItem.parent) {
                    // 添加父级事项名称
                    item.parentItemName = selectedItem.parent.name
                    // 添加顶级事项名称（如果父级有父级）
                    if (selectedItem.parent.parent) {
                      item.topItemName = selectedItem.parent.parent.name
                    }
                  }
                  return item
                })
              }
            }
            // 根据选中的层级动态调整列表展示项
            this.adjustColumns()
            this.tableData = serviceData
          }
          if (this.tableData.length == 0) {
            document.styleSheets[0].insertRule('.el-table::before {display: none}', 0)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    // 科室报修量报表
    getMaintenanceCount() {
      this.tableLoading = true
      let params = JSON.stringify(this.params)
      params = JSON.parse(params)
      if (params.btnType != '5') {
        // 删除params中的startTime和endTime
        delete params.startTime
        delete params.endTime
      }
      if (params.btnType == '5') {
        params.accurate = true
      }
      this.$api
        .getMaintenanceCount(params)
        .then((res) => {
          if (res.code == '200') {
            this.tableLoading = false
            // this.targetColumn = [
            //   {
            //     prop: 'deptName',
            //     label: '科室名称'
            //   },
            //   {
            //     prop: 'deptCount',
            //     label: '报修总量'
            //   },
            //   {
            //     prop: 'deptRate',
            //     label: '百分比'
            //   }
            // ]
            this.targetColumn = res.head
            // 给this.targetColumn最前面加科室名称，最后面加报修总量、百分比
            this.targetColumn.unshift({
              prop: 'deptName',
              label: '科室名称'
            })
            this.targetColumn.push({
              prop: 'deptCount',
              label: '报修总量'
            })
            this.targetColumn.push({
              prop: 'deptRate',
              label: '百分比'
            })
            this.tableData = res.data
          }
          if (this.tableData.length == 0) {
            document.styleSheets[0].insertRule('.el-table::before {display: none}', 0)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    // 综合报表
    getIntegratedReport() {
      this.tableLoading = true
      let params = JSON.stringify(this.params)
      params = JSON.parse(params)
      if (params.btnType != '5') {
        // 删除params中的startTime和endTime
        delete params.startTime
        delete params.endTime
      }
      if (params.btnType == '5') {
        params.accurate = true
      }
      this.$api
        .getIntegratedReport(params)
        .then((res) => {
          if (res.code == '200') {
            this.tableLoading = false
            this.targetColumn = [
              {
                prop: 'serviceType',
                label: '服务类型'
              },
              {
                prop: 'serviceCount',
                label: '服务总量'
              },
              {
                prop: 'serviceTB',
                label: '同比'
              },
              {
                prop: 'serviceHB',
                label: '环比'
              },
              {
                prop: 'finishCount',
                label: '已完工'
              },
              {
                prop: 'finishRate',
                label: '完工率'
              },
              {
                prop: 'avgResponse',
                label: '平均响应时间（小时）'
              },
              {
                prop: 'avgFinish',
                label: '平均完工时间（小时）'
              },
              {
                prop: 'avgDegree',
                label: '满意度'
              }
            ]
            this.tableData = res.data
            this.serviceEcharts()
          }
          if (this.tableData.length == 0) {
            document.styleSheets[0].insertRule('.el-table::before {display: none}', 0)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    // 一站式服务分布echarts
    serviceEcharts() {
      const chartsWarp = echarts.init(document.getElementById('charts'))
      const chartsData = []
      this.tableData.forEach((i) => {
        const item = {
          value: i.serviceCount,
          name: i.serviceType
        }
        chartsData.push(item)
      })
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{c}件'
        },
        series: [
          {
            type: 'pie',
            radius: '70%',
            label: {
              show: true,
              formatter: '{b}{c}件 {d}%'
            },
            data: chartsData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      chartsWarp.setOption(option)
    },
    // 服务满意度分布echarts
    getIntegratedReportDegree() {
      this.tableLoading = true
      let params = JSON.stringify(this.params)
      params = JSON.parse(params)
      if (params.btnType != '5') {
        // 删除params中的startTime和endTime
        delete params.startTime
        delete params.endTime
      }
      if (params.btnType == '5') {
        params.accurate = true
      }
      this.$api
        .getIntegratedReportDegree(params)
        .then((res) => {
          if (res.code == '200') {
            this.tableLoading = false
            const chartsWarp = echarts.init(document.getElementById('charts1'))
            const chartsData = []
            res.degreeArray.forEach((i) => {
              const item = {
                value: i.count,
                name: i.degree
              }
              chartsData.push(item)
            })
            const option = {
              tooltip: {
                trigger: 'item',
                formatter: '{c}件'
              },
              series: [
                {
                  type: 'pie',
                  radius: '70%',
                  label: {
                    show: true,
                    formatter: '{b}{c}件 {d}%'
                  },
                  data: chartsData,
                  emphasis: {
                    itemStyle: {
                      shadowBlur: 10,
                      shadowOffsetX: 0,
                      shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                  }
                }
              ]
            }
            chartsWarp.setOption(option)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    // 绩效表
    getCwaStatistics() {
      this.tableLoading = true
      let data = {
        taskBeginTime: this.timeLine[0],
        taskEndTime: this.timeLine[1],
        type: this.workTypeCodeArr.length > 0 ? this.workTypeCodeArr.toString() : '',
        deptCode: this.designateDeptCode.length > 0 ? this.designateDeptCode.toString() : ''
      }
      if (this.clickType == '5') {
        data.accurate = true
      }
      this.$api
        .getCwaStatistics(data)
        .then((res) => {
          if (res.code == '200') {
            this.tableLoading = false
            this.targetColumn = [
              {
                prop: 'deptName',
                label: '服务部门'
              },
              {
                prop: 'workTypeName',
                label: '工单类型'
              },
              {
                prop: 'matterName',
                label: '服务事项'
              },
              {
                prop: 'workCount',
                label: '工单数量'
              },
              {
                prop: 'individualScore',
                label: '单项得分'
              },
              {
                prop: 'individualScore',
                label: '总分'
              }
            ]
            this.tableData = res.data.list
            // 统计科室出现次数
            const deptArr = this.tableData.map((i) => i.deptCode) // 科室数组
            const mergeObj = deptArr.reduce((item, key) => {
              if (key in item) {
                item[key]++
              } else {
                item[key] = 1
              }
              return item
            }, {})
            this.deptObj = new Array(deptArr.length)
            for (let key in mergeObj) {
              if (deptArr.findIndex((item) => item == key) >= 0) {
                this.deptObj[deptArr.findIndex((item) => item == key)] = mergeObj[key]
              }
            }
            this.deptObj.find((i, index) => {
              if (i == undefined) {
                this.deptObj[index] = 0
              }
            })
            // 统计工单类型出现次数
            this.deptObj.forEach((item, index) => {
              if (item != 0) {
                console.log(item, index)
                const typeList = this.tableData.slice(index, index + item)
                const typeArr = typeList.map((i) => i.deptCode) // 科室数组
                const mergeObj = typeArr.reduce((item, key) => {
                  if (key in item) {
                    item[key]++
                  } else {
                    item[key] = 1
                  }
                  return item
                }, {})
                let typeObj = new Array(typeArr.length)
                for (let key in mergeObj) {
                  if (typeArr.findIndex((item) => item == key) >= 0) {
                    typeObj[typeArr.findIndex((item) => item == key)] = mergeObj[key]
                  }
                }
                typeObj.find((i, index) => {
                  if (i == undefined) {
                    typeObj[index] = 0
                  }
                })
                this.typeObj = this.typeObj.concat(typeObj)
              }
            })
          }
          if (this.tableData.length == 0) {
            document.styleSheets[0].insertRule('.el-table::before {display: none}', 0)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    // 表格合并
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if ([0, 1, 6].includes(columnIndex)) {
        const _row = this.deptObj[rowIndex]
        const _col = this.deptObj[rowIndex] > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      } else if (columnIndex == 2) {
        const _row = this.typeObj[rowIndex]
        const _col = this.typeObj[rowIndex] > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.reportPage {
  .el-tree-node {
    white-space: normal;
    .el-tree-node__content {
      height: 100%;
      align-items: start;
    }
  }
}
::v-deep.list {
  height: calc(100% - 2px) !important;
}
.dom-content-top {
  height: 28px;
  width: 100%;
  border-bottom: 1px solid #eee;
  box-sizing: border-box;
  background: rgb(247 248 250);
  div {
    margin-left: 5px;
    height: 16px;
    display: inline-block;
    margin-top: 6px;
    line-height: 12px;
    cursor: pointer;
    img {
      width: 16px;
      vertical-align: middle;
    }
    span {
      font-size: 12px;
      padding: 2px 2px 2px 1px;
    }
  }
  div:hover {
    background: #ccc;
  }
}
.sino_content {
  height: calc(100% - 35px);
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  background: #fff;
  .radio-button,
  .radio-button:hover {
    background: #fff;
    color: rgb(0 0 0);
  }
  .activeButton {
    background: #5482ee !important;
    color: #fff !important;
    border-color: #5482ee !important;
  }
  .searchButtonStyle {
    height: 26px;
    width: 66px;
    background: rgb(119 175 253);
    border-radius: 13px;
    font-size: 12px;
    line-height: 0;
  }
  .buttonStyle {
    height: 24px;
    width: 80px;
    background: rgb(255 255 255);
    border-width: 1px;
    border-style: solid;
    border-color: rgb(173 173 173);
    font-size: 12px;
    line-height: 3px;
  }
  .sino_content_top {
    padding: 4px 0;
    width: 100%;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgb(247 248 250);
    ::v-deep.el-select {
      width: 200px;
      .el-select__tags {
        max-width: 125px !important;
        flex-wrap: nowrap;
      }
    }
  }
  .sino_top_two {
    margin-left: 50px;
    font-size: 13px;
  }
  .sino_content_left {
    width: 20%;
    height: 100%;
    border-right: 1px solid #ddd;
  }
  .sino_content_right {
    width: auto;
    height: calc(100% - 115px);
    overflow-y: scroll;
    .sino_tools {
      height: 50px;
      width: 100%;
      margin-bottom: 20px;
    }
    .sino_table {
      width: 100%;
      // height: 100%;
      overflow-y: scroll;
      .report-title {
        text-align: center;
        margin: 30px auto 0;
        width: 60%;
        font-size: 14px;
        font-weight: 600;
      }
      .report-date {
        text-align: center;
        margin: 10px auto;
        width: 60%;
        font-size: 12px;
        font-weight: 600;
      }
      ::v-deep .el-table td,
      .el-table th.is-leaf,
      .el-table--border,
      .el-table--group {
        border-color: black;
      }
      ::v-deep .el-table--border::after,
      .el-table--group::after {
        width: 0;
      }
      ::v-deep .el-table__row {
        > td:first-child {
          border-left: 1px solid black;
        }
      }
      ::v-deep .el-table--border::after,
      .el-table--group::after,
      .el-table::before {
        background-color: black;
      }
      ::v-deep .is-group {
        .cell {
          color: #000;
          font-weight: 700;
        }
        color: #000;
        > tr:first-child > th:first-child {
          border: none !important;
        }
        > tr:nth-child(2) > th:first-child {
          border: none !important;
        }
        > tr:last-child {
          > th {
            border-top: 1px solid black;
            border-right: 1px solid black;
            border-bottom: 1px solid black;
          }
          > th:first-child {
            border-left: 1px solid black;
          }
        }
      }
      ::v-deep.el-table__row {
        > td:last-child {
          border-right: 1px solid black;
        }
      }
      .tableDom {
        margin: auto;
        width: 60%;
        tr {
          td {
            padding: 7px 15px;
            font-size: 13px;
            color: #000;
            border: 1px solid #000;
            text-align: center;
            font-family: '微软雅黑';
          }
        }
      }
    }
    .pagination {
      margin-right: 100px;
      margin-top: 20px;
      float: right;
    }
    ::v-deep .el-table .el-table__body-wrapper td.el-table__cell {
      border-right: 1px solid #000;
    }
  }
}
</style>
