<template>
  <PageContainer class="originalTab">
    <template #content>
      <div class="originalTab__header">
        <el-form ref="formRef" :model="searchForm" inline @submit.native.prevent="name">
          <el-form-item prop="date">
            <el-date-picker
              v-model="searchForm.date"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
            ></el-date-picker>
          </el-form-item>
          <el-form-item prop="queryType" style="margin-right: 0px">
            <el-select v-model="searchForm.queryType" class="dutyTeam">
              <el-option label="值班考勤组" value="4"></el-option>
              <el-option label="部门" value="1"></el-option>
              <el-option label="单位" value="2"></el-option>
              <el-option label="成员" value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="personName">
            <el-input v-model="searchForm.personName" placeholder="请输入内容"> </el-input>
          </el-form-item>
        </el-form>
        <div>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" plain @click="onReset">重置</el-button>
        </div>
      </div>
      <div class="originalTab__actions">
        <el-button type="primary" @click="onOperate('export')"> 导出 </el-button>
      </div>
      <div class="originalTab__table">
        <el-table v-loading="tableLoadingStatus" height="100%" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
          <el-table-column prop="userName" label="姓名" show-overflow-tooltip></el-table-column>
          <el-table-column prop="unitName" label="部门" show-overflow-tooltip></el-table-column>
          <el-table-column prop="departmentName" label="单位" show-overflow-tooltip></el-table-column>
          <el-table-column prop="personNo" label="工号" show-overflow-tooltip></el-table-column>
          <!-- 岗位 -->
          <el-table-column prop="postName" label="岗位" show-overflow-tooltip></el-table-column>
          <el-table-column prop="dutyAttendanceConfigName" label="值班考勤组" show-overflow-tooltip></el-table-column>
          <!-- 值班岗 -->
          <el-table-column prop="dutyPostName" label="值班岗" show-overflow-tooltip></el-table-column>
          <el-table-column prop="signTimeStr" label="班次" show-overflow-tooltip></el-table-column>
          <el-table-column prop="signTime" label="打卡时间" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.signTime ? moment(row.signTime).format('YYYY-MM-DD HH:mm:ss') : '' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="attendanceStatusName" label="打卡结果" show-overflow-tooltip></el-table-column>
          <el-table-column prop="signDeviceName" label="打卡设备" show-overflow-tooltip></el-table-column>
          <el-table-column label="打卡图片" width="150px">
            <template #default="{ row }">
              <el-button v-if="row.signDeviceType == 1" type="text" @click="onOperate('view', row)">查看</el-button>
              <el-button v-if="row.signDeviceType == 2" type="text" class="fingerprint">指纹</el-button>
              <el-button v-if="row.signDeviceType == 3" type="text">卡片</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        class="originalTab__pagination"
        :current-page="pagination.page"
        :page-sizes="pagination.pageSizeOptions"
        :page-size="pagination.size"
        :layout="pagination.layoutOptions"
        :total="pagination.total"
        @size-change="paginationSizeChange"
        @current-change="paginationCurrentChange"
      >
      </el-pagination>
      <el-dialog :visible.sync="dialogVisible">
        <img width="100%" :src="dialogImageUrl" alt="" />
      </el-dialog>
    </template>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin'
import moment from 'moment'
import store from '@/store/index'
import axios from 'axios'
export default {
  name: 'originalTab',
  mixins: [tableListMixin],
  data() {
    return {
      moment,
      searchForm: {
        date: [], // 时间
        personName: '', //
        queryType: '4'
      },
      dialogImageUrl: '',
      dialogVisible: false,
      tableData: [],
      tableLoadingStatus: false
    }
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    // 获取列表数据
    getDataList() {
      this.tableData = []
      this.tableLoadingStatus = true
      const params = {
        pageSize: this.pagination.size,
        page: this.pagination.current,
        ...this.searchForm
      }
      params.queryStartTime = params.date.length ? params.date[0] : ''
      params.queryEndTime = params.date.length ? params.date[1] : ''
      delete params.date
      this.$api.supplierAssess
        .queryDutyGroupRawDataByPage(params)
        .then((res) => {
          this.tableLoadingStatus = false
          if (res.code === '200') {
            this.tableData = res.data.records
            this.pagination.total = res.data.total
          } else {
            this.tableData = []
            this.pagination.total = 0
          }
        })
        .catch((err) => {
          this.tableLoadingStatus = false
        })
    },
    // 表单搜索按钮点击
    onSearch() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 表单重置按钮点击
    onReset() {
      this.$refs.formRef.resetFields()
      this.searchForm.queryType = '1'
      this.onSearch()
    },
    // 操作
    onOperate(type, row) {
      if (type === 'view') {
        if (!row.signPicture) return this.$message.error('暂无打卡图片！')
        this.dialogImageUrl = this.$tools.imgUrlTranslation(row.signPicture)
        this.dialogVisible = true
      } else if (type === 'export') {
        let params = {
          unitCode: this.$store.state.user.userInfo.user.unitCode,
          hospitalCode: this.$store.state.user.userInfo.user.hospitalCode,
          ...this.searchForm
        }
        params.queryStartTime = params.date.length ? params.date[0] : ''
        params.queryEndTime = params.date.length ? params.date[1] : ''
        delete params.date
        axios({
          method: 'post',
          url: __PATH.SPACE_API + 'supSign/exportDutyGroupData',
          data: params,
          responseType: 'blob',
          headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + store.state.user.token
          }
        })
          .then((res) => {
            this.$message.success(res.message || '导出成功')
            this.$tools.downloadFile(res, this)
          })
          .catch((res) => {
            this.$message.error('导出失败！')
          })
      }
    }
  }
}
</script>
<style scoped lang="scss">
.originalTab {
  ::v-deep(> .container-content) {
    height: 100%;
    width: 100%;
    background-color: #fff;
    .dutyTeam {
      // width: 110px;
      margin-right: 10px;
    }
  }
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  &__actions {
    padding-bottom: 10px;
  }
  &__table {
    height: calc(100% - 180px);
  }
  &__pagination {
    margin-top: 10px;
  }
}
.el-form-item {
  margin-bottom: 8px !important;
}
.fingerprint {
  color: #606266 !important;
  cursor: default;
}
</style>
