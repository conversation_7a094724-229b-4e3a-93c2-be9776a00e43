<template>
  <el-dialog
    v-if="alarmDetailsDialogVisible"
    v-dialogDrag
    custom-class="polling-dialog"
    :modal="false"
    :close-on-click-modal="false"
    :visible.sync="alarmDetailsDialogVisible"
    :before-close="() => alarmDetailsDialogVisible = false"
  >
    <span slot="title">
      报警分析
    </span>
    <div class="polling-content">
      <div class="date_time">
        <el-date-picker
          ref="datePicker"
          v-model="dateRange"
          class="my-date-picker"
          popper-class="diabloPopperClass"
          type="daterange"
          range-separator="至"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          :picker-options="pickerOptions"
          :clearable="false"
          value-format="yyyy-MM-dd"
        >
        </el-date-picker>
        <div class="dialog_btn reset" @click="reset">重置</div>
        <div class="dialog_btn query" @click="getList">查询</div>
      </div>
      <div class="alarm_num_echars">
        <div class="alarm_num_echars_left">
          <div class="alarm_num_echars_left_content">
            <img src="@/assets/images/monitor/boiler-alarm.png" alt="">
            <div class="dialog_alarm_statistics">
              <p>报警统计</p>
              <b>{{alarmStatistics?.total || 0}}<span>次</span></b>
            </div>
            <div class="separate_status">
              <div>
                <p>未处理</p>
                <b>{{alarmStatistics?.noDealCount || 0}}<span>次</span></b>
              </div>
              <div>
                <p>处理中</p>
                <b>{{alarmStatistics?.isDealCount || 0}}<span>次</span></b>
              </div>
            </div>
          </div>
        </div>
        <div class="alarm_num_echars_right">
          <div v-if="!dialogAlarmTypeStatisticsShow" class="echart-null">
            <img src="@/assets/images/null.png" alt="" />
            <div>暂无数据~</div>
          </div>
          <div v-else style="width: 100%; height: calc(100%);">
            <div id="dialogAlarmTypeStatistics"></div>
          </div>
        </div>
      </div>
      <div class="dialog_table_and_single_device">
        <div class="single_device">
          <span>单设备：</span>
          <el-select v-model="singleDeviceId" class="custom_select" :popper-append-to-body="false" placeholder="请选择" @change="getTableList()">
            <el-option v-for="item in singleDeviceOptions" :key="item.code" :label="item.name" :value="item.code"> </el-option>
          </el-select>
        </div>
        <el-table
          v-loading="tableLoading"
          class="diablo-table"
          :data="tableData"
          height="100%"
          style="width: 100%"
          element-loading-background="rgba(0, 0, 0, 0.2)"
        >
          <el-table-column show-overflow-tooltip label="序号" width="80px">
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="alarmStartTime" show-overflow-tooltip label="报警时间" width="220px"></el-table-column>
          <el-table-column prop="alarmObjectName" show-overflow-tooltip label="报警对象名称"></el-table-column>
          <el-table-column prop="iphPoliceLevel" show-overflow-tooltip label="严重等级">
            <template slot-scope="scope">
              <span :style="{ color: alarmLevelItem[scope.row.alarmLevel]?.color }">{{ alarmLevelItem[scope.row.alarmLevel]?.text }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="incidentName" show-overflow-tooltip label="事件类型"></el-table-column>
          <el-table-column prop="menuName" show-overflow-tooltip label="类型"></el-table-column>
          <el-table-column prop="alarmSpaceName" show-overflow-tooltip label="位置"></el-table-column>
          <el-table-column label="操作">
            <template>
              <el-button type="text" @click="toHandle">处理</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination_style">
          <el-pagination
            :current-page="queryParams.page"
            :page-sizes="[15, 20, 30, 40]"
            popper-class="diabloPopperClass"
            :page-size="queryParams.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            class="pagination"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import * as echarts from 'echarts'
export default {
  data() {
    return {
      alarmLevelItem: {
        0: { text: '通知', color: '#3562DB' },
        1: { text: '一般', color: '#3562DB' },
        2: { text: '紧急', color: '#FF9435' },
        3: { text: '重要', color: '#FA403C' }
      },
      queryParams: {
        page: 1,
        pageSize: 15
      },
      total: 0,
      tableData: [],
      alarmDetailsDialogVisible: false,
      tableLoading: false,
      singleDeviceId: null,
      singleDeviceOptions: [
        
      ],
      dialogAlarmTypeStatisticsShow: true,
      dateRange: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      },
      projectCode: null,
      alarmStatistics: {}
    }
  },
  mounted() {

  },
  methods: {
    toHandle() {
      this.$router.push({
        path: '/alarmRecord/index',
        query: {
          projectCode: this.projectCode
        }
      })
    },
    getOpenDialog(projectCode) {
      this.alarmDetailsDialogVisible = true
      this.projectCode = projectCode
      this.$api.GetAirPoliceMenuList({projectCode}).then(res => {
        this.singleDeviceOptions = [
          {
            name: '全部',
            code: ''
          },
          ...res.data
        ]
      })
      this.getList()
    },
    getTableList(data) {
      this.$api.GetAirRunPoliceDetail({...data, menuCode: this.singleDeviceId}).then(res => {
        if (res.code == 200) {
          this.tableData = res.data.policeList?.list || []
        }
      })
    },
    getList() {
      let data = {
        projectCode: this.projectCode,
        startTime: this.dateRange[0],
        endTime: this.dateRange[1], 
        ...this.queryParams
      }
      this.$api.GetAirRunPoliceDetail(data).then(res => {
        if (res.code == 200) {
          this.getTableList(data)
          this.alarmStatistics = {
            total: res.data.airRunPolice?.total || 0,
            noDealCount: res.data.airRunPolice?.noDealCount || 0,
            isDealCount: res.data.airRunPolice?.isDealCount || 0
          }
          this.total = res.data.policeList?.count || 0
          this.dialogAlarmTypeStatisticsShow = res.data && res.data.airRunPolice?.policeList.length
          if (!this.dialogAlarmTypeStatisticsShow) return
          this.$nextTick(() => {
            let arr = res.data.airRunPolice.policeList.map(ele => {
              return {
                name: ele.menuName,
                value: ele.policeCount,
                policeRate: ele.policeRate
              }
            })
            this.setDialogElevatorBrandEcharts(arr)
          })
        }
        
      })
    },
    reset() {
      this.dateRange = []
      this.getList()
    },
    handleSizeChange(e) {
      this.queryParams.pageSize = e
      this.getTableList()
    },
    handleCurrentChange(e) {
      this.queryParams.page = e
      this.getTableList()
    },
    array2obj(array, key) {
      var resObj = {}
      for (var i = 0; i < array.length; i++) {
        resObj[array[i][key]] = array[i]
      }
      return resObj
    },
    // 报警统计echarts
    setDialogElevatorBrandEcharts(data) {
      const getchart = echarts.init(document.getElementById('dialogAlarmTypeStatistics'))
      const sum = data.reduce((a, b) => a + b.value, 0)
      var objData = this.array2obj(data, 'name')
      let option = {
        tooltip: {
          // show: false,
          trigger: 'item',
          // backgroundColor: "rgba(0, 0, 0, 0.1)",
          formatter: function (params) {
            if (params.name) {
              return params.name + ' : ' + params.value + '    (' + ((params.value / sum) * 100).toFixed(2) + '%)'
            }
          }
        },
        legend: {
          type: 'scroll',
          orient: 'vertical',
          top: '30',
          x: '40%',
          pageIconColor: '#5188fc', // 激活的分页按钮颜色
          pageIconInactiveColor: '#c2c6ce', // 没激活的分页按钮颜色
          data: data,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          formatter: function (name) {
            return '{a|' + name + '}{c|' + objData[name].value + '}'
          },
          textStyle: {
            rich: {
              a: {
                align: 'center',
                fontSize: 13,
                color: 'rgba(255,255,255,1)'
              },
              c: {
                align: 'center',
                width: 70,
                fontSize: 15,
                color: '#00C2FF'
              }
            }
          }
        },
        title: [
          // {
          //   text: sum,
          //   left: '48%',
          //   top: '37%',
          //   textAlign: 'center',
          //   textStyle: {
          //     fontSize: '24',
          //     fontWeight: '600',
          //     color: '#FFF',
          //     textAlign: 'center'
          //   }
          // },
          {
            text: '报警类型统计',
            x: '20%',
            y: '45%',
            textAlign: 'center',
            textStyle: {
              fontSize: '16',
              fontWeight: '400',
              color: '#A3A9AD',
              textAlign: 'center'
            }
          }
        ],
        series: [
          {
            type: 'pie',
            roundCap: true,
            radius: ['60%', '72%'],
            center: ['20%', '50%'],
            color: ['RGBA(100, 210, 255, 1)', 'RGBA(10, 132, 255, 1)', 'RGBA(244, 220, 110, 1)', 'RGBA(212, 222, 236, 1)', 'RGBA(94, 92, 230, 1)'],
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            data: data
          },
          {
            name: '',
            type: 'pie',
            startAngle: 80,
            radius: ['54%'],
            hoverAnimation: false,
            center: ['20%', '50%'],
            tooltip: {
              show: false
            },
            itemStyle: {
              color: 'rgba(10, 25, 40, 1)',
              borderWidth: 1,
              borderColor: '#1E2C39'
            },
            data: [sum]
          },
          {
            name: '',
            type: 'pie',
            startAngle: 80,
            radius: ['48%'],
            hoverAnimation: false,
            center: ['20%', '50%'],
            tooltip: {
              show: false
            },
            itemStyle: {
              color: 'rgba(21, 36, 50, 1)'
            },
            data: [sum]
          },
          {
            name: '',
            type: 'pie',
            startAngle: 80,
            radius: ['77%', '78%'],
            hoverAnimation: false,
            center: ['20%', '50%'],
            tooltip: {
              show: false
            },
            itemStyle: {
              normal: {
                color: 'rgba(10, 25, 40, .1)',
                borderColor: '#1E2C39',
                borderWidth: 1
              }
            },
            data: [sum]
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
#dialogAlarmTypeStatistics{
  width: 100%;
  height: 100%;
  z-index: 2;
}
::v-deep .polling-dialog {
  width: 1200px;
  height: 700px;
  background: url("~@/assets/images/monitor/alarm-analysis-dialog.png") no-repeat;
  background-size: 100% 100%;

  .el-dialog__header {
    padding: 20px 10px 10px 26px;
    span {
      font-size: 20px;
      font-family: "HarmonyOS Sans SC-Medium", "HarmonyOS Sans SC";
      font-weight: 500;
      color: #dce9ff;
    }

    .el-dialog__headerbtn .el-dialog__close {
      color: #7cd0ff;
      font-size: 20px;
    }
  }
  .el-dialog__body{
    height: calc(100% - 56px);
    padding: 20px 20px;
  }
  .polling-content {
    height: 100%;
    .dialog_table_and_single_device{
      height: calc(70% - 124px);
      .pagination_style{
        padding: 15px;
        display: flex;
        justify-content: flex-end;
        .el-select{
          width: auto;
        }
        .el-pagination .el-select .el-input .el-input__inner{
          background: center;
          border: 1px solid #193382;
          color: rgba(255, 255, 255, .8);
        }
        .el-pagination__total{
          color: rgba(255, 255, 255, .8);
        }
        .btn-prev, .el-pager li, .btn-next{
          background: center;
          color: rgba(255, 255, 255, .8);
        }
        .el-pager .active{
          color: rgba(244, 219, 103, 1);
        }
        .el-pagination__jump{
          color: rgba(255, 255, 255, .8);
        }
        .el-input__inner{
          background: center;
          border: 1px solid #193382;
          color: rgba(255, 255, 255, .8);
        }
      }
      .diablo-table{
        height: calc(100%);
      }
      .single_device{
        span{
          font-size: 14px;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: 400;
          color: rgba(255, 255, 255, .8);
          line-height: 24px;
        }
        .custom_select{
          .popper__arrow{
            border-bottom-color: rgba(64, 158, 255, 1);
          }
          .popper__arrow::after{
            // border-color: rgba(64, 158, 255, 1);
            border-bottom-color: rgba(19, 25, 30, .7);
          }
          .el-select-dropdown{
            background: rgba(19, 25, 30, .7);
            border: 1px solid rgba(64, 158, 255, 1);
            border-radius: 8px;
          }
        }
        .el-input {
          border-radius: 4px;

          .el-input__inner {
            background: rgb(3 23 81 / 50%);
            border: 1px solid #193382;
            color: #fff;
          }

          .el-input-group__append,
          .el-input-group__prepend {
            padding: 0 10px;
            background: rgb(3 23 81 / 50%);
            border-color: #193382;
            color: #fff;
          }
        }
      }
    }
    .alarm_num_echars{
      display: flex;
      height: 30%;
      .alarm_num_echars_left{
        width: 38%;
        padding: 20px 0;
        &_content{
          display: flex;
          align-items: center;
          .dialog_alarm_statistics{
            display: flex;
            flex-direction: column;
            margin-left: 30px;
            padding-right: 30px;
            border-right: 1px solid #112B62;
            p{
              font-size: 16px;
              font-family: Alibaba PuHuiTi-Regular, Alibaba PuHuiTi;
              font-weight: 400;
              color: #A2B7D9;
              margin: 0;
            }
            b{
              font-size: 28px;
              font-family: DIN-Bold, DIN;
              font-weight: bold;
              color: #EEFBFE;
              span{
                font-size: 14px;
                font-family: PingFang SC-Regular, PingFang SC;
                font-weight: 400;
                color: #CCCED3;
                margin-left: 5px;
              }
            }
          }
          .separate_status{
            display: flex;
            flex-direction: column;
            margin-left: 30px;
            div{
              display: flex;
              align-items: center;
              margin-bottom: 10px;
              p{
                font-size: 16px;
                font-family: Alibaba PuHuiTi-Regular, Alibaba PuHuiTi;
                font-weight: 400;
                color: #A2B7D9;
                margin: 0;
                margin-right: 10px;
              }
              b{
                font-size: 28px;
                font-family: DIN-Bold, DIN;
                font-weight: bold;
                color: #EEFBFE;
                span{
                  font-size: 14px;
                  font-family: PingFang SC-Regular, PingFang SC;
                  font-weight: 400;
                  color: #CCCED3;
                  margin-left: 5px;
                }
              }
            }
          }
        }
      }
      .alarm_num_echars_right{
        width: 62%;
      }
    }
    .date_time{
      display: flex;
      margin-bottom: 10px;
      .my-date-picker {
        padding: 0 10px;
        background: rgb(3 23 81 / 50%);
        border-color: #193382;
        color: #fff;
        border-radius: 4px;
        .el-range-input {
          background: rgb(3 23 81 / 50%);
          color: #fff;
        }
        .el-range-separator{
          color: rgba(255, 255, 255, .5);
        }
        .el-input__icon{
          color: #2181F4;
        }
      }
    }
    
  }
}
.echart-null {
  margin: 0 auto;
  height: calc(100% - 31px);
  width: 50%;
  text-align: center;
  color: #8a8c8f;

  img {
    max-width: 100%;
    max-height: calc(100% - 20px);
  }

  div {
    font-size: 14px;
  }
}
</style>