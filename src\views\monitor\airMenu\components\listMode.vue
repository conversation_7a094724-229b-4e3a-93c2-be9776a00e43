<template>
  <div ref="listMode" class="listMode">
    <div v-loading="loading" class="listMode-main">
      <el-row v-if="listData.length">
        <!-- <el-col v-for="item in listData" :key="item.surveyEntityCode" :xs="24" :sm="12" :md="24" :lg="12" :xl="8"> -->
        <el-col v-for="(item, index) in listData" :key="item.surveyEntityCode" :xs="24" :md="24" :lg="12" :xl="8">
          <el-tooltip :popper-options="{ removeOnDestroy: true }" effect="light" placement="right-start"
            :open-delay="500">
            <div class="card-content">
              <div class="card-heade">
                <div class="heade-info">
                  <p class="heade-name" @click="jumpMonitorDetail(item)">{{ item.surveyEntityName }}</p>
                  <span v-for="alarm in item.policeHistoryGroup" :key="alarm.alarmLevel" class="info-icon-box"
                    :style="{ display: !alarmType[alarm.alarmLevel] ? 'none' : '' }" @click.stop="viewAlarm(alarm)">
                    <svg-icon v-if="[0, 1, 2, 3].includes(alarm.alarmLevel)" :name="alarmType[alarm.alarmLevel]"
                      class="info-icon" />
                  </span>
                </div>
                <div class="heade-control">
                  <p class="btn-item" :class="{ 'btn-active': item.surveyStatus == 1 }">自动</p>
                  <p class="btn-item" :class="{ 'btn-active': item.surveyStatus == 0 }">手动</p>
                  <svg-icon v-if="item.status == 0" name="on_icon" class="on-icon" />
                  <svg-icon v-else-if="item.status == 1" name="off_icon" class="on-icon" />
                  <svg-icon v-else-if="item.status == 6" name="break_icon" class="on-icon" style="cursor: no-drop" />
                </div>
              </div>
              <div v-if="item.isOpen" class="card-main">
                <div class="main-left">
                  <div class="left-content">
                    <div
                      v-for="v in item.parameterList.slice((item.leftPaging.page - 1) * item.leftPaging.size, item.leftPaging.page * item.leftPaging.size)"
                      :key="v.parameterId" class="left-item">
                      <p class="item-label">
                        <svg-icon v-if="v.parameterIcon" :name="v.parameterIcon" class="item-icon" />
                        <span :style="{ width: v.parameterIcon ? '75%' : '100%' }">{{ v.parameterName }}</span>
                      </p>
                      <p class="item-value">
                        <span style="font-size: 18px">{{ v.parameterValue || '-' }}</span>
                        <span style="font-size: 12px">{{ v.parameterUnit || '' }}</span>
                      </p>
                    </div>
                  </div>
                  <div class="left-footer">
                    <div v-if="item.filterStatus.length" class="footer-box">
                      <span v-for="v in item.filterStatus" :key="v.label" :class="{ errorCol: v.value == 1 }">{{ v.label
                        }}/</span>效过滤网状态
                    </div>
                    <el-pagination layout="prev, pager, next" hide-on-single-page :page-size="item.leftPaging.size"
                      :current-page="item.leftPaging.page" :total="item.parameterList.length" @current-change="
                        (val) => {
                          pageChange(val, index, 'left')
                        }
                      ">
                    </el-pagination>
                  </div>
                </div>
                <div class="main-right">
                  <div v-if="item.controlParameterList.length" class="right-content">
                    <div
                      v-for="v in item.controlParameterList.slice((item.rightPaging.page - 1) * item.rightPaging.size, item.rightPaging.page * item.rightPaging.size)"
                      :key="v.parameterId" class="right-item">
                      <p class="item-label">
                        <svg-icon v-if="v.parameterIcon" :name="v.parameterIcon" class="item-icon" />
                        <span :style="{ width: v.parameterIcon ? '75%' : '100%' }">{{ v.parameterName }}</span>
                      </p>
                      <p v-if="v.child && v.child.length" class="item-value">
                        <el-select v-model="v.parameterValue" placeholder="请选择">
                          <el-option v-for="item in v.child" :key="item.value" :label="item.paramName"
                            :value="item.value"> </el-option>
                        </el-select>
                        <!-- <span style="font-size: 18px;">{{ v.parameterValue || '-' }}</span> -->
                        <!-- <span style="font-size: 12px;">{{ v.parameterUnit || '' }}</span> -->
                        <!-- <span v-if="item.surveyStatus == 0" class="control-btn">
                          <i class="el-icon-caret-top"></i>
                          <i class="el-icon-caret-bottom"></i>
                        </span> -->
                      </p>
                      <p v-else class="item-value">
                        <el-input v-model="v.parameterValue">
                          <el-button slot="append">{{ v.parameterUnit || '' }}</el-button>
                        </el-input>
                        <!-- <span style="font-size: 18px;">{{ v.parameterValue || '-' }}</span> -->
                        <!-- <span style="font-size: 12px;">{{ v.parameterUnit || '' }}</span> -->
                        <!-- <span v-if="item.surveyStatus == 0" class="control-btn">
                          <i class="el-icon-caret-top"></i>
                          <i class="el-icon-caret-bottom"></i>
                        </span> -->
                      </p>
                    </div>
                  </div>
                  <div v-else class="right-noData">
                    <img src="../../../../assets/images/monitor/noDataImg.png" />
                    <p>暂无可操作项</p>
                  </div>
                  <div class="right-footer">
                    <el-pagination layout="prev, pager, next" hide-on-single-page :page-size="item.rightPaging.size"
                      :current-page="item.rightPaging.page" :total="item.controlParameterList.length" @current-change="
                        (val) => {
                          pageChange(val, index, 'right')
                        }
                      ">
                    </el-pagination>
                  </div>
                  <el-button v-if="item.surveyStatus == 0" class="right-btn" type="primary" size="mini"
                    @click="application(item.surveyEntityCode, item.controlParameterList)">应用</el-button>
                </div>
              </div>
              <div v-else class="openCard" @click="openCard(index)">
                <span>
                  展开
                  <svg-icon name="down_icon" class="open-icon" />
                </span>
              </div>
              <div v-if="item.status == 6" class="card-mask"></div>
            </div>
            <div slot="content" class="planingImg" @click="viewPlaningImg(item)">
              <svg-icon name="planing_icon" class="planingImg-icon" />
              <span>剖面图</span>
            </div>
          </el-tooltip>
        </el-col>
      </el-row>
      <div v-else class="echart-null">
        <img src="@/assets/images/monitor/null.png" alt="" />
        <div>暂无数据~</div>
      </div>
    </div>
    <div class="listMode-footer">
      <el-pagination :current-page="pagination.current" :page-sizes="pagination.pageSizeOptions"
        :page-size="pagination.size" :layout="pagination.layoutOptions" :total="pagination.total"
        @size-change="paginationSizeChange" @current-change="paginationCurrentChange" />
    </div>
    <alarm-dialog v-if="alarmDialog" :visible.sync="alarmDialog" :alarmTypeItem="alarmTypeItem" />
    <el-dialog v-dialogDrag :modal="false" custom-class="model-dialog scada-dialog" width="60%" append-to-body
      title="剖面图" top="10vh" :visible.sync="scadaDialogShow" :close-on-click-modal="false" :before-close="() => {
          scadaDialogShow = false
        }
        ">
      <graphics-mode v-if="scadaDialogShow" :entityMenuCode="requestInfo.entityMenuCode"
        :surveyEntityCode="surveyEntityCode" scadaType="profile" :projectId="requestInfo.projectCode" />
    </el-dialog>
    <SwitchPromptDialog v-if="SwitchPromptDialog" :projectCode="requestInfo.projectCode"
      :visible.sync="SwitchPromptDialog" />
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import tableListMixin from '@/mixins/tableListMixin.js'
import alarmDialog from './alarmDialog'
import graphicsMode from './graphicsMode'
import SwitchPromptDialog from './switchPromptDialog.vue'
export default {
  name: 'listMode',
  components: {
    alarmDialog,
    graphicsMode,
    SwitchPromptDialog
  },
  mixins: [tableListMixin],
  data() {
    return {
      SwitchPromptDialog: false,
      alarmDialog: false,
      loading: false,
      requestInfo: {},
      listData: [],
      alarmType: {
        3: 'urgent_icon',
        2: 'serious_icon',
        1: 'commonly_icon',
        0: 'tips_icon'
      },
      alarmTypeItem: {},
      surveyEntityCode: '', // 剖面图选中实体编码
      scadaDialogShow: false // scada图弹窗
    }
  },
  computed: {
    ...mapGetters({
      socketIemcMsgs: 'socket/socketIemcMsgs'
    })
  },
  watch: {
    socketIemcMsgs(data) {
      let itemData = JSON.parse(data)
      let newList = JSON.parse(JSON.stringify(this.listData))
      newList.forEach((item) => {
        if (item.surveyEntityCode == itemData.surveyEntityCode) {
          item.leftPaging = { page: 1, size: 6 }
          item.rightPaging = { page: 1, size: 3 }
          Object.assign(item, itemData)
        }
      })
      this.listData = this.setData(newList)
    }
  },
  created() { },
  methods: {
    application(surveyEntityCode, controlParameterList) {
      console.log(surveyEntityCode, 'surveyEntityCode');
      console.log(controlParameterList, 'controlParameterList');


      let data = {
        imsCode: surveyEntityCode,
        paramList: controlParameterList.map((ele) => {
          return {
            paramId: ele.parameterId,
            value: ele.parameterValue
          }
        })
      }
      this.$api.setControl(data).then((res) => {
        this.getDataList()
      })
    },
    // 跳转监测历史趋势
    jumpMonitorDetail(item) {
      this.$router.push({
        path: '/airMenu/airMonitor/monitorDetails',
        query: {
          surveyCode: item.surveyEntityCode,
          surveyName: item.surveyEntityName,
          projectCode: this.requestInfo.projectCode,
          assetId: item.assetId
        }
      })
    },
    // 查看报警记录
    viewAlarm(item) {
      this.alarmTypeItem = { ...item, projectCode: this.requestInfo.projectCode }
      this.alarmDialog = true
    },
    // 查看刨面图
    viewPlaningImg(item) {
      if (item.imageId && item.imageId != '' && item.imageId != null) {
        this.surveyEntityCode = item.surveyEntityCode
        this.scadaDialogShow = true
      } else {
        this.$message.warning('暂无关联剖面图')
      }
    },
    // 获取检测项列表
    init(params) {
      this.pagination.current = 1
      this.requestInfo = params
      this.getDataList()
    },
    // 显示报警列表
    showAlarms(e, alarm) {
      console.log(e, alarm)
    },
    // 隐藏报警列表
    hideAlarms() { },
    // 获取检测项列表
    getDataList() {
      let params = {
        ...this.requestInfo,
        page: this.pagination.current,
        pageSize: this.pagination.size
      }
      this.loading = true
      this.listData = []
      this.$api.getRealMonitoringList(params).then((res) => {
        this.loading = false
        if (res.code == 200) {
          this.listData = res.data.list ? this.setData(res.data.list) : []
          this.pagination.total = res.data.totalCount ? res.data.totalCount : 0
        }
      })
    },
    setData(list) {
      let newObj = {
        2054: '初',
        2057: '中',
        2060: '高'
      }
      list.forEach((item) => {
        item.filterStatus = []
        if (!item.leftPaging) {
          item.leftPaging = { page: 1, size: 6 }
        }
        if (!item.rightPaging) {
          item.rightPaging = { page: 1, size: 3 }
        }
        item.isOpen = item.status != 1
        item.parameterList.forEach((v) => {
          // switch (v.parameterId) {
          //   case 2054:
          //     v.quoteValue = v.parameterValue == 0 ? '正常' : v.parameterValue == 1 ? '堵塞' : ''
          //     break
          //   case 2057:
          //     v.quoteValue = v.parameterValue == 0 ? '正常' : v.parameterValue == 1 ? '堵塞' : ''
          //     break
          //   case 2060:
          //     v.quoteValue = v.parameterValue == 0 ? '正常' : v.parameterValue == 1 ? '堵塞' : ''
          //     break
          //   case 2606:
          //     v.quoteValue = v.parameterValue == 0 ? '正常' : v.parameterValue == 1 ? '报警' : ''
          //     break
          //   case 2765:
          //     v.quoteValue = v.parameterValue == 0 ? '手动' : v.parameterValue == 1 ? '自动' : ''
          //     break
          //   case 2770:
          //     v.quoteValue = v.parameterValue == 0 ? '开' : v.parameterValue == 1 ? '关' : ''
          //     break
          //   case 2762:
          //     v.quoteValue = v.parameterValue == 0 ? '正常' : v.parameterValue == 1 ? '故障' : ''
          //     break
          //   default:
          //     v.quoteValue = v.parameterValue
          //     break
          // }
          if (newObj[v.parameterId]) {
            item.filterStatus.push({
              label: newObj[v.parameterId],
              value: v.parameterValue
            })
          }
        })
      })
      return list
    },
    // 展开
    openCard(index) {
      let newObj = JSON.parse(JSON.stringify(this.listData[index]))
      newObj.isOpen = true
      this.listData.splice(index, 1, newObj)
    },
    // 前端分页
    pageChange(page, index, type) {
      let newObj = JSON.parse(JSON.stringify(this.listData[index]))
      newObj[type + 'Paging'].page = page
      this.listData.splice(index, 1, newObj)
    }
  }
}
</script>
<style lang="scss" scoped>
.listMode {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;

  .listMode-main {
    overflow: auto;
    width: 100%;
    flex: 1;
    display: flex;
    flex-wrap: wrap;

    ::v-deep .el-loading-mask {
      left: 16px;
    }

    ::v-deep .el-row {
      width: 100%;

      .el-col {
        margin-top: 16px;
        padding-left: 16px;
      }
    }

    .echart-null {
      margin: 0 auto;
      width: 50%;
      text-align: center;
      color: #8a8c8f;
    }

    .card-content {
      width: 100%;
      background: #fff;
      border-radius: 4px;
      position: relative;

      p {
        margin: 0;
      }

      .card-heade {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        box-shadow: 0 0 5px 0 rgb(18 31 62 / 12%);

        // cursor: pointer;
        .heade-info {
          display: flex;
          align-items: center;

          .heade-name {
            cursor: pointer;
            font-size: 15px;
            color: #121f3e;
          }

          .info-icon-box {
            cursor: pointer;

            .info-icon {
              color: #fff;
              margin-left: 7px;
              font-size: 18px;
            }
          }
        }

        .heade-control {
          display: flex;
          align-items: center;

          .btn-item {
            cursor: pointer;
            margin-right: 8px;
            padding: 3px 0;
            text-align: center;
            min-width: 42px;
            font-size: 14px;
            line-height: 14px;
            color: #7f848c;
            background: #f6f5fa;
            border-radius: 2px;
            border: 1px solid #ededf5;
          }

          .btn-active {
            cursor: no-drop;
            background: #3562db;
            color: #fff;
            border-color: #3562db;
          }

          .on-icon {
            z-index: 1001;
            font-size: 20px;
            cursor: pointer;
          }
        }
      }

      .card-main {
        padding: 16px;
        height: 290px;
        display: flex;

        .main-left {
          width: 60%;
          height: 100%;
          border-right: 1px solid #dcdfe6;

          .left-content {
            height: 84%;
            width: 100%;
            display: flex;
            flex-wrap: wrap;
          }

          .left-footer {
            height: 16%;
            display: flex;
            justify-content: space-between;
            position: relative;

            ::v-deep .el-pagination {
              padding: 0;
              position: absolute;
              right: 16px;
              bottom: 0;
            }
          }

          .footer-box {
            font-size: 14px;

            span {
              position: relative;

              &::after {
                content: '';
                display: inline-block;
                position: absolute;
                bottom: -16px;
                left: 40%;
                transform: translateX(-50%);
                width: 10px;
                height: 10px;
                border-radius: 50%;
                background: #00bc6d;
              }
            }

            .errorCol::after {
              background: #fa403c;
            }
          }

          .left-item {
            width: 50%;
          }
        }

        .main-right {
          width: 40%;
          height: 100%;

          .right-content {
            height: 75%;
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            padding-left: 24px;
          }

          .right-noData {
            height: 75%;
            width: 100%;
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: center;

            img {
              width: 80%;
            }

            p {
              margin-top: 10px;
              color: #ccced3;
              font-size: 12px;
            }
          }

          .right-item {
            width: 100%;
          }

          .right-btn {
            height: 9%;
            font-size: 12px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 auto;
          }

          .right-footer {
            height: 16%;
            display: flex;
            justify-content: space-between;
            position: relative;

            ::v-deep .el-pagination {
              padding: 0;
              position: absolute;
              right: 0;
              bottom: 0;
            }
          }
        }

        .item-label {
          font-size: 14px;
          color: #414653;
          display: flex;
          align-items: center;

          span {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .item-value {
          margin-top: 6px;
          font-weight: 600;
          position: relative;
          color: #121f3e;
          display: flex;

          .control-btn {
            position: absolute;
            display: flex;
            flex-direction: column;
            color: #c0c4cc;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 14px;

            i {
              cursor: pointer;
            }

            i:hover {
              color: #3562db;
            }

            .el-icon-caret-top,
            .el-icon-caret-bottom {
              margin-top: -2px;
              margin-bottom: -2px;
            }
          }
        }

        .item-icon {
          font-size: 20px;
          margin-right: 4px;
        }

        ::v-deep .el-pagination {

          .btn-next,
          .btn-prev {
            min-width: auto;
            height: 21px;
            line-height: 21px;

            .el-icon {
              line-height: 21px;
            }
          }

          .btn-prev {
            padding: 0 2px 0 0;
          }

          .btn-next {
            padding: 0 0 0 2px;
          }

          .el-pager>li {
            min-width: auto;
            height: auto;
            line-height: 21px;
            font-size: 13px;
            padding: 0 2px;
          }
        }
      }

      .openCard {
        display: flex;
        justify-content: center;
        padding-top: 10px;

        span {
          cursor: pointer;
          padding: 3px 10px;
          display: flex;
          align-items: center;
          background: rgb(53 98 219 / 20%);
          border-radius: 4px 4px 0 0;
          font-size: 14px;
          color: #3562db;
        }

        .open-icon {
          font-size: 10px;
          margin-left: 6px;
        }
      }

      .card-mask {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgb(255 255 255 / 80%);
        z-index: 1000;
      }
    }
  }

  .listMode-footer {
    padding: 10px 0 10px 16px;

    ::v-deep .el-pagination {

      .btn-next,
      .btn-prev {
        background: transparent;
      }

      .el-pager li {
        background: transparent;
      }
    }
  }
}
</style>
<style lang="scss">
.planingImg {
  cursor: pointer;
  font-size: 14px;
  color: #7f848c;
  display: flex;
  align-items: center;

  .planingImg-icon {
    font-size: 12px;
    margin-right: 4px;
  }
}

.el-tooltip__popper.is-light {
  /* border-color: #fff !important;
  padding: 9px 8px !important; */
}

.scada-dialog {
  height: 80%;

  .el-dialog__body {
    max-height: none;
    height: calc(100% - 60px);
  }
}
</style>
