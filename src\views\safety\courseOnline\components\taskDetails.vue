<template>
    <PageContainer>
      <div slot="content" class="role-content" style="height: 100%">
        <div class="topFilter">
          <div class="backBar">
            <span style="cursor: pointer" @click="$router.go(-1)">
              <i class="el-icon-arrow-left"></i>
              我的任务详情
            </span>
          </div>
        </div>
        <!-- 基础信息  -->
        <div class="baseInfo">
             <div class="study_name">
                <span>{{formInfo.name}}</span>
                <span class="study_status">{{formInfo.taskStatusPc=='0'?'未开始':formInfo.taskStatusPc=='1'?'学习中':formInfo.taskStatusPc=='2'?'已完成':formInfo.taskStatusPc=='3'?'已超时':'即将超时'}}</span>
             </div>
             <div class="contenter">
             <div class="center">
               <div class="item">
                 <span class="key">所属部门：</span>
                 <span class="label">{{formInfo.deptName}}</span>
               </div>
               <div class="item">
                 <span class="key">任务时间：</span>
                 <span class="label">{{ formInfo.startTime }} 至 {{ formInfo.endTime }}</span>
               </div>
               <div class="item">
                 <span class="key">所属科目：</span>
                 <span class="label">{{formInfo.subjectName}}</span>
               </div>
             </div>
             </div>
        </div>
        <div class="center">
          <el-tabs v-model="activeName">
            <el-tab-pane label="课程内容" name="0"></el-tab-pane>
            <el-tab-pane label="考试内容" name="1"></el-tab-pane>
            <el-tab-pane label="培训内容" name="2"></el-tab-pane>
          </el-tabs>
          <div class="courseInfo" v-if="activeName=='0'">
            <h1>课程内容</h1>
            <el-row type="flex" class="courseItem" justify="space-between"  v-for="(item,index) in formInfo.courseList" :key="index">
              <el-col :span="6">
                <div class="course_name">
                  <img src="../../../../assets/images/icon_course.png" alt="">
                  {{item.courseName}}
                </div>
              </el-col>
              <el-col :span="6">
                  <span class="title">课程类型：</span>
                  <span>{{item.subjectName}}</span>
              </el-col>
              <el-col :span="4">
                  <span class="title">课时数：</span>
                  <span>{{item.periodCount}}课时</span>
              </el-col>
              <el-col :span="2">
                 <span class="title">创建人：</span>
                 <span>{{item.createName}}</span>
              </el-col>
              <el-col :span="2">
                <span class="operate" @click="courseDetails(item)">查看</span> 
                <span class="operate" v-if="item.learnStatus=='0'" @click="courseDetails(item)">立即学习</span> 
              </el-col>
            </el-row>
            <div >
            </div>
          </div>
          <div class="courseInfo" v-if="activeName=='1'">
            <h1>考试内容</h1>
            <el-row type="flex" class="courseItem" justify="space-between"  v-for="(item,index) in formInfo.examList" :key="index">
              <el-col :span="6">
                <div class="course_name">
                  <img src="../../../../assets/images/icon_exam.png" alt="">
                  {{item.examName}}
                </div>
              </el-col>
              <el-col :span="4">
                  <span class="title">考试时间：</span>
                  <span>{{item.startTime}} 至 {{ item.endTime}}</span>
              </el-col>
              <el-col :span="4">
                  <span class="title">考试时长：</span>
                  <span>{{item.duration}}</span>
              </el-col>
              <el-col :span="2">
                 <span class="title">考试题数：</span>
                 <span>{{item.questionNum}}</span>
              </el-col>
              <el-col :span="2">
                 <span class="title">通过分数：</span>
                 <span>{{item.passScore}}</span>
              </el-col>
            </el-row>
            <div>
            </div>
          </div>
          <div class="courseInfo" v-if="activeName=='2'">
            <h1>培训内容</h1>
            <el-row type="flex" class="courseItem" justify="space-between"  v-for="(item,index) in formInfo.trainList" :key="index">
              <el-col :span="6">
                <div class="course_name">
                  <img src="../../../../assets/images/icon_train.png" alt="">
                  {{item.trainName}}
                </div>
              </el-col>
              <el-col :span="4">
                  <span class="title">培训时间：</span>
                  <span>{{item.startTime}} 至 {{ item.endTime}}</span>
              </el-col>
              <el-col :span="4">
                  <span class="title">培训老师：</span>
                  <span>{{item.teacherName}}</span>
              </el-col>
              <el-col :span="4">
                 <span class="title">培训地点：</span>
                 <span>{{item.address}}</span>
              </el-col>
              <el-col :span="2">
                <div class="operate" @click="trainDetails(item)">查看</div> 
              </el-col>
            </el-row>
            <div >
            </div>
          </div>
        </div>
      </div>
    </PageContainer>
  </template>
  <script>
  import moment from "moment";
  export default {
    data() {
      return {
        routeInfo:{},
        id:'',
        type:'',
        moment,
        formInfo:{},
        activeName:'0'
      };
    },
    created() {
      this.routeInfo= JSON.parse(sessionStorage.getItem('routeInfo'))
      this.id=this.$route.query.id||''
      if(this.id){
        this.getQuestionsDetails()
      }
    },
    methods: {
      // 获取课程详情
      getQuestionsDetails(){
        this.$api.getNyTaskInfo({id:this.id,userId:this.routeInfo.userId}).then(res=>{
          if(res.code=='200'){
             this.formInfo = res.data
          }else{
            this.$message.error(res.msg)
          }
        })
      },
      //课程详情
      courseDetails(item){
        this.$router.push({
          path:'courseDetils',
          query:{
            id:item.id
          }
        })
      },
      //培训详情
      trainDetails(item){
        this.$router.push({
        path: 'trainPlanDetail',
        query: {
          id: item.trainId,
        }
      })
      },
    },
  };
  </script>
  <style>
  .process-tooltip {
    width: 712px;
    height: 528px;
  }
  </style>
  <style lang="scss" scoped>
  .topFilter {
    padding: 15px;
    height: 60px;
    background-color: #fff;
    .backBar {
      color: #121f3e;
      height: 30px;
      border-bottom: 1px solid #dcdfe6;
    }
  }
  .baseInfo {
    background-color: #fff;
    padding:16px 24px;
    margin-bottom: 16px;
    .contenter {
      padding-top: 24px;
      display: flex;
      font-size: 14px;
      .center {
        flex: 1;
        margin: 0 24px;
      }
      .item {
        display: flex;
        .key {
          color: #666;
          width: 120px;
          text-align: left;
        }
        .label{
          flex: 1;
          line-height: 20px;
        }
      }
      .item:nth-child(2) {
        margin: 16px 0;
      }
    }
    .study_name {
      display: flex;
      align-items: center;
       .study_status{
        width: 60px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        font-size: 14px;
        background-color: #FFECE8;
        color: #CB2634;
        margin-left: 10px;
       } 
    }
  }
  .center{
    padding:16px 24px;
    background-color: #fff;
    height: calc(100% - 200px);
    margin-bottom: 16px;
    font-size: 14px;
    .courseInfo{
      height: calc(100% - 20px);
      overflow: auto;
      h1{
        font-size: 16px;
        margin: 16px 0;
      }
      .courseItem {
        height: 50px;
        background-color: #FAF9FC;
        align-items: center;
        margin-bottom: 16px;
        padding-left:24px;
        .course_name {
          display: flex;
          align-items: center;
          img{
            margin-right: 10px;
          }
        }
        .title {
          color: #7F848C;
        }
        .operate {
          color: #3562DB;
          cursor: pointer;
          margin-right: 10px;
        }
      }
    }
  }
  </style>
   