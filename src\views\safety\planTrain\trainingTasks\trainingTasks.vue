<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%">
      <div class="content_top">
        <el-tabs @tab-click="handleClick" class="tabsMenu">
          <el-tab-pane v-for="(item, index) in tabsList" :key="index" :label="item.name"
            :class="{ active: activeIndex == index }"></el-tab-pane>
        </el-tabs>
        <!-- 培训任务 -->
        <div class="content_box" v-if="activeIndex == '0' || activeIndex == '2'">
          <div class="open_conter">
            <div class="seachTop">
              <el-input v-model="seachForm.name" style="width: 200px; margin-right:16px" placeholder="请输入课程名称">
              </el-input>
              <el-cascader ref="myCascader" v-model="seachForm.deptIds" :options="deptList" :props="deptTree"
                placeholder="请选择所属部门" style="width: 200px; margin-right: 10px"></el-cascader>
              <el-input v-model="seachForm.teacherName" style="width: 200px; margin: 0 16px" placeholder="请输入培训老师">
              </el-input>
              <el-select v-model="seachForm.taskStatus" placeholder="请选择培训状态">
                <el-option v-for="item in statusList" :key="item.id" :label="item.label" :value="item.id"></el-option>
              </el-select>
              <el-date-picker v-model="timeLine" type="daterange" value-format="yyyy-MM-dd" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" style="width: 260px; margin: 0 16px">
              </el-date-picker>
              <el-input v-model="seachForm.principalName" style="width: 200px; margin-right:16px" placeholder="请输入负责人员">
              </el-input>
              <el-button type="primary" plain @click="resetForm">重置</el-button>
              <el-button type="primary" @click="search">查询</el-button>
            </div>
            <div>
              <el-button type="primary" @click="addTrain">录入培训记录</el-button>
              <el-button type="primary" v-if="activeIndex=='0'" :disabled="multipleSelection.length<1" @click="exportClickExport">导出</el-button>
            </div>
            <div class="table">
              <el-table ref="taskTable" v-loading="tableLoading" :data="trainData" style="width: 100%;" height="100%" border stripe @selection-change="handleSelectionChange"
                title="双击查看详情">
                <el-table-column type="selection" width="55" fixed="left" align="center"></el-table-column>
                <el-table-column prop="name" label="培训名称" width="120" show-overflow-tooltip
                  align="center"></el-table-column>
                <el-table-column prop="deptName" label="所属单位" show-overflow-tooltip align="center"></el-table-column>
                <el-table-column prop="subjectName" label="所属科目" width="120" show-overflow-tooltip
                  align="center"></el-table-column>
                <el-table-column prop="address" label="培训时间" show-overflow-tooltip align="center">
                  <template slot-scope="scope">
                    <span>{{ scope.row.startTime }} - {{ scope.row.startTime }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="培训方式" width="120" show-overflow-tooltip align="center">
                  <template slot-scope="scope">
                    <span>{{ scope.row.type == 0 ? '线上培训' : '线下培训' }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="address" label="培训地点" show-overflow-tooltip align="center"></el-table-column>
                <el-table-column prop="name" label="考试内容" show-overflow-tooltip align="center"></el-table-column>
                <el-table-column prop="taskStatus" label="培训状态" show-overflow-tooltip align="center">
                  <template slot-scope="scope">
                    <span>{{scope.row.taskStatus | filterStatus}}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="principalName" label="负责人员" show-overflow-tooltip align="center"></el-table-column>
                <el-table-column label="操作" show-overflow-tooltip width="240" align="center">
                  <template slot-scope="scope">
                    <el-button type="text" @click="examineTrain(scope.row)">查看</el-button>
                    <el-button type="text" v-if="activeIndex == '0'&&scope.row.type == 1" @click="downloadQR(scope.row)">签到二维码</el-button>
                    <el-button type="text" v-if="activeIndex == '0'&& (scope.row.taskStatus=='1'||scope.row.taskStatus=='2')" @click="uploadInformation(scope.row)">上传资料</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="contentTable-footer">
              <el-pagination style="margin-top: 3px" :current-page="paginationData.pageNo"
                layout="total, sizes, prev, pager, next, jumper" :total="paginationData.total"
                :page-size="paginationData.pageSize" :page-sizes="[15, 30, 50, 100]" @size-change="handleSizeChange"
                @current-change="handleCurrentChange"></el-pagination>
            </div>
          </div>
        </div>
        <!-- 会议任务 -->
        <div class="content_box" v-if="activeIndex =='1'||activeIndex == '3'">
          <div class="open_conter">
            <div class="seachTop">
              <el-input v-model="meetingSeachForm.name" style="width: 200px; margin-right:16px" placeholder="会议计划名称">
              </el-input>
              <el-cascader v-model="meetingSeachForm.deptId" placeholder="请选择所属部门" :options="deptList" :props="deptTree"
                :show-all-levels="false" clearable filterable collapse-tags style="width: 300px; margin: 0 16px">
              </el-cascader>
              <el-select v-model="meetingSeachForm.taskStatus" placeholder="请选择会议状态">
                <el-option v-for="item in statusList" :key="item.id" :label="item.label" :value="item.id"></el-option>
              </el-select>
              <el-date-picker v-model="timeLine" type="daterange" value-format="yyyy-MM-dd" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" style="width: 260px; margin: 0 16px">
              </el-date-picker>
              <el-button type="primary" plain @click="resetForm">重置</el-button>
              <el-button type="primary" @click="search">查询</el-button>
            </div>
            <div>
              <el-button type="primary" @click="addConferences">录入会议记录</el-button>
              <el-button type="primary" v-if="activeIndex=='1'" :disabled="multipleSelection.length<1" @click="exportClickExport">导出</el-button>
            </div>
            <div class="table">
              <el-table v-loading="tableLoading" :data="conferenceData" style="width: 100%;" height="100%" border stripe @selection-change="handleSelectionChange"
                title="双击查看详情">
                <el-table-column type="selection" width="55" fixed="left" align="center"></el-table-column>
                <el-table-column prop="name" label="培训名称" width="120" show-overflow-tooltip
                  align="center"></el-table-column>
                <el-table-column prop="deptName" label="所属单位"  align="center" width="120"></el-table-column>
                <el-table-column  label="会议时间" width="200" show-overflow-tooltip align="center">
                  <template slot-scope="scope">
                    <span>{{ scope.row.startTime }} - {{ scope.row.endTime }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="address" label="会议方式" show-overflow-tooltip align="center">
                  <template slot-scope="scope">
                    <span>{{ scope.row.type == 0 ? '线上会议' : '线下会议' }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="name" label="会议地点" width="120" show-overflow-tooltip
                  align="center"></el-table-column>
                <el-table-column prop="taskStatus" label="会议状态" show-overflow-tooltip align="center">
                  <template slot-scope="scope">
                    <span>{{scope.row.taskStatus | filterStatus}}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="principalName" label="负责人员" show-overflow-tooltip align="center"></el-table-column>
                <el-table-column label="操作" show-overflow-tooltip align="center">
                  <template slot-scope="scope">
                    <el-button type="text" @click="examineConference(scope.row)">查看</el-button>
                    <el-button type="text" v-if="activeIndex == '1'&&scope.row.type == 1">签到二维码</el-button>
                    <el-button type="text" v-if="activeIndex == '1'&& (scope.row.taskStatus=='1'||scope.row.taskStatus=='2')" @click="uploadInformation(scope.row)">上传资料</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="contentTable-footer">
              <el-pagination style="margin-top: 3px" :current-page="paginationData.pageNo"
                layout="total, sizes, prev, pager, next, jumper" :total="paginationData.total"
                :page-size="paginationData.pageSize" :page-sizes="[15, 30, 50, 100]" @size-change="handleSizeChange"
                @current-change="handleCurrentChange"></el-pagination>
            </div>
          </div>
        </div>
      </div>
      <!-- 记录上传 -->
      <el-dialog
        class="changeStatusDialog"
        title="记录上传"
        :visible.sync="dialogVisible"
        width="50%"
        :before-close="handleClose"
      >  
      <div class="contenter">
          <div class="itemInfo">
            <span class="title">{{activeIndex == '0'?'培训':'会议'}}名称：</span>
            <span class="value">{{itemInfo.name}}</span>
          </div>
          <div class="itemInfo">
            <span class="title">{{activeIndex == '0'?'培训':'会议'}}类型：</span>
            <span class="value">{{itemInfo.type =='0'?'线上会议':'线下培训'}}</span>
          </div>
          <div class="itemInfo">
            <span class="title">{{activeIndex == '0'?'培训':'会议'}}老师：</span>
            <span class="value">{{itemInfo.teacherName}}</span>
          </div>
          <div class="itemInfo">
            <span class="title">{{activeIndex == '0'?'培训':'会议'}}时间：</span>
            <span class="value">{{itemInfo.startTime}}至{{itemInfo.endTime}}</span>
          </div>
        </div>
        <el-form
          label-width="140px"
          :model="formInfo"
          :rules="rules"
          ref="formInfo"
          class="formInfo"
        >
          <el-form-item :label="`${activeIndex == '0'?'培训':'会议'}记录上传`" prop="fileQuestions">
            <el-upload
              ref="uploadFile2"
              drag
              multiple
              class="mterial_file file"
              action="string"
              :file-list="formInfo.fileQuestions"
              :http-request="httpRequest"
              accept=".excel,.xlsx"
              :limit="30"
              :on-exceed="handleExceed"
              :before-upload="beforeAvatarUpload"
              :on-remove="handleRemove"
              :on-change="fileChange"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text" style="top: 100px">
                将文件拖到此处，或
                <em>点击上传</em>
              </div>
              <div slot="tip" class="el-upload__tip">支持上传单个Excel文件</div>
            </el-upload>
          </el-form-item>
          <el-form-item :label="`${activeIndex == '0'?'培训':'会议'}模板描述：`" prop="remark">
            <el-input v-model.trim="formInfo.remark" type="textarea" :autosize="{ minRows: 2, maxRows: 4 }"
              placeholder="请输入培训模板描述:" maxlength="200" show-word-limit style="width: 600px;"></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="handleClose">取消</el-button>
          <el-button type="primary" @click="batchOk">确认</el-button>
        </span>
      </el-dialog>
      <!-- 导出 -->
      <exportDialog
        :exportType="exportType"
        :materRows="multipleSelection"
        :activeName="activeIndex"
        :dialogVisibleExport="dialogVisibleExport"
        @closeDialog="closeDialog"
        ref="dialogExport"
      ></exportDialog>
    </div>
  </PageContainer>
</template>

<script type="text/ecmascript-6">
import moment from 'moment'
import axios from 'axios'
import exportDialog from "@components/recordExport/recordExport";  //导出
export default {
  name: 'feedbackRecord',
  components: {exportDialog},
  data() {
    return {
      moment,
      trainData: [], // 培训任务列表
      conferenceData: [], // 会议任务列表
      tableLoading: false,
      routeInfo: {},
      activeName: '0',
      props: {
        children: "childList",
        label: "name",
        value: "id",
        checkStrictly: true,
      },
      seachForm: {
        name: "",
        deptIds: "",
        teacherName: '',
        taskStatus: '',
        startTime: '',
        endTime: '',
        principalName: ''
      },
      meetingSeachForm:{
        name:'',
        deptId:'',
        taskStatus:'',
        startTime:'',
        endTime:''
      },
      deptTree: {
        children: "children",
        label: "teamName",
        value: "id",
        multiple: true,
        emitPath: false,
      },
      tabsList: [
        {
          name: '培训任务',
          value: '0',
        },
        {
          name: '会议记录',
          value: '1',
        },
        {
          name: '参加的培训',
          value: '2',
        },
        {
          name: '参加的会议',
          value: '3 ',
        }
      ],
      deptList: [],
      timeLine: [],
      statusList: [
        {
          id: '0',
          label: '未签到'
        },
        {
          id: '1',
          label: '未上传资料'
        },
        {
          id: '2',
          label: '超时'
        },
        {
          id: '3',
          label: '未开始'
        },
        {
          id: '4',
          label: '已签到'
        },
        {
          id: '5',
          label: '已完成'
        }
      ],
      paginationData: {
        pageNo: 1,
        pageSize: 15,
        total: 0
      },
      activeIndex: '0',
      dialogVisible: false,
      
      formInfo:{
        fileQuestions: [],
        disc:''
      },
      rules:{
        fileQuestions:[{ required: true, message: "请上传培训记录文件", trigger: "change"}],
        remark: [
          { required: true, message: "请输入培训备注", trigger: "blur" },
        ],
      },
      itemInfo:{},
      fileIds:'',
      multipleSelection:[],
      exportType:3,
      dialogVisibleExport:false
    }
  },
  filters:{
    filterStatus(val){
      return val=='0'?'未签到':val=='1'?'未上传资料':val=='2'?'超时':val=='3'?'未开始':val=='4'?'已签到':'已完成'
    },
  },
  watch: {
    timeLine(val) {
      if (val.length > 0) {
        this.seachForm.startTime = val[0];
        this.seachForm.endTime = val[1];
      } else {
        this.seachForm.startTime = "";
        this.seachForm.endTime = "";
      }
    },
  },
  created(){
    if (this.$route.query) {
      sessionStorage.setItem("routeInfo", JSON.stringify(this.$route.query));
      this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
    }
    this.getdeptList() 
    this.getTrainList()
  },
  methods: {
    // 获取组织
    getdeptList() {
      this.$api.getDeptListLaboratory({}).then((res) => {
        this.deptList = this.$tools.transData(
          res.data.list,
          "id",
          "parentId",
          "children"
        );
      });
    },
    // ---------------------------------------------------------------- 培训任务 -------------------------------------------------------------------------------------------
    // 培训列表
    getTrainList() {
      this.tableLoading = true
      let data = {
        current: this.paginationData.pageNo,
        size: this.paginationData.pageSize,
        ...this.seachForm,
        listType: this.activeIndex == '0'?'0':'1'
      }
      data.deptIds = data.deptIds?data.deptIds.join(','):''
      this.$api.trainTasksList(data).then(res => {
        if (res.code == 200) {
          this.trainData = res.data.list
          this.paginationData.total = res.data.total
        } else {
          this.$message.error(res.msg)
        }
        this.tableLoading = false
      })
    },
    // 创建培训记录
    addTrain() {
      this.$router.push({
        path: '/addTrainlate',
      })
    },
    // 创建会议记录
    addConferences() {
      this.$router.push({
        path: '/addConference',
      })
    },
    // 查看详情
    examineTrain(row) {
      this.$router.push({
        path: '/trainDetails',
        query: {
          id: row.taskId,
          activeFlag:this.activeIndex
        }
      })
    },
   
    // -------------------------------------------------------------- 会议记录 ---------------------------------------------------------------------------------------------
    // 会议记录列别
    getTonferenceList() {
      this.tableLoading = true
      let data = {
        current: this.paginationData.pageNo,
        size: this.paginationData.pageSize,
        ...this.meetingSeachForm,
        listType: this.activeIndex == '1'?'0':'1'
      }
      this.$api.conferenceList(data).then(res => {
        if (res.code == 200) {
          this.conferenceData = res.data.list
          this.paginationData.total = res.data.total
        } else {
          this.$message.error(res.msg)
        }
        this.tableLoading = false
      })
    },
     // 查看会议详情
     examineConference(row) {
      this.$router.push({
        path: '/conferenceDetails',
        query: {
          id: row.taskId,
          activeFlag:this.activeIndex
        }
      })
    },
    // 切换tabs
    handleClick(tab, event) {
      this.activeIndex = tab.index
      this.resetForm()
    },
    resetForm() {
      this.paginationData.pageNo = 1
      this.paginationData.pageSize = 15
      this.timeLine = []
      if(this.activeIndex == '0'|| this.activeIndex == '2'){
        this.seachForm = {
          name: "",
          deptIds: "",
          teacherName: '',
          taskStatus: '',
          startTime: '',
          endTime: '',
          principalName: ''
        }
        this.getTrainList()
      }else{
        if(this.activeIndex == '1'|| this.activeIndex == '3'){
          this.meetingSeachForm = {
            name:'',
            deptId:'',
            taskStatus:'',
            startTime:'',
            endTime:''
          }
          this.getTonferenceList()
        }
      }
      
    },
    search() {
      this.paginationData.pageNo = 1
      if(this.activeIndex == '0' || this.activeIndex == '2'){
        this.getTrainList()
      }else{
        this.getTonferenceList()
      }
      
    },
    // 查看详情
    getDetils() {
      this.$router.push('/courseDetils')
    },
    handleCurrentChange(val) {
      this.paginationData.pageNo = val
      if(this.activeIndex == '0'|| this.activeIndex == '2'){
        this.getTrainList()
      }else{
        this.getTonferenceList()
      }
    },
    // 每页的条数
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.pageNo = 1
      if(this.activeIndex == '0'|| this.activeIndex == '2'){
        this.getTrainList()
      }else{
        this.getTonferenceList()
      }
    },
    uploadInformation(row){
      this.itemInfo = row
      console.log(row,'row');
      this.dialogVisible = true
    },
    // 上传资料
    handleClose(){
      this.formInfo.fileQuestions = []
      this.formInfo.remark = ''
      this.dialogVisible = false
    },
    batchOk(){
      let data = {
        id:this.itemInfo.planId,
        material:this.fileIds,
        remark:this.formInfo.remark
      }
      let  str = this.activeIndex=='1'?'uploadMaterial':'conferenceUploadMaterial'
      this.$api[str](data).then(res => {
        if (res.code == 200) {
          if(this.activeIndex == '0'|| this.activeIndex == '2'){
            this.getTrainList()
          }else{
            this.getTonferenceList()
          }
          this.handleClose()
        } else {
          this.$message.error(res.data.msg)
        }
      })
      
    },
    fileChange(file, fileList,val) {
      this.formInfo.fileQuestions = fileList
    },
    httpRequest() {
      this.formData = new FormData()
      this.formInfo.fileQuestions.forEach((item) => {
        this.formData.append('file', item.raw)
      })
      this.formData.append('hospitalCode', this.routeInfo.hospitalCode)
      axios({
        method: 'post',
        url: __PATH.BASE_URL_LABORATORY + "minio/upload",
        data: this.formData,
        headers: {
          "Content-Type": "application/json",
          token: this.routeInfo.token,
        },
      }).then(res => {
        if(res.data.code=='200'){
          this.fileIds = res.data.data.fileRecordIds
        }else{
          this.$message.error(res.data.msg)
        }
      }).catch(() => {
        this.$message.error(res.data.msg)
      })
    },
    handleExceed() {
      this.$message.error("最多上传30个附件");
    },
    beforeAvatarUpload(file) {
      if (file.name.indexOf(",") != -1) {
        this.$message.error("非法的文件名");
        return false;
      }
    },
    handleRemove(file, fileList,val) {
      this.formInfo.fileQuestions = fileList;
    },
    //勾选
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 导出
    exportClickExport(){
      this.dialogVisibleExport = true;
      this.$nextTick(()=>{
        if(this.activeIndex=='0'){
          this.$refs.dialogExport.exportType = 3
        }else{
          this.$refs.dialogExport.exportType = 4
        }
        let str = this.activeIndex == '0'?'0':'1'
        this.$refs.dialogExport.getExportField(str)
      })
    },
    closeDialog() {
      this.dialogVisibleExport = false;
    },
    downloadQR(row){
      console.log(row,'row');
      let params = {
        taskId: row.taskId,
      }
      axios({
        method: 'post',
        url: __PATH.BASE_URL_LABORATORY + '/trainPlan/QRDownload',
        data: params,
        responseType: 'blob',
        headers: {
          "Content-Type": "application/json",
          token:this.routeInfo.token||''
        }
      })
        .then((res) => {
          console.log(res,'res');
          console.log(JSON.stringify(res.data));
          let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
          let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          // 释放这个临时的对象url
          window.URL.revokeObjectURL(url)
        })
        .catch((res) => {
          this.$message.error('下载失败！')
        })
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.el-tabs--left) {
  width: 200px;
  margin: 20px auto 0 auto;
}

.content_top {
  height: 100%;
  border-radius: 10px;
  padding: 16px;
  background: #fff;
  display: flex;
  flex-direction: column;

  .tabsMenu {
    margin-bottom: 15px;

    :deep(.el-tabs__item) {
      width: 120px;
    }
  }

  .top_content {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
}

.content_box {
  height: 100%;
}

.open_conter {
  height: 100%;

  .seachTop {
    margin-bottom: 16px;
    font-size: 14px;
  }

  .table {
    height: calc(100% - 150px);
    margin-top: 16px;
    overflow: auto;
    font-size: 14px;
  }
}

:deep(.el-tabs__nav) {
  width: 100%;

  .el-tabs__item {
    padding: 0 10px;
    width: 50%;
    text-align: center;
  }
}

.item {
  // width: 372px;
  margin-bottom: 30px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e6eb;
  display: flex;
  flex-direction: column;

  .text {
    padding: 16px 24px 10px 24px;
    font-family: PingFang SC-Regular, PingFang SC;

    .item_bottom {
      height: 40px;
      line-height: 40px;
      padding-top: 10px;
      border-top: 1px solid #e4e4e4;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #999999;

      .collect {
        width: 58px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        background-color: #f2f4f9;
      }

      .studyIng {
        width: 68px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        background-color: #d7e0f8;
        color: #3562db;
      }

      .studyOK {
        width: 79px;
        height: 24px;
        line-height: 24px;
        margin-left: 20px;
        background: #e8ffea;
        border-radius: 4px;
        color: #009a29;
        text-align: center;

        img {
          vertical-align: middle;
          margin-right: 8px;
        }
      }
    }
  }
}

.course_cove {
  width: 100%;
  height: 172px;
  border-radius: 8px 8px 0px 0px;
  position: relative;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .status-ing {
    width: 72px;
    height: 24px;
    background: #f53f3f;
    opacity: 1;
    font-size: 14px;
    color: #fff;
    line-height: 24px;
    text-align: center;
    border-radius: 4px;
    position: absolute;
    left: 8px;
    top: 8px;
  }
}

.headline {
  height: 22px;

  display: flex;

  p {
    font-size: 16px;
    line-height: 22px;
    color: #333333;
    font-weight: 600;
    overflow: hidden;

    white-space: nowrap;
  }

  .draft {
    width: 44px;
    height: 100%;
    background: #fff7e8;
    border-radius: 4px;
    color: #d25f00;
    font-size: 14px;
    line-height: 22px;
    margin-right: 8px;
  }
}

.introduce {
  margin: 6px 0;
  font-size: 12px;
  color: #999999;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.remark {
  margin-bottom: 20px;
  font-size: 14px;
  color: #666666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.course_time {
  .course_bottom {
    i {
      color: #3562db;
      font-size: 14px;
    }

    .el-icon-edit {
      margin-right: 10px;
    }
  }
}

// minibutton
.miniBtn {
  color: #fff;
  border-color: #3562db;
  background: #3562db;
  font-weight: 400;
  font-size: 14px;
  padding: 6px 16px;
  font-family: PingFangSC-Regular;
}

.miniBtn:hover {
  color: #fff;
  font-family: PingFangSC-Regular;
  border-color: rgba(53, 98, 219, 0.8);
  background-color: rgba(53, 98, 219, 0.8);
  font-weight: 500;
}

/deep/ .el-cascader .el-input .el-input__inner {
  min-height: 32px !important;
}

/deep/ .el-input__inner {
  height: 32px !important;
}
.contenter {
      padding-top: 24px;
      font-size: 14px;
      display: flex;
      flex-wrap: wrap;
      .itemInfo {
        width: 33.3%;
        margin-bottom: 16px;
        display: flex;
        .title {
          width: 100px;
          color: #666;
          margin-top: 3px;
        }

        .value {
          flex: 1;
          line-height: 20px;
        }
      }
    }
</style>
