<script>
import { UsingStatusOptions } from '@/assets/common/constant'
export default {
  name: 'DictionaryValueEdit',
  props: {
    visible: Boolean,
    id: String,
    type: String,
    readonly: Boolean
  },
  events: ['update:visible', 'success'],
  data: function() {
    return {
      formModel: {
        name: '',
        code: '',
        status: 1
      },
      rules: {
        name: [{ required: true, message: '请输入字典值名称' }],
        code: [{ required: true, message: '请输入字典值编码' }],
        status: [{ required: true, message: '请选择字典值状态' }]
      },
      loadingStatus: false,
      statusOptions: UsingStatusOptions
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
    title() {
      if (this.readonly) {
        return '查看字典值'
      } else {
        return !this.id ? '新增字典值' : '编辑字典值'
      }
    },
    toFetch() {
      return this.dialogVisible && this.id
    }
  },
  watch: {
    toFetch(value) {
      value && this.getDetail()
    }
  },
  methods: {
    // 获取详情信息进行反显
    getDetail() {
      this.loadingStatus = true
      this.$api
        .rentalHousingApi
        .getDictById({ id: this.id })
        .then((res) => {
          if (res.code === '200') {
            this.formModel.name = res.data.dictName
            this.formModel.code = res.data.dictCode
            this.formModel.status = +res.data.dictState
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取详情失败'))
        .finally(() => (this.loadingStatus = false))
    },
    // dialog点击右上角关闭按钮，重置表单
    onDialogClosed() {
      this.$refs.formRef.resetFields()
    },
    // 表单提交
    onSubmit() {
      this.$refs.formRef
        .validate()
        .catch(() => Promise.reject())
        .then(() => {
          this.loadingStatus = true
          const params = {
            dictType: this.type, // 字典类型
            dictCode: this.formModel.code, // 	字典编码
            dictName: this.formModel.name, // 字典名称
            dictState: this.formModel.status, // 状态 0-停用 1-启用
            userId: this.$store.getters.userId, // 用户id
            userName: this.$store.getters.userName // 用户名
          }
          if (this.id) {
            params.id = this.id
            return this.$api.rentalHousingApi.dictUpdate(params)
          } else {
            return this.$api.rentalHousingApi.dictSave(params)
          }
        })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('操作成功')
            this.$emit('success')
            this.dialogVisible = false
          } else {
            throw res.message
          }
        })
        .catch((msg) => {
          msg && this.$message.error(msg)
        })
        .finally(() => {
          this.loadingStatus = false
        })
    }
  }
}
</script>
<template>
  <el-dialog
    v-dialogDrag
    class="component dictionary-value-edit"
    :title="title"
    width="550px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    custom-class="model-dialog"
    @closed="onDialogClosed"
  >
    <el-form ref="formRef" :model="formModel" :rules="rules" label-width="95px" :disabled="readonly" :class="{ readonly }">
      <el-form-item label="字典值名称" prop="name">
        <el-input v-model="formModel.name" placeholder="请输入" clearable></el-input>
      </el-form-item>
      <el-form-item label="字典值编码" prop="code">
        <el-input v-model="formModel.code" placeholder="请输入" clearable></el-input>
      </el-form-item>
      <el-form-item label="启用状态" prop="status">
        <el-select v-model="formModel.status" placeholder="请选择">
          <el-option v-for="item of statusOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" plain @click="dialogVisible = false">取消</el-button>
      <el-button v-if="!readonly" type="primary" @click="onSubmit">确认</el-button>
    </template>
  </el-dialog>
</template>
<style lang="scss" scoped>
.el-form {
  width: 100%;
  background-color: #fff;
  padding: 10px 16px 0;
  .el-cascader,
  .el-select {
    width: 100%;
  }
}
</style>
