<template>
  <div>
    <el-dialog custom-class="model-dialog" title="风险研判" :visible.sync="dialogVisible" :before-close="closeDialog">
      <div v-loading="tableLoading" class="continer">
        <el-form ref="formInline" :model="formInline" :inline="true" class="advanced-search-form" label-position="right" label-width="100px" :rules="rules">
          <el-form-item label="风险研判：" prop="judgeType">
            <el-select v-model="formInline.judgeType" class="sino_sdcp_input mr15" placeholder="请选择研判方法" filterable @change="yanpanChange">
              <el-option v-for="(item, index) in yanpanlList" :key="index" :label="item.dictLabel" :value="item.dictValue" class="set_zindex"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="formInline.judgeType == '1'" label="风险等级：" prop="riskLevel">
            <el-select v-model="formInline.riskLevel" class="sino_sdcp_input mr15" placeholder="请选择风险等级" filterable @change="dengjiSelectChange">
              <el-option v-for="(item, index) in riskLevelList" :key="index" :label="item.dictLabel" :value="item.dictValue" class="set_zindex"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-else label="风险等级：" prop="riskLevel">
            <span
              :class="formInline.riskLevel == 1 ? 'red' : formInline.riskLevel == 2 ? 'orange' : formInline.riskLevel == 3 ? 'yellow' : formInline.riskLevel == 4 ? 'blue' : ''"
              >{{ formInline.riskLevelName + '（' + formInline.judgeScore + '分）' }}</span
            >
          </el-form-item>
          <br />
          <div v-if="formInline.judgeType == '2'" style="line-height: 32px; background: #f5f6f9; margin-bottom: 16px; padding-left: 10px;">
            研判规则D：
            <span class="identification" style="background: rgb(255 0 0);"></span>重大风险{{ tableD[0].score }}
            <span class="identification" style="background: rgb(255 97 0);"></span>较大风险{{ tableD[1].score }}
            <span class="identification" style="background: rgb(255 255 0);"></span>一般风险{{ tableD[2].score }}
            <span class="identification" style="background: rgb(0 0 255);"></span>低风险{{ tableD[3].score }}
            <span>D=L*E*C</span>
          </div>
          <div v-if="formInline.judgeType == '3'" style="line-height: 32px; background: #f5f6f9; margin-bottom: 16px; padding-left: 10px;">
            研判规则D：
            <span class="identification" style="background: rgb(255 0 0);"></span>重大风险{{ tableR[0].score }}
            <span class="identification" style="background: rgb(255 97 0);"></span>较大风险{{ tableR[1].score }}
            <span class="identification" style="background: rgb(255 255 0);"></span>一般风险{{ tableR[2].score }}
            <span class="identification" style="background: rgb(0 0 255);"></span>低风险{{ tableR[3].score }}
            <span>R=L*S</span>
          </div>

          <div v-if="formInline.judgeType == '2'" class="judge-info">
            <div style="margin-bottom: 20px;">
              <div class="content-table">
                <div class="table-title">
                  <span class="title">事故可能造成的后果（C）</span>
                </div>
              </div>
              <div v-for="(item, index) of tableLECDL" :key="index" class="judge-list">
                <div class="content">
                  <div class="judge-radio">
                    <el-radio v-model="formInline.judgeProbableScore" :label="item.score" @change="yanpanChange"></el-radio>
                  </div>
                  <div class="judge-state">
                    {{ item.judgeExplain }}
                  </div>
                </div>
              </div>
            </div>
            <div style="margin-bottom: 20px;">
              <div class="content-table">
                <div class="table-title">
                  <span class="title">暴露于危险环境的频繁程度（E）</span>
                </div>
              </div>
              <div v-for="(item, index) of tableLECDE" :key="index" class="judge-list">
                <div class="content">
                  <div class="judge-radio">
                    <el-radio v-model="formInline.judgeFrequencyScore" :label="item.score" @change="yanpanChange"></el-radio>
                  </div>
                  <div class="judge-state">
                    {{ item.judgeExplain }}
                  </div>
                </div>
              </div>
            </div>
            <div style="margin-bottom: 20px;">
              <div class="content-table">
                <div class="table-title">
                  <span class="title">发生事故的可能性（L）：</span>
                </div>
              </div>
              <div v-for="(item, index) of tableLEDCC" :key="index" class="judge-list">
                <div class="content">
                  <div class="judge-radio">
                    <el-radio v-model="formInline.judgeResultScore" :label="item.score" @change="yanpanChange"></el-radio>
                  </div>
                  <div class="judge-state">
                    {{ item.judgeExplain }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-if="formInline.judgeType == '3'" class="judge-info">
            <div style="margin-bottom: 20px;">
              <div class="content-table">
                <div class="table-title">
                  <span class="title">发生事故的可能性（L）：</span>
                </div>
              </div>
              <div v-for="(item, index) of tableLSRL" :key="index" class="judge-list">
                <div class="content">
                  <div class="judge-radio">
                    <el-radio v-model="formInline.lsl" :label="item.score" @change="yanpanChange"></el-radio>
                  </div>
                  <div class="judge-state">
                    {{ item.judgeExplain }}
                  </div>
                </div>
              </div>
            </div>
            <div style="margin-bottom: 20px;">
              <div class="content-table">
                <div class="table-title">
                  <span class="title">事故后果严重性（S）</span>
                </div>
              </div>
              <div v-for="(item, index) of tableLSRS" :key="index" class="judge-list">
                <div class="content">
                  <div class="judge-radio">
                    <el-radio v-model="formInline.lss" :label="item.score" @change="yanpanChange"></el-radio>
                  </div>
                  <div class="judge-state">
                    {{ item.judgeExplain }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="closeDialog">取 消</el-button>
        <el-button type="primary" class="sino-button-sure" @click="closeDialogRole">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script type="text/ecmascript-6">
export default {
  props: {
    dialogVisible: {
      type: Boolean
    },
    type: {
      type: String
    },
    dataContent: {},
    id: {
      type: String
    }
  },
  data() {
    return {
      tableLoading: false,
      formInline: {
        judgeScore: '',
        judgeType: '1',
        riskLevel: '',
        riskLevelName: '',
        judgeProbableScore: '',  // L
        judgeFrequencyScore: '',  // E
        judgeResultScore: '',  // C
        lsl: '',
        lss: ''
      },
      yanpanlList: [],
      riskLevelList: [],
      tableLECDL: [],
      tableLECDE: [],
      tableLEDCC: [],
      tableLSRL: [],
      tableLSRS: [],
      tableD: [],
      tableR: [],
      rules: {
        judgeType: [
          { required: true, message: '请选择研判方法', trigger: 'change' }
        ],
        riskLevel: [
          { required: true, message: '请选择风险等级', trigger: 'change' }
        ]
      },
      jsonContent: []
    }
  },
  watch: {
    id() {
      if (this.type != 'add') {
        this.id ? this.getjudge() : ''
      } else {
        this.getTableData()
      }
    }
  },
  created() {
    this.getDictValue()
    this.getTableData()
  },
  mounted() {
    if (this.type != 'add') {
      this.getjudge()
    }
  },
  methods: {
    // 获取研判详情
    getjudge() {
      this.tableLoading = true
      let obj = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      let data = {
        deptName: obj.officeName,
        deptCode: obj.officeCode,
        id: this.id
      }
      this.$api.ipsmRiskManageGetRiskDetail(data).then(res => {
        this.tableLoading = false
        if (res.code == 200) {
          this.formInline.riskLevelName = res.data.riskLevelName
          this.formInline.riskLevel = res.data.riskLevel.toString()
          if (res.data.judgeType) {
            this.formInline.judgeScore = res.data.judgeScore
            this.formInline.judgeType = res.data.judgeType.toString()
            if (this.formInline.judgeType == '2') {
              this.formInline.judgeProbableScore = res.data.judgeProbableScore
              this.formInline.judgeFrequencyScore = res.data.judgeFrequencyScore
              this.formInline.judgeResultScore = res.data.judgeResultScore
              this.formInline.lsl =  this.tableLSRL[this.tableLSRL.length - 1].score
              this.formInline.lss =  this.tableLSRS[this.tableLSRS.length - 1].score
            }
            if (this.formInline.judgeType == '3') {
              this.formInline.lsl = res.data.judgeProbableScore
              this.formInline.lss = res.data.judgeResultScore
              this.formInline.judgeProbableScore = this.tableLECDL[this.tableLECDL.length - 1].score
              this.formInline.judgeFrequencyScore = this.tableLECDE[this.tableLECDE.length - 1].score
              this.formInline.judgeResultScore = this.tableLEDCC[this.tableLEDCC.length - 1].score
            }
            if (this.formInline.judgeType == '1') {
              this.formInline.judgeProbableScore = this.tableLECDL[this.tableLECDL.length - 1].score
              this.formInline.judgeFrequencyScore = this.tableLECDE[this.tableLECDE.length - 1].score
              this.formInline.judgeResultScore = this.tableLEDCC[this.tableLEDCC.length - 1].score
              this.formInline.lsl =  this.tableLSRL[this.tableLSRL.length - 1].score
              this.formInline.lss =  this.tableLSRS[this.tableLSRS.length - 1].score
            }
          }
        }
      })
    },
    getDictValue() {
      this.$api
        .ipsmGetDictValue({
          dictType: 'risk_level',
          isShowParent: 0
        })
        .then((res) => {
          if (res.code == 200) {
            this.riskLevelList = res.data
          }
        })
      this.$api
        .ipsmGetDictValue({
          dictType: 'judge_type',
          isShowParent: 0
        })
        .then((res) => {
          if (res.code == 200) {
            this.yanpanlList = res.data
          }
        })
    },
    getTableData() {
      this.$api.ipsmRiskJudgeFindList({}).then(res => {
        if (res.code == 200) {
          this.tableLECDL = res.data.LECDLInfo
          this.tableLECDE = res.data.LECDEInfo
          this.tableLEDCC = res.data.LECDCInfo
          this.tableLSRL = res.data.LSRLInfo
          this.tableLSRS = res.data.LSRSInfo
          this.tableD = res.data.LECDRiskLevelInfo
          this.tableR = res.data.LSRRiskLevelInfo
          if (this.type == 'add' || this.type == 'add2') {
            this.formInline.judgeProbableScore = this.tableLECDL[this.tableLECDL.length - 1].score
            this.formInline.judgeFrequencyScore = this.tableLECDE[this.tableLECDE.length - 1].score
            this.formInline.judgeResultScore = this.tableLEDCC[this.tableLEDCC.length - 1].score
            this.formInline.lsl =  this.tableLSRL[this.tableLSRL.length - 1].score
            this.formInline.lss =  this.tableLSRS[this.tableLSRS.length - 1].score
          }
          if (this.id) {
            this.getjudge()
          }

        } else {
          this.$message.error(res.message)
        }
      })
    },
    closeDialogRole() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          // 研判详情
          let LECDLExplain = ''
          let LECDEExplain = ''
          let LECDCExplain = ''
          let LSRLExplain = ''
          let LSRSExplain = ''
          if (this.formInline.judgeType == 2) {
            this.tableLECDL.forEach(i => {
              if (this.formInline.judgeProbableScore == i.score) {
                LECDLExplain = i.score + '；' + i.judgeExplain
              }
            })
            this.tableLECDE.forEach(i => {
              if  (this.formInline.judgeFrequencyScore == i.score) {
                LECDEExplain = i.score + '；' + i.judgeExplain
              }
            })
            this.tableLEDCC.forEach(i => {
              if (this.formInline.judgeResultScore == i.score) {
                LECDCExplain = i.score + '；' + i.judgeExplain
              }
            })
          }
          if (this.formInline.judgeType == 3) {
            this.tableLSRL.forEach(i => {
              if (this.formInline.lsl == i.score) {
                LSRLExplain = i.score + '；' + i.judgeExplain
              }
            })
            this.tableLSRS.forEach(i => {
              if (this.formInline.lss == i.score) {
                LSRSExplain = i.score + '；' + i.judgeExplain
              }
            })
          }
          this.jsonContent = [{
            LECDLExplain,
            LECDEExplain,
            LECDCExplain,
            LSRLExplain,
            LSRSExplain
          }]
          if (this.type == 'add' || this.type == 'edit2') {
            this.$emit('yanpan')
          } else if (this.type == 'edit' || this.type == 'add2') {
            let data = {...this.formInline}
            data.createPersonCode = JSON.parse(sessionStorage.getItem('LOGINDATA')).id
            data.createPersonName = JSON.parse(sessionStorage.getItem('LOGINDATA')).name
            data.riskId = this.id
            data.jsonContent = JSON.stringify(this.jsonContent)
            if (this.formInline.judgeType == 1) {
              data.judgeProbableScore = ''
              data.judgeFrequencyScore = ''
              data.judgeResultScore = ''
            }
            if (this.formInline.judgeType == 3) {
              data.judgeProbableScore  = data.lsl
              data.judgeResultScore  = data.lss
            }
            delete data.lsl
            delete data.lss
            this.$api.ipsmSaveRiskJudgeRecord(data).then(res => {
              if (res.code == 200) {
                this.$message.success(res.message)
                this.$emit('closeDialogRole')
              } else {
                this.$message.error(res.message)
              }
            })
          }

        }
      })
    },
    closeDialog() {
      this.$emit('closeDialog')
    },
    yanpanChange(val) {
      if (this.formInline.judgeType == '2') {
        var score1 = this.tableD[0]
        var score2 = this.tableD[1]
        var score3 = this.tableD[2]
        var score4 = this.tableD[3]
        var val = this.formInline.judgeProbableScore * this.formInline.judgeFrequencyScore * this.formInline.judgeResultScore
      } else if (this.formInline.judgeType == '3') {
        var score1 = this.tableR[0]
        var score2 = this.tableR[1]
        var score3 = this.tableR[2]
        var score4 = this.tableR[3]
        var val = this.formInline.lsl * this.formInline.lss
      }
      // ( 不包含   [ 包含
      if ((score1.score[0] == '(' ? val > score1.minScore : val >= score1.minScore) &&
        (score1.score.slice(-1) == ')' ? val < score1.maxScore : val <= score1.maxScore)) {
        this.formInline.riskLevelName = '重大风险'
        this.formInline.riskLevel = '1'
      } else if ((score2.score[0] == '(' ? val > score2.minScore : val >= score2.minScore) &&
        (score2.score.slice(-1) == ')' ? val < score2.maxScore : val <= score2.maxScore)) {
        this.formInline.riskLevelName = '较大风险'
        this.formInline.riskLevel = '2'
      } else if ((score3.score[0] == '(' ? val > score3.minScore : val >= score3.minScore) &&
        (score3.score.slice(-1) == ')' ? val < score3.maxScore : val <= score3.maxScore)) {
        this.formInline.riskLevelName = '一般风险'
        this.formInline.riskLevel = '3'
      } else if ((score4.score[0] == '(' ? val > score4.minScore : val >= score4.minScore) &&
        (score4.score.slice(-1) == ')' ? val < score4.maxScore : val <= score4.maxScore)) {
        this.formInline.riskLevelName = '低风险'
        this.formInline.riskLevel = '4'
      } else {
        this.formInline.riskLevelName = '该分值不在风险等级配置内'
        this.formInline.riskLevel = ''
      }
      this.formInline.judgeScore = Math.round(val * 100) / 100
    },
    dengjiSelectChange(val) {
      this.formInline.riskLevelName = this.riskLevelList.find(
        (item) => {
          return item.dictValue == val
        }
      ).dictLabel
    }
  }
}
</script>

<style lang="scss" scoped>
.continer {
  width: 100%;
  background-color: #fff;
  padding: 10px;
  min-height: 300px;
}

.identification {
  display: inline-block;
  width: 9px;
  height: 9px;
  border-radius: 50%;
  margin-left: 8px;
}

.red {
  color: rgb(255 0 0);
}

.orange {
  color: rgb(255 97 0);
}

.yellow {
  color: rgb(255 255 0);
}

.blue {
  color: rgb(0 0 255);
}

.judge-info {
  max-height: 260px;
  overflow: auto;

  .judge-list {
    .content {
      display: flex;
      line-height: 32px;

      .judge-radio {
        width: 80px;
      }

      .judge-state {
        margin-top: 4px;
        width: calc(100% - 100px);
        line-height: 24px;
      }
    }
  }
}
</style>
