<template>
  <div style="height: 100%;">
    <div class="content_box">
      <div class="maintain-content-block" style="min-height: 150px;">
        <div class="maintain-title">
          <span class="title-tag"></span>
          <span class="title-text">{{ titleOne }}</span>
        </div>
        <div v-loading="blockLoading" class="maintain-list">
          <div
            v-for="(item, index) in option"
            :key="index"
            :class="[!item.remake ? 'maintain-list-item' : 'maintain-list-block']"
            :style="{ display: item.inline || item.remake ? 'block' : 'inline-block' }"
          >
            <template>
              <label style="display: flex;" class="list-item">
                <span class="label">{{ item.label }}</span>
                <span
                  class="value"
                  style="width: calc(100% - 100px); text-overflow: ellipsis; white-space: nowrap; overflow: hidden; display: inline-block;"
                  :title="taskDetail[item.value] || '无'"
                  >{{ taskDetail[item.value] || '无' }}</span
                >
              </label>
            </template>
          </div>
        </div>
      </div>
      <div class="maintain-content-block">
        <div class="maintain-title">
          <span class="title-tag"></span>
          <span class="title-text">{{ titleTwo }}</span>
        </div>
        <div class="maintain-list">
          <el-table v-loading="contentTableLoading" :data="taskTable" :border="true" stripe :cell-style="{ padding: '8px' }">
            <el-table-column type="index" label="序号" width="65">
              <template slot-scope="scope">
                <span>{{ scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column v-for="(item, index) in task" :key="index" :prop="item.value" :label="item.label" show-overflow-tooltip :width="item.width"></el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div class="bottomBar">
      <div class="bottomWrap">
        <el-button type="primary" plain @click="close">关闭</el-button>
      </div>
    </div>
  </div>
</template>
<script type="text/ecmascript-6">
export default {

  name: '',
  async beforeRouteLeave(to, from, next) {
    // 离开详情页时，如果返回不是上级页面，不缓存
    if (
      this.$store.state.fromPath == to.name &&
      to.name == 'hiddenDangerProgress'
    ) {
      await this.$store.commit('keepAliveChange', true)
    }
    next()
  },
  data() {
    return {
      title: '',
      titleOne: '',
      titleTwo: '',
      taskDetail: {},
      option: [],
      table: [],
      task: [],
      approval: [],
      approval1: [],
      information: [],
      show: true,
      taskTable: [],
      assignmentInfo: null,
      header: {
        HiddenDangerProgress: {
          title: '计划详情',
          titleOne: '计划详情',
          titleTwo: '排查内容',
          detailNode: [
            { label: '排查人：', value: 'createPersonName', remake: false },
            { label: '部门：', value: 'teamName', remake: false },
            { label: '联系方式：', value: 'createPersonPhone', remake: false },
            { label: '用户类别：', value: 'investigationType', remake: false },
            { label: '排查结果：', value: 'recordState', remake: false },
            { label: '隐患记录：', value: 'workOrderCode', remake: false }
          ],
          detailList: [
            { label: '排查内容', value: 'investigationContent', width: '200' },
            { label: '标准项', value: 'standard', width: '200' },
            { label: '参考依据', value: 'referenceResources', width: '' },
            { label: '排查结果', value: 'state', width: '' }
          ]
        }
      },
      assetsTotal: 0,
      assetsCurrentPage: 1,
      blockLoading: false,
      contentTableLoading: false,
      assetsTableLoading: false,
      dialogVisible: false,
      dataList: []
    }
  },
  created() {
    // console.log(this.$store.state.fromPath);
    // this.$store.replaceState(
    //   Object.assign(
    //     this.$store.state,
    //     JSON.parse(sessionStorage.getItem('beforeunload'))
    //   )
    // )
    // window.addEventListener('beforeunload', () => {
    //   let state = JSON.stringify(this.$store.state)
    //   sessionStorage.setItem('beforeunload', state)
    // })
    // sessionStorage.removeItem('beforeunload')
    this.planTaskDetail()
    // this.getAssetsList();
    let query = this.$route.query
    this.title = this.header[query.type].title
    this.titleOne = this.header[query.type].titleOne
    this.titleTwo = this.header[query.type].titleTwo
  },

  methods: {
    handleAssetsCurrentChange(val) {
      this.assetsCurrentPage = val
    },
    handleCurrentChange(val) {
      this.assetsCurrentPage = val
      this.getAssetsList()
    },
    getArrIndex(arr, obj) {
      let index = null
      let key = Object.keys(obj)[0]
      arr.every(function (value, i) {
        if (value[key] === obj[key]) {
          index = i
          return false
        }
        return true
      })
      return index
    },
    look(row) {
      this.dataList = JSON.parse(row.investigationContents)
      this.dialogVisible = true
    },
    closeDialog() {
      this.dialogVisible = false
    },
    planTaskDetail() {
      // 获取详情
      this.blockLoading = true
      this.contentTableLoading = true

      this.$api
        .ipsmGetRiskInvestigationRecordDetail({
          investigationRecordId: this.$route.query.id
        })
        .then((res) => {
          this.blockLoading = false
          this.contentTableLoading = false
          if (res.code == '200') {
            this.taskDetail = res.data.riskInvestigationRecordDetail
            this.taskTable = res.data.list || []// 列表
            this.option = this.header[this.$route.query.type].detailNode
            this.table = this.header[this.$route.query.type].detailTabel
            this.task = this.header[this.$route.query.type].detailList
            this.approval = this.header[this.$route.query.type].detailApprovalList
            this.information = this.header[this.$route.query.type].information
          } else {
            this.$message.error(res.message)
          }
        })

    },

    close() {
      //   this.$store.commit('keepAliveChange', true)
      this.$router.go(-1)
    }
  }
}
</script>
<style lang="scss" scoped>
.maintain-content-block {
  margin-bottom: 20px;

  .maintain-title {
    height: 40px;
    line-height: 40px;

    .title-tag {
      display: inline-block;
      width: 4px;
      height: 16px;
      position: relative;
      top: 3px;
      background: #5188fc;
    }

    .title-text {
      padding-left: 5px;
      font-size: 14px;
      font-weight: 600;
      color: rgb(96 98 102 / 100%);
    }
  }

  .maintain-list {
    padding: 0 0 0 26px;

    .maintain-list-item {
      display: inline-block;
      width: 33%;
      overflow: hidden;
      text-overflow: ellipsis;

      .list-item {
        display: block;
        height: 40px;
        line-height: 40px;

        .label {
          display: inline-block;
          width: 100px;
          text-align: right;
          font-weight: 400;
          color: rgb(96 98 102 / 100%);
        }

        .value {
          font-weight: 400;
          color: rgb(144 147 153 / 100%);
        }
      }
    }

    .maintain-list-block {
      // height: 40px;
      line-height: 20px;

      .list-item {
        line-height: 40px;
      }

      .label {
        display: inline-block;
        width: 100px;
        vertical-align: top;
        text-align: right;
        font-weight: 400;
        color: rgb(96 98 102 / 100%);
      }

      .value {
        display: inline-block;
        width: 880px;
        font-weight: 400;
        color: rgb(144 147 153 / 100%);
      }
    }
  }
}

.content_box {
  height: calc(100% - 82px);
  overflow-y: auto;
  margin: 15px;
  padding: 20px;
  background: #fff;
  display: flex;
  flex-direction: column;
}

.bottomBar {
  height: 52px;
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: left;
  align-items: center;

  .bottomWrap {
    padding: 0 16px;
  }
}
</style>
