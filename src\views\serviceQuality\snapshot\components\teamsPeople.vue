<template>
  <div class="main">
    <el-dialog custom-class="mainDialog main" append-to-body :visible.sync="changeTeamsPeopleShow" :close-on-click-modal="false" :before-close="closeDialog" class="personDialog">
      <template slot="title">
        <span class="dialog-title">服务人员选择</span>
      </template>
      <div class="dialog-content" style="width: 100%;">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          height="300"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          style="width: 100%;"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          @selection-change="selectionChange"
        >
          <el-table-column fixed prop="deptName" show-overflow-tooltip label="服务部门"></el-table-column>
          <el-table-column fixed type="selection" width="45px"></el-table-column>
          <el-table-column fixed prop="member_name" show-overflow-tooltip label="服务人员"></el-table-column>
          <el-table-column fixed prop="phone" show-overflow-tooltip label="人员电话"></el-table-column>
          <el-table-column fixed prop="flag" show-overflow-tooltip label="派工状态">
            <template slot-scope="scope">
              <span class="redColor">{{ scope.row.flag }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed prop="number" show-overflow-tooltip label="派工数量">
            <template slot-scope="scope">
              <span class="redColor">{{ scope.row.number }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed prop="completeNumber" show-overflow-tooltip label="今日已完工">
            <template slot-scope="scope">
              <span class="redColor">{{ scope.row.completeNumber }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="sino-button-sure" @click="closeDialog">取 消</el-button>
        <el-button class="sino-button-sure" type="primary" @click="savePeople">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: 'teamsPeople',
  props: {
    changeTeamsPeopleShow: {
      type: Boolean,
      default: false
    },
    selectTeamsData: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data() {
    return {
      tableData: [],
      tableLoading: false,
      selectionParams: {}
    }
  },
  watch: {},
  mounted() {
    this.getDesignPersonByDept()
  },
  methods: {
    getDesignPersonByDept() {
      this.tableLoading = true
      this.$api.getServicePersonName(this.selectTeamsData).then((res) => {
        // getDesignPersonByDept(this.selectTeamsData).then((res) => {
        const data = res.data.list
        data.map((e) => {
          e.deptName = this.selectTeamsData.deptName
        })
        this.tableData = data
        this.tableLoading = false
      })
    },
    selectionChange(selection) {
      // console.log(selection)
      if (selection.length) {
        // const person = Array.from(selection, ({ name }) => name)
        // const personSplit = selection.map((e) => {
        //   return e.id + '_' + e.name + '_' + e.mobile
        // })
        // this.selectionParams = {
        //   designatePerson: person.toString(),
        //   designatePersonValTransfer: personSplit.toString()
        // }
        this.selectionParams = selection
      } else {
        this.selectionParams = {}
      }
    },
    // 取消按钮
    closeDialog() {
      this.$emit('closeDialog')
    },
    // 确认按钮
    savePeople() {
      this.$emit('peopleSure', this.selectionParams)
      // if (Object.keys(this.selectionParams).length) {
      //   this.$emit('peopleSure', this.selectionParams)
      // }
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
::v-deep .mainDialog {
  width: 45%;

  .el-dialog__body {
    padding: 10px 20px;
  }

  .el-dialog__footer {
    padding-right: 30px;
  }

  .dialog-title {
    color: #333;
    font-family: PingFangSC-Medium, "PingFang SC";
  }

  .dialog-title::before {
    content: "";
    display: inline-block;
    width: 2px;
    border-radius: 1px;
    height: 13px;
    background: #3562db;
    margin-right: 10px;
  }
}

::v-deep .el-table-column--selection .cell {
  padding-left: 0.875rem;
  padding-right: 0.875rem;
  text-overflow: initial;
}

.redColor {
  color: red;
}

.el-button {
  height: 40px;
}

::v-deep .el-table .el-table__header .el-table__cell .cell {
  padding-left: 13px;
  text-overflow: clip;
}
</style>
