<template>
  <el-dialog title="请选择部门" width="55%" :visible.sync="sectionDialogShow" custom-class="model-dialog" :before-close="closeDialog">
    <div class="content">
      <div class="leftContent">
        <div class="topSearch">
          <el-input v-model="filterText" placeholder="请输入关键字" clearable suffix-icon="el-icon-search"> </el-input>
        </div>
        <div class="treeBox">
          <el-tree
            ref="tree"
            v-loading="treeLoading"
            :check-strictly="true"
            :data="treeData"
            :props="defaultProps"
            style="margin-top: 10px"
            :filter-node-method="filterNode"
            :highlight-current="true"
            :default-expanded-keys="expanded"
            @node-click="handleNodeClick"
          ></el-tree>
        </div>
      </div>
      <div class="rightcontent">
        <div class="search-box">
          <el-input v-model="searchForm.deptName" clearable filterable placeholder="请输入部门名称"></el-input>
          <div class="ml-16">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button type="primary" plain @click="reset">重置</el-button>
          </div>
        </div>
        <div class="sino_table">
          <div></div>
          <el-table ref="sinoTable" v-loading="tableLoading" :data="tableData" :row-key="getRowKeys" height="300px" stripe border @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center" :reserve-selection="true"></el-table-column>
            <el-table-column v-for="(column, index) in tableColumn" :key="index" :prop="column.prop" :label="column.label" :min-width="column.minWidth" show-overflow-tooltip>
              <template slot-scope="scope">
                <span>{{ scope.row[column.prop] }}</span>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            :current-page="pagination.current"
            :page-sizes="[15, 30, 50, 100]"
            :page-size="pagination.size"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="submitDialog('exportForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'SelectDept',
  components: {},
  props: {
    sectionDialogShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      filterText: '',
      treeData: [],
      treeLoading: true,
      expanded: [],
      tableLoading: false,
      unitTypeList: [],
      tableHeight: '',
      tableData: [],
      multipleSelection: [],
      radioObj: {},
      // -----------------------------Pagination
      pagination: {
        current: 1,
        size: 15
      }, // 分页数据
      total: 0, // 数据总条数
      selectList: [],
      allSpaceTreeData: [],
      defaultProps: {
        label: 'unitComName',
        children: 'children'
      },
      tableColumn: [
        {
          prop: 'deptName',
          label: '名称'
        },
        {
          prop: 'deptCode',
          label: '编码'
        }
      ],
      searchForm: {
        deptName: ''
      }
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  mounted() {
    this.getUnitTreeFn()
  },
  methods: {
    search() {
      this.getDeptListFn()
    },
    reset() {
      this.searchForm = {
        staffName: ''
      }
      this.getDeptListFn()
    },
    getRowKeys(row) {
      return row.id
    },
    getTableData() {
      this.getUnitTreeFn()
    },

    // 单位列表tree 树
    getUnitTreeFn() {
      this.treeLoading = true
      this.$api.getUnitList({}).then((res) => {
        this.treeLoading = false
        if (res.code == 200) {
          this.treeData = res.data ? res.data : []
          this.getDeptListFn()
        }
      })
    },
    // 部门列表
    getDeptListFn() {
      this.tableLoading = true
      let data = {
        ...this.pagination,
        ...this.searchForm,
        unitId: this.checkedData ? this.checkedData.umId : ''
      }
      this.$api.departList(data).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.tableData = res.data.records
          this.total = res.data.total
        }
      })
    },

    // 过滤
    filterNode(value, data) {
      if (!value) return true
      return data.unitComName.indexOf(value) !== -1
    },
    // 树状图点击
    handleNodeClick(data) {
      this.pagination.current = 1
      this.checkedData = data
      this.getDeptListFn()
    },
    //  ----------------------------------------------Tree_Fn

    // ---------------------------------------------------------- TabelFn
    handleSizeChange(val) {
      this.pagination.current = 1
      this.pagination.size = val
      this.getDeptListFn()
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.getDeptListFn()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
      this.selectList = JSON.parse(JSON.stringify(this.multipleSelection))
    },
    closeDialog() {
      this.$emit('closeSectionDialog')
    },
    submitDialog() {
      this.$emit('submitSectionDialog', this.selectList)
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  .content {
    margin: 0 auto;
    background: #fff;
    border-radius: 4px;
    display: flex;
    width: 100%;
    height: 100%;
    padding: 10px 10px 10px 0px;
    box-sizing: border-box;

    .leftContent {
      width: 260px;
      height: 100%;
      .topSearch {
        padding: 0px 10px;
      }
      .treeBox {
        height: 330px;
        width: 100%;
        margin-top: 10px;
        overflow-x: hidden;
        overflow-y: scroll;
      }
    }
    .rightcontent {
      margin-left: 10px;
      width: calc(100% - 270px);
    }
  }
  .search-box {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .el-input {
      width: 180px;
      margin-right: 16px;
    }
  }
  .el-pagination {
    margin-top: 10px;
    width: 100%;
  }
}
</style>

