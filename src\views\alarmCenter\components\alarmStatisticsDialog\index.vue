<!-- 报警统计弹窗 -->
<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="报警统计"
    width="60%"
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="content">
      <div class="content-heade">
        <el-date-picker
          v-model="searchFrom.dataRange"
          type="daterange"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptions"
          :clearable="false"
        />
        <div style="display: inline-block">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="searchForm">查询</el-button>
        </div>
      </div>
      <div v-loading="loading" class="content-main">
        <ContentCard class="content-left" title="报警统计">
          <div slot="content" style="width: 100%; height: 100%; display: flex">
            <div class="cardContent-left">
              <div v-for="item in alarmStatisticsList" :key="item.title" class="left-item">
                <div class="lt">
                  <p class="item-title">{{ item.title }}</p>
                  <p class="item-value" :style="{ color: item.color }">{{ item.value }}<span>个</span></p>
                </div>
                <img class="item-icon" :src="item.icon" :alt="item.title" />
              </div>
            </div>
            <echarts ref="deviceType" domId="deviceType" width="65%" height="100%" />
          </div>
        </ContentCard>
        <ContentCard class="content-right" title="监测类型">
          <div slot="content" class="card-content">
            <!-- <el-select v-model="searchFrom.entityTypeId" placeholder="请选择监测类型" clearable filterable @change="changeEntity">
              <el-option label="全部" value=""> </el-option>
              <el-option v-for="item in entityList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select> -->
            <div>
              <el-select
                v-model="searchFrom.projectCode"
                placeholder="报警系统"
                filterable
                clearable
                :disabled="!!projectCode"
                @change="getIncidentAndSourceGroup"
                @clear="
                  () => {
                    searchFrom.alarmType = []
                  }
                "
              >
                <el-option v-for="item in alarmSourceOptions" :key="item.thirdSystemCode" :label="item.thirdSystemName" :value="item.thirdSystemCode"> </el-option>
              </el-select>
              <el-select
                v-model="searchFrom.alarmType"
                style="width: 300px"
                placeholder="报警类型"
                multiple
                collapse-tags
                clearable
                :disabled="!searchFrom.projectCode"
                @change="changeEntityType"
              >
                <el-option v-for="item in eventTypeOptions" :key="item.id" :label="item.alarmDictName" :value="item.id"> </el-option>
              </el-select>
            </div>
            <div class="card-content-table table-content">
              <el-table ref="table" :resizable="false" border :data="tableData" :height="tableHeight" style="width: 100%">
                <el-table-column prop="alarmObjectName" label="报警对象" width="120" show-overflow-tooltip></el-table-column>
                <el-table-column prop="iphPoliceLevel" label="严重等级" width="110">
                  <span slot-scope="scope" class="alarmLevel" :style="{ background: alarmLevelItem[scope.row.alarmLevel].color }">
                    {{ alarmLevelItem[scope.row.alarmLevel].text }}
                  </span>
                </el-table-column>
                <el-table-column prop="alarmType" label="报警类型" show-overflow-tooltip></el-table-column>
                <el-table-column prop="alarmSource" label="报警系统" show-overflow-tooltip></el-table-column>
                <el-table-column prop="alarmSpaceName" label="报警位置" show-overflow-tooltip></el-table-column>
                <el-table-column prop="alarmStatus" label="处理状态" width="120">
                  <div slot-scope="scope" class="alarmStatus" :style="{ color: scope.row.alarmStatus == 0 ? '#FA403C' : scope.row.alarmStatus == 1 ? '#fd9434' : '#999' }">
                    <span class="alarmStatusIcon" :style="{ background: scope.row.alarmStatus == 0 ? '#FA403C' : scope.row.alarmStatus == 1 ? '#ff9435' : '#999' }"></span>
                    {{ scope.row.alarmStatus == 0 ? '未处理' : scope.row.alarmStatus == 1 ? '处理中' : '已关闭' }}
                  </div>
                </el-table-column>
              </el-table>
            </div>
            <div class="card-content-footer">
              <el-pagination
                :current-page="pagination.current"
                :page-sizes="pagination.pageSizeOptions"
                :page-size="pagination.size"
                :layout="pagination.layoutOptions"
                :total="pagination.total"
                @size-change="paginationSizeChange"
                @current-change="paginationCurrentChange"
              >
              </el-pagination>
            </div>
          </div>
        </ContentCard>
      </div>
    </div>
    <span slot="footer">
      <el-button plain type="primary" @click="closeDialog">关 闭</el-button>
    </span>
  </el-dialog>
</template>
<script>
import alarmStatistics from '@/assets/images/monitor/alarm-statistics.png'
import alarmAlarm from '@/assets/images/monitor/alarm-pending.png'
import alarmDoing from '@/assets/images/monitor/alarm-doing.png'
import alarmEnd from '@/assets/images/monitor/alarmEnd.png'
import chartMixin from '@/views/monitor/airMenu/mixins/chartMixin'
import tableListMixin from '@/mixins/tableListMixin.js'
import moment from 'moment'
moment.locale('zh-cn')
export default {
  name: 'alarmStatisticsDialog',
  mixins: [chartMixin, tableListMixin],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    projectCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // entityList: [],
      loading: false,
      tableData: [],
      eventTypeOptions: [],
      alarmSourceOptions: [],
      searchFrom: {
        projectCode: this.projectCode, // 报警系统
        alarmType: [], // 报警类型
        dataRange: [] // 时间范围
      },
      pickerOptions: {
        shortcuts: [
          {
            text: '今日',
            onClick(picker) {
              picker.$emit('pick', [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')])
            }
          },
          {
            text: '本月',
            onClick(picker) {
              picker.$emit('pick', [moment().startOf('month').format('YYYY-MM-DD'), moment().endOf('month').format('YYYY-MM-DD')])
            }
          },
          {
            text: '本年',
            onClick(picker) {
              picker.$emit('pick', [moment().startOf('year').format('YYYY-MM-DD'), moment().endOf('year').format('YYYY-MM-DD')])
            }
          }
        ]
      },
      alarmStatisticsList: [
        {
          title: '报警统计',
          color: '#3562DB',
          icon: alarmStatistics,
          value: 0
        },
        {
          title: '未处理',
          color: '#FA403C',
          icon: alarmAlarm,
          value: 0
        },
        {
          title: '处理中',
          color: '#FF9435',
          icon: alarmDoing,
          value: 0
        },
        {
          title: '已处理',
          color: '#00bc6d',
          icon: alarmEnd,
          value: 0
        }
      ],
      alarmLevelItem: [
        { text: '提示', color: '#3562DB' },
        { text: '一般', color: '#3562DB' },
        { text: '较重', color: '#FF9435' },
        { text: '严重', color: '#FA403C' }
      ]
    }
  },
  computed: {},
  created() {
    // this.getDictionaryList()
    this.$nextTick(() => {
      this.getStatisticData()
      this.getDataList()
      this.getAlarmSource()
      if (this.projectCode) {
        this.getIncidentAndSourceGroup(this.projectCode)
      }
    })
  },
  mounted() {},
  methods: {
    // 获取报警来源
    getAlarmSource() {
      this.$api.getAlarmThirdSystemData().then((res) => {
        if (res.code == 200) {
          this.alarmSourceOptions = res.data
        }
      })
    },
    // 获取事件类型以及报警来源分组
    getIncidentAndSourceGroup(val) {
      this.changeEntityType()
      this.searchFrom.alarmType = []
      this.$api.getAlarmThirdTypeData({ thirdSystemCode: val }).then((res) => {
        if (res.code == 200) {
          this.eventTypeOptions = res.data
        }
      })
    },
    changeEntityType() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 获取实体类型
    getDictionaryList() {
      this.$api.getDictionaryList({ dictType: 12 }).then((res) => {
        if (res.code == 200) {
          this.entityList = res.data
        }
      })
    },
    // 查看详情
    getStatisticData() {
      let params = {
        startTime: this.searchFrom.dataRange[0],
        endTime: this.searchFrom.dataRange[1],
        projectCode: this.projectCode
      }
      let newArr = []
      this.loading = true
      this.$api
        .selectDiffProjectCodeAlarm(params)
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            ;(res.data?.policeList ?? []).forEach((item) => {
              newArr.push({
                name: item.entityTypeName,
                value: item.count
              })
            })
            this.alarmStatisticsList[0].value = res.data?.total ?? 0
            this.alarmStatisticsList[1].value = res.data?.noDealCount ?? 0
            this.alarmStatisticsList[2].value = res.data?.isDealCount ?? 0
            this.alarmStatisticsList[3].value = res.data?.dealCount ?? 0
            this.$refs.deviceType.init(this.setPieChart('单', newArr, ['监测项类型', '报警占比']))
          } else {
            this.$refs.deviceType.init(this.setPieChart())
          }
        })
        .catch(() => {
          this.loading = false
          this.$refs.deviceType.init(this.setPieChart())
        })
    },
    // 获取列表数据
    getDataList() {
      const params = {
        pageNo: this.pagination.current,
        pageSize: this.pagination.size,
        projectCode: this.projectCode || this.searchFrom.projectCode,
        alarmType: this.searchFrom.alarmType.join(','),
        // entityTypeId: this.searchFrom.entityTypeId,
        startTime: this.searchFrom.dataRange[0],
        endTime: this.searchFrom.dataRange[1]
      }
      this.$api
        .GetAllAlarmRecord(params)
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            this.tableData = res.data ? res.data.records : []
            this.pagination.total = res.data ? res.data.total : 0
          } else {
            this.tableData = []
            this.pagination.total = 0
          }
        })
        .catch((err) => {
          this.loading = false
        })
    },
    // 重置查询
    resetForm() {
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.pagination.current = 1
      this.searchForm()
    },
    // 查询
    searchForm() {
      this.getStatisticData()
      this.getDataList()
    },
    changeEntity() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 0;
  max-height: calc(88vh - 110px);
}
.content {
  width: 100%;
  height: 100%;
  .content-heade {
    padding: 10px;
    background: #fff;
    & > div {
      margin-right: 10px;
    }
  }
  .content-main {
    width: 100%;
    padding: 15px 10px;
  }
  .content-left {
    height: 415px;
    width: 100%;
    .cardContent-left {
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: 35%;
      height: 100%;
      padding-right: 16px;
      p {
        margin: 0;
      }
      .left-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 77px;
        padding: 14px 22px;
        background: #faf9fc;
        border-radius: 4px;
        margin-bottom: 7px;
        position: relative;
        .lt {
          display: flex;
          align-items: center;
          .item-title {
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 15px;
            color: #333333;
            font-style: normal;
            text-transform: none;
            margin-right: 16px;
          }
          .item-value {
            font-family: Arial;
            font-weight: bold;
            font-size: 30px;
            color: #3562db;
            text-align: left;
            font-style: normal;
            text-transform: none;
            & > span {
              font-family: PingFang SC;
              font-weight: 500;
              font-size: 15px;
              color: #96989a;
              line-height: 18px;
              text-align: center;
              font-style: normal;
              text-transform: none;
              margin-left: 4px;
            }
          }
        }
        .item-icon {
          position: absolute;
          right: 22px;
          bottom: 14px;
          width: 40px;
          height: 40px;
        }
      }
      & :last-child {
        margin-bottom: 0;
      }
    }
  }
  .content-right {
    height: 500px;
    width: 100%;
    margin-top: 16px;
    .card-content {
      height: 100%;
      padding: 0 0 0 4px;
      display: flex;
      flex-direction: column;
      .card-content-table {
        flex: 1;
        overflow: auto;
        margin-top: 16px;
        ::v-deep .el-progress {
          .el-progress-bar__outer {
            border-radius: 0;
            height: 8px !important;
          }
          .el-progress-bar__inner {
            border-radius: 0;
          }
        }
        .alarmLevel {
          padding: 3px 6px;
          border-radius: 4px;
          color: #fff;
          line-height: 14px;
        }
        .alarmStatus {
          position: relative;
          display: inline-block;
          padding-left: 12px;
          .alarmStatusIcon {
            display: inline-block;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            border-radius: 100%;
          }
        }
      }
      .card-content-footer {
        padding: 10px 0 0;
      }
    }
  }
}
::v-deep .model-dialog {
  padding: 0 !important;
  margin-top: 8vh !important;
}
</style>
