<template>
  <ContentCard
    :title="item.componentTitle"
    :scrollbarHover="true"
    :cstyle="{ height: '100%' }"
    class="drag_class"
    :hasMoreOper="['more', 'edit']"
    @more-oper-event="(val) => $emit('all-more-Oper', val, 'warnManageTable')"
  >
    <div slot="content" style="height: 100%" class="warnManage-table">
      <TablePage ref="table" v-el-table-infinite-scroll="tableLoad" v-loading="loading" stripe :showPage="false" :tableColumn="tableColumn" :data="tableData" height="100%" />
      <template v-if="alarmDetailShow">
        <AlarmDetailDialog ref="alarmDetail" :alarmId="alarmDetail.alarmId" :visible.sync="alarmDetailShow" @closeAlarmDialog="closeAlarmDialog" />
      </template>
    </div>
  </ContentCard>
</template>
<script lang="jsx">
import { alarmLevelItem, alarmAffirmItem, alarmStatusList } from '@/util/dict.js'
export default {
  name: 'warnManageTable',
  props: {
    item: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      pageData: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      tableColumn: [
        {
          label: '报警时间',
          prop: 'alarmStartTime'
        },
        {
          label: '报警等级',
          prop: 'alarmLevel',
          render: (h, scope) => {
            return (
              <span class="alarmLevel" style={{ background: alarmLevelItem[scope.row.alarmLevel].color }}>
                {alarmLevelItem[scope.row.alarmLevel].text}
              </span>
            )
          }
        },
        {
          label: '位置',
          prop: 'alarmSpaceName'
        },
        {
          label: '报警对象',
          prop: 'alarmObjectName'
        },
        {
          label: '事件类型',
          prop: 'alarmType'
        },
        {
          label: '警情确认',
          prop: 'alarmAffirm',
          formatter: (scope) => {
            return alarmAffirmItem[scope.row.alarmAffirm]
          }
        },
        {
          label: '处理状态',
          prop: 'alarmStatus',
          render: (h, scope) => {
            return (
              <div class="alarmStatus" style={{ color: alarmStatusList[scope.row.alarmStatus].color }}>
                <span class="alarmStatusIcon" style={{ background: alarmStatusList[scope.row.alarmStatus].color }}></span>
                {alarmStatusList[scope.row.alarmStatus].text}
              </div>
            )
          }
        },
        {
          label: '查看',
          prop: 'alarmStatus',
          render: (h, scope) => {
            return (
              <el-button type="text" onClick={() => this.getWarnDetail(scope.row)}>
                查看
              </el-button>
            )
          }
        }
      ],
      alarmDetailShow: false,
      alarmDetail: {}
    }
  },
  mounted() {
    this.getAlarmList()
  },
  methods: {
    // 获取报警记录
    getAlarmList() {
      let params = {
        timeOrType: 2,
        pageNo: this.pageData.page,
        pageSize: this.pageData.pageSize
      }
      this.loading = true
      this.$api
        .GetAllAlarmRecord(params)
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            if (this.pageData.page == 1) {
              this.tableData = res.data?.records ?? []
            } else {
              this.tableData = this.tableData.concat(res.data?.records ?? [])
            }
            this.pageData.total = res.data?.total ?? 0
          } else {
            this.tableData = []
            this.pageData.total = 0
          }
        })
        .catch((err) => {
          this.loading = false
        })
    },
    tableLoad() {
      if (this.pageData.total > this.pageData.page * this.pageData.pageSize) {
        this.pageData.page++
        this.getAlarmList()
      }
    },
    // 查看详情
    getWarnDetail(row) {
      this.$router.push({
        path: '/allAlarm/alarmDetail',
        query: {
          alarmId: row.alarmId
        }
      })
    },
    closeAlarmDialog() {
      this.getAlarmList()
    }
  }
}
</script>
<style lang="scss" scoped></style>
<style lang="scss">
.warnManage-table {
  .alarmLevel {
    padding: 3px 6px;
    border-radius: 4px;
    color: #fff;
    line-height: 14px;
  }
  .alarmStatus {
    position: relative;
    display: inline-block;
    padding-left: 12px;
    .alarmStatusIcon {
      display: inline-block;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      border-radius: 100%;
    }
  }
  .el-table .el-table__header .el-table__cell {
    background: #f3f4f6;
  }
}
</style>
