<template>
  <PageContainer class="videoMonitor">
    <div slot="content" class="elevator-content">
      <div ref="largeScreenMonitoring" class="largeScreenMonitoring">
        <div class="largeScreen_content">
          <div class="md_title">
            <i class="el-icon-arrow-left" @click="$router.go(-1)"></i>
            <span>视频监控</span>
          </div>
          <div class="videoMonitor-main">
            <div v-loading="leftLoading" class="main-left">
              <div class="left-content">
                <el-input v-model="searchValue" placeholder="请输入关键词" suffix-icon="el-icon-search" clearable @input="searchForm" />
                <div class="content-main">
                  <el-checkbox-group v-model="selectDeviceList" :max="4" @change="selectDeviceChange">
                    <el-checkbox v-for="item in deviceList" :key="item.icmId" :label="item.icmId">{{ item.icmName }}</el-checkbox>
                  </el-checkbox-group>
                </div>
              </div>
            </div>
            <div v-if="playlist.length" class="main-right" :style="playlist.length == 1 ? 'grid-template-columns: 100%' : ''" >
              <div v-for="item in playlist" :key="item.icmId + 'video'" class="video_item">
                <rtspCavas 
                  v-if="item.icmRtsp"
                  ref="rtspCavas" 
                  :rtspUrl="item.icmRtsp" 
                  :videoName="item.icmName" 
                  :hasCavas="Boolean(playlist.length)" 
                  class="videoflv"
                ></rtspCavas>
              </div>
            </div>
          </div>
          
        </div>
      </div>
      <AlarmDialog ref="alarmDialog" />
    </div>
  </PageContainer>
</template>

<script>
import { debounce } from 'lodash/function'
import mixin from './mixin/mixin.js'
export default {
  name: 'videoMonitor',
  mixins: [mixin],
  data() {
    return {
      leftLoading: false,
      searchValue: '',
      deviceList: [], // 设备列表
      selectDeviceList: [],
      playlist: []
    }
  },
  computed: {

  },
  created() {
    this.getCameraListByProjectCode()
  },
  methods: {
    selectDeviceChange(val) {
      this.playlist = this.deviceList.filter(ele => val.indexOf(ele.icmId) != -1)
      console.log(this.playlist)
    },
    // 查询
    searchForm: debounce(function() {
      this.getCameraListByProjectCode()
    }, 500),
    getCameraListByProjectCode() {
      this.leftLoading = true
      this.$api.GetCameraListByProjectCode({...this.$route.query, icmName: this.searchValue}).then((res) => {
        this.leftLoading = false
        if (res.code == 200) {
          this.deviceList = res.data
        }
      })
    }
  }
}

</script>

<style lang="scss" scoped>
.elevator-content {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  .largeScreenMonitoring {
    width: 100%;
    height: 100%;
    overflow: hidden;
    .largeScreen_content {
      height: calc(100% - 0px);
      width: 100%;
      padding: 16px;
      display: flex;
      flex-direction: column;
      // justify-content: space-between;
      background: url('~@/assets/images/elevator/elevator-bg.png') no-repeat;
      background-size: 100% 100%;
      .videoMonitor-main {
        height: 100%;
        overflow: auto;
        display: flex;
        padding-top: 16px;

        .main-left {
          width: 230px;
          margin-right: 16px;
          // background: #fff;
          border-radius: 4px;

          .left-content {
            height: 100%;
            display: flex;
            flex-direction: column;
            ::v-deep .el-input {
              border-radius: 4px;

              .el-input__inner {
                background: rgb(3 23 81 / 50%);
                border: 1px solid #193382;
                color: #fff;
              }

              .el-input-group__append,
              .el-input-group__prepend {
                padding: 0 10px;
                background: rgb(3 23 81 / 50%);
                border-color: #193382;
                color: #fff;
              }
            }
            .content-main {
              flex: 1;
              overflow: auto;
              padding-top: 16px;
              
            }

            :deep(.el-checkbox-group) {
              .el-checkbox {
                padding: 8px 12px;
                display: block;
                margin: 4px 0 0;
                display: flex;
                justify-content: center;
                align-items: center;
                background: #091632;
                .el-checkbox-group .el-checkbox{
                  background: #091632;
                }
                .el-checkbox__inner{
                  background: center;
                  border: 1px solid rgba(33, 129, 244, 0.8) !important;
                }
                .el-checkbox__label {
                  color: rgba(33, 129, 244, .8) !important;
                  width: auto;
                }
              }

              .is-checked {
                background: rgba(244,219,103,0.1);
                box-shadow: inset 0px 0px 8px 0px rgba(255,227,166,0.72);
                .el-checkbox__label {
                  color: rgba(255,227,166,0.72) !important;
                }
                .el-checkbox__inner{
                  background: #F4DB67;
                  border: 1px solid #F4DB67 !important;
                }
              }
              
            }
          }
        }

        .main-right {
          flex: 1;
          // background: #fff;
          border-radius: 4px;
          display: grid;
          grid-gap: 16px;
          grid-template-columns: calc(50% - 8px) calc(50% - 8px);
          .video_item{
            z-index: 500;
            height: calc(100% - 16px);
            background: black;
          }
        }
      }
      .md_title{
        position: relative;
        display: flex;
        align-items: center;
        background: url('~@/assets/images/monitor/title_style.png') no-repeat;
        background-position: 15px 15px;
        .el-icon-arrow-left{
          cursor: pointer;
          color: #FFFFFF;
        }
        span{
          margin-left: 10px;
          font-size: 20px;
          font-family: HarmonyOS Sans SC-Medium, HarmonyOS Sans SC;
          font-weight: 500;
          color: #DCE9FF;
        }
      }
    }
  }
}
</style>
