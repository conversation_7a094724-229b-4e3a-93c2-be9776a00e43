<template>
  <PageContainer class="originalTab">
    <template #content>
      <div class="originalTab__header">
        <el-form ref="formRef" :model="searchForm" inline @submit.native.prevent="name">
          <el-form-item prop="date">
            <el-date-picker v-model="searchForm.date" type="date" value-format="yyyy-MM-dd" placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" plain @click="onReset">重置</el-button>
        </div>
      </div>
      <div class="originalTab__table">
        <el-table v-loading="tableLoadingStatus" height="100%" :data="tableData" border stripe table-layout="auto"
          class="tableAuto" row-key="id">
          <el-table-column prop="operationTime" label="值班日期" show-overflow-tooltip></el-table-column>
          <el-table-column prop="dutyPostName" label="值班岗" show-overflow-tooltip></el-table-column>
          <el-table-column prop="dutyAttendanceTimeName" label="班次名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="slotTimeStr" label="时段" show-overflow-tooltip></el-table-column>
          <el-table-column prop="shouldDutyPersonName" label="值班人员" show-overflow-tooltip></el-table-column>
          <el-table-column prop="handoverTime" label="交班时间" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.handoverTime ? moment(row.handoverTime).format('YYYY-MM-DD HH:mm:ss') : '' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150px">
            <template #default="{ row }">
              <el-button type="text" @click="onOperate(row)">查看日志</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination class="originalTab__pagination" :current-page="pagination.page"
        :page-sizes="pagination.pageSizeOptions" :page-size="pagination.size" :layout="pagination.layoutOptions"
        :total="pagination.total" @size-change="paginationSizeChange" @current-change="paginationCurrentChange">
      </el-pagination>
      <el-dialog :visible.sync="dialogVisible">
        <img width="100%" :src="dialogImageUrl" alt="" />
      </el-dialog>
    </template>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin'
import moment from 'moment'
import store from '@/store/index'
import axios from 'axios'
export default {
  name: 'dutyLogTab',
  mixins: [tableListMixin],
  data() {
    return {
      moment,
      searchForm: {
        date: '', // 时间
      },
      dialogImageUrl: '',
      dialogVisible: false,
      tableData: [],
      tableLoadingStatus: false
    }
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    // 获取列表数据
    getDataList() {
      this.tableData = []
      this.tableLoadingStatus = true
      const params = {
        pageSize: this.pagination.size,
        page: this.pagination.current,
        ...this.searchForm
      }
      params.requestDate = params.date
      delete params.date
      this.$api.supplierAssess
        .getDutyLOgList(params)
        .then((res) => {
          this.tableLoadingStatus = false
          if (res.code === '200') {
            this.tableData = res.data.records
            this.pagination.total = res.data.total
          } else {
            this.tableData = []
            this.pagination.total = 0
          }
        })
        .catch((err) => {
          this.tableLoadingStatus = false
        })
    },
    // 表单搜索按钮点击
    onSearch() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 表单重置按钮点击
    onReset() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    // 操作
    onOperate(row) {
      window.open(row.reportFileUrl, '_blank');
    }
  }
}
</script>
<style scoped lang="scss">
.originalTab {
  ::v-deep(> .container-content) {
    height: 100%;
    width: 100%;
    background-color: #fff;

    .dutyTeam {
      // width: 110px;
      margin-right: 10px;
    }
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__actions {
    padding-bottom: 10px;
  }

  &__table {
    height: calc(100% - 180px);
  }

  &__pagination {
    margin-top: 10px;
  }
}

.el-form-item {
  margin-bottom: 8px !important;
}
</style>
