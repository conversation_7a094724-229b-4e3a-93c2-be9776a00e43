<template>
  <div class="content-box">
    <div class="content-left">
      <div class="left-top-title">
        <div class="top-icon"></div>
        <div>参数类型</div>
      </div>
      <div class="left-bottom-box">
        <el-tree ref="tree" v-loading="treeLoading" :data="treeData" :props="defaultProps" node-key="id" :check-on-click-node="true" @node-click="handleNodeClick"> </el-tree>
      </div>
    </div>
    <div class="content-right">
      <div class="right-top-form">
        <el-form ref="formInline" :inline="true" :model="formInline" class="demo-form-inline">
          <el-form-item label="" prop="materialName">
            <el-input v-model="formInline.materialName" placeholder="配件名称/配件编码" style="width: 300px"></el-input>
          </el-form-item>
          <el-form-item label="" prop="warehouseId">
            <el-select v-model="formInline.warehouseId" placeholder="选择仓库" filterable clearable>
              <el-option v-for="item in warehouseList" :key="item.id" :label="item.warehouseName" :value="item.id"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSubmit">查询</el-button>
            <!-- 重置 -->
            <el-button type="primary" plain @click="onReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="right-bottom-box">
        <!-- 导出 -->
        <el-button type="primary" @click="exportExcel">导出</el-button>
        <div class="right-bottom-table">
          <el-table
            v-loading="tableLoading"
            :data="tableData"
            border
            height="calc(100%)"
            style="width: 100%; overflow: auto"
            :cell-style="{ padding: '8px 0' }"
            stripe
            :header-cell-style="{ background: '#f2f4fbd1' }"
            :empty-text="emptyText"
            highlight-current-row
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="60"> </el-table-column>
            <el-table-column v-for="(item, index) in inventoryManagement.tableHeader" :key="index" :prop="item.prop" :label="item.label" :width="item.width" show-overflow-tooltip>
            </el-table-column>
          </el-table>
        </div>
        <div class="" style="padding-top: 10px; text-align: right">
          <el-pagination
            :current-page="paginationData.currentPage"
            :page-sizes="[15, 30, 50, 100]"
            :page-size="paginationData.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="paginationData.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { inventoryManagement } from './json/index.js'
import axios from 'axios'
import { transData } from '@/util'
export default {
  name: 'inventoryManagement',
  components: {},
  mixins: [],
  props: {},
  data() {
    return {
      inventoryManagement,
      treeData: [],
      defaultProps: {
        value: 'id',
        label: function (data, node) {
          return `${data.name}(${data.count})`
        }
      },
      tableData: [],
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      formInline: {
        warehouseId: '',
        materialName: '',
        materialCode: '',
        materialTypeCode: ''
      },
      emptyText: '暂无数据',
      userInfo: {},
      warehouseList: [],
      multipleSelection: [],
      treeLoading: false,
      tableLoading: false
    }
  },
  computed: {},
  watch: {},
  created() {
    this.userInfo = this.$store.state.user.userInfo.user
    this.formInline.materialName = this.$route.query?.materialCode
    this.init()
    this.onSubmit()
  },
  mounted() {},
  methods: {
    onSubmit() {
      const {
        paginationData: { currentPage, pageSize },
        formInline: { warehouseId, materialName, materialTypeCode }
      } = this
      const params = {
        currentPage,
        pageSize,
        warehouseId,
        materialName,
        materialCode: materialName,
        materialTypeCode,
        materialTypeName: '',
        userType: '1'
      }
      this.tableLoading = true
      this.$api
        .getListData(params)
        .then((res) => {
          const { code, message } = res
          if (code === '200') {
            const { list, sum } = res.data
            this.tableData = list
            this.paginationData.total = sum
          } else {
            this.$message.error(message)
          }
        })
        .catch((err) => {
          this.$message.error('查询失败！')
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    // 重置
    onReset() {
      this.formInline = {
        warehouseId: '',
        materialName: '',
        materialCode: '',
        materialTypeCode: ''
      }
      this.onSubmit()
    },
    // 导出
    exportExcel() {
      const {
        userInfo: { staffId, staffName, unitCode, hospitalCode },
        multipleSelection
      } = this
      if (!multipleSelection.length) {
        this.$message.error('请选择要导出的数据')
        return
      }
      const ids = multipleSelection.map((item) => item.id).join(',')
      const formData = {
        ids,
        userId: staffId,
        userName: staffName,
        unitCode,
        hospitalCode
      }
      axios({
        method: 'get',
        url: __PATH.VUE_APP_PARTS_LIBRARY + 'materialinfo/export2',
        params: formData,
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          const name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          const blob = new Blob([res.data])
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
        })
        .catch(() => {
          this.$message.error('导出失败')
        })
    },
    // 条数
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.onSubmit()
    },
    // 页数
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.onSubmit()
    },
    // 多选
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // cilck树
    handleNodeClick() {
      let res = this.$refs.tree.getCheckedNodes()
      let temp = []
      if (res.length > 0) {
        res.forEach((res) => {
          if (res.code) {
            temp.push(res.code)
          }
        })
      }
      this.formInline.materialTypeCode = temp.length ? temp.join(',') : ''
      this.onSubmit()
      this.$refs.tree.setCheckedKeys([])
    },
    // 初始化
    init() {
      const { staffId, staffName } = this.userInfo
      const params = {
        userId: staffId,
        userName: staffName,
        warehouseType: '2',
        status: '0',
        userType: '1'
      }
      this.$api.getWarehouseList(params).then((res) => {
        const { code } = res
        if (code === '200') {
          this.warehouseList = res.data.list
        }
      })
      this.treeLoading = true
      this.$api
        .getMaterialTypeTree(params)
        .then((res) => {
          const { code, message } = res
          if (code === '200') {
            this.treeData = transData(res.data, 'id', 'parentId', 'children')
          } else {
            this.$message.error(message)
          }
        })
        .catch((err) => {
          this.$message.error('查询失败！')
        })
        .finally(() => {
          this.treeLoading = false
        })
    }
  }
}
</script>
<style lang="scss" scoped>
@import './styles/index.scss';
</style>
