<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <div class="search-from">
        <el-select v-model="searchFrom.functionType" class="sino_form_input" placeholder="请选择功能类型" filterable clearable>
          <el-option v-for="item in spaceTypeList" :key="item.id" :label="item.dictName" :value="item.dictName"></el-option>
        </el-select>
        <el-input v-model="searchFrom.spaceLocalName" placeholder="空间名称" clearable style="width: 200px;"></el-input>
        <div style="display: inline-block;">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="searchForm">查询</el-button>
          <el-button type="primary" icon="el-icon-plus" @click="control('add')">新增</el-button>
        </div>
        <el-image-viewer v-if="showViewer" :on-close="() => showViewer = false" :url-list="iconPathList" />
      </div>
    </div>
    <div slot="content" class="table-content" style="position: relative;">
      <TablePage
        ref="table"
        v-loading="tableLoading"
        :showPage="true"
        :tableColumn="tableColumn"
        :data="tableData"
        height="calc(100% - 40px)"
        :pageData="pageData"
        :pageProps="pageProps"
        @pagination="paginationChange"
        @row-dblclick="control('detail', $event)"
      />
      <el-drawer
        style="position: absolute;"
        title="租赁历史"
        :modal="false"
        :size="'30%'"
        :visible.sync="drawer"
        direction="rtl">
        <el-timeline>
          <el-timeline-item
            v-for="(item, index) in activities"
            :key="index">
            <template>
              <p>{{ getTimeRange(item) }}</p>
              <p>租户名称<span style="margin-left: 20px; color: #686565;">{{ tenantAll.find(ele => ele.id == item.tenantName)?.tenantName || item.tenantName }}</span></p>
              <p>租赁用途<span style="margin-left: 20px; color: #686565;">{{ purposeArr.find(ele => ele.dictCode == item.usage)?.dictValue || item.usage }}</span></p>

            </template>

          </el-timeline-item>
        </el-timeline>
      </el-drawer>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import moment from 'moment'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
export default {
  name: 'userManagement',
  components: {
    ElImageViewer
  },
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    // 进入页面时，先将当前页面的 name 信息存入 keep-alive 全局状态
    setTimeout(() => {
      next((vm) => {
        vm.$store.commit('keepAlive/add', names)
      })
    }, 0)
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['addLease'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      moment,
      purposeArr: [],  // 租赁用途
      tenantAll: [],   // 所有租户
      spaceTypeList: [],  // 功能类型
      activities: [],
      drawer: false,
      tableLoading: false,
      showViewer: false,
      iconPathList: [], // 图片列表
      searchFrom: {
        spaceLocalName: '', // 空间名称
        functionType: ''
      },
      tableColumn: [
        {
          prop: '',
          label: '序号',
          formatter: (scope) => {
            return (this.pageData.current - 1) * this.pageData.size + scope.$index + 1
          }
        },
        {
          prop: 'spaceLocalName',
          label: '空间名称'
        },
        {
          prop: 'spaceLocalNo',
          label: '本地编码'
        },
        {
          prop: 'functionType',
          label: '功能类型'
        },
        {
          prop: 'localtion',
          label: '位置'
        },
        {
          prop: 'tenantName',
          label: '租户名称',
          formatter: (scope) => {
            return this.tenantAll?.find(ele => ele.id == scope.row.tenantName)?.tenantName
          }
        },
        {
          prop: 'usage',
          label: '租赁用途',
          formatter: (scope) => {
            return this.purposeArr?.find(ele => ele.dictCode == scope.row.usage)?.dictValue
          }
        },
        {
          prop: 'tenantStartTime',
          label: '租赁开始时间',
          formatter: (scope) => {
            return moment(scope.row.tenantStartTime).format('YYYY-MM-DD')
          }
        },
        {
          prop: 'tenantEndTime',
          label: '租赁结束时间',
          formatter: (scope) => {
            return moment(scope.row.tenantEndTime).format('YYYY-MM-DD')
          }
        },
        {
          prop: 'updateTime',
          label: '更新时间',
          formatter: (scope) => {
            return moment(scope.row.updateTime).format('YYYY-MM-DD')
          }
        },
        // {
        //   prop: 'type',
        //   label: '应用类型',
        //   formatter: (scope) => {
        //     if (scope.row.type == 1) {
        //       return '子系统'
        //     } else if (scope.row.type == 2) {
        //       return '子模块 '
        //     } else if (scope.row.type == 3) {
        //       return '外部应用'
        //     } else {
        //       return ''
        //     }
        //   }
        // },
        // {
        //   prop: 'icon',
        //   label: '图标',
        //   render: (h, row) => {
        //     return (
        //       <span style="color: #3562DB; cursor: pointer;"  onClick={() => this.viewImage(row.row)}>点击查看</span>
        //     )
        //   }
        // },
        // {
        //   width: 200,
        //   prop: 'remark',
        //   label: '简介'
        // },
        {
          width: 180,
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" style="color: #121F3E" onClick={() => this.control('edit', row.row)}>编辑</span>
                <span class="operationBtn-span" style="color: #fa403c" onClick={() => this.control('del', row.row)}>删除</span>
                <span class="operationBtn-span" style="color: #3562db" onClick={() => this.getDrawerLeaseHistory(row.row)}>租赁历史</span>
              </div>
            )
          }
        }
      ],
      tableData: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      pageProps: {
        page: 'current',
        pageSize: 'size',
        total: 'total'
      }
    }
  },
  watch: {

  },
  activated() {
    this.getApplicationList()
  },
  mounted() {
    this.valveTypeListFn()
    this.getQueryTenantAllList()
    this.getDictionaryList()
    this.getApplicationList()
  },
  methods: {
    getTimeRange(item) {
      return this.moment(item.tenantStartTime).format('YYYY-MM-DD') + '~' + this.moment(item.tenantEndTime).format('YYYY-MM-DD')
    },
    getDrawerLeaseHistory(row) {
      this.$api.queryLeaseHistoryById({id: row.spaceId}).then(res => {
        this.activities = res.data
        this.drawer = true
      })
    },
    // 获取空间功能类型字典列表
    valveTypeListFn() {
      this.$api
        .valveTypeList({
          typeValue: 'SP'
        })
        .then((res) => {
          if (res.code == 200) {
            this.spaceTypeList = res.data
          }
        })
    },
    getDictionaryList() {
      let data = {
        dictType: '1',
        parentId: '1'
      }
      this.$api.getSpaceDictionaryList(data).then(res => {
        this.purposeArr = res.data
      })
    },
    getQueryTenantAllList() {
      this.$api.queryTenantAllList().then(res => {
        this.tenantAll = res.data
      })
    },
    // 查看图片
    viewImage(row) {
      let {activeIcon, defaultIcon} = JSON.parse(row.icon)
      let images = []
      if (!activeIcon && !defaultIcon) {
        this.$message.info('当前没有可预览的图片')
        return
      }
      if (activeIcon) images.push(activeIcon)
      if (defaultIcon) images.push(defaultIcon)
      this.iconPathList = images
      this.showViewer = true
    },
    // 查询
    searchForm() {
      this.getApplicationList()
    },
    // 重置查询
    resetForm() {
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.searchForm()
    },
    control(type, row) {
      if (['add', 'edit', 'detail'].includes(type)) {
        this.$router.push({
          path: '/spaceLeasing/leaseManagement/addLease',
          query: {
            type,
            id: row?.id ?? ''
          }
        })
      } else if (type == 'del') {  // 删除
        this.$confirm('删除后将无法恢复，是否确定删除？', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api.deleteById({id: row.id}, { 'operation-type': 3, 'operation-name': row.spaceLocalName, 'operation-id': row.id}).then(res => {
            if (res.code == 200) {
              this.$message({ message: '删除成功', type: 'success'})
              this.isLastPage(1)
              this.searchForm()
            } else {
              this.$message({ message: res.msg, type: 'error'})
            }
          })
        })
      }
    },
    // 获取应用列表
    getApplicationList() {
      let param = {
        ...this.searchFrom,
        pageParams: {
          pageSize: this.pageData.size,
          currentPage: this.pageData.current
        }
      }
      this.tableLoading = true
      this.$api.getLeaseListByPage(param).then(res => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.pageData.total = res.data.total
        }
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    // 判断当前页是否是最后一页
    isLastPage (deleteNum) {
      let deleteAfterPage = Math.ceil((this.pageData.total - deleteNum) / this.pageData.size)
      let currentPage = this.pageData.current > deleteAfterPage ? deleteAfterPage : this.pageData.current
      this.pageData.current = currentPage < 1 ? 1 : currentPage
    },
    paginationChange(pagination) {
      // this.pageData.size = pagination.pageSize
      // this.pageData.current = pagination.page
      Object.assign(this.pageData, pagination)
      this.getApplicationList()
    }
  }
}
</script>
<style lang="scss" scoped>
.control-btn-header {
  padding: 10px !important;

  .search-from {
    & > div {
      margin-right: 10px;
    }
  }
}

.table-content {
  margin-top: 15px;
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 10px;
}
</style>
<style lang="scss">
.operationBtn-span {
  margin-right: 10px;
}
</style>
