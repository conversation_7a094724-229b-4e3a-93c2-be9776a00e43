<!-- 运行监测 -->
<template>
  <ContentCard :title="item.componentTitleShow" :scrollbarHover="true" :cstyle="{ height: '100%' }" class="drag_class"
    :hasMoreOper="['edit']" @more-oper-event="(val) => $emit('all-more-Oper', val, 'operationMonitor')">
    <div slot="content" class="operation-list">
      <div class="monitor-content-left">
        <p v-for="(item, index) in options" :key="index" @click="changeSelect(item.sysOf1, index)"
          :class="{ 'active': currentSelectedIndex === index }">
          <img src="@/assets/images/newMonitor/humiture-sel.png"
            v-if="item.code === 'PDXTWSDCGQ' && currentSelectedIndex === index" alt="">
          <img src="@/assets/images/newMonitor/humiture.png"
            v-if="item.code === 'PDXTWSDCGQ' && currentSelectedIndex !== index" alt="">
          <img src="@/assets/images/newMonitor/water-sel.png"
            v-if="item.code === 'PDXTSJCGQ' && currentSelectedIndex === index" alt="">
          <img src="@/assets/images/newMonitor/water.png"
            v-if="item.code === 'PDXTSJCGQ' && currentSelectedIndex !== index" alt="">
          <img src="@/assets/images/newMonitor/gas-sel.png"
            v-if="item.code === 'PDXTQTCGQ' && currentSelectedIndex === index" alt="">
          <img src="@/assets/images/newMonitor/gas.png"
            v-if="item.code === 'PDXTQTCGQ' && currentSelectedIndex !== index" alt="">
          <img src="@/assets/images/newMonitor/noise-sel.png"
            v-if="item.code === 'PDXTZSCGQ' && currentSelectedIndex === index" alt="">
          <img src="@/assets/images/newMonitor/noise.png"
            v-if="item.code === 'PDXTZSCGQ' && currentSelectedIndex !== index" alt="">
          {{ item.name }}
        </p>
      </div>
      <div class="monitor-content-right" v-if="currentSelectedIndex === 0">
        <div class="monitor-content-list">
          <el-select v-model="grouping" placeholder="请选择" @change="handleSelectChange">
            <el-option v-for="item in listData" :key="item.id" :label="item.assetsName" :value="item.id">
            </el-option>
          </el-select>
          <a style="color: #3f63d3;" @click="more">更多</a>
        </div>
        <div class="monitor-content-list">
          <div id="temperature"></div>
          <div id="humidity"></div>
        </div>
      </div>
      <div class="monitor-content-right" v-else>
        <div>
          <a class="monitor-content-a" @click="more">更多</a>
          <div class="monitor-content-list">
            <div class="monitor-content-div" v-for="item in listData" :key="item.id" @click="skip(item)">
              <img :src="getImageSource(filteredProperties(item.iotPropertyList))" alt="Dynamic Image">
              <p>
                {{ item.assetsName }}<br>
                <span v-if="item.iotPropertyList && item.iotPropertyList.length > 0">
                  <span v-for="state in filteredProperties(item.iotPropertyList)" :key="state.id">
                    {{ state.metadataName }}: {{ state.valueText }}
                  </span>
                </span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ContentCard>
</template>

<script>
import * as echarts from 'echarts'
import waterImage from '@/assets/images/newMonitor/water.png';
import gasImage from '@/assets/images/newMonitor/gas.png';
import noiseImage from '@/assets/images/newMonitor/noise.png';
import defaultImage from '@/assets/images/newMonitor/water-sel.png';
export default {
  name: 'envMonitor',
  props: {
    item: {
      type: Object,
      default: () => { }
    },
    systemCode: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      currentSelectedIndex: 0,
      options: [],
      listData: [],
      grouping: '',
      completionRate: 0,
      pageSize: 100,
      sysOf1: "",
      temp: {},//温度
      humidity: {},//湿度
      ImmersionState: '',//水浸
      concentration: '',//气体
      decibel: '',//噪声
      groupId: '',//噪声
      equipAttr: "2",
    }
  },
  watch: {
    projectCode: {
      handler(val, oldVal) {
        this.getEquipmentOperationMonitor()
      },
      deep: true
    }
  },
  mounted() {
    setTimeout(() => {
      this.getProductList()
      this.humitureChart()
      this.humitureChart1()
    }, 100)
  },
  methods: {
    getImageSource(filteredStates) {
      if (filteredStates.length > 0) {
        const metadataTag = filteredStates[0].metadataTag;
        switch (metadataTag) {
          case 'ImmersionState':
            return waterImage;
          case 'concentration':
            return gasImage;
          case 'decibel':
            return noiseImage;
          default:
            return defaultImage; // 默认图片
        }
      }
      return defaultImage; // 如果没有匹配的状态，返回默认图片
    },
    filteredProperties(iotPropertyList) {
      const tagsToFilter = ['ImmersionState', 'concentration', 'decibel'];
      return iotPropertyList.filter(item => tagsToFilter.includes(item.metadataTag));
    },
    // 品类切换
    changeSelect(val, index) {
      this.sysOf1 = val
      this.currentSelectedIndex = index;
      if (index === 0) {
        this.pageSize = 100
        setTimeout(() => {
          this.humitureChart()
          this.humitureChart1()
        }, 100)
      } else {
        this.pageSize = 4
      }
      this.getDataList(this.groupId)
    },
    // 更多
    more() {
      let path = 'operationalMonitoring'
      // 强制更新组件
      this.$forceUpdate();
      if (!path) return
      this.$router.push({
        path: path,
        query: {
          sysOf1: this.sysOf1,
        }
      })
    },
    // 跳转详情
    skip(item) {
      let path = 'operationalMonitoring/deviceDetails'
      if (!path) return
      this.$router.push({
        path: path,
        query: {
          id: item.id,
          assetsName: item.assetsName,
          systemCode: this.systemCode,
          equipAttr: this.equipAttr
        }
      })
    },
    // 获取监测项列表
    getDataList(id) {
      let params = {
        dictionaryDetailsCode: this.systemCode,
        equipAttr: this.equipAttr,
        page: 1,
        pageSize: this.pageSize,
        sysOf1: this.sysOf1,
        groupId: id || "",
      }
      this.listData = []
      this.$api
        .getOperationalMonitoringList(params)
        .then((res) => {
          if (res.code === '200' || res.code === 200) {
            this.listData = res.data.records || []
            if (this.listData.length) {
              this.grouping = this.listData[0].id
              this.handleSelectChange(this.grouping)
            } else {
              this.grouping = ''
              this.handleSelectChange(this.grouping)
            }
            this.ImmersionState = this.listData.iotPropertyList.find((item) => {
              return item.metadataTag == "ImmersionState"
            })
          }
        })
        .catch(() => {
        })
    },
    // 温湿度切换
    handleSelectChange(val) {
      if (val) {
        const dataList = this.listData.find((item) => {
          return item.id == val
        })
        this.temp = dataList.iotPropertyList.find((item) => {
          return item.metadataTag == "temp"
        })
        this.humidity = dataList.iotPropertyList.find((item) => {
          return item.metadataTag == "humidity"
        })

      } else {
        this.temp = {}
        this.humidity = {}
      }
      this.humitureChart()
      this.humitureChart1()
    },
    humitureChart() {
      const getchart = echarts.init(document.getElementById('temperature'))
      const option = {
        series: [
          {
            type: 'gauge',
            radius: '90%',
            center: ['50%', '55%'],
            itemStyle: {
              color: '#3562db'
            },
            progress: {
              show: true,
              width: 5
            },
            axisLine: {
              lineStyle: {
                width: 5
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              length: 8,
              lineStyle: {
                width: 2,
                color: '#999'
              }
            },
            axisLabel: {
              distance: 16,
              color: '#999',
              fontSize: 14
            },
            anchor: {
              show: true,
              showAbove: true,
              size: 25,
              itemStyle: {
                borderWidth: 5,
                borderColor: '#3562db'
              }
            },
            title: {
              show: true,
              offsetCenter: [0, '40%']
            },
            detail: {
              show: true,
              valueAnimation: true,
              fontSize: 18,
              offsetCenter: [0, '65%'],
              formatter: '{value}℃'
            },
            data: [
              {
                value: Number(this.temp.value),
                name: this.temp.metadataName
              }
            ]
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    humitureChart1() {
      const getchart1 = echarts.init(document.getElementById('humidity'))
      const option = {
        series: [
          {
            type: 'gauge',
            radius: '90%',
            center: ['50%', '55%'],
            itemStyle: {
              color: '#3562db'
            },
            progress: {
              show: true,
              width: 5
            },
            axisLine: {
              lineStyle: {
                width: 5
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              length: 8,
              lineStyle: {
                width: 2,
                color: '#999'
              }
            },
            axisLabel: {
              distance: 16,
              color: '#999',
              fontSize: 14
            },
            anchor: {
              show: true,
              showAbove: true,
              size: 25,
              itemStyle: {
                borderWidth: 5,
                borderColor: '#3562db'
              }
            },
            title: {
              show: true,
              offsetCenter: [0, '40%']
            },
            detail: {
              show: true,
              valueAnimation: true,
              fontSize: 20,
              offsetCenter: [0, '65%'],
              formatter: '{value}%'
            },
            data: [
              {
                value: Number(this.humidity.value),
                name: this.humidity.metadataName
              }
            ]
          }
        ]
      }
      getchart1.clear()
      getchart1.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart1.resize()
      })
    },
    getProductList(id) {
      this.groupId = id
      this.$api.getProductList({ groupId: id || '' }).then((res) => {
        if (res.code == 200 || res.code == '200') {
          this.options = res.data
          this.sysOf1 = this.options[0]?.sysOf1
          this.getDataList(id)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.operation-list {
  height: 100%;
  display: flex;
  gap: 16px;
  flex-direction: row;

  .monitor-content-left {
    width: 150px;

    p {
      padding: 10px 15px;
      color: #333;
      border-radius: 4px;
      margin-bottom: 10px;
      background: #faf9fc;
      height: 22%;
      display: flex;
      align-items: center;
    }

    p.active {
      background: #3f63d3;
      color: #fff;

      i {
        background: #3f63d3;
      }
    }
  }

  .monitor-content-right {
    width: 100%;
    height: 100%;
    flex: 1;
    flex-direction: column;
    overflow: auto;
    margin-left: 16px;

    .monitor-content-list {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
    }

    #temperature,
    #humidity {
      width: 49%;
      height: 230px;
    }

    .monitor-content-a {
      color: #3f63d3;
      display: block;
      text-align: right;
    }

    .monitor-content-div {
      display: flex;
      justify-content: center;
      align-items: center;
      background: #faf9fc;
      flex: 0 0 calc(50% - 20px);
      box-sizing: border-box;
      padding: 6%;
      margin: 10px;
      cursor: pointer;

      p {
        margin-left: 10px;
        margin-bottom: 0;
      }
    }
  }
}
</style>