import Layout from '@/layout'
import EmptyLayout from '@/layout/empty'
export default [
  {
    path: '/targetAnalysis',
    component: Layout,
    redirect: '/targetAnalysis/targetFile',
    name: 'targetAnalysis',
    meta: {
      title: '指标档案',
      menuAuth: '/targetAnalysis/targetFile'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'targetFile',
        name: 'targetFile',
        component: () => import('@/views/targetMangement/targetFile/index.vue'),
        meta: {
          title: '指标档案',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/targetAnalysis'
        }
      },
      {
        path: 'targetResult',
        name: 'targetResult',
        component: () => import('@/views/targetMangement/targetFile/targetResult.vue'),
        meta: {
          title: '指标结果',
          sidebar: false,
          activeMenu: '/targetAnalysis'
        }
      },
      {
        path: 'targetTracking',
        name: 'targetTracking',
        component: () => import('@/views/targetMangement/targetFile/targetTracking.vue'),
        meta: {
          title: '指标跟踪',
          sidebar: false,
          activeMenu: '/targetAnalysis'
        }
      }
    ]
  },
  {
    path: '/targetPlan',
    component: Layout,
    redirect: '/targetPlan/targetPlanIndex',
    name: 'targetPlan',
    meta: {
      title: '指标计划',
      menuAuth: '/targetPlan/targetPlanIndex'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'targetPlanIndex',
        name: 'targetPlanIndex',
        component: () => import('@/views/targetMangement/targetPlan/index.vue'),
        meta: {
          title: '指标计划',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/targetPlan'
        }
      },
      {
        path: 'addTargetPlan',
        name: 'addTargetPlan',
        component: () => import('@/views/targetMangement/targetPlan/addTargetPlan.vue'),
        meta: {
          title: '新增指标计划',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/targetPlan'
        }
      },
      {
        path: 'targetPlanDetail',
        name: 'targetPlanDetail',
        component: () => import('@/views/targetMangement/targetPlan/targetPlanDetail.vue'),
        meta: {
          title: '指标计划详情',
          sidebar: false,
          activeMenu: '/targetPlan'
        }
      }
    ]
  },

  {
    path: '/targetTemplate',
    component: Layout,
    redirect: '/targetTemplate/targetTemplateIndex',
    name: 'targetTemplate',
    meta: {
      title: '指标模板',
      menuAuth: '/targetTemplate/targetTemplateIndex'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'targetTemplateIndex',
        name: 'targetTemplateIndex',
        component: () => import('@/views/targetMangement/targetTemplate/index.vue'),
        meta: {
          title: '指标模板',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/targetTemplate'
        }
      },
      {
        path: 'updateTemplate',
        name: 'updateTemplate',
        component: () => import('@/views/targetMangement/targetTemplate/update.vue'),
        meta: {
          title: '新增指标模板',
          sidebar: false,
          breadcrumb: false,
          activeMenu: 'targetTemplate'
        }
      }
    ]
  },
  {
    path: '/targetLibrary',
    component: Layout,
    redirect: '/targetLibrary/targetLibraryIndex',
    name: 'targetLibrary',
    meta: {
      title: '指标库',
      menuAuth: '/targetLibrary/targetLibraryIndex'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'targetLibraryIndex',
        name: 'targetLibraryIndex',
        component: () => import('@/views/targetMangement/targetLibrary/index.vue'),
        meta: {
          title: '指标库',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/targetLibrary'
        }
      }
    ]
  },
  {
    path: '/targetConfig',
    component: Layout,
    redirect: '/targetConfig/targetConfigIndex',
    name: 'targetConfig',
    meta: {
      title: '指标配置',
      menuAuth: '/targetConfig/targetConfigIndex'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'targetConfigIndex',
        name: 'targetConfigIndex',
        component: () => import('@/views/targetMangement/targetConfig/index.vue'),
        meta: {
          title: '指标配置',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/targetConfig'
        }
      },
      {
        path: 'updateCycleRules',
        name: 'updateCycleRules',
        component: () => import('@/views/targetMangement/targetConfig/cycleRules/update.vue'),
        meta: {
          title: '添加周期规则',
          sidebar: false,
          breadcrumb: false,
          activeMenu: 'targetConfig'
        }
      },
      {
        path: 'cycleRulesDetail',
        name: 'cycleRulesDetail',
        component: () => import('@/views/targetMangement/targetConfig/cycleRules/detail.vue'),
        meta: {
          title: '周期规则详情',
          sidebar: false,
          breadcrumb: false,
          activeMenu: 'targetConfig'
        }
      },

      {
        path: 'updateLevelRules',
        name: 'updateLevelRules',
        component: () => import('@/views/targetMangement/targetConfig/levelRules/update.vue'),
        meta: {
          title: '添加等级规则',
          sidebar: false,
          breadcrumb: false,
          activeMenu: 'targetConfig'
        }
      }
    ]
  }
]
