<template>
  <div style="width: 100%;height: 100%;padding: 10px" class="drag_class">
    <div class="card_box_title card_box_short_bg">使用年限分布</div>
    <div v-if="!serviceLifeShow" class="echart-null">
      <img src="@/assets/images/null.png" alt="" />
      <div>暂无数据~</div>
    </div>
    <div v-else style="width: 100%; height: calc(100% - 31px)">
      <div id="serviceLife" ref="serviceLife"></div>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
export default {
  data() {
    return {
      serviceLifeShow: false,
      serviceLifeData: [],
      timer: null
    }
  },
  mounted() {
    this.getIaasStatistics()
    this.scheduledTasks()
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
  },
  methods: {
    scheduledTasks() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
      this.timer = setInterval(() => {
        this.getIaasStatistics()
      }, 30000)
    },
    echartsResize() {
      this.$nextTick(() => {
        setTimeout(() => {
          if (document.getElementById('serviceLife')) {
            echarts.init(document.getElementById('serviceLife')).resize()
          }
        }, 50)
      })
    },
    getIaasStatistics() {
      this.$api.getIaasStatistics({}).then((res) => {
        if (res.code === '200') {
          // 获取使用年限数据
          if (res.data.hasOwnProperty('life')) {
            this.serviceLifeShow = true
            this.$nextTick(() => {
              this.setServiceLifeEcharts(res.data.life)
            })
          } else {
            this.serviceLifeShow = false
          }
        }
      })
    },
    // 使用年限数据echarts
    setServiceLifeEcharts(data) {
      const getchart = echarts.init(document.getElementById('serviceLife'))
      const nameList = data.xserial
      const valueList = data.yserial
      // 数组求和
      // const total = eval(valueList.join('+'))
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        grid: {
          left: '5%',
          right: '15%',
          top: '10%',
          bottom: '5%',
          containLabel: true
        },
        xAxis: [
          {
            name: '年',
            type: 'category',
            axisLine: {
              lineStyle: {
                color: '#283849',
                type: 'dashed'
              }
            },
            axisLabel: {
              textStyle: {
                color: '#6A737C'
              }
            },
            data: nameList,
            axisTick: { show: false }
          }
        ],
        yAxis: [
          {
            type: 'value',
            minInterval: 1, // 最小刻度是1
            axisLabel: {
              textStyle: {
                color: '#6A737C'
              }
            },
            splitLine: {
              lineStyle: {
                color: '#283849',
                type: 'dashed'
              }
            }
          }
        ],
        series: [
          {
            type: 'bar',
            barWidth: 8,
            data: valueList,
            itemStyle: {
              normal: {
                color: 'rgba(10,132,255,0.4)'
                // color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                //   {
                //     offset: 0,
                //     color: 'rgba(10,132,255,0.4)'
                //   },
                //   {
                //     offset: 1,
                //     color: 'rgba(10,132,255,0)'
                //   }
                // ])
                // label: {
                //   show: true,
                //   position: 'top',
                //   formatter: (params) => {
                //     var text
                //     text = ((params.data * 100) / total).toFixed(2) + '%'
                //     return text
                //   }
                // }
              }
            }
          },
          {
            type: 'scatter',
            symbolSize: 10,
            itemStyle: {
              color: {
                type: 'radial',
                r: 2,
                colorStops: [
                  {
                    offset: 0,
                    color: '#0A84FF'
                  },
                  {
                    offset: 1,
                    color: '#0A84FF'
                  }
                ],
                global: false
              }
            },
            tooltip: {
              show: false
            },
            silent: true,
            data: valueList
          },
          {
            name: '',
            type: 'line',
            symbolSize: 25,
            symbol: 'circle',
            label: {
              // normal: {
              //   show: true,
              //   position: 'top',
              //   formatter: '{c}%',
              //   color: '#fff'
              // }
            },
            tooltip: {
              show: false
            },
            itemStyle: {
              normal: {
                color: 'rgba(0, 0, 0, 0)',
                borderColor: '#0A84FF',
                borderWidth: 2
              }
            },
            data: valueList
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.card_box_short_bg {
  background: url('~@/assets/images/elevator/module-bg.png') no-repeat;
  background-size: 100% 100%;
}
.card_box_title {
  height: 31px;
  width: 100%;
  line-height: 31px;
  padding-left: 10px;
  font-size: 15px;
  font-family: Alibaba PuHuiTi-Medium, Alibaba PuHuiTi;
  font-weight: 500;
  color: #b7cfff;
}
.card_box_bg {
  background: url('~@/assets/images/elevator/card-title-bg.png') no-repeat;
  background-size: 100% 100%;
}
.echarts_title {
  height: 20px;
  font-size: 15px;
  font-family: NotoSansHans-Medium, NotoSansHans;
  font-weight: 600;
  color: #393a3d;
  line-height: 20px;
  text-align: center;
}
.echart-null {
  margin: 0 auto;
  height: calc(100% - 31px);
  width: 50%;
  text-align: center;
  color: #8a8c8f;
  img {
    max-width: 100%;
    max-height: calc(100% - 20px);
  }
  div {
    font-size: 14px;
  }
}
#serviceLife{
  width: 100%;
  height: 100%;
  z-index: 2;
}
</style>
