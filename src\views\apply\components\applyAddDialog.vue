<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="应用添加"
    width="60%"
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="dialog-content">
      <TablePage
        ref="roleTable"
        v-loading="tableLoading"
        :showPage="false"
        :tableColumn="tableColumn"
        :data="tableData"
        height="100%"
        :pageData="pageData"
        @pagination="paginationChange"
        @selection-change="handleSelectionChange"
      />
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="confirm">确 定</el-button>
      <el-image-viewer v-if="showViewer" :on-close="() => showViewer = false" :url-list="iconPathList" />
    </span>
  </el-dialog>
</template>

<script lang="jsx">
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
export default {
  name: 'applyAddDialog',
  components: {
    ElImageViewer
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectAppList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      tableLoading: false,
      showViewer: false,
      tableData: [],
      multipleSelection: [],
      tableColumn: [
        {
          type: 'selection',
          align: 'center'
        },
        {
          prop: '',
          label: '序号',
          formatter: (scope) => {
            return (this.pageData.page - 1) * this.pageData.pageSize + scope.$index + 1
          }
        },
        {
          prop: 'shortName',
          label: '应用简称'
        },
        {
          prop: 'fullName',
          label: '应用全称'
        },
        {
          prop: 'path',
          label: '应用地址'
        },
        {
          prop: 'type',
          label: '应用类型',
          formatter: (scope) => {
            if (scope.row.type == 1) {
              return '子系统'
            } else if (scope.row.type == 2) {
              return '子模块 '
            } else if (scope.row.type == 3) {
              return '外部应用'
            } else {
              return ''
            }
          }
        },
        {
          prop: 'icon',
          label: '图标',
          render: (h, row) => {
            return (
              <span style="color: #3562DB; cursor: pointer;"  onClick={() => this.viewImage(row.row)}>点击查看</span>
            )
          }
        },
        {
          width: 200,
          prop: 'remark',
          label: '简介'
        }
      ],
      pageData: {
        page: 1,
        pageSize: 999,
        total: 0
      }
    }
  },
  computed: {

  },
  created() {
    this.getApplicationList()
  },
  methods: {
    confirm() {
      if (!this.multipleSelection.length) {
        this.$message({message: '请选择应用', type: 'error'})
        return
      }
      this.$emit('selectAppFinish', this.multipleSelection)
      this.$emit('update:visible', !this.visible)
    },
    // 查看图片
    viewImage(row) {
      let {activeIcon, defaultIcon} = JSON.parse(row.icon)
      let images = []
      if (!activeIcon && !defaultIcon) {
        this.$message.info('当前没有可预览的图片')
        return
      }
      if (activeIcon) images.push(this.$tools.imgUrlTranslation(activeIcon))
      if (defaultIcon) images.push(this.$tools.imgUrlTranslation(defaultIcon))
      this.iconPathList = images
      this.showViewer = true
    },
    // 获取应用列表
    getApplicationList() {
      let param = {
        pageSize: this.pageData.pageSize,
        page: this.pageData.page
      }
      let newArr = []
      this.tableLoading = true
      // this.$refs.table.clearSelection()
      this.$api.GetApplicationList(param).then(res => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.pageData.total = res.data.total
          if (this.selectAppList.length) {
            this.tableData.forEach(item => {
              if (this.selectAppList.map(v => v.id).includes(item.id)) {
                newArr.push(item)
              }
            })
            this.$nextTick(() => {
              newArr.forEach(item => {
                this.$refs.roleTable.toggleRowSelection(item)
              })
            })
          }
        }
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    // 多选
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    paginationChange(pagination) {
      this.pageData.page = pagination.page
      this.pageData.pageSize = pagination.pageSize
      this.getApplicationList()
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    }
  }
}

</script>

<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  background: #fff;
  padding: 10px;
  height: 400px;
}
</style>
