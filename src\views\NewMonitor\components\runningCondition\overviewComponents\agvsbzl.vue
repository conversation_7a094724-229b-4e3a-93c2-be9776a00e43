<!-- 运行监测 -->
<template>
  <ContentCard :title="item.componentTitleShow" :scrollbarHover="true" :cstyle="{ height: '100%' }" class="drag_class"
    :hasMoreOper="['edit']" @more-oper-event="(val) => $emit('all-more-Oper', val, 'operationMonitor')">
    <div slot="content" class="operation-list">
      <div class="list-item" @click="detailed(item)" style="cursor: pointer;">
        <p><span>总数</span><i>{{ operationList.total }}</i><em>台</em></p>
        <p><span>空闲</span><i>{{ operationList.idling }}</i><em>台</em></p>
        <p><span>工作中</span><i>{{ operationList.working }}</i><em>台</em></p>
        <p><span>充电中</span><i>{{ operationList.charging }}</i><em>台</em></p>
        <p><span>呼叫中</span><i>{{ operationList.calling }}</i><em>台</em></p>
        <p><span>离线</span><i>{{ operationList.offline }}</i><em>台</em></p>
        <p><span>故障</span><i>{{ operationList.failing }}</i><em>台</em></p>
      </div>
    </div>
  </ContentCard>
</template>
<script>
export default {
  name: 'agvsbzl',
  props: {
    item: {
      type: Object,
      default: () => { }
    },
    systemCode: {
      type: String,
      default: ''
    },
    deviceId: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      operationList: [],
    }
  },
  created() {
    setTimeout(() => {
      if (this.deviceId) {
        this.getDeviceAnalysisData()
      }
    }, 150)
  },
  methods: {
    // 查看详情
    detailed(val) {
      // let path = 'operationalMonitoring'
      // // 强制更新组件
      // this.$forceUpdate();
      // if (!path) return
      // this.$router.push({
      //   path: path,
      //   query: {
      //     sysOf1: val.sysOf1,
      //   }
      // })
    },
    getDeviceAnalysisData() {
      this.$api.getQueryInstanceFunction(this.deviceId, "robotStatus", {}).then((res) => {
        if (res.status == 200) {
          this.operationList = res.result[0]
        }
      })
    },
  }
}
</script>
<style lang="scss" scoped>
.operation-list {
  width: 100%;
  height: 100%;


  .list-item {
    width: 100%;
    padding: 0 10px;
    display: flex;
    justify-content: space-between;

    p {

      height: 120px;
      background: #fff;
      border-radius: 5px;
      text-align: center;
      margin-bottom: 0;

      span {
        font-size: 14px;
        color: #666;
        display: block;
        margin-top: 30px;
      }

      i {
        font-size: 24px;
        color: #333;
        font-style: normal;
      }

      em {
        font-size: 14px;
        color: #96989A;
        font-style: normal;
      }
    }
  }
}
</style>
