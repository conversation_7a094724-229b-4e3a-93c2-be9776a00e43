<template>
  <el-dialog class="sino_dialog" :title="opty == 'teacher' ? '培训老师' : opty == 'user' ? '到会人员' : '人员'"
    :visible.sync="peopleDialog" :before-close="handleClose" append-to-body>
    <div class="dialog_box">
      <div class="left_tree">
        <div class="filter_input">
          <el-input placeholder="输入关键字进行过滤" v-model="filterText">
          </el-input>
        </div>
        <div class="tree_div">
          <el-tree class="filter-tree" :data="officeTreeData" :props="defaultProps" node-key="id" default-expand-all
            :highlight-current="true" :filter-node-method="filterNode" @node-click="handleNodeClick" ref="tree">
            <span class="span-ellipsis" slot-scope="{ node }">
              <span :title="node.label">{{ node.label }}</span>
            </span>
          </el-tree>
        </div>
      </div>
      <div class="right_table">
        <div class="right_table_view">
          <!-- 搜索部分 -->
          <div class="search_view">
            <div class="input_item">
              <el-input v-model="filterObj.name" placeholder="请输入姓名"></el-input>
            </div>
            <div class="input_item">
              <el-input v-model="filterObj.mobile" placeholder="请输入联系方式"></el-input>
            </div>

            <el-button type="primary" @click="inquireUser">查询</el-button>
            <el-button type="primary" @click="resetUser">重置</el-button>
          </div>
          <!-- table -->
          <div class="table_view">
            <el-table v-loading="tableComponentLoading" :data="userTableData" @selection-change="selectionChange" border
              ref="userTable" :row-key="getRowKeys" height="100%" width="100%">
              <el-table-column type="selection">
              </el-table-column>
              <el-table-column type="index" label="序号" width="70">
              </el-table-column>
              <el-table-column prop="name" label="姓名">
              </el-table-column>
              <el-table-column prop="mobilePhone" label="联系方式">
              </el-table-column>
              <el-table-column prop="controlTeamName" label="组织机构">
              </el-table-column>
            </el-table>
          </div>
          <!-- 分页 -->
          <div class="paging_box">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
              :current-page="paginationData.currentPage" :page-sizes="[50, 100, 200, 500]"
              :page-size="paginationData.pageSize" layout="total, sizes, prev, pager, next, jumper"
              :total="paginationData.total"></el-pagination>
          </div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="sureDialog">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      filterText: "",
      officeTreeData: [],
      defaultProps: {
        children: 'children',
        label: function (data, node) {
          return data.teamName
        },
        value: 'id'
      },
      tableComponentLoading: false,
      userSelectData: [],
      userTableData: [],
      // 人员弹框分页参数
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0,
      },
      // 人员弹框查询参数
      filterObj: {
        mobilePhone: "",
        name: "",
      },
      checkedData: {} // 选择组织数据
    };
  },
  props: {
    peopleDialog: {
      type: Boolean,
      default: false,
    },
    opty: {
      type: String,
      default: '',
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  filters: {},
  mounted() {
    this.hispitalTreeDataAll();
    console.log(this.opty, 'stateOpty');
  },
  methods: {
    handleClose() {
      this.$emit("closeDialog");
    },
    getRowKeys(row) {
      return row.id;
    },
    selectionChange(val) {
      this.userSelectData = val;
    },
    sureDialog() {
      this.$emit("sureDialogUser");
    },
    closeUserDialog() {
      //   this.$emit("closeUserDialog");
    },
    // 查询
    inquireUser() {
      this.paginationData.currentPage = 1;
      this.getTableList();
    },
    // 重置
    resetUser() {
      this.filterObj.mobilePhone = ''
      this.filterObj.name = ''
    },
    handleSizeChange(val) {
      this.paginationData.pageSize = val;
      this.getTableList();
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val;
      this.getTableList();
    },
    handleNodeClick(val) {
      this.checkedData = val
      this.getTableList()
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.teamName.indexOf(value) !== -1;
    },
    // 获取院级组织树结构
    hispitalTreeDataAll() {
      this.$api.getDeptListLaboratory({}).then((res) => {
        let planText = {
          id: '#',
          teamName: '安全管控部门',
          parentId: '',
          allParentIds: '',
          level: 0
        }
        let list = res.data.list
        list.push(planText)
        this.officeTreeData = this.$tools.transData(list, 'id', 'parentId', 'children')
        // this.$nextTick(() => {
        //   this.$refs.tree.setCurrentKey(this.officeTreeData[0].id)
        // })
        this.checkedData = this.officeTreeData[0]
        this.getTableList()
      });

    },
    // 获取人员弹框table 数据
    getTableList(toggleRow = false) {
      this.userTableData = []
      this.tableComponentLoading = true
      let data = {
        currentPage: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize,
        controlTeamId: this.checkedData.id == '#' ? '' : this.checkedData.id, // 选第一级传空
        ...this.filterObj
      }
      this.$api.getControlTeamUserListLaboratory(data).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.tableComponentLoading = false
          this.userTableData = res.data.list
          this.paginationData.total = parseInt(res.data.sum)
        }
      })
        .catch(() => {
          this.tableLoading = this.$store.state.loadingShow
        })
    },
  },
};
</script>

<style lang="scss" scoped>
.sino_dialog {
  ::v-deep .el-dialog {
    height: 600px;
  }
}

.dialog_box {
  height: 420px;
  width: 100%;
  overflow: auto;
  box-sizing: border-box;
}

.dialog-footer {
  border-radius: 0px 0px 10px 10px;
  width: 100%;
  height: 60px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.dialog_box {
  display: flex;

  .left_tree {
    width: 200px;
    height: 100%;
    flex: 0 0 auto;
    margin-right: 20px;
  }

  .right_table {
    flex: 1;
  }

  >div {
    height: 100%;
  }
}

.tree_div {
  height: calc(100% - 34px);
  overflow: auto;
  padding: 10px;
}

.right_table_view {
  height: 100%;

  .search_view {
    display: flex;
    padding: 0px 24px 24px 0;

    .input_item {
      width: 180px;
      margin-right: 16px;
    }
  }

  .table_view {
    width: 100%;
    height: calc(100% - 112px);
  }

  .span-ellipsis {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .paging_box {
    display: flex;
    // justify-content: flex-end;
    padding: 16px 16px 0;
  }
}
</style>
