<template>
  <div class="tabs_content">
    <div slot="content" class="time-line">
      <div v-for="item in data" :key="item.id" class="time-line-box">
        <div class="top-date">
          <div class="data-ymd">{{ item.createdTime.slice(0, 10) }}</div>
          <div class="defaule-icon"></div>
          <div class="data-hms">{{ item.createdTime.slice(10, 19) }}</div>
        </div>
        <!-- 0:创建报警记录 1:真实报警 2:误报 3:演练 4:调试 5: 未确认 6:备注 7:关闭 8:设置为经典案例 9:取消为经典案例 10:派单 11:新建工单 12:已屏蔽 13:取消屏蔽 -->
        <div class="box-content">
          <div class="time-work-order">
            <div class="time-work-order-event">{{ item.operationTypeName }}</div>
            <div class="work-order-detail">
              <span>操作人：</span>
              <span style="padding-left: 10px">{{ item.operationPersonName }}{{ item.operationPersonId ? `（${item.operationPersonId}）` : '' }}</span>
            </div>
            <div class="work-order-detail">
              <span>操作端：</span>
              <span style="padding-left: 10px">{{ item.operationSourceName || '-' }}</span>
            </div>
            <div class="work-order-detail">
              <span>说明：</span>
              <span style="padding-left: 10px">{{ item.remark }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      operationRecord: [],
      operationType: {
        0: '产生报警',
        1: '真实报警',
        2: '误报',
        3: '演练',
        4: '调试',
        5: '未确认', // 不显示信息
        6: '添加说明',
        7: '报警关闭',
        8: '设置为经典案例',
        9: '取消设置为经典案例',
        10: '派单',
        11: '新建工单',
        12: '已屏蔽',
        13: '取消屏蔽'
      }
    }
  },
  mounted() {},
  methods: {}
}
</script>
<style lang="scss" scoped>
.tabs_content {
  width: 100%;
  height: 100%;
  padding: 24px 0;
  overflow: auto;
}
.time-line {
  .time-line-box {
    margin-bottom: 6px;
    .top-date {
      height: 24px;
      line-height: 24px;
      display: flex;
      .data-ymd {
        min-width: 85px;
        font-size: 14px;
        font-family: 'PingFang SC-Medium', 'PingFang SC';
        color: #121f3e;
        font-weight: 600;
      }
      .defaule-icon {
        width: 12px;
        height: 12px;
        margin: auto 10px;
        background: #fff;
        border: 2px solid #3562db;
        border-radius: 50%;
      }
      .data-hms {
        font-size: 14px;
        font-family: 'PingFang SC-Regular', 'PingFang SC';
        color: #414653;
      }
    }
    .box-content {
      margin-left: 100px;
      padding-left: 20px;
      border-left: 2px solid #f6f5fa;
      .time-work-order {
        background: rgb(246 245 250 / 60%);
        border-radius: 4px;
        padding: 15px 10px 10px;
        font-family: 'PingFang SC-Regular', 'PingFang SC';
        .time-work-order-event {
          font-size: 16px;
          color: #3562db;
          margin-bottom: 8px;
        }
        .work-order-detail {
          color: #121f3e;
          padding-bottom: 5px;
        }
      }
    }
  }
}
</style>
