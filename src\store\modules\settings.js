/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-02-27 14:35:19
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-05-18 15:08:54
 * @FilePath: \ihcrs_pc\src\store\modules\settings.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * 存放全局公用状态
 */
import settings from '@/settings'
import storage from '@/util/storage'

const state = {
  ...settings,
  sidebarCollapse: typeof JSON.parse(storage.session.get('sidebarCollapse')) === 'boolean' ? JSON.parse(storage.session.get('sidebarCollapse')) : settings.sidebarCollapse,
  // 侧边栏是否收起（用于记录 pc 模式下最后的状态）
  sidebarCollapseLastStatus: typeof JSON.parse(storage.session.get('sidebarCollapse')) === 'boolean' ? JSON.parse(storage.session.get('sidebarCollapse')) : settings.sidebarCollapse,
  // 显示模式，支持：mobile、pc
  mode: 'pc',
  // 页面标题
  title: '',
  // 是否开启drag模块编辑侧边栏
  dragSidebarCollapse: false,
  // 当前选中的路由标题
  currentRouteMetaTitle: ''
}

const getters = {}

const actions = {}

const mutations = {
  // 设置访问模式，页面宽度小于 992px 时切换为移动端展示
  setMode(state, width) {
    if (state.enableMobileAdaptation && width < 992) {
      state.mode = 'mobile'
    } else {
      state.mode = 'pc'
    }
  },
  // 设置网页标题
  setTitle(state, title) {
    state.title = title
  },
  // 设置侧边栏导航状态
  setSidebarCollapse(state, bool = false) {
    state.sidebarCollapse = bool
    storage.session.set('sidebarCollapse', state.sidebarCollapse)
  },
  // 切换侧边栏导航展开/收起
  toggleSidebarCollapse(state, bool = false) {
    state.sidebarCollapse = bool || !state.sidebarCollapse
    if (state.mode == 'pc') {
      state.sidebarCollapseLastStatus = bool || !state.sidebarCollapseLastStatus
    }
    storage.session.set('sidebarCollapse', state.sidebarCollapse)
  },
  dragSidebarCollapse(state, bool) {
    state.dragSidebarCollapse = bool
    // commit('toggleSidebarCollapse', true)
  },
  setDefaultLang(state, lang) {
    state.defaultLang = lang
  },
  // 更新主题配置
  updateThemeSetting(state, data) {
    Object.assign(state, data)
  },
  // 设置路由标题
  setCurrentRouteMetaTitle(state, title) {
    state.currentRouteMetaTitle = title
  }
}

export default {
  namespaced: true,
  state,
  actions,
  getters,
  mutations
}
