<template>
  <PageContainer>
    <div slot="content" class="page_containner">
      <el-tabs v-model="active">
        <el-tab-pane v-for="(item, index) in tabList" :key="index" :label="item.label" :name="item.value"></el-tab-pane>
      </el-tabs>
      <allDoc v-if="active == '1'" :fromId="fromId" />
      <recycleBin v-if="active == '2'" :constructionType="2" />
    </div>
  </PageContainer>
</template>
<script>
import allDoc from './allDoc.vue'
import recycleBin from '@/views/hazardousChemicalGoods/hazardousChemicalGoods_safety/safetyManageSystem/recycleBin.vue'
export default {
  name: 'constructionFileManage',
  components: {
    allDoc,
    recycleBin
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.fromId = from.query.fromId
    })
  },
  data() {
    return {
      active: '1',
      fromId: '',
      tabList: [
        {
          label: '全部文档',
          value: '1'
        },
        {
          label: '回收站',
          value: '2'
        }
      ]
    }
  },
}
</script>
<style lang="scss" scoped>
.page_containner {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 0 16px;
}
</style>
