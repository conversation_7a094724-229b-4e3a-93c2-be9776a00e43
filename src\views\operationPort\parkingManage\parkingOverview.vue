<template>
  <div id="parkingOverview">
    <div class="batch-control">
      <p>运营总览</p>
      <div v-if="saveAndCancelBtnShow">
        <el-button type="primary" plain @click="cancelStaging">取消</el-button>
        <el-button type="primary" @click="saveStaging">保存</el-button>
      </div>
    </div>
    <dashboard v-if="dashExampleShow" id="dashExample">
      <dash-layout v-bind="dlayout" :debug="false">
        <dash-item v-for="(item, index) in dlayout.items" v-bind.sync="dlayout.items[index]" :key="item.id" @moveEnd="moveEnd" @resizeEnd="resizeEnd(item.id)">
          <div class="card-item">
            <div class="card-empty">
              <header class="header drag_class airconditioning">
                <div class="legend-title">
                  {{ item.name }}
                </div>
                <div v-if="item.name == '运营总览'">
                  <div class="btns">
                    <span :class="{ 'active-btn': collectType == '1' }" @click="changeCompletionRateType('1')">日</span>
                    <span :class="{ 'active-btn': collectType == '2' }" @click="changeCompletionRateType('2')">周</span>
                    <span :class="{ 'active-btn': collectType == '3' }" @click="changeCompletionRateType('3')">月</span>
                  </div>
                </div>
                <el-dropdown class="dropdown-btn" trigger="click" @command="dropDownCommand">
                  <span class="more-operations" @click.stop.prevent>· · ·</span>
                  <el-dropdown-menu slot="dropdown" class="dropdownSelect">
                    <el-dropdown-item>编辑</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </header>

              <div class="card-content" v-if="item.id == '17ghk64g56h4hfh5468s4732'">
                <div class="cont">
                  <div>
                    <div>
                      <img src="../../../assets/images/parkingLot/count-one.png" alt="" />
                    </div>
                    <div>
                      <div style="font-size: 15px; color: #333333">入场车次</div>
                      <div>
                        <span style="font-size: 24px; color: #333333; font-weight: 600">{{ parkingLot.parkingInTime || 0 }}</span
                        >&nbsp;<span style="font-size: 14px; color: #7f848c">辆</span>
                      </div>
                    </div>
                    <div>
                      <div style="font-size: 15px; color: #333333">出场车次</div>
                      <div>
                        <span style="font-size: 24px; color: #333333; font-weight: 600">{{ parkingLot.parkingOutTime || 0 }}</span
                        >&nbsp;<span style="font-size: 14px; color: #7f848c">辆</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div>
                      <img src="../../../assets/images/parkingLot/count-two.png" alt="" />
                    </div>
                    <div>
                      <div style="font-size: 15px; color: #333333">实时收费总额</div>
                      <div>
                        <span style="font-size: 24px; color: #333333; font-weight: 600">{{ parkingLot.chargeTotal || 0 }}</span
                        >&nbsp;<span style="font-size: 14px; color: #7f848c">元</span>
                      </div>
                    </div>
                    <div>
                      <div style="font-size: 15px; color: #333333">收费笔数</div>
                      <div>
                        <span style="font-size: 24px; color: #333333; font-weight: 600">{{ parkingLot.chargeTime || 0 }}</span
                        >&nbsp;<span style="font-size: 14px; color: #7f848c">笔</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-content" v-if="item.id == '2z8df1246f2c8d21bn86s2f5'">
                <div class="cont">
                  <div>
                    <div>
                      <img src="../../../assets/images/parkingLot/count-three.png" alt="" />
                    </div>
                    <div>
                      <div style="font-size: 15px; color: #333333">总车位个数</div>
                      <div>
                        <span style="font-size: 24px; color: #333333; font-weight: 600">{{ carYardStatistics.parkingSpaceTotal || 0  }}</span
                        >&nbsp;<span style="font-size: 14px; color: #7f848c">个</span>
                      </div>
                    </div>
                    <div>
                      <div style="font-size: 15px; color: #333333">可用车位个数</div>
                      <div>
                        <span style="font-size: 24px; color: #333333; font-weight: 600">{{ carYardStatistics.remainingSpace || 0 }}</span
                        >&nbsp;<span style="font-size: 14px; color: #7f848c">个</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div>
                      <img src="../../../assets/images/parkingLot/count-four.png" alt="" />
                    </div>
                    <!-- <div>
                      <div style="font-size: 15px; color: #333333">总流量数</div>
                      <div>
                        <span style="font-size: 24px; color: #333333; font-weight: 600">{{ parkingMonit.flowRateTotal||0  }}</span
                        >&nbsp;<span style="font-size: 14px; color: #7f848c">次</span>
                      </div>
                    </div> -->
                    <div>
                      <div style="font-size: 15px; color: #333333">总放行数</div>
                      <div>
                        <span style="font-size: 24px; color: #333333; font-weight: 600">{{ carYardStatistics.passedTotal || 0 }}</span
                        >&nbsp;<span style="font-size: 14px; color: #7f848c">次</span>
                      </div>
                    </div>
                    <div></div>
                  </div>
                </div>
              </div>
              <div class="card-content" v-if="item.id == '5d9nb6a5bc56d5g95s8v3s1f'">
                <div class="statistics">
                  <div>
                    <div>
                      <div style="font-size: 15px; color: #666666">总车位数</div>
                      <div>
                        <span style="font-size: 24px; color: #333333; font-weight: 600">{{ carYardStatistics.parkingSpaceTotal || 0 }}</span>
                      </div>
                    </div>
                    <div>
                      <div style="font-size: 15px; color: #666666">可用车位</div>
                      <div>
                        <span style="font-size: 24px; color: #333333; font-weight: 600">{{ carYardStatistics.remainingSpace || 0 }}</span>
                      </div>
                    </div>
                    <div>
                      <div style="font-size: 15px; color: #666666">已用车位</div>
                      <div>
                        <span style="font-size: 24px; color: #333333; font-weight: 600">{{ carYardStatistics.inUseSpace || 0 }}</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div style="height: 100%; width: 100%">
                      <div id="carYardStatistics" class="full-style" ref="5d9nb6a5bc56d5g95s8v3s1f"></div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-content" v-if="item.id == '39d8w5f8g89s5v5d8d8g5d5d'">
                <div style="height: 100%; width: 100%">
                  <div id="flowAnalysis" class="full-style" ref="39d8w5f8g89s5v5d8d8g5d5d"></div>
                </div>
              </div>
              <div class="card-content" v-if="item.id == '44n9d9n4d66b6s5as9w825f8'">
                <div style="height: 100%; width: 100%">
                  <div id="chargeStatistics" class="full-style" ref="39d8w5f8g89s5v5d8d8g5d5d"></div>
                </div>
              </div>
            </div>
          </div>
        </dash-item>
      </dash-layout>
    </dashboard>
  </div>
</template>

<script>
import { Dashboard, DashLayout, DashItem } from 'vue-responsive-dash'
import * as echarts from 'echarts'
export default {
  name: 'parkingOverview',
  components: {
    Dashboard,
    DashLayout,
    DashItem
  },
  data() {
    return {
      parkingMonit: '',
      parkingLot: '',
      collectType: '1',
      hospitalEvn: __PATH.VUE_APP_HOSPITAL_NODE_ENV,
      dlayout: {
        breakpoint: 'xs',
        numberOfCols: 24,
        items: []
      },
      airData: [],
      carYardStatisticsList: [],
      carYardStatistics: '',
      flowAnalysisList: {},
      chargeStatisticsList: {},
      defaultItems: [],
      echartsDom: ['carYardStatistics', 'flowAnalysis', 'chargeStatistics'],
      dashExampleShow: true,
      saveAndCancelBtnShow: false // 保存按钮组显示
    }
  },
  watch: {
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          let echartsDom = this.echartsDom
          setTimeout(() => {
            echartsDom.forEach((item) => {
              echarts.init(document.getElementById(item)).resize()
            })
          }, 250)
        })
      },
      deep: true
    }
  },
  mounted() {
    let items = []

    items = [
      { id: '17ghk64g56h4hfh5468s4732', name: '运营总览', x: 0, y: 0, width: 8, height: 6 },
      { id: '2z8df1246f2c8d21bn86s2f5', name: '车位监控', x: 8, y: 0, width: 8, height: 6 },
      { id: '5d9nb6a5bc56d5g95s8v3s1f', name: '车场监管统计', x: 16, y: 0, width: 8, height: 6 },
      { id: '39d8w5f8g89s5v5d8d8g5d5d', name: '出入口流量压力分析', x: 0, y: 6, width: 12, height: 6 },
      { id: '44n9d9n4d66b6s5as9w825f8', name: '出口收费统计', x: 12, y: 6, width: 12, height: 6 }
    ]

    this.defaultItems = items
    this.getDragManageList()
    this.changeCompletionRateType('1')
    // this.getParkingMonit()
    setTimeout(() => {
      this.getCarYardStatistics()
      this.getFlowAnalysis()
      this.getChargeStatistics()
    }, 500)
  },
  methods: {
    // 车位监控
    getParkingMonit() {
      this.$api.getParkingMonit({}).then((res) => {
        if (res.code == 200) {
          this.parkingMonit = res.data
        } else {
          this.$message.error(res.message)
        }
      })
    },
    //出口收费统计
    getChargeStatistics() {
      this.$api.getExistChargeDetail({}).then((res) => {
        if (res.code == '200') {
          this.chargeStatisticsList = res.data
        } else {
          this.chargeStatisticsList = {}
          this.$message.error(res.message)
        }
        this.initChargeStatistics(this.chargeStatisticsList)
      })
    },
    initChargeStatistics(data) {
      console.log(data, 'data')
      const getchart = echarts.init(document.getElementById('chargeStatistics'))
      let colors = ['#3562DB', '#FF9435']
      let option = {}
      if (JSON.stringify(data) != '{}') {
        option = {
          color: colors,
          tooltip: {
            trigger: 'axis'
            // axisPointer: {
            //   type: 'cross'
            // }
          },
          grid: {
            right: '15%',
            left: '12%'
          },
          legend: {
            data: [
              {
                name: '实收金额/元',
                icon: 'circle',
                x: 'left'
              },
              {
                name: '支付笔数/笔',
                x: 'right'
              }
            ],
            textStyle: {
              color: '#333' // 图例文字颜色
            }
          },
          xAxis: [
            {
              type: 'category',
              axisTick: {
                alignWithLabel: true
              },
              data: data.existDetail
            }
          ],
          yAxis: [
            {},
            {
              type: 'value',
              name: '',
              min: 0,
              max: Math.max.apply(null, data.actualPrice),
              position: 'left',
              axisLine: {
                lineStyle: {
                  color: '#414653'
                }
              },
              axisLabel: {
                formatter: '{value}'
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: '#e3e7ec'
                }
              }
            },
            {
              type: 'value',
              name: '',
              min: 0,
              max: Math.max.apply(null, data.paymentFrequency),
              offset: 22,
              position: 'right',
              axisLine: {
                lineStyle: {
                  color: '#414653'
                }
              },
              axisLabel: {
                formatter: '{value}'
              },
              splitLine: {
                show: false
              }
            }
          ],
          series: [
            {
              name: '实收金额/元',
              type: 'bar',
              yAxisIndex: 1,
              barWidth: 10,
              data: data.actualPrice,
              itemStyle: {
                borderRadius: 2
              }
            },
            {
              name: '支付笔数/笔',
              type: 'line',
              yAxisIndex: 2,
              data: data.paymentFrequency,
              symbol: 'circle',
              itemStyle: {
                borderType: 'solid',
                borderColor: '#fff',
                borderWidth: 1
              }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 出入口流量压力分析
    getFlowAnalysis() {
      this.$api.getPassagewayStress({}).then((res) => {
        if (res.code == 200) {
          this.flowAnalysisList = res.data
        } else {
          this.flowAnalysisList = {}
          this.$message.error(res.message)
        }
        this.initFlowAnalysis()
      })
    },
    initFlowAnalysis() {
      let myChart = echarts.init(document.getElementById('flowAnalysis'))
      let option = {}
      if (JSON.stringify(this.flowAnalysisList) != '{}') {
        option = {
          color: ['#FA403C', '#3562DB'],
          tooltip: {
            show: true,
            trigger: 'axis'
          },
          legend: {
            data: this.flowAnalysisList.flowRateType
          },
          grid: {
            left: '2%',
            right: '3%',
            bottom: '50',
            top: '15',
            containLabel: true
          },
          dataZoom: [
            {
              fillerColor: '#BBC3CE',
              backgroundColor: '#fff',
              height: 10,
              type: 'slider',
              bottom: 10,
              textStyle: {
                color: '#000'
              },
              start: 0,
              end: 85
            },
            {
              type: 'inside', // 支持内部鼠标滚动平移
              start: 0,
              end: 85,
              zoomOnMouseWheel: false, // 关闭滚轮缩放
              moveOnMouseWheel: true, // 开启滚轮平移
              moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
            }
          ],
          xAxis: [
            {
              type: 'category',
              data: this.flowAnalysisList.time,
              axisLabel: {
                clickable: true
              },
              axisLine: {
                lineStyle: {
                  color: '#D8DEE7' // 更改坐标轴颜色
                }
              },
              // 去除x轴上的刻度线
              axisTick: {
                show: false
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              name: '个',
              axisLabel: {
                formatter: '{value}',
                show: true,
                textStyle: {
                  color: '#909399' // 更改坐标轴文字颜色
                }
              },
              // 控制y轴线是否显示
              axisLine: {
                lineStyle: {
                  color: '#D8DEE7' // 更改坐标轴颜色
                }
              },
              // 网格样式
              splitLine: {
                show: true,
                lineStyle: {
                  color: ['#f5f5f5'],
                  width: 1,
                  type: 'dashed'
                }
              },
              // 去除y轴上的刻度线
              axisTick: {
                show: false
              }
            }
          ],
          series: [
            {
              // symbol: 'none',
              name: this.flowAnalysisList.flowRateData[0].name,
              data: this.flowAnalysisList.flowRateData[0].data,
              type: 'line',
              areaStyle: {
                color: {
                  x: 0,
                  y: 1,
                  x2: 0,
                  y2: 0,
                  colorStops: [
                    {
                      offset: 0,
                      color: '#fff'
                    },
                    {
                      offset: 1,
                      color: '#FA403C'
                    }
                  ]
                }
              }
            },
            {
              name: this.flowAnalysisList.flowRateData[1].name,
              data: this.flowAnalysisList.flowRateData[1].data,
              type: 'line',
              areaStyle: {
                color: {
                  x: 0,
                  y: 1,
                  x2: 0,
                  y2: 0,
                  colorStops: [
                    {
                      offset: 0,
                      color: '#fff'
                    },
                    {
                      offset: 1,
                      color: '#3F63D3'
                    }
                  ]
                }
              }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }

      myChart.clear()
      myChart.setOption(option) // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    //车场监管统计
    getCarYardStatistics() {
      this.$api.getParkingSuperviseInfo({}).then((res) => {
        if (res.code == '200') {
          this.carYardStatistics = res.data
          this.carYardStatisticsList = res.data.chartData

          this.initCarYardStatistics()
        } else {
          this.carYardStatisticsList = []
          this.$message.error(res.message)
        }
      })
    },
    initCarYardStatistics() {
      let arr = []
      if (this.carYardStatisticsList.length) {
        this.carYardStatisticsList.forEach((item) => {
          let obj = {
            name: item.name,
            value: item.value,
            rate: item.rate
          }
          if (item.name == '固定车辆') obj.color = '#00bc6d'
          if (item.name == '普通临时车辆') obj.color = '#3562db'
          if (item.name == '访客车辆') obj.color = '#ff9435'
          if (item.name == '可用') obj.color = '#ffbe00'
          arr.push(obj)
        })
      }
      const getchart = echarts.init(document.getElementById('carYardStatistics'))
      let option = {}
      if (this.carYardStatisticsList.length) {
        option = {
          color: arr.map((i) => i.color),
          tooltip: {
            trigger: 'item'
          },
          legend: {
            show: false
          },
          series: [
            {
              type: 'pie',
              radius: '60%',
              center: ['48%', '50%'],
              label: {
                color: '#333333',
                formatter: function (params) {
                  return params.data.name + ' ' + params.data.value + '辆' + ' ' + params.data.rate
                }
              },
              data: arr
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }

      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    changeCompletionRateType(type) {
      this.collectType = type
      this.$api.getOperateTotalDetail({ querySection: this.collectType }).then((res) => {
        if (res.code == 200) {
          this.parkingLot = res.data
        } else {
          this.$message.error(res.message)
        }
      })
    },
    moveEnd() {
      console.log('moveEnd', this.dlayout)
    },
    echartsResize() {
      let echartsDom = this.echartsDom
      this.$nextTick(() => {
        setTimeout(() => {
          echartsDom.forEach((item) => {
            echarts.init(document.getElementById(item)).resize()
          })
        }, 100)
      })
    },
    resizeEnd(val) {
      this.echartsResize(val)
    },
    cancelStaging() {
      this.saveAndCancelBtnShow = false
      this.getDragManageList()
      setTimeout(() => {
        this.getCarYardStatistics()
        this.getFlowAnalysis()
        this.getChargeStatistics()
      }, 300)
    },
    // 保存工作台模块
    saveStaging() {
      console.log(this.dlayout.items)
      const items = this.dlayout.items
      if (!items.length) {
        return this.$message.warning('无可配置模块')
      }
      const jsonList = items.map((e) => {
        return {
          id: e.id,
          name: e.name,
          x: e.x,
          y: e.y,
          width: e.width,
          height: e.height
        }
      })
      const params = {
        jsonList: JSON.stringify(jsonList),
        userId: this.$store.state.user.userInfo.user.staffId,
        menuType: 10
      }
      console.log('save提交', params)
      this.$api.saveWorktopManage(params).then((res) => {
        if (res.code == 200) {
          this.$message.success('保存成功')
          this.saveAndCancelBtnShow = false
          this.getDragManageList()
          setTimeout(() => {
            this.getCarYardStatistics()
            this.getFlowAnalysis()
            this.getChargeStatistics()
          }, 300)
        } else {
          this.$message.error(res.message)
        }
      })
    },
    dropDownCommand() {
      this.saveAndCancelBtnShow = true
      this.dlayout.items.map((e) => {
        e.resizable = true
        e.draggable = true
      })
      // console.log(val)
    },
    // 获取可拖拽列表
    getDragManageList() {
      const params = {
        userId: this.$store.state.user.userInfo.user.staffId,
        menuType: 10
      }
      this.$api.getWorktopManageList(params).then((res) => {
        if (res.code == 200) {
          const data = res.data
          let componentItems = []
          // 有接口数据取接口数据 无接口数据取字典默认配置数据
          if (data.length) {
            componentItems = data
          } else {
            componentItems = this.defaultItems
          }
          // 遍历增加dragAllowFrom、resizable、draggable字段
          const items = componentItems.map((item) => {
            return {
              id: item.id,
              name: item.name || this.defaultItems.find((e) => e.id == item.id).name,
              x: item.x,
              y: item.y,
              width: item.width,
              height: item.height,
              dragAllowFrom: '.drag_class',
              resizable: false,
              draggable: false
            }
          })
          this.dashExampleShow = false
          this.$nextTick(() => {
            this.dashExampleShow = true
            this.dlayout.items = items
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
#parkingOverview {
  // background: #fff;
  height: calc(100% - 30px);
  // width: calc(100% - 30px);
  max-width: 1620px;
  padding: 0 20px;
  margin: 15px auto;
  display: flex;
  flex-direction: column;
  .batch-control {
    display: flex;
    justify-content: space-between;
    height: 44px;
    padding: 6px 10px;
    > p {
      padding-left: 15px;
      font-size: 16px;
      text-align-last: left;
      font-family: 'PingFang SC-Medium', 'PingFang SC';
      font-weight: 550;
      color: #121f3e;
    }
    > div {
      text-align: right;
    }
  }
}

#dashExample {
  flex: 1;
  max-height: 100%;
  overflow-y: auto;
  overflow-x: hidden;

  ::v-deep(.placeholder) {
    background: #e2e6eb !important;
    border-radius: 10px;
    opacity: 1;
  }
}
.dropdownSelect {
  margin: 0;
  padding: 3px 0;

  .el-dropdown-menu__item {
    line-height: 30px;
    padding: 0 15px;
  }
}
.card-item {
  height: 100%;
  width: 100%;
  background: #fff;
  box-sizing: border-box;
  padding: 5px;
  box-shadow: 0 2px 7px 0 #e4e4ec !important;
  border-radius: 10px !important;

  .card-empty {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  header {
    align-items: center;
    height: 48px;
    // .dropdown-btn {
    //   position: absolute;
    //   right: 0;
    // }
    .more-operations {
      cursor: pointer;
      color: #3562db;
      user-select: none;
      font-size: 14px;
      font-weight: 500;
      height: auto;
    }
  }

  // .mover {
  //   cursor: move;
  // }

  .airconditioning {
    display: flex;
    padding: 0 15px 0 0;
    color: #606266;
    // font-weight: 600;
    // border-bottom: 1px solid #d8dee7;

    .legend-title {
      // font-size: 16px;
      // color: #606266;
      font-size: 15px;
      font-family: 'PingFang SC-Medium', 'PingFang SC';
      font-weight: bold;
      color: #333;
      padding-left: 15px;
      position: relative;
      line-height: 48px;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      height: 100%;
    }

    .notlinkBg {
      background-color: #f2f2f2;
      color: #606266;
    }

    .faultBg {
      background-color: #fff4ea;
      color: #f39038;
    }

    .warnBg {
      background-color: #ffebeb;
      color: #ff4848;
    }

    .legend-right-box {
      width: 49px;
      height: 22px;
      border-radius: 2px;
      margin: auto;
      display: flex;
      margin-right: 15px;

      span {
        display: inline-block;
        width: 22px;
        height: 22px;
        margin-right: 5px;
        line-height: 22px;
      }

      i {
        flex: 1;
        line-height: 22px;
      }

      .notlink {
        background: url('~@/assets/images/monitor/notlink.png') no-repeat;
        background-size: 100% 100%;
      }

      .fault {
        background: url('~@/assets/images/monitor/fault.png') no-repeat;
        background-size: 100% 100%;
      }

      .warn {
        background: url('~@/assets/images/monitor/warn.png') no-repeat;
        background-size: 100% 100%;
      }
    }

    & > span {
      flex: 1;
      text-align: center;
    }
  }

  .card-content {
    display: flex;
    flex-wrap: wrap;
    padding: 10px;
    height: calc(100% - 48px);
    overflow: auto;

    .air_conditioner {
      width: 120px;
      height: 100px;
      text-align: center;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      font-size: 13px;
      padding: 6px 0;

      span {
        font-size: 22px;
        color: #5188fc;
      }

      p {
        padding: 0;
        margin: 0;
        align-items: center;
      }

      p:first-child {
        height: 55px;
        line-height: 55px;
      }

      p {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0;
      }
    }

    .safe_conditioner {
      &:nth-child(4),
      &:nth-child(5) {
        cursor: pointer;

        &:hover {
          box-shadow: 2px 3px 5px 3px #e4e4ec !important;
        }
      }
    }
  }
}
::-webkit-scrollbar-thumb:hover,
::-webkit-scrollbar-track:hover {
  background: transparent;
}
::-webkit-scrollbar-thumb {
  background-color: transparent;
}
.btns {
  width: 150px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-right: 10px;
}

.btns > span {
  width: 30%;
  background-color: #f6f5fa;
  font-size: 14px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: none;
  border-radius: 4px;
  color: #414653;
  cursor: pointer;
}
.active-btn {
  background-color: #e6effc !important;
  color: #3562db !important;
  border: none !important;
}
.cont {
  width: 100%;
  height: 100%;
  > div {
    height: 50%;
    width: 100%;
    display: flex;
    align-items: center;
    > div {
      flex: 1;
      text-align: center;
    }
    > div:nth-child(1) {
      border-right: 1px solid #e4e7ed;
    }
  }
}
.statistics {
  width: 100%;
  height: 100%;
  > div:nth-child(1) {
    background-color: #faf9fc;
    height: 30%;
    width: 100%;
    display: flex;
    align-items: center;
    > div {
      flex: 1;
      text-align: center;
    }
    > div:nth-child(1) {
      border-right: 1px solid #e4e7ed;
    }
  }
  > div:nth-child(2) {
    height: 70%;
    width: 100%;
  }
}
.full-style {
  width: 100%;
  height: 100%;
  .contenWrap {
    display: flex;
    height: 100%;
    .content-left {
      width: 66%;
      height: 100%;
      .item-wrap {
        display: flex;
        height: calc(50% - 5px);
        margin-bottom: 10px;
        .items {
          width: calc(50% - 5px);
          background-color: #ccc;
          display: flex;
          justify-content: center;
          align-items: center;
          .numText {
            text-align: center;
            font-size: 20px;
            font-weight: bold;
          }
          .itemText {
            color: #414653;
          }
        }
        .items:first-child {
          margin-right: 10px;
        }
      }
    }
    .content-right {
      width: 34%;
      height: 100%;
      .chartsWrap {
        margin-left: 10px;
        background-color: #faf9fc;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }
    }
  }
}
</style>
