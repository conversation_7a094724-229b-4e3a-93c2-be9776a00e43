<template>
  <el-select ref="select" :value="value" filterable :filter-method="handleFilter" v-bind="$attrs" :allow-create="allowCreate" v-on="$listeners" @visible-change="changeVisible">
    <yx-option ref="virtualRef" :allowCreate="allowCreate" :virtualData="virtualData" :select-value="value" :label="propsOptions.label" :value="propsOptions.value" />
  </el-select>
</template>
<script>
import YxOption from './YxOption.vue'
export default {
  name: 'VirtualListSelect',
  components: { YxOption },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    options: {
      type: Array,
      default: () => []
    },
    value: {
      type: [String, Number, Array],
      default: ''
    },
    filterMethod: {
      type: Function,
      default: null
    },
    propsOptions: {
      type: Object,
      default: () => {
        return {
          label: 'label',
          value: 'value'
        }
      }
    },
    allowCreate: {
      type: <PERSON><PERSON>an,
      default: false
    }
  },
  data() {
    return {
      virtualData: []
    }
  },
  watch: {
    options: {
      handler(newVal) {
        this.virtualData = newVal
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleFilter(val) {
      if (this.filterMethod) return (this.virtualData = this.filterMethod(val))
      this.virtualData = this.options.filter((item) => {
        return item[this.propsOptions.label] && item[this.propsOptions.label].includes(val)
      })
    },
    changeVisible(cb) {
      this.$nextTick(() => {
        if (cb) {
          this.$refs['virtualRef'].resetVirtual() // 解决打开白屏问题，必须使用 $nextTick 延时处理
        } else {
          this.options.length !== this.virtualData.length && setTimeout(() => this.handleFilter(''), 100) // 解决筛选后关闭 数据丢失问题 setTimeout解决重置数据弹窗闪一下问题
        }
        'multiple' in this.$attrs && this.$attrs.multiple !== false && (this.$refs['virtualRef'].isShow = cb) // 解决多选回显问题
      })
    }
  }
}
</script>
<style></style>
