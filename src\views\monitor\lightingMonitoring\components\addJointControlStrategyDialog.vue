<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="切换提示"
    width="60%"
    :visible="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="content">
      <div style="padding: 24px; background: #fff;">
        <el-form ref="ruleForm" :model="form" :rules="rules" label-width="120px">
          <el-form-item label="策略名称" prop="controlName">
            <el-input v-model="form.controlName" placeholder="请输入" style="width: 350px"/>
          </el-form-item>
          <el-form-item label="执行时间" prop="dataRange">
            <el-time-picker
              v-model="form.dataRange"
              is-range
              arrow-control
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="HH:mm:ss"
            >
            </el-time-picker>
          </el-form-item>
          <el-form-item label="传感器设备" prop="illuminanceHarvesterId">
            <el-select v-model="form.illuminanceHarvesterId" placeholder="请选择" clearable>
              <el-option v-for="item in sensorList" :key="item.surveyCode" :label="item.surveyName" :value="item.surveyCode"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="关联回路" prop="loopIds">
            <el-select v-model="form.loopIds" placeholder="请选择" multiple collapse-tags clearable style="width: 350px;">
              <el-option v-for="item in circuitList" :key="item.surveyCode" :label="item.surveyName" :value="item.surveyCode"> </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div style="padding: 24px;">
        <ContentCard title="联控策略配置">
          <div slot="content" class="card-main">
            <div class="main-left">
              <el-form label-width="120px">
                <el-form-item label="联控规则">
                  <el-select v-model="configData.compareInt" placeholder="请选择" clearable style="width: 140px;">
                    <el-option v-for="item in compareList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                  </el-select>
                  <el-input v-model="configData.value" type="number" placeholder="请输入" style="width: 140px;"/>
                </el-form-item>
                <el-form-item label="控制设定">
                  <el-select v-model="configData.controlSetting" placeholder="请选择" clearable style="width: 140px;" @clear="() => configData.controlSettingLeave = ''" @change="() => configData.controlSettingLeave = ''">
                    <el-option label="亮度控制" :value="0"> </el-option>
                    <el-option label="开关控制" :value="1"> </el-option>
                  </el-select>
                  <el-select v-model="configData.controlSettingLeave" placeholder="请选择" :disabled="configData.controlSetting === ''" clearable style="width: 140px;">
                    <el-option v-for="item in leaveList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                  </el-select>
                </el-form-item>
              </el-form>
              <div style="text-align: right;padding-right: 10px;">
                <el-button type="primary" icon="el-icon-plus" @click="changeConfig('add')">添加</el-button>
              </div>
            </div>
            <div class="main-right">
              <TablePage
                ref="table"
                :showPage="false"
                :tableColumn="tableColumn"
                :data="form.list"
                height="300px"
              />
            </div>
          </div>
        </ContentCard>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script lang="jsx">
import { monitorTypeList } from '@/util/dict.js'
export default {
  name: 'addJointControlStrategyDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: [String, Number]
  },
  data() {
    return {
      projectCode: monitorTypeList.find(item => item.projectName == '照明监测').projectCode,
      sensorList: [], // 传感器列表
      circuitList: [], // 回路列表
      form: {
        controlName: '',
        dataRange: ['00:00:00', '23:59:00'],
        illuminanceHarvesterId: '', // 传感器的监测实体id
        loopIds: [], // 回路ids
        list: []
      },
      configData: {
        linkageRuleName: '照度值',
        compareInt: 2, // 比较符号
        value: '', // 数值
        controlSetting: 0, // 控制设定(0:亮度控制 1:开关控制)
        controlSettingLeave: 2  // 控制设定级别(0:低 1:中 2:高 3:开 4:关)
      },
      rules: {
        controlName: [{ required: true, message: '请输入策列名称', trigger: 'change' }],
        dataRange: [{ required: true, message: '请选择执行时间', trigger: 'change' }],
        illuminanceHarvesterId: [{ required: true, message: '请选择传感器设备', trigger: 'change' }],
        loopIds: [{ required: true, message: '请选择关联回路', trigger: 'change' }]
      },
      compareList: [
        {label: '<', value: 0},
        {label: '=', value: 1},
        {label: '>', value: 2},
        {label: '<=', value: 3},
        {label: '>=', value: 4}
      ],
      tableColumn: [
        {
          prop: 'serialNumber',
          label: '序号',
          formatter: (scope) => {
            return scope.$index + 1
          }
        },
        {
          prop: 'linkageRuleName',
          label: '联控规则'
        },
        {
          prop: 'value',
          label: '区间值',
          formatter: (scope) => {
            return scope.row.compare + scope.row.value
          }
        },
        {
          prop: 'controlSettingLeave',
          label: '控制设定',
          formatter: (scope) => {
            return (scope.row.controlSetting == 0 ? '亮度控制：' : '开关控制：') + scope.row.controlSettingName
          }
        },
        {
          width: 100,
          prop: 'operation',
          label: '控制',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span style="color: #3562DB" onClick={() => this.changeConfig('del', row.index)}>删除</span>
              </div>
            )
          }
        }
      ]
    }
  },
  computed: {
    leaveList() {
      if (this.configData.controlSetting == 0) {
        return [
          {label: '低', value: 0},
          {label: '中', value: 1},
          {label: '高', value: 2}
        ]
      } else if (this.configData.controlSetting == 1) {
        return [
          {label: '开', value: 3},
          {label: '关', value: 4}
        ]
      } else {
        return []
      }
    }
  },
  mounted() {
    this.selectInfoByType(0)
    this.selectInfoByType(1)
    if (this.id) {
      this.getOneLightingControl()
    } else {
      Object.assign(this.$data, this.$options.data())
    }
  },
  methods: {
    // 查询详情
    getOneLightingControl() {
      this.$api.GetOneLightingControl({id: this.id}).then(res => {
        if (res.code == 200) {
          this.form = res.data
          this.form.dataRange = [res.data.startTime, res.data.endTime]
          this.form.loopIds = res.data.loopIds.split(',')
        }
      })
    },
    // 获取传感器 回路列表
    selectInfoByType(type) {
      let params = {
        projectCode: this.projectCode,
        sensor: type
      }
      this.$api.SelectInfoByType(params).then(res => {
        if (res.code == 200) {
          if (type == 0) {
            this.sensorList = res.data
          } else {
            this.circuitList = res.data
          }
        }
      })
    },
    // 新增/删除配置
    changeConfig(type, index) {
      if (type == 'add') {
        let {linkageRuleName, compareInt, value, controlSetting, controlSettingLeave} = this.configData
        this.form.list.push({
          linkageRuleName,
          compare: this.compareList.find(item => item.value == compareInt).label,
          compareInt,
          value,
          controlSetting,
          controlSettingLeave,
          controlSettingName: this.leaveList.find(item => item.value == controlSettingLeave).label
        })
        Object.assign(this.$data.configData, this.$options.data().configData)
      } else {
        this.form.list.splice(index, 1)
      }
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    },
    confirm(formName) {
      let {controlName, dataRange, illuminanceHarvesterId, loopIds, list} = this.form
      let params = {
        controlName,
        startTime: dataRange[0],
        endTime: dataRange[1],
        illuminanceHarvesterId,
        illuminanceHarvesterName: this.sensorList.find(v => v.surveyCode == illuminanceHarvesterId).surveyName,
        loopCount: loopIds.length,
        loopIds: loopIds.join(','),
        list
      }
      if (!list.length) {
        this.$message.warning('请添加联控策略！')
        return
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let header = {}
          let fn = ''
          if (this.id) {
            fn = 'UpdateLightingControl'
            header = {
              'operation-type': 2,
              'operation-id': this.id,
              'operation-name': params.controlName
            }
          } else {
            fn = 'AddLightingControl'
            header = {
              'operation-type': 1
            }
          }

          this.$api[fn](params, header).then((res) => {
            if (res.code == 200) {
              this.$emit('update:visible', !this.visible)
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  p {
    margin: 0;
  }
  ::v-deep .el-date-editor {
    .el-input__icon {
      line-height: 24px;
    }
  }
  ::v-deep .box-card {
    padding: 16px;
  }
  .card-main {
    display: flex;
    width: 100%;
    .main-left {
      width: 40%;
    }
    .main-right {
      width: 60%;
    }
    ::v-deep .el-form-item {
      .el-form-item__content {
        display: flex;
        justify-content: space-between;
      }
    }
  }
}

.model-dialog {
  padding: 0 !important;
}

// ::v-deep .el-form-item__error {
//   height: 20px;
//   width: 300px;
//   color: #f56c6c;
//   font-size: 12px;
//   line-height: 20px;
//   padding-top: 4px;
//   position: absolute;
//   left: 78px;
// }

::v-deep .el-dialog__body {
  padding: 0 !important;
}
</style>
