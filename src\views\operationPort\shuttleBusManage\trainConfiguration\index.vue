<template>
  <PageContainer class="dictionary-list">
    <template #content>
      <div class="dictionary-list__right">
        <div class="dictionary-list__right__header">
          <div>
            <el-input v-model="searchForm.trainNo" clearable filterable placeholder="请输入车次编号" style="width: 200px; margin-right: 10px"></el-input>
            <el-select v-model="searchForm.lineId" placeholder="请选择路线" style="width: 200px; margin-right: 10px">
              <el-option v-for="item in busRoadMapList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
            </el-select>
            <el-select v-model="searchForm.busType" placeholder="请选择班车类型" style="width: 200px; margin-right: 10px">
              <el-option v-for="item in regularBusList" :key="item.code" :label="item.name" :value="item.code"> </el-option>
            </el-select>
            <el-select v-model="searchForm.passengerType" placeholder="请选择乘客类型" style="width: 200px; margin-right: 10px">
              <el-option v-for="item in userTypeList" :key="item.code" :label="item.name" :value="item.code"> </el-option>
            </el-select>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" plain @click="onReset">重置</el-button>
          </div>
          <div class="dictionary-list__right__actions">
            <el-button type="primary" :disabled="tableClickArry.length <= 1" @click="doDelete('0')">批量删除</el-button>
            <el-button type="primary" @click="trainExport()">导出</el-button>
            <el-button type="primary" @click.stop="trainImport">导入</el-button>
            <el-button type="primary" @click="onOperate('add')">新建</el-button>
          </div>
        </div>
        <div class="dictionary-list__table">
          <el-table
            ref="multipleTable"
            v-loading="tableLoadingStatus"
            height="100%"
            :data="tableData"
            border
            stripe
            table-layout="auto"
            @selection-change="handleSelectionChange"
            class="tableAuto"
            row-key="id"
          >
            <el-table-column type="selection" width="60" :reserve-selection="true" align="center"></el-table-column>
            <el-table-column v-for="(col, index) in columnList" :key="index" :prop="col.code" :label="col.name" show-overflow-tooltip></el-table-column>
            <el-table-column label="操作" :render-header="renderHeader">
              <template #default="{ row }">
                <el-button type="text" @click="onOperate('edit', row)"> 编辑 </el-button>
                <el-button type="text" class="delete" @click="doDelete('1', row)"> 删除 </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination
          :current-page="pagination.current"
          :page-sizes="pagination.pageSizeOptions"
          :page-size="pagination.size"
          :layout="pagination.layoutOptions"
          :total="pagination.total"
          @size-change="paginationSizeChange"
          @current-change="paginationCurrentChange"
        >
        </el-pagination>
      </div>
      <!--路线新增-->
      <trainConfigurationForm
        :dialogShow="dialogShow"
        ref="showTrainConfig"
        :OperateType="OperateType"
        :dictType="currentKey"
        :trainConfigId="trainConfigId"
        :regularBusList="regularBusList"
        :userTypeList="userTypeList"
        @success="getDataList"
        @closeTrainDialog="closeTrainDialog"
      />
      <trainImportForm ref="organization" :roadmapVisible="roadmapVisible" @closeDialog="closeDialog" @getDataList="getDataList" />
      <!-- 自定义表头 -->
      <customForm
        v-if="customVisible"
        :customVisible="customVisible"
        :columnCode="columnCode"
        :tableColumn="tableColumn"
        @customClose="customClose"
        @getDataList="getDataList"
        @getCustomData="getCustomData"
      />
    </template>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin'
import axios from 'axios'
import { UsingStatusOptions } from '@/views/operationPort/constant'
export default {
  name: 'DictionaryList',
  components: {
    trainConfigurationForm: () => import('./components/trainConfigurationForm'),
    trainImportForm: () => import('./components/trainImportForm.vue'),
    customForm: () => import('./components/customForm.vue')
  },
  filters: {
    statusFilter(status) {
      return UsingStatusOptions.find((it) => it.value == status)?.label ?? '-'
    }
  },
  mixins: [tableListMixin],
  data() {
    return {
      busRoadMapList: [], // 班车路线
      currentKey: -1,
      treeSearchKeyWord: '',
      searchForm: {
        trainNo: '',
        lineId: '',
        busType: '',
        passengerType: ''
      },
      OperateType: '',
      treeData: [],
      dialogShow: false,
      treeLoadingStatus: false,
      tableData: [],
      tableLoadingStatus: false,
      currentImage: '', // 当前预览的图片
      roadmapVisible: false,
      tableClickArry: [],
      trainConfigId: '',
      busType: 'BUS_TYPE', // 班车类型
      userType: 'PASSENGER_TYPE', // 乘客类型
      trainType: 'bus_train_number', // 车次自定义表头类型
      userTypeList: [],
      regularBusList: [],
      tableColumn: [],
      customVisible: false, // 自定义表头弹窗
      columnCode: [], // 自定义表头选中的值
      columnList: []
    }
  },
  mounted() {
    this.getDataList()
    this.getBusTypeList()
    this.getUserList()
    this.getCustomData()
    this.getLineList()
  },
  methods: {
    handleSelectionChange(val) {
      this.tableClickArry = val
    },
    // 获取列表数据
    getDataList() {
      this.tableLoadingStatus = true
      const params = {
        pageSize: this.pagination.size,
        pageNo: this.pagination.current,
        trainNo: this.searchForm.trainNo,
        lineId: this.searchForm.lineId,
        busType: this.searchForm.busType,
        passengerType: this.searchForm.passengerType
      }
      this.$api.fileManagement
        .trainConfigurationList(params)
        .then((res) => {
          if (res.code === '200') {
            this.tableData = res.data.records
            this.pagination.total = res.data.total
          } else {
            throw res.msg || res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取列表数据失败'))
        .finally(() => {
          this.tableLoadingStatus = false
        })
    },
    // 获取班车类型
    getBusTypeList() {
      this.$api.fileManagement.getFindDictListByCodeList({ code: this.busType }).then((res) => {
        if (res.code == 200) {
          this.regularBusList = res.data
        }
      })
    },
    // 获取乘客类型
    getUserList() {
      this.$api.fileManagement.getFindDictListByCodeList({ code: this.userType }).then((res) => {
        if (res.code == 200) {
          this.userTypeList = res.data
        }
      })
    },
    // 获取路线
    getLineList() {
      this.$api.fileManagement.roadmapReplyList().then((res) => {
        if (res.code == 200) {
          this.busRoadMapList = res.data
        }
      })
    },
    // 表单搜索按钮点击
    onSearch() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 表单重置按钮点击
    onReset() {
      this.searchForm = {
        trainNo: '',
        lineId: '',
        busType: '',
        passengerType: ''
      }
      this.pagination.size = 15
      this.pagination.current = 1
      this.getDataList()
    },
    // 列表操作相关的事件绑定
    onOperate(type, row) {
      this.$refs.showTrainConfig.resetForm()
      this.dialogShow = true
      this.OperateType = type
      this.regularBusList = this.regularBusList // 班车类型
      this.userTypeList = this.userTypeList // 乘客类型
      if (this.OperateType != 'add') {
        this.trainConfigId = row.id
      }
      this.$nextTick(() => {
        this.$refs.showTrainConfig.$refs.trainConfigFrom.clearValidate()
      })
      // this.dialog.id = row?.id || 0
    },
    // 获取自定义表头
    getCustomData() {
      this.tableLoadingStatus = true
      this.$api.fileManagement.getCustomList({ type: this.trainType }).then((res) => {
        this.tableLoadingStatus = false
        if (res.code == 200) {
          this.columnList = res.data.filter((item) => item.checked === 1)
          this.columnCode = this.columnList.map((el) => el.code)
          this.tableColumn = res.data
        }
      })
    },
    closeTrainDialog() {
      this.dialogShow = false
    },
    // 删除一行数据
    doDelete(type, row) {
      let paramsId = {}
      if (type == '0') {
        paramsId.id = this.tableClickArry.map((item) => item.id).join(',')
      } else {
        paramsId.id = row.id
      }
      this.$confirm('此操作将删除选中车次信息，是否继续？', '信息提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.$api.fileManagement
          .trainConfigurationDelete(paramsId)
          .then((res) => {
            if (res.code === '200') {
              this.$refs.multipleTable.clearSelection()
              this.$message.success('已删除')
              this.getDataList()
            } else {
              throw res.message || '删除失败'
            }
          })
          .catch((msg) => msg && this.$message.error(msg))
          .finally(() => (this.tableLoadingStatus = false))
      })
    },
    // 导出
    trainExport() {
      const userInfo = this.$store.state.user.userInfo.user
      const params = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        pageSize: this.pagination.size,
        pageNo: this.pagination.current,
        trainNo: this.searchForm.trainNo,
        lineName: this.searchForm.lineName,
        busType: this.searchForm.busType,
        passengerType: this.searchForm.passengerType
      }
      if (this.tableClickArry.length > 0) {
        params.ids = this.tableClickArry.map((i) => i.id).join(',')
      } else {
        params.ids = ''
      }
      axios({
        method: 'post',
        url: __PATH.SPACE_API + 'busTrainNumber/exportExcel',
        data: params,
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token,
          'Content-Type': 'application/json'
        }
      })
        .then((res) => {
          let name = '车次.xlsx'
          const blob = new Blob([res.data])
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
        })
        .catch(() => {
          this.$message.error('导出失败')
        })
    },
    // 导入
    trainImport() {
      this.roadmapVisible = true
    },
    closeDialog() {
      this.roadmapVisible = false
    },
    // 关闭自定义表头弹窗
    customClose() {
      this.customVisible = false
    },
    renderHeader(h, { column }) {
      // h即为cerateElement的简写，具体可看vue官方文档
      return h('div', [
        h('span', column.label),
        h('i', {
          class: 'el-icon-setting',
          style: 'color:#000000;margin-left:50px;cursor: pointer;',
          on: {
            click: () => {
              console.log(this.tableColumn, ' this.tableColumn*************************')

              this.customVisible = true
              this.tableColumn = this.tableColumn
              this.columnCode = this.columnCode
              // 在这里处理点击事件
            }
          }
        })
      ])
    }
  }
}
</script>
<style scoped lang="scss">
.dictionary-list {
  ::v-deep(> .container-content) {
    display: flex;
    background-color: #fff;
  }
  &__right {
    flex: 1;
    overflow: hidden;
    padding: 16px;
    display: flex;
    flex-flow: column nowrap;
  }
  &__right__header {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  &__right__actions {
    margin: 20px 0px;
  }
  &__table {
    height: calc(100% - 150px);
    overflow: auto;
  }
  &__pagination {
    margin-top: 10px;
  }
  &__tag {
    // 停用
    &--0 {
      --color: #f64646;
    }
    // 启用
    &--1 {
      --color: #00b42a;
    }
    &:before {
      content: '';
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
      height: 4px;
      width: 4px;
      border-radius: 4px;
      background-color: var(--color);
    }
  }
  &__table__color {
    display: inline-block;
    height: 16px;
    width: 16px;
    vertical-align: text-top;
    margin-right: 4px;
  }
  ::v-deep(.dictionary-list__table__col-img) {
    padding: 0;
    .cell {
      padding: 4px;
    }
    .el-image {
      height: 38px;
      .el-image__error {
        background-color: transparent;
      }
    }
  }
  .delete {
    color: red !important;
  }
}
</style>
