<template>
  <ContentCard
    :title="item.componentTitle + '(件)'"
    :scrollbarHover="true"
    :cstyle="{ height: '100%' }"
    class="drag_class"
    :hasMoreOper="['edit']"
    @more-oper-event="(val) => $emit('all-more-Oper', val, 'workOderType')"
  >
    <div slot="title-right" class="data-btns">
      <span v-for="item in dataTypeList" :key="item.type" :class="{ 'active-btn': selectDataType == item.type }" @click="changeDateType(item.type)">{{ item.name }}</span>
    </div>
    <div slot="content" style="height: 100%">
      <div v-if="echartsShow" id="orderTypeCharts"></div>
      <el-empty v-else></el-empty>
    </div>
  </ContentCard>
</template>
<script>
import { dataTypeList } from '@/util/dict.js'
import * as echarts from 'echarts'
export default {
  name: 'workOderType',
  props: {
    item: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dataTypeList: dataTypeList,
      selectDataType: 'day',
      echartsShow: true
    }
  },
  watch: {
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          // 页面重载+插件缩放大概需要800ms
          setTimeout(() => {
            echarts.init(document.getElementById('orderTypeCharts')).resize()
          }, 900)
        })
      },
      deep: true
    },
    // dragSidebarCollapse
    '$store.state.settings.dragSidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          // 页面重载+插件缩放大概需要800ms
          setTimeout(() => {
            echarts.init(document.getElementById('orderTypeCharts')).resize()
          }, 900)
        })
      },
      deep: true
    }
  },
  mounted() {
    this.getServiceWorkOrderStatistics()
  },
  methods: {
    echartsResize() {
      this.$nextTick(() => {
        setTimeout(() => {
          echarts.init(document.getElementById('orderTypeCharts')).resize()
        }, 100)
      })
    },
    // 获取工单类型统计
    getServiceWorkOrderStatistics() {
      let params = {
        dateType: this.selectDataType
      }
      this.$api.getServiceWorkOrderStatistics(params).then((res) => {
        if (res.code == '200') {
          const list = res.data?.list ?? []
          let arr = list.map((e) => {
            return {
              name: e.workTypeName,
              value: e.workOrderNum
            }
          })
          this.echartsShow = arr.length > 0
          if (arr.length) {
            this.$nextTick(() => {
              this.initOrderTypeCharts(arr)
            })
          }
        }
      })
    },
    // echarts渲染
    initOrderTypeCharts(arr) {
      let color = ['#08CB83', '#3562DB', '#0CA6ED', '#FF9435', '#FFBE00', '#4AEAB0']
      const option = {
        backgroundColor: '#fff',
        color: color,
        tooltip: {
          formatter: '{b0}<br />{c0}件<br />{d}%'
        },
        title: [],
        series: [
          {
            type: 'pie',
            radius: ['45%', '60%'],
            center: ['50%', '50%'],
            data: arr,
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 2
            },
            labelLine: {
              length: 20,
              length2: 30
            },
            label: {
              overflow: 'break',
              formatter: function (params) {
                var oa = option.series[0].data
                var num = oa.reduce((sum, e) => sum + e.value, 0)
                var percentage = ((params.data.value / num) * 100).toFixed(2)
                if (percentage.endsWith('.00')) {
                  percentage = parseInt(percentage) // 如果小数点后两位都是0，则显示整数部分
                }
                return params.data.name + ' ' + percentage + '%\n' + params.data.value + '件'
              }
              // formatter: params => {
              //   var oa = option.series[0].data
              //   var num = oa.reduce((sum, e) => sum + e.value, 0)
              //   for (var i = 0; i < option.series[0].data.length; i++) {
              //     if (params.name === oa[i].name) {
              //       return (
              //         '{name|' + params.name + '}{value|' + ((oa[i].value / num) * 100).toFixed(2) + '% ' + '}{value|' + oa[i].value + '件}'
              //       )
              //     }
              //   }
              // },
              // // padding: [0, -30, 15, -30],
              // rich: {
              //   name: {
              //     fontSize: 12,
              //     color: '#414653'
              //   },
              //   value: {
              //     fontSize: 12,
              //     // padding: [0, 0, 0, 4],
              //     color: '#414653'
              //   }
              // }
            }
          }
        ]
      }
      setTimeout(() => {
        const getchart = echarts.init(document.getElementById('orderTypeCharts'))
        getchart.resize()
        getchart.clear()
        getchart.setOption(option)
        // 随着屏幕大小调节图表
        window.addEventListener('resize', () => {
          getchart.resize()
        })
      }, 250)
    },
    // 切换日期类型
    changeDateType(type) {
      this.selectDataType = type
      this.getServiceWorkOrderStatistics()
    }
  }
}
</script>
<style lang="scss" scoped>
.data-btns {
  position: absolute;
  right: 7%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  & > span {
    width: 40px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    margin: 0 4px;
    background-color: #faf9fc;
    font-size: 14px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    border-radius: 4px;
    color: #414653;
    cursor: pointer;
  }

  .active-btn {
    background-color: #e6effc !important;
    color: #3562db !important;
  }
}

#orderTypeCharts {
  width: 100%;
  height: 100%;
  z-index: 999;
}
</style>
