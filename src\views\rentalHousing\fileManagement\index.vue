<template>
  <PageContainer>
    <div slot="content" class="space-content" style="height: 100%">
      <div class="search-from">
        <el-input
          v-model.trim="fileName"
          style="width: 300px"
          suffix-icon="el-icon-search"
          clearable
          maxlength="50"
          placeholder="文件名称"
          onkeyup="if(value.length>50)value=value.slice(0,50)"
        ></el-input>
        <el-button type="primary" plain style="margin-right: 10px" @click="resetForm">重置</el-button>
        <el-button type="primary" @click="searchForm">查询</el-button>
      </div>
      <div class="search-from" style="margin-bottom: 10px">
        <el-button type="primary" icon="el-icon-plus" style="margin: 0" @click="addFile">新增</el-button>
        <el-button type="primary" icon="el-icon-download" :disabled="multipleSelection.length == 0" @click="downloadFile">下载</el-button>
        <el-button type="danger" icon="el-icon-delete" :disabled="multipleSelection.length == 0" @click="deleteFile">删除</el-button>
      </div>
      <template v-if="isExist">
        <el-table
          ref="table"
          v-loading="tableLoading"
          border
          height="calc(100% - 126px)"
          :data="tableData"
          default-expand-all
          stripe
          row-key="id"
          :tree-props="{ children: 'children' }"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"> </el-table-column>
          <el-table-column label="序号" type="index" width="100"> </el-table-column>
          <el-table-column label="文件名称" prop="fileName" show-overflow-tooltip></el-table-column>
          <el-table-column label="格式" prop="fileFormat" show-overflow-tooltip></el-table-column>
          <el-table-column label="大小（MB）" prop="fileSize" show-overflow-tooltip></el-table-column>
          <el-table-column label="说明" prop="reason" show-overflow-tooltip></el-table-column>
          <el-table-column label="上传人" prop="createByName" show-overflow-tooltip></el-table-column>
          <el-table-column label="上传时间" prop="updateDate" show-overflow-tooltip></el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-link type="primary" :underline="false" @click="viewFile(scope.row)">查看</el-link>
              <el-link type="primary" :underline="false" style="margin-left: 10px" @click="editFile(scope.row)">编辑</el-link>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="pagination.currentPage"
          :page-size="pagination.pageSize"
          :page-sizes="[15, 20, 30, 40]"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </template>
      <template v-else>
        <div class="empty">您还未创建文件哦~</div>
      </template>
    </div>
  </PageContainer>
</template>
<script>
import axios from 'axios'
export default {
  components: {},
  data() {
    return {
      fileName: '',
      tableData: [],
      pagination: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      tableLoading: false,
      multipleSelection: [],
      isExist: false
    }
  },
  created() {
    this.checkOutList()
    this.getListData()
  },
  methods: {
    checkOutList() {
      this.$api.rentalHousingApi.checkExistFile().then((res) => {
        if (res.code == 200) {
          this.isExist = res.data
        }
      })
    },
    resetForm() {
      this.pagination = {
        currentPage: 1,
        pageSize: 15
      }
      this.fileName = ''
      this.getListData()
    },
    searchForm() {
      this.pagination.currentPage = 1
      this.getListData()
    },
    downloadFile() {
      const ids = this.multipleSelection.map((item) => item.id)
      const params = {
        name: this.fileName,
        pageNum: this.pagination.currentPage,
        pageSize: this.pagination.pageSize,
        ids: ids.join(',')
      }
      axios
        .post(__PATH.VUE_RHMS_API + 'fileInfo/downloadFileBatch', params, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + this.$store.state.user.token,
            hospitalCode: this.$store.state.user.userInfo.user.hospitalCode,
            unitCode: this.$store.state.user.userInfo.user.unitCode
          },
          responseType: 'blob'
        })
        .then((res) => {
          this.download(res)
        })
    },
    download(res) {
      const downloadElement = document.createElement('a')
      const href = window.URL.createObjectURL(res.data) // 创建下载的链接
      downloadElement.style.display = 'none'
      downloadElement.href = href
      downloadElement.download = this.$tools.getFileName(res) // 下载后文件名
      document.body.appendChild(downloadElement)
      downloadElement.click() // 点击下载
      document.body.removeChild(downloadElement) // 下载完成移除元素
      window.URL.revokeObjectURL(href) // 释放掉blob对象
    },
    getListData() {
      const params = {
        pageNum: this.pagination.currentPage,
        pageSize: this.pagination.pageSize,
        name: this.fileName
      }
      this.tableLoading = true
      this.$api.rentalHousingApi.fileManagementList(params).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data.records
          this.pagination.total = res.data.total
        }
        this.tableLoading = false
      })
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.getListData()
    },
    handleCurrentChange(val) {
      this.pagination.currentPage = val
      this.getListData()
    },
    viewFile(item) {
      this.$router.push({
        path: '/fileManagement/addFile',
        query: {
          type: 'detail',
          id: item.id
        }
      })
    },
    editFile(item) {
      this.$router.push({
        path: '/fileManagement/addFile',
        query: {
          type: 'edit',
          id: item.id
        }
      })
    },
    addFile() {
      this.$router.push({
        path: '/fileManagement/addFile',
        query: {
          type: 'add'
        }
      })
    },
    deleteFile() {
      this.$confirm('是否确认删除？删除后无法恢复，请再次确认！', '提醒', { type: 'warning' }).then((res) => {
        const idList = this.multipleSelection.map((item) => item.id)
        const params = {
          name: this.fileName,
          pageNum: this.pagination.currentPage,
          pageSize: this.pagination.pageSize,
          idList
        }
        this.$api.rentalHousingApi.fileBatchDelete(params).then((res) => {
          if (res.code == '200') {
            this.$message.success('删除成功')
            this.resetForm()
          }
        })
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    }
  }
}
</script>
<style lang="scss" scoped>
.space-content {
  height: calc(100% - 30px);
  overflow: auto;
  padding: 10px 20px 20px 20px;
  background: #fff;
  border-radius: 0px 4px 4px 0px;
  .search-from {
    & > div,
    .el-button {
      margin-right: 10px;
      margin-top: 10px;
    }
    & > .el-button:last-child {
      margin: 0px;
    }
  }
  .empty {
    height: calc(100% - 94px);
    width: 100%;
    background: url('@/assets/images/operationPort/yuan-empty.png') no-repeat center center;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding-bottom: 5%;
  }
}
</style>
