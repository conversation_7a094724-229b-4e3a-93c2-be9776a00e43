<template>
  <el-dialog class="sino-dialog-device" :title="title" :visible.sync="dialogVisibleDp" @close="closeDialog">
    <div class="top-search" style="margin-bottom: 15px;">
      <el-input v-model.trim="dialogFilters.teamName" clearable placeholder="小组名称" class="sino_sdcp_input mr15"></el-input>
      <el-input v-model.trim="dialogFilters.responsiblePersonName" clearable placeholder="负责人姓名" class="sino_sdcp_input mr15"></el-input>
      <el-select v-model="dialogFilters.structureType" clearable class="sino_sdcp_input mr15" placeholder="组织机构类型" filterable>
        <el-option v-for="(item, index) in structureTypeList" :key="index" :label="item.responsiblePersonName" :value="item.id" class="set_zindex"></el-option>
      </el-select>
      <el-button type="primary" plain @click="_resetCondition">重置</el-button>
      <el-button type="primary" @click="_searchByCondition">查询</el-button>
    </div>
    <div class="hasSelected">
      <div style="color: #5b5b5b; font-weight: 600;">已选择：</div>
      <span v-if="!multipleSelection.length">暂无</span>
      <div v-for="(item, index) in multipleSelection" v-else :key="index" style="color: #5188fc;">
        <span>{{ item.teamName }}</span>
        <span v-show="multipleSelection.length > 1 && index !== multipleSelection.length - 1">、</span>
      </div>
    </div>
    <div class="table-list">
      <el-table
        ref="materialTable"
        v-loading="dialogtableLoading"
        :data="tableWz"
        :border="true"
        stripe
        height="350px"
        :row-key="
          (row) => {
            return row.id
          }
        "
        :cell-style="{ padding: '8px' }"
        style="overflow: auto;"
        :header-cell-style="$tools.setHeaderCell(3)"
        @selection-change="handleSelectionChangeDialog"
      >
        <el-table-column :reserve-selection="true" type="selection" width="55" align="center"></el-table-column>
        <el-table-column type="index" width="65" label="序号" align="center"> </el-table-column>
        <el-table-column prop="teamName" show-overflow-tooltip label="小组名称"></el-table-column>
        <el-table-column prop="teamCode" show-overflow-tooltip label="小组编码"></el-table-column>
        <el-table-column prop="structureType" show-overflow-tooltip label="组织机构">
          <template slot-scope="scope">
            <span>
              {{ scope.row.structureType == 1 ? '院级' : scope.row.structureType == 2 ? '科级' : '' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="responsiblePersonName" show-overflow-tooltip label="负责人"></el-table-column>
        <el-table-column prop="groupTypeName" show-overflow-tooltip label="状态">
          <template slot-scope="scope">
            <span>{{ scope.row.useStatus == 1 ? '启用' : scope.row.useStatus == 2 ? '禁用' : '' }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="table-page my_page">
      <el-pagination
        :current-page="paginationData.currentPage"
        :page-sizes="[15, 20, 30, 50]"
        :page-size="paginationData.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="paginationData.total"
        style="margin: 15px 0;"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="sureWz">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: '',
  props: {
    title: {
      type: String,
      default: '选择小组'
    },
    dialogVisibleDp: {
      type: Boolean,
      default: false
    },
    flag: {
      type: String,
      default: 'maintainFlag'
    }
  },
  data() {
    return {
      dialogFiltersSelectArr: [],
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      props: {
        label: 'gridName',
        value: 'id',
        children: 'children'
      },
      arr: [],
      materialTypeID: '',
      areaList: [],
      multipleSelection: [],
      tableWz: [],
      dialogtableLoading: false,
      dialogFilters: {
        teamName: '',
        responsiblePersonName: '',
        structureType: '',
        useStatus: 1
      },
      structureTypeList: [
        {
          responsiblePersonName: '院级小组',
          id: 1
        },
        {
          responsiblePersonName: '科级小组',
          id: 2
        }
      ],
      buttonLoading: false,
      tableSort: '',
      tableOrderBy: '',
      list: []
    }
  },
  created() {},
  methods: {
    // 选择
    handleSelectionChangeDialog(val) {
      this.multipleSelection = val
      console.log(this.multipleSelection, '选择的任务点')
      this.multipleSelection.forEach((v, i) => {
        this.$nextTick(() => {
          this.$refs.materialTable.toggleRowSelection(v, true) // 然后让未删除的列表显示选中状态
        })
      })
    },
    // 点击确定
    sureWz() {
      this.$emit('treeNodeData', this.multipleSelection)
      this.closeDialog()
    },
    parentComponentFun() {
      // 获取安全管控部门列表
      this._getControlGroupInfoList()
    },
    // 查询表格
    _getControlGroupInfoList() {
      this.dialogtableLoading = true
      let obj = this.LOGINDATA
      let data = {
        ...this.dialogFilters,
        ...this.paginationData,
        allParentIds: '#'
      }
      delete data.total
      this.$api.ipsmGetControlGroupInfoList(data).then((res) => {
        this.dialogtableLoading = false
        if (res.code == 200) {
          this.paginationData.total = res.data.sum
          this.tableWz = res.data.list
          if (res.data.list.length == 0) {
            this.emptyText = '未查到任何记录，请放大查询条件试试！'
          }
        }
      })
    },

    // 条件查询
    _searchByCondition() {
      this.paginationData.currentPage = 1
      this._getControlGroupInfoList()
    },
    // 重置查询条件
    _resetCondition() {
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.dialogFilters = {
        teamName: '',
        responsiblePersonName: '',
        structureType: '',
        useStatus: 1
      }
      this.$refs.materialTable.clearSelection()
      this._getControlGroupInfoList()
    },
    closeDialog() {
      this.dialogFilters = {
        teamName: '',
        responsiblePersonName: '',
        structureType: '',
        useStatus: 1
      }
      this.$emit('closeDialog')
    },
    handleSizeChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      this._getControlGroupInfoList()
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this._getControlGroupInfoList()
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.el-dialog__body) {
  height: 520px !important;
}

.hasSelected {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  min-height: 20px;
  max-height: 38px;
  height: auto;
  overflow-y: auto;
}

.sino_sdcp_input {
  margin-right: 10px;
}

.top-search {
  :deep(.el-input) {
    width: auto;
  }
}
</style>