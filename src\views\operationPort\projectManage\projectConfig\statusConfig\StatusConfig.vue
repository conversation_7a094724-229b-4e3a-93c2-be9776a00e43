<script>
import { UsingStatusOptions } from '@/views/operationPort/constant'
import tableListMixin from '@/mixins/tableListMixin'
export default {
  name: 'StatusConfig',
  components: {
    StatusDetail: () => import('./components/StatusDetail.vue'),
    StatusBase: () => import('./components/StatusBase.vue')
  },
  filters: {
    statusFilter(status) {
      return UsingStatusOptions.find((it) => it.value === +status)?.label ?? '-'
    }
  },
  mixins: [tableListMixin],
  data: () => ({
    searchForm: {
      name: ''
    },
    tableData: [],
    loadingStatus: false,
    dialog: {
      // 基础表单
      baseVisible: false,
      detailVisible: false,
      detailReadonly: false,
      id: 0
    }
  }),
  computed: {
    OperateType() {
      return {
        View: 'view',
        Edit: 'edit',
        Delete: 'delete',
        Create: 'create'
      }
    }
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    getDataList() {
      this.loadingStatus = true
      const params = {
        name: this.searchForm.name,
        pageSize: this.pagination.size,
        pageNo: this.pagination.current
      }
      this.$api.SporadicProject.queryProjectStateByPage(params)
        .then((res) => {
          if (res.code === '200') {
            this.tableData = res.data.records
            this.pagination.total = res.data.total
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '查询失败'))
        .finally(() => (this.loadingStatus = false))
    },
    onSearch() {
      this.pagination.current = 1
      this.getDataList()
    },
    onReset() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    onOperate(row, type) {
      switch (type) {
        case this.OperateType.Delete:
          if (+row.state === 1) {
            this.$message.error('启用的配置不允许删除！')
          } else {
            this.$confirm('删除后将无法恢复，是否确定删除？', '信息提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消'
            }).then(() => this.doDelete(row.id))
          }
          break
        case this.OperateType.Create:
          this.dialog.baseVisible = true
          break
        default:
          this.dialog.detailVisible = true
          this.dialog.id = row?.id ?? ''
          this.dialog.detailReadonly = type === this.OperateType.View
          break
      }
    },
    doDelete(id) {
      this.$api.SporadicProject.deleteProjectStateById({ id })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('删除成功')
            this.getDataList()
          } else {
            throw res.message
          }
        })
        .catch((msg) => msg && this.$message.error(msg))
        .finally(() => (this.loadingStatus = false))
    }
  }
}
</script>
<template>
  <div class="status-config">
    <div class="status-config__top">
      <el-form ref="formRef" :model="searchForm" class="status-config__search" inline @submit.native.prevent="onSearch">
        <el-form-item prop="name">
          <el-input v-model="searchForm.name" clearable filterable placeholder="状态名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" plain @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="status-config__actions">
        <el-button type="primary" icon="el-icon-plus" @click="onOperate(undefined, OperateType.Create)">新增状态
        </el-button>
      </div>
    </div>
    <div class="status-config__table">
      <el-table v-loading="loadingStatus" height="100%" :data="tableData" border stripe table-layout="auto" row-key="id">
        <el-table-column label="序号" type="index" width="55" />
        <el-table-column label="名称" prop="name" width="150px"></el-table-column>
        <el-table-column label="编码" prop="code" width="150px"></el-table-column>
        <el-table-column label="项目类型" prop="businessFormName" show-overflow-tooltip></el-table-column>
        <el-table-column label="启用状态" prop="state" width="100px">
          <template #default="{ row }">
            <span class="status-config__tag" :class="`status-config__tag--${row.state}`">
              {{ row.state | statusFilter }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" show-overflow-tooltip :formatter="row => row.remark||'-'"></el-table-column>
        <el-table-column label="操作" width="150px">
          <template #default="{ row }">
            <el-button type="text" @click="onOperate(row, OperateType.View)">查看</el-button>
            <el-button type="text" @click="onOperate(row, OperateType.Edit)">编辑</el-button>
            <!--预置值 presetsType，如果是预设的不允许删除 -->
            <el-button v-if="row.presetsType !== 1" type="text" class="text-red" @click="onOperate(row, OperateType.Delete)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination
      class="status-config__pagination"
      :current-page="pagination.page"
      :page-sizes="pagination.pageSizeOptions"
      :page-size="pagination.size"
      :layout="pagination.layoutOptions"
      :total="pagination.total"
      @size-change="paginationSizeChange"
      @current-change="paginationCurrentChange"
    >
    </el-pagination>
    <!--表单基础信息-->
    <StatusBase :visible.sync="dialog.baseVisible" @success="getDataList" @config="(id) => onOperate({ id }, OperateType.Edit)"></StatusBase>
    <!--表单详情 查看或者编辑-->
    <StatusDetail :id="dialog.id" :visible.sync="dialog.detailVisible" :readonly="dialog.detailReadonly" @success="getDataList"></StatusDetail>
  </div>
</template>
<style scoped lang="scss">
.status-config {
  display: flex;
  flex-flow: column nowrap;
  padding: 16px;
  &__top {
    display: flex;
    justify-content: space-between;
  }
  &__actions {
    line-height: 40px;
  }
  &__pagination {
    margin-top: 10px;
  }
  &__table {
    margin-top: -10px;
    flex: 1;
  }
  &__tag {
    // 停用
    &--0 {
      --color: #f64646;
    }
    // 启用
    &--1 {
      --color: #00b42a;
    }
    &:before {
      content: '';
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
      height: 4px;
      width: 4px;
      border-radius: 4px;
      background-color: var(--color);
    }
  }
}
</style>
