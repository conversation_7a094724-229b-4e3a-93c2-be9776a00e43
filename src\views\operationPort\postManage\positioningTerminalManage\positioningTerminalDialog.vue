<template>
  <el-dialog title="添加定位终端" width="60%" :visible.sync="visible" custom-class="model-dialog" :before-close="closeDialog">
    <div class="camera_content" style="padding: 10px 20px 10px 10px">
      <div class="header_operation">
        <div class="search_box">
          <div class="search_select">
            <el-input v-model="filter.name" style="width: 200px; margin-right: 16px" placeholder="设备名称"></el-input>
            <el-input v-model="filter.code" style="width: 200px" placeholder="资产编码"></el-input>
            <el-select v-model="filter.type" style="margin: 0px 15px" placeholder="系统类别">
              <el-option v-for="item in systemCategoryOptions" :key="item.id" :label="item.baseName" :value="item.id"> </el-option>
            </el-select>
          </div>
        </div>
        <div class="header_btn">
          <el-button type="primary" plain @click="userReset">重置</el-button>
          <el-button type="primary" @click="userQuery">查询</el-button>
        </div>
      </div>
      <div class="table_div">
        <el-table
          ref="cameraTable"
          row-key="assetsId"
          :data="tableData"
          v-loading="tableDataLoading"
          tooltip-effect="dark"
          style="width: 100%"
          height="310px"
          border
          @selection-change="bindSelectChange"
        >
          <el-table-column prop="radio" width="55" v-if="isRadio">
            <template slot-scope="scope">
              <el-radio v-model="selectedRow" :label="scope.row.assetsId" name="" @change="handleRadioChange(scope.row)"></el-radio>
            </template>
          </el-table-column>
          <el-table-column type="selection" :reserve-selection="true" width="55" align="center" v-else> </el-table-column>
          <el-table-column type="index" label="序号" width="55" align="center">
            <template slot-scope="scope">
              <span>{{ (pageinationData.page - 1) * pageinationData.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="定位点名称" align="center" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="code" label="资产编码" align="center" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="sn" label="SN" align="center" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="sysTypeName" label="专业类型" align="center" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="typeName" label="系统类别" align="center" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="bindState" label="状态" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-tag v-if="scope.row.bindState == 1" size="mini" type="success">已绑定</el-tag>
              <el-tag v-else-if="scope.row.bindState == 0" size="mini" type="danger">未绑定</el-tag>
              <span v-else></span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div style="padding: 6px">
        <el-pagination
          class="pagination"
          :current-page="pageinationData.page"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="pageinationData.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageinationData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="submitAnchorPointDialog">确 定</el-button>
    </span>
  </el-dialog>
</template>
  <script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 列表选择方式 是否单选
    isRadio: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      filter: {
        name: '', // 名称
        code: '', // 资产编码
        type: ''
      },
      selectedRow: '',
      tableDataLoading: false,
      tableData: [], // 数据
      pageinationData: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      checkedData: {},
      systemCategoryOptions: [],
      multipleSelection: []
    }
  },
  mounted() {
    this.getProfessionalCategory()
    this.getStaffListByPage()
  },
  methods: {
    bindSelectChange(val) {
      this.multipleSelection = val
    },
    handleRadioChange(row) {
      this.checkedData = row
    },
    // 重置
    userReset() {
      this.filter = {
        name: '', // 名称
        code: '', // 编码
        type: ''
      }
      this.userQuery()
    },
    // 点击查询
    userQuery() {
      this.pageinationData.page = 1
      this.getStaffListByPage()
    },
    // 获取列表
    getStaffListByPage() {
      let data = {
        ...this.filter,
        page: this.pageinationData.page,
        pageSize: this.pageinationData.pageSize
      }
      this.tableDataLoading = true
      this.$api.supplierAssess.getLocateAssetsData(data).then((res) => {
        this.tableDataLoading = false
        if (res.code == 200) {
          this.tableData = res.data.records
          this.pageinationData.total = res.data.total
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 获取系统类别
    getProfessionalCategory() {
      this.$api.getProfessionalCategory().then((res) => {
        if (res.code == '200') {
          this.systemCategoryOptions = res.data
        }
      })
    },
    // 弹框分页
    handleSizeChange(val) {
      this.pageinationData.pageSize = val
      this.getStaffListByPage()
    },
    handleCurrentChange(val) {
      this.pageinationData.page = val
      this.getStaffListByPage()
    },
    submitAnchorPointDialog() {
      let fn, params
      if (this.isRadio) {
        if (JSON.stringify(this.checkedData) == '{}') {
          this.$message.error('请选择定位终端！')
          return
        }
        params = {
          ...this.checkedData
        }
        fn = this.$api.supplierAssess.insertLocateAssetsData
      } else {
        if (this.multipleSelection.length === 0) {
          this.$message.error('请选择定位终端！')
          return
        }
        params = {
          list: this.multipleSelection
        }
        fn = this.$api.supplierAssess.batchAddLocatePoint
      }
      fn(params).then((res) => {
        if (res.code == 200) {
          this.$message.success('添加成功')
          this.$emit('submitDialog')
        } else {
          this.$message.error('添加失败')
        }
      })
    },
    closeDialog() {
      this.$emit('close')
    }
  }
}
</script>
  <style lang="scss" scoped>
.model-dialog {
  .camera_content {
    margin: 0 auto;
    background: #fff;
    border-radius: 4px;
    width: 100%;
    height: 100%;

    .header_operation {
      margin-bottom: 10px;
      display: flex;

      .search_box {
        flex: 1;
        .search_select {
          margin-right: 16px;
        }

        .search_input {
          width: 200px;
        }
      }
    }

    .table_div {
      height: calc(100% - 100px);
    }

    .paging_box {
      display: flex;
      justify-content: flex-end;
      padding: 6px;
    }
  }
}
.el-radio {
  display: flex;
  justify-content: center;
  align-items: center;
}
::v-deep .el-radio__label {
  display: none;
}
</style>