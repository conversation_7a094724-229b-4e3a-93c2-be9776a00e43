<template>
  <PageContainer style="position: relative">
    <div slot="content" class="whole">
      <div class="left">
        <div class="title_box"><svg-icon name="right-arrow" /> 空间结构</div>
        <el-input v-model="filterText" style="width: 230px; padding-top: 10px" placeholder="请输入关键字"></el-input>
        <div v-loading="treeLoading" class="sino_tree_box">
          <div class="tree_div">
            <el-tree
              ref="tree"
              class="filter-tree"
              :data="treeData"
              :props="defaultProps"
              :default-expanded-keys="idArr"
              :filter-node-method="filterNode"
              :highlight-current="true"
              node-key="id"
              @node-click="nodeClick"
            ></el-tree>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="right_top">
          <div><svg-icon name="right-arrow" /> 总览</div>
          <div v-for="item in list" :key="item.id">
            <div class="box">
              <div class="box_left">{{ item.name + '(' + item.count + ')' }}</div>
              <div class="box_right">
                <div v-for="item2 in item.children" :key="item2.id">
                  <div class="box_right_l">{{ item2.name + '(' + item2.count + ')' }}</div>
                  <div class="box_right_r">{{ namesString(item2.children) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div style="display: flex">
          <el-input v-model="searchFrom.keywords" placeholder="房间号/名称" style="width: 200px"></el-input>&nbsp;
          <el-button type="primary" @click="inquiry">查询</el-button>
          <el-button type="primary" plain @click="rest">重置</el-button>
        </div>
        <div slot="content" style="height: 80%">
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table :data="tableData" :height="tableHeight" stripe border>
                <el-table-column label="房间名称" width="150" prop="localSpaceName" show-overflow-tooltip> </el-table-column>
                <el-table-column label="房间号" prop="roomCode" show-overflow-tooltip> </el-table-column>
                <el-table-column label="位置" prop="simName" show-overflow-tooltip> </el-table-column>
                <el-table-column label="归属科室" prop="dmName" show-overflow-tooltip> </el-table-column>
                <el-table-column label="操作" prop="" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span style="cursor: pointer; padding: 0 5px; color: #3562db" @click="lookOver(scope.row)">查看</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="contentTable-footer">
            <el-pagination
              :current-page="pagination.current"
              :page-sizes="[15, 30, 50, 100]"
              :page-size="pagination.size"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            ></el-pagination>
          </div>
        </div>
      </div>
      <el-drawer :visible.sync="drawer" :with-header="false" :wrapperClosable="false">
        <div class="drawer">
          <div class="drawer_title" @click="closeDrawer"><i class="el-icon-arrow-left"></i>{{ roomDetails.localSpaceName }}</div>
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="设施列表" name="0">
              <el-button type="primary" @click="addTable" v-if="btnType == 'add'" style="margin-top: 20px">+新增</el-button>
              <el-table :data="tableData2" border style="width: 100%; margin-top: 20px; height: 600px" :height="tableHeight">
                <el-table-column prop="name" label="设施类型">
                  <template slot-scope="scope">
                    <span v-if="scope.row.primaryFacilityTypeName != ''">{{ scope.row.primaryFacilityTypeName + '/' + scope.row.secondaryFacilityTypeName || '' }}</span>
                    <el-cascader
                      :options="dictTypeList"
                      :props="cascaderProps"
                      :show-all-levels="false"
                      @change="(value) => handleChange(value, scope.row)"
                      v-model="selectedPath"
                      v-else
                    ></el-cascader>
                  </template>
                </el-table-column>
                <el-table-column prop="total" label="数量">
                  <template slot-scope="scope">
                    <span v-if="btnType != 'add'">{{ scope.row.deviceQuantity }}</span>
                    <div v-else>
                      <el-input-number v-model="scope.row.deviceQuantity" size="mini" :min="0" label=""></el-input-number>&nbsp;
                      <i class="el-icon-delete-solid" style="color: red; font-size: 20px" @click="deleTable(scope.row)"></i>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="变动记录" name="1">
              <el-table :data="tableData3" :span-method="objectSpanMethod" border style="width: 100%; margin-top: 20px; height: 500px" :height="tableHeight">
                <el-table-column prop="changeTime" label="时间" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="operator" label="操作人" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="operatorDepartment" label="操作人部门" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="facilityTypeName" label="设施类型" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="quantityBefore" label="变更前"> </el-table-column>
                <el-table-column prop="quantityAfter" label="变更后"> </el-table-column>
              </el-table>
              <el-pagination
                :current-page="pagination2.current"
                :page-sizes="[15, 30, 50, 100]"
                :page-size="pagination2.size"
                :total="total2"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange2"
                @current-change="handleCurrentChange2"
              ></el-pagination>
            </el-tab-pane>
          </el-tabs>
          <div>
            <div style="display: flex" class="btn" v-if="activeName == '0'">
              <el-button type="primary" plain @click="closeDrawer">关闭</el-button>
              <el-button type="primary" @click="btnType = 'add'" v-if="btnType == 'edit'">编辑</el-button>
              <el-button type="primary" @click="add" v-else>保存</el-button>
            </div>
          </div>
        </div>
      </el-drawer>
    </div>
  </PageContainer>
</template>
<script>
import { transData } from '@/util'
import tableListMixin from '@/mixins/tableListMixin.js'
export default {
  mixins: [tableListMixin],
  data() {
    return {
      params: {},
      dictTypeList: [],
      dictTypeList2: [],
      cascaderProps: {
        label: 'meetingDictName',
        value: 'meetingDictCode',
        children: 'children',
        emitPath: true
      },
      selectedPath: [],

      searchFrom: {
        keywords: ''
      },
      list: [],
      btnType: 'edit',
      tableData2: [],
      tableData3: [],
      activeName: '0',
      roomDetails: '',
      drawer: false,
      functionDictOptions: [],
      functionDictProps: {
        label: 'name',
        value: 'id',
        leaf: 'leaf'
      },
      treeLoading: true,
      filterText: '',
      treeData: [],
      idArr: [],
      spaceIds: [],
      defaultProps: {
        label: 'ssmName',
        children: 'list'
      },
      // spaceTypeList: [],
      tableData: [
        {
          name: '电梯间',
          id: '0'
        }
      ],
      // -----------------------------Pagination
      pagination: {
        current: 1,
        size: 15
      }, // 分页数据
      total: 0, // 数据总条数
      pagination2: {
        current: 1,
        size: 15
      }, // 分页数据
      total2: 0 // 数据总条数
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {},
  mounted() {
    this.spaceTreeListFn()
    this.valveTypeListFn()
  },
  methods: {
    getFacilityRecords(id) {
      let params = {
        spaceId: id,
        currentPage: this.pagination2.current,
        pageSize: this.pagination2.size
      }
      this.$api.getFacilityRecords(params).then((res) => {
        if (res.code == 200) {
          this.tableData3 = res.data.records
          this.total2 = res.data.total
        }
      })
    },
    handleChange(path, row) {
      const exists = this.tableData2.some((item) => item.secondaryFacilityTypeId === path[2])
      if (exists) {
        this.selectedPath = []
        return this.$message.error('已存在该设施,请重新选择')
      }

      row.primaryFacilityTypeId = path[0] + '/' + path[1]
      const primaryFacilityTypeName = this.dictTypeList2.find((item) => item.meetingDictCode === path[0])
      const primaryFacilityTypeName2 = this.dictTypeList2.find((item) => item.meetingDictCode === path[1])
      row.primaryFacilityTypeName = primaryFacilityTypeName.meetingDictName + '/' + primaryFacilityTypeName2.meetingDictName
      row.secondaryFacilityTypeId = path[2]
      const secondaryFacilityTypeName = this.dictTypeList2.find((item) => item.meetingDictCode === path[2])
      row.secondaryFacilityTypeName = secondaryFacilityTypeName.meetingDictName
      this.selectedPath = []
    },
    getDictList() {
      const params = {
        dictType: 'facility',
        status: '0',
        page: {
          current: 1,
          size: 99999,
          total: 0
        }
      }
      this.$api.meetingDictGetList(params).then((res) => {
        if (res.code == '200') {
          this.dictTypeList2 = res.data.list
          this.processOptions(transData(res.data.list, 'id', 'parentId', 'children'))
        } else {
          this.$message.error('获取字典列表失败！')
        }
      })
    },
    //只保留三级数据
    processOptions(options) {
      this.dictTypeList = options
        .filter((parent) => {
          return parent.children && parent.children.some((child) => child.children && child.children.length > 0)
        })
        .map((parent) => ({
          ...parent,
          children: parent.children
            .filter((child) => {
              return child.children && child.children.length > 0
            })
            .map((child) => ({
              ...child,
              children: child.children
            }))
        }))
    },
    namesString(arr) {
      return arr.map((obj) => obj.name + '(' + obj.count + ')').join(', ')
    },
    deleTable(row) {
      this.tableData2 = this.tableData2.filter((item) => item.id !== row.id)
    },
    addTable() {
      this.tableData2.push({
        deviceQuantity: '1',
        primaryFacilityTypeId: '',
        primaryFacilityTypeName: '',
        secondaryFacilityTypeId: '',
        secondaryFacilityTypeName: '',
        id: this.tableData2.length
      })
    },
    edit() {
      this.btnType = 'add'
    },
    add() {
      const facilityTypeId = this.tableData2.some((obj) => obj.primaryFacilityTypeId === '')
      if (facilityTypeId) return this.$message({ message: '请选择设施类型', type: 'error' })
      let params = {
        deptId: this.$store.state.user.userInfo.user.deptId,
        spaceId: this.roomDetails.id,
        userId: this.$store.state.user.userInfo.user.staffId,
        fullSpaceId: this.roomDetails.simCode,
        jsonString: JSON.stringify(this.tableData2)
      }
      this.$api.processDeviceChanges(params).then((res) => {
        if (res.code == 200) {
          this.btnType = 'edit'
          this.$message({ message: '保存成功', type: 'success' })
          this.getDevicesList(this.roomDetails.id)
          this.getFacilityRecords(this.roomDetails.id)
          this.selectedPath = []
        }
      })
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0 || columnIndex === 1 || columnIndex === 2) {
        const currentGroup = row.groupIndex
        let rowspan = 1

        for (let i = rowIndex + 1; i < this.tableData3.length; i++) {
          if (this.tableData3[i].groupIndex === currentGroup) {
            rowspan++
          } else {
            break
          }
        }

        if (this.tableData3[rowIndex - 1]?.groupIndex !== currentGroup || rowIndex === 0) {
          return {
            rowspan: rowspan,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 1
          }
        }
      } else {
        return {
          rowspan: 1,
          colspan: 1
        }
      }
    },
    handleClick(tab, event) {
      console.log(tab, event)
    },
    //查看
    lookOver(row) {
      this.roomDetails = row
      this.getDevicesList(row.id)
      this.getFacilityRecords(row.id)
      this.drawer = true
      this.getDictList()
    },
    //根据空间ID分页查询设备列表记录
    getDevicesList(id) {
      let params = {
        spaceId: id,
        currentPage: 1,
        pageSize: 9999
      }
      this.$api.getDevicesList(params).then((res) => {
        if (res.code == 200) {
          this.tableData2 = res.data.records
        }
      })
    },
    closeDrawer() {
      this.activeName = '0'
      this.btnType = 'edit'
      this.drawer = false
    },
    inquiry() {
      this.pagination.current = 1
      this.pagination.size = 15
      this.getSpacelistFn()
    },
    rest() {
      this.pagination.current = 1
      this.pagination.size = 15
      this.searchFrom.keywords = ''
      this.getSpacelistFn()
    },
    //  ------------------------------获取空间结构 Tree And fn
    spaceTreeListFn() {
      this.$api.spaceTreeList().then((res) => {
        this.treeLoading = false
        if (res.code == 200) {
          res.data.map((tree) => {
            tree.ssmType == 1 || tree.ssmType == 2 ? this.idArr.push(tree.id) : ''
            return this.idArr
          })
          this.treeData = transData(res.data, 'id', 'pid', 'list')
          this.$nextTick(function () {
            this.$refs.tree.setCurrentKey(res.data[0].id)
          })
          this.getDeviceCountGroupedByFacilityType(res.data[0].id)

          this.spaceIds.push(res.data[0].id)
          this.getSpacelistFn()
        }
      })
    },
    filterNode(value, data) {
      if (!value) return true
      return data.ssmName.indexOf(value) !== -1
    },
    nodeClick(val) {
      const { tree } = this.$refs
      this.spaceIds = []

      let checkedNode = tree.getNode(val.id)
      this.getTreeNode(checkedNode)

      this.pagination.current = 1
      this.pagination.size = 15
      this.getDeviceCountGroupedByFacilityType(val.id)
      this.getSpacelistFn()
    },
    //查询总览
    getDeviceCountGroupedByFacilityType(id) {
      this.$api.getDeviceCountGroupedByFacilityType({ spaceId: id }).then((res) => {
        if (res.code == 200) {
          this.list = res.data
        }
      })
    },
    getTreeNode(node) {
      if (node.level >= 1) {
        this.spaceIds.unshift(node.data.id)
        this.getTreeNode(node.parent)
      } else {
        this.spaceIds.unshift('#')
      }
    },
    //  获取功能类型字典列表
    valveTypeListFn() {
      this.$api.GetSuperiorData({ dictionaryCategoryId: 'SPACE_FUNCTION' }).then((res) => {
        if (res.code == 200) {
          this.functionDictOptions = this.findChild(res.data[0].children)
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    // 递归处理最末级数据
    findChild(treeData) {
      return findItem(treeData)
      function findItem(data) {
        data.forEach((i) => {
          if (!i.children.length) {
            delete i.children
            i.leaf = false
          } else {
            findItem(i.children)
          }
        })
        return treeData
      }
    },
    getSpacelistFn() {
      let data = {
        simCode: '',
        current: this.pagination.current,
        size: this.pagination.size,
        keywords: this.searchFrom.keywords
      }
      data.simCode = this.spaceIds.join()
      this.$api.getSpaceInfoList(data).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.total = res.data.total
        }
      })
    },
    handleSizeChange(val) {
      this.pagination.size = val
      this.getSpacelistFn()
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.getSpacelistFn()
    },
    handleSizeChange2(val) {
      this.pagination2.size = val
      this.getFacilityRecords(this.roomDetails.id)
    },
    handleCurrentChange2(val) {
      this.pagination2.current = val
      this.getFacilityRecords(this.roomDetails.id)
    }
    // ---------------------------sino-slide-Panel
  }
}
</script>
<style lang="scss" scoped>
.page-container {
  position: relative;
}
.sino_tab {
  .sino_tab_content {
    height: 280px;
    overflow: auto;
    .sino_tab_btn {
      position: absolute;
      right: 0;
      top: 0;
      margin: 0;
    }
    .line_style {
      border-bottom: 1px solid #ddd;
    }
  }
  .sino_tab_btn_box {
    height: 280px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .floor_box {
    line-height: 32px;
    margin: 0 100px 0 20px;
    padding-left: 15px;
    font-size: 14px;
  }
}
.checkbox_group {
  height: 200px;
  overflow: auto;
}
.el-main {
  padding: 15px;
  overflow: hidden;
  background-color: #fff;
  .sino_panel {
    height: 100%;
    border-radius: 10px;
    background: #fff;
    position: relative;
    font-size: 14px;
  }
  .sino_page {
    height: 100%;
    position: relative;
    .el-aside {
      width: 260px;
      margin: 0 16px 0 0;
      overflow: hidden;
    }
    .sino_page_left {
      margin: 0 0 0 16px !important;
    }
    .el-aside,
    .el-main {
      // height: 100%;
      background: #fff;
      border-radius: 10px;
      padding: 0;
      .el-collapse {
        height: calc(100% - 40px);
        overflow: auto;
      }
    }
  }
}
.title_box {
  box-sizing: border-box;
  padding-left: 24px;
  height: 50px;
  width: 100%;
  line-height: 50px;
  text-align: left;
  border-bottom: 1px solid #d8dee7;
  .title-tip {
    display: inline-block;
    width: 4px;
    height: 16px;
    margin-right: 8px;
    position: relative;
    top: 2px;
    background: #5188fc;
  }
  .title_name {
    font-size: 16px;
    font-weight: bold;
  }
  .title_btn_icon {
    float: right;
  }
  .title_btn_icon i {
    margin-right: 20px;
    cursor: pointer;
  }
}
.whole {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  .left {
    text-align: center;
    width: 250px;
    height: 100%;
    background-color: #fff;
    border-radius: 5px;
    overflow: hidden;
    .left_d {
      height: calc(100% - 20px);
    }
  }
  .right {
    width: calc(100% - 260px);
    background-color: #fff;
    height: 100%;
    border-radius: 5px;
    padding: 10px;
    .right_top {
      width: 100%;
      height: 20%;
      overflow: auto;
      margin: 0 0 2px 0;
      .box {
        width: 100%;
        display: flex;
        line-height: 30px;
        .box_left {
           margin: 3px 0;
          width: 20%;
          margin-right: 10px;
          border: 1px solid #ebecf0;
          border-radius: 5px;
          background-color: #f7f8fa;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .box_right {
          width: 79%;
          > div {
            display: flex;
            margin: 3px 0;
            width: 100%;
            .box_right_l {
              padding: 5px;
              width: 20%;
              margin-right: 10px;
              border: 1px solid #ebecf0;
              border-radius: 5px;
              background-color: #f7f8fa;
            }
            .box_right_r {
              padding: 5px;
              border-radius: 5px;
              width: 79%;
              border: 1px dashed #ebecf0;
            }
          }
        }
      }
    }
  }
}
.contentTable {
  height: calc(100% - 58px);
  background: #fff;
  border-radius: 4px;
  padding: 16px 0 0;
  display: flex;
  flex-direction: column;
  // overflow: auto;
  .contentTable-main {
    flex: 1;
    overflow: auto;
  }
  .contentTable-footer {
    padding: 10px 0 0;
  }
  .alarmLevel {
    padding: 3px 6px;
    border-radius: 4px;
    color: #fff;
    line-height: 14px;
  }
  .alarmStatus {
    position: relative;
    display: inline-block;
    padding-left: 12px;
    .alarmStatusIcon {
      display: inline-block;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      border-radius: 100%;
    }
  }
  .collectIcon {
    font-size: 16px;
    margin-right: 4px;
  }
}
.sino_tree_box {
  margin: 10px;
  height: calc(100% - 100px) !important;
  overflow: auto;
  .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    background-color: #f5f6fb;
  }
  .el-tree-node__content {
    height: 38px;
    .el-tree-node__label {
      font-size: 15px;
    }
  }
}
.color_blue {
  color: #5d81e2;
  cursor: pointer;
}
.ipt {
  width: 200px;
  margin-right: 10px;
}
.el_radio {
  width: 120px;
  padding: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
}
::v-deep .el-dialog {
  width: 70%;
}
.drawer {
  height: 100%;
  position: relative;
  padding: 10px;
  .drawer_title {
    line-height: 30px;
    cursor: pointer;
  }
  .drawer_title:hover {
    color: #3562db;
  }
  .btn {
    position: absolute;
    right: 5%;
    bottom: 1%;
  }
}
</style>
