<!--
 * @Author: hedd
 * @Date: 2023-04-23 16:03:23
 * @LastEditTime: 2024-08-05 09:49:56
 * @FilePath: \ihcrs_pc\src\views\monitor\elevatorMenu\elevatorAnalysis.vue
 * @Description:
-->
<template>
  <commonStatisticalAnalysis :projectCode="projectCode" :requestHttp="requestHttp" />
</template>
<script>
import commonStatisticalAnalysis from '../commonPage/statisticalAnalysis/index'
import { monitorTypeList } from '@/util/dict.js'
export default {
  name: 'elevatorAnalysis',
  components: { commonStatisticalAnalysis },
  data() {
    return {
      requestHttp: __PATH.VUE_IEMC_ELEVATOR_API,
      projectCode: monitorTypeList.find((item) => item.projectName == '电梯监测').projectCode
    }
  },
  computed: {},
  created() {},
  methods: {}
}
</script>
<style lang="scss" scoped></style>
