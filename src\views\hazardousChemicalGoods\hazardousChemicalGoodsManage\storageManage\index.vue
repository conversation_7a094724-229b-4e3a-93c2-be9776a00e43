<template>
  <PageContainer class="storageManage">
    <template #content>
      <el-tabs class="storageManage__nav" :value="currentTab" @tab-click="onTabClick">
        <el-tab-pane name="manual" label="手工入库"></el-tab-pane>
        <!-- <el-tab-pane name="scanCode" label="扫码入库"></el-tab-pane> -->
        <el-tab-pane name="list" label="入库单查询"></el-tab-pane>
        <el-tab-pane name="particulars" label="入库明细查询"></el-tab-pane>
      </el-tabs>
      <component :is="currentComponent" class="storageManage__content" @openDetailComponent="openDetailComponent" />
    </template>
  </PageContainer>
</template>
<script>
import storageList from './storageList.vue'
import particulars from './particulars.vue'
import scanCodeStorage from './scanCodeStorage.vue'
import manualStorage from './manualStorage.vue'
export default {
  name: 'storageManage',
  components: {
    storageList,
    particulars,
    scanCodeStorage,
    manualStorage
  },
  beforeRouteEnter(to, from, next) {
    if (from.name !== 'hazardousChemicalAdd') {
      sessionStorage.removeItem('hazardousChemicalBaseInfo');
    }
    next((vm) => {
      if (from.query && from.query.currentType) {
        vm.currentTab = from.query.currentType
      }
    })
  },
  data() {
    return {
      currentTab: 'manual',
      componentConfig: {
        manual: 'manualStorage',
        list: 'storageList',
        particulars: 'particulars',
        scanCode: 'scanCodeStorage'
      }
    }
  },
  computed: {
    currentComponent() {
      return this.componentConfig[this.currentTab]
    }
  },
  methods: {
    onTabClick(e) {
      const name = e.name
      if (name !== this.currentTab) {
        this.$router.push({ query: { name } })
        this.currentTab = name
      }
      if (name !== 'manual') {
        sessionStorage.removeItem('hazardousChemicalBaseInfo');
      }
    },
    // 组件详情
    openDetailComponent(row) {
      if (row.type === 'warehouseWarrant') {
        this.$router.push({ query: row })
        this.currentTab = 'manual'
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.page-container.storageManage {
  ::v-deep(.container-content) {
    background: #fff;

    .storageManage__nav {
      .el-tabs__nav-scroll {
        padding: 0 32px;
      }
    }

    .storageManage__content {
      height: calc(100% - 40px);
      overflow: hidden;
    }
  }
}
</style>
