<template>
  <div class="main-container">
    <el-form ref="inputForm" :model="formItem" :rules="rules" label-width="100px">
      <div style="background-color: #fff;">
        <el-row>
          <el-col :span="1.5" class="table-label">
            <i class="is-require">*</i>
            <span>标题 :</span>&nbsp;
          </el-col>
          <el-col :span="21">
            <el-form-item prop="name" :rules="rules.name" label-width="0">
              <el-input v-model="formItem.name" type="textarea" rows="1" style="width: 40%;"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="1.5" class="table-label"> <span>类型 :</span>&nbsp; </el-col>
          <el-col :span="21" style="margin-top: 15px;">
            <el-form-item label-width="0">
              <el-radio-group v-model="formItem.inputType" style="line-height: 20px;">
                <el-radio label="row">单行输入</el-radio>
                <el-radio label="rows">多行输入</el-radio>
                <el-radio label="number">数字</el-radio>
                <el-radio label="phone">手机号</el-radio>
                <el-radio label="email">邮箱</el-radio>
                <el-radio label="incard">身份证</el-radio>
                <el-radio label="url">网址</el-radio>
                <el-radio label="date">日期</el-radio>
                <el-radio label="time">时间</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <el-row style="display: flex; margin-top: 10px; background-color: #fff; align-items: center; padding-left: 10px;">
        <el-col :span="3">
          <el-form-item label-width="0" style="margin: 0;">
            <el-checkbox v-model="isSetMust">是否必填</el-checkbox>
          </el-form-item>
        </el-col>
        <el-col v-show="formItem.inputType === 'number'" :span="6">
          <el-form-item label="最小值" style="margin: 0;">
            <el-input v-model="formItem.minValue" type="number" @mousewheel.native.prevent></el-input>
          </el-form-item>
        </el-col>
        <el-col v-show="formItem.inputType === 'number'" :span="6">
          <el-form-item prop="maxValue" :rules="rules.maxMinJudge" label="最大值" style="margin: 0;">
            <el-input v-model="formItem.maxValue" type="number" @mousewheel.native.prevent></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import notice from '../utils/notice'
import utils from '../utils/utils'
// import axiosConter from "../../../../axios/centralControl";
import checkCentralControl from '../check/checkCentralControl'
export default {
  props: {
    questionSubject: {
      type: Object
    }
  },
  data() {
    return {
      formItem: {
        name: '',
        isMust: 0, // 是否必填
        inputType: 'row', // row, rows, number,phone, email,incard,url,date,time
        maxValue: '',
        minValue: ''
      },
      rules: this.getCheckConfigCompletion() // 获取配置函数
    }
  },
  computed: {
    isSetMust: {
      set(value) {
        this.formItem.isMust = value ? 1 : 0
      },
      get() {
        return this.formItem.isMust === 1
      }
    }
  },
  mounted() {
    if (this.questionSubject.id) {
      this.formItem = JSON.parse(JSON.stringify(this.questionSubject))
    }
    notice.$emit('initChildComponent', this, '填空题')
    notice.$on('handleSubmit', (component) => {
      if (this === component) {
        this.submitForm()
      }
    })
  },
  methods: {
    getCheckConfigCompletion() {
      var configObj = checkCentralControl.getCheckConfig('completion')
      configObj.maxMinJudge = [
        {
          validator: (rule, value, callback) => {
            this.checkMaxMinJudge(rule, value, callback)
          },
          trigger: 'submit'
        }
      ] // 获取表单校验对象  (填空题校验)
      return configObj
    },
    checkMaxMinJudge(rule, value, callback) {
      if ((this.formItem.minValue && this.formItem.maxValue) || this.formItem.minValue == 0 || this.formItem.maxValue == 0) {
        var max = Number(this.formItem.maxValue)
        var min = Number(this.formItem.minValue)
        if (max < min) {
          return callback(new Error('不能小于最小值'))
        }
      }
      callback()
    },
    submitForm() {
      this.$refs['inputForm'].validate((valid) => {
        if (valid) {
          this.saveQuestionSubject()
        } else {
          return false
        }
      })
    },
    saveQuestionSubject() {
      const params = {
        id: this.questionSubject.id ? this.questionSubject.id : '',
        type: 'input',
        inputType: this.formItem.inputType,
        name: this.formItem.name,
        isMust: this.formItem.isMust,
        pvqId: localStorage.getItem('questId'),
        maxValue: this.formItem.maxValue,
        minValue: this.formItem.minValue
      }
      if (params.id == '') {
        this.$api.saveQuestion(params).then((res) => {
          if (res.status == 200) {
            this.$message({
              message: res.message,
              type: 'success'
            })
            notice.$emit('closeQuestionDialog', this)
          }
        })
      } else {
        this.$api.updateQuestion(params).then((res) => {
          if (res.status == 200) {
            this.$message({
              message: res.message,
              type: 'success'
            })
            notice.$emit('closeQuestionDialog', this)
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
.main-container {
  width: 100%;

  .table-label {
    text-align: left;
    box-sizing: border-box;
    padding-left: 15px;
    margin-top: 10px;
  }

  .is-require {
    position: absolute;
    top: 0;
    left: 0%;
    color: red;
    padding-top: 12px;
  }

  div .el-input__inner {
    padding: 0 0 0 10px;
  }
}
</style>
