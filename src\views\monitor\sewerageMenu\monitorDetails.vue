<!-- 环境监测详情 -->
<template>
  <PageContainer v-loading="loading" class="monitorDetails">
    <div slot="header" class="monitorDetails-heade">
      <i
        class="el-icon-arrow-left"
        @click="
          () => {
            $router.go(-1)
          }
        "
      />
      <span>{{ $route.query.surveyName }}</span>
    </div>
    <div slot="content" class="content-details">
      <div class="content-heade">
        <div class="heade-info">
          <p>
            <span>服务空间：</span>
            <span>{{ detailsData.spaceName }}</span>
          </p>
          <p>
            <span>编号：</span>
            <span>{{ detailsData.surveyNo }}</span>
          </p>
        </div>
        <ContentCard title="监测参数">
          <div slot="content">
            <div class="control-btn-header">
              <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 0 }" plain @click="timeTypeChange(0)">今日</el-button>
              <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 1 }" plain @click="timeTypeChange(1)">本月</el-button>
              <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 2 }" plain @click="timeTypeChange(2)">本年</el-button>
              <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 3 }" plain style="margin-right: 10px;" @click="timeTypeChange(3)">自定义</el-button>
              <el-date-picker
                v-model="requestInfo.dataRange"
                type="daterange"
                unlink-panels
                :disabled="requestInfo.timeType != 3"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                clearable
              />
              <div style="display: inline-block;">
                <el-button type="primary" plain @click="resetForm">重置</el-button>
                <el-button type="primary" @click="searchForm">查询</el-button>
              </div>
            </div>
          </div>
        </ContentCard>
      </div>
      <div class="content-main">
        <ContentCard v-for="(item, index) in detailsData.list" :key="item.paramName" :title="item.paramName + (item.unit || '')">
          <echarts slot="content" :ref="'chart' + index" onTimeLineChange :domId="'chart' + index" @timelinechanged="(v) => timelinechanged(index, v)" />
        </ContentCard>
      </div>
    </div>
  </PageContainer>
</template>

<script>
import moment from 'moment'
import { monitorTypeList } from '@/util/dict.js'
moment.locale('zh-cn')
export default {
  name: 'monitorDetails',
  async beforeRouteLeave(to, from, next) {
    if (!['sewerageMonitor'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      detailsData: {},
      loading: true,
      requestInfo: {
        projectCode: monitorTypeList.find(item => item.projectName == '给排水监测').projectCode,
        dataRange: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')], // 时间范围
        timeType: 0 // 0: 当天 1: 本月 2: 本年 3: 自定义
      }
    }
  },
  computed: {},
  mounted() {
    if (!this.$store.state.keepAlive.list.includes('sewerageMonitor')) {
      this.getDetails()
    }
  },
  activated() {
    this.getDetails()
  },
  methods: {
    // 图表分页
    timelinechanged(chartIndex, month) {
      let { projectCode, timeType, dataRange } = this.requestInfo
      let params = {
        startTime: dataRange[0],
        endTime: dataRange[1],
        projectCode, timeType,
        yearMouth: month.currentIndex + 1,
        paramId: this.detailsData.list[chartIndex].paramId,
        ...this.$route.query
      }
      this.$api.YearTimeByParamId(params).then((res) => {
        if (res.code == 200) {
          if (res.data.unit) {
            this.$refs['chart' + chartIndex][0].init(this.chartOptions(res.data, month.currentIndex))
          } else {
            this.$refs['chart' + chartIndex][0].init(this.heatChart(res.data, month.currentIndex))
          }
        }
      })
    },
    // 日期选择
    timeTypeChange(type) {
      let newObj = {
        0: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        1: [moment().startOf('month').format('YYYY-MM-DD'), moment().endOf('month').format('YYYY-MM-DD')],
        2: [moment().startOf('year').format('YYYY-MM-DD'), moment().endOf('year').format('YYYY-MM-DD')],
        3: []
      }
      this.requestInfo.dataRange = newObj[type]
      this.requestInfo.timeType = type
    },
    // 获取详情
    getDetails() {
      let { projectCode, timeType, dataRange } = this.requestInfo
      this.$api.GetParameterListByImsCode({ startTime: dataRange[0], endTime: dataRange[1], projectCode, timeType, ...this.$route.query }).then((res) => {
        this.loading = false
        if (res.code == 200) {
          this.detailsData = res.data
          this.$nextTick(() => {
            (this.detailsData?.list ?? []).forEach((item, index) => {
              if (item.unit) {
                this.$refs['chart' + index][0].init(this.chartOptions(item, this.requestInfo.timeType == 2 ? Number(res.data.yearMouth[res.data.yearMouth.length - 1]) - 1 : 0))
              } else {
                this.$refs['chart' + index][0].init(this.heatChart(item, this.requestInfo.timeType == 2 ? Number(res.data.yearMouth[res.data.yearMouth.length - 1]) - 1 : 0))
              }
            })
          })
          console.log(this.detailsData)
        }
      }).catch(() => {
        this.loading = false
      })
    },
    heatChart(item, index = 0) {
      let option
      if (item.valueList.length) {
        option = {
          tooltip: {
            formatter: '{a} <br> {b}'
          },
          dataZoom: [
            {
              type: 'inside',
              start: 0,
              end: 100
            }
          ],
          grid: {
            left: '20',
            right: '70',
            bottom: this.requestInfo.timeType == 2 ? '60' : '25',
            top: '20',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            splitArea: {
              show: false
            }
          },
          yAxis: {
            type: 'category',
            data: item.dictList,
            axisLabel: {
              color: '#999',
              textStyle: {
                fontSize: 12
              }
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#F3F4F4'
              }
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            splitArea: {
              show: false
            }
          },
          visualMap: {
            show: false,
            min: 0,
            max: 5,
            inRange: {
              color: ['#5291FF', '#C7DBFF']
            }

          },
          series: [
            {
              name: item.paramName,
              type: 'heatmap',
              data: item.valueList.map(v => [v.time, item.dictList.indexOf(v.valueName), parseInt(v.value)]),
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      if (this.requestInfo.timeType == 2) {
        option.timeline = {
          axisType: 'category',
          realtime: false,
          autoPlay: false,
          controlStyle: {
            showPlayBtn: false
          },
          left: 20,
          right: 20,
          currentIndex: index,
          data: this.detailsData?.yearMouth ?? [],
          label: {
            formatter: function (s) {
              return Number(s) + '月'
            }
          }
        }
      }
      return option
    },
    chartOptions(item, index = 0) {
      let option
      if (item.valueList.length) {
        option = {
          backgroundColor: '#fff',
          color: ['#73A0FA', '#73DEB3', '#FFB761'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              crossStyle: {
                color: '#999'
              },
              lineStyle: {
                type: 'dashed'
              }
            }
          },
          dataZoom: [
            {
              type: 'inside',
              start: 0,
              end: 100
            }
          ],
          grid: {
            left: '20',
            right: '70',
            bottom: this.requestInfo.timeType == 2 ? '60' : '25',
            top: '20',
            containLabel: true
          },
          xAxis: {
            type: 'time',
            boundaryGap: false,
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            }
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              color: '#999',
              textStyle: {
                fontSize: 12
              }
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#F3F4F4'
              }
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            }
          },
          series: [
            {
              name: item.paramName + item.unit,
              type: 'line',
              data: item.valueList.map((v) => [v.time, v.value]),
              markLine: {
                symbol: 'none',
                data: [
                  {
                    label: {
                      show: true,
                      position: 'end',
                      formatter: '{b}:{c}',
                      fontSize: 11,
                      color: '#FA403C',
                      offset: [0, -10]
                    },
                    lineStyle: {
                      color: '#FA403C'
                    },
                    type: 'max',
                    name: '最大值'
                  },
                  {
                    label: {
                      show: true,
                      position: 'end',
                      formatter: '{b}:{c}',
                      fontSize: 11,
                      color: '#00BC6D'
                    },
                    lineStyle: {
                      color: '#00BC6D'
                    },
                    type: 'average',
                    name: '平均值'
                  },
                  {
                    label: {
                      show: true,
                      position: 'end',
                      formatter: '{b}:{c}',
                      fontSize: 11,
                      color: '#3562DB',
                      offset: [0, 10]
                    },
                    lineStyle: {
                      color: '#3562DB'
                    },
                    type: 'min',
                    name: '最小值'
                  }
                ]
              }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      if (this.requestInfo.timeType == 2) {
        option.timeline = {
          axisType: 'category',
          realtime: false,
          autoPlay: false,
          controlStyle: {
            showPlayBtn: false
          },
          left: 20,
          right: 20,
          currentIndex: index,
          data: this.detailsData?.yearMouth ?? [],
          label: {
            formatter: function (s) {
              return Number(s) + '月'
            }
          }
        }
      }
      return option
    },
    // 重置查询表单
    resetForm() {
      this.requestInfo = {
        projectCode: monitorTypeList.find(item => item.projectName == '环境监测').projectCode,
        dataRange: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        timeType: 0
      }
      this.searchForm()
    },
    // 查询
    searchForm() {
      if (!this.requestInfo.dataRange.length) {
        this.$message.warning('请选择时间段！')
        return
      }
      this.getDetails()
    }
  }
}
</script>

<style lang="scss" scoped>
.monitorDetails {
  margin: 16px 16px 16px 0;

  p {
    margin: 0;
  }

  ::v-deep .container-header {
    margin-left: 16px;
  }

  .monitorDetails-heade {
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    align-items: center;
    padding: 13px 16px;
    font-size: 14px;
    font-weight: 500;
    color: #121f3e;
    line-height: 14px;

    .el-icon-arrow-left {
      font-size: 13px;
      margin-right: 8px;
      cursor: pointer;
      color: #7f848c;
    }
  }

  ::v-deep .container-header {
    border-radius: 4px 4px 0 0;
  }

  .content-details {
    height: 100%;
    overflow: auto;

    .content-heade {
      padding: 24px 16px 16px;
      background: #fff;
      border-radius: 0 0 4px 4px;
      margin-left: 16px;
    }

    .heade-info {
      display: flex;
      padding: 0 0 14px 16px;

      p {
        width: 50%;
        font-size: 14px;

        & :nth-child(1) {
          color: #414653;
        }

        & :nth-child(2) {
          color: #121f3e;
        }
      }
    }

    ::v-deep .box-card {
      padding: 0;
      height: auto;

      .card-body {
        overflow: hidden;
      }
    }

    .control-btn-header {
      padding: 0;

      & > div {
        margin-right: 10px;
        margin-top: 10px;
      }

      .btn-item {
        border: 1px solid #3562db;
        color: #3562db;
        font-family: none;
      }

      .btn-active {
        color: #fff;
        background: #3562db;
      }
    }

    .content-main {
      display: flex;
      flex-wrap: wrap;

      ::v-deep .box-card {
        width: calc(50% - 16px);
        margin-left: 16px;
        margin-top: 15px;
        padding: 24px 10px 0 24px;

        .card-body {
          height: 250px!important;
        }
      }
    }
  }
}
</style>
