<template>
  <PageContainer v-if="routeInfo.isFalg == 0">
    <div slot="content" class="role-content" style="height: 100%">
      <div class="role-content-right">
        <div style="height: 100%">
          <div class="search-from">
            <el-input v-model.trim="filters.name" clearable placeholder="请输入科目名称" style="width: 200px"></el-input>
            <el-button type="primary" plain @click="resetData">重置</el-button>
            <el-button type="primary" class="sino-button-sure-search" style="margin-top: 10px"
              @click="init">查询</el-button>
          </div>
          <div class="middle_tools">
            <el-button type="primary" icon="el-icon-plus" @click="addSubject()">新增学科科目</el-button>
            <el-button type="primary" :disabled="multipleSelection.length < 1"
              @click="abatchDelete('optyPl')">批量删除</el-button>
          </div>
          <div slot="content" style="height: 100%;">
            <div class="contentTable">
              <div class="contentTable-main table-content">
                <el-table ref="multipleTable" v-loading="tableLoading" highlight-current-row :data="tableData"
                  row-key="id" border stripe height="100%"
                  :tree-props="{ children: 'childList', hasChildren: 'hasChildren' }"
                  @selection-change="handleSelectionChange" @row-dblclick="dblclick">
                  <el-table-column type="selection" width="55" align="center"></el-table-column>
                  <el-table-column prop="name" show-overflow-tooltip label="科目名称"></el-table-column>
                  <el-table-column prop="level" show-overflow-tooltip label="所属等级" width="200"></el-table-column>
                  <el-table-column label="操作" width="200" align="center">
                    <template slot-scope="scope">
                      <el-button type="text" @click="dblclick(scope.row)">查看</el-button>
                      <el-button type="text" @click="upDataSubject(scope.row)">编辑</el-button>
                      <el-button type="text" @click="delData(scope.row, 'deList')">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <!-- 分页 -->
              <div class="contentTable-footer">
                <el-pagination :current-page="paginationData.currentPage" :page-sizes="[15, 30, 50, 100]"
                  :page-size="paginationData.pageSize" layout="total, sizes, prev, pager, next, jumper"
                  :total="paginationData.total" @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"></el-pagination>
              </div>
            </div>
            <!-- 新增学科科目弹窗 -->
            <addSubject ref="showSubject" :showSubjectForm="showSubjectForm" :optType="optType" :edit-id="currentId"
              @closeAdd="closeAdd" @refreshTable="getTblleList"></addSubject>
          </div>
        </div>
      </div>
    </div>

  </PageContainer>
  <div v-else>
  <permissionPrompt></permissionPrompt>
  </div>
</template>

<script>
import permissionPrompt from "@/views/safety/courseIndex/components/permissionPrompt.vue";
import { Row } from "element-ui";
import axios from 'axios'
import qs from "qs";
import addSubject from './components/addSubject.vue'
export default {
  name: "securityOrganization",
  components: { addSubject,permissionPrompt },
  data() {
    return {
      showSubjectForm: false, // 新增学科科目弹窗
      filters: {
        name: "",
        dictLabel: "",
      },
      paginationData: {
        pageNo: 1,
        pageSize: 15,
        total: 0,
      },
      multipleSelection: [], // 列表勾选中的值
      tableData: [],
      tableLoading: false,
      checkedData: "",
      optType: '',  // 点击学科科目类型
      listTable: '',
      Ids: [], // 选择列表id集合
      currentId: '', // 列表id
      params: [], // 删除id集合
      paramsIds: [],
      routeInfo:{},
    };
  },
  created() {
     this.routeInfo = JSON.parse(sessionStorage.getItem('routeInfo'))
    if (this.routeInfo.isFalg == 1) {
      return
    }
    this.getTblleList();
  },
  mounted() {
    let self = this;
    document.onkeydown = function (event) {
      let _key = event.keyCode;
      if (_key == 13) {
        event.preventDefault();
        self.init();
      }
    };
  },
  methods: {
    // --------------------------------------------学科科目分类 -------------------------------------------------------------
    // 打开新增学科科目
    addSubject() {
      this.$refs.showSubject.clearForm()
      this.optType = 'add'
      this.showSubjectForm = true;
      this.$refs.showSubject.getList()
    },
    // 关闭新增学科科目
    closeAdd() {
      this.showSubjectForm = false;
      this.$refs.showSubject.clearForm()
    },
    // 打开编辑学科科目类型
    upDataSubject(row) {
      this.optType = 'edit'
      this.showSubjectForm = true;
      this.currentId = row.id.toString()
    },
    // 获取学科科目列表
    getTblleList() {
      this.tableLoading = true
      let data = {
        ...this.filters,
        pageNo: this.paginationData.pageNo,
        pageSize: this.paginationData.pageSize
      };
      this.$api.ipsmFicationlList(data).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.paginationData.total = res.data.total;
          this.tableData = res.data.records;

        } else {
          this.$message.error(res.message);
        }
      });
    },
    // ---------------------------------------------- 实验室类型 ---------------------------------------------------------------
    // 获取实验室类型
    getlaboratoryList() {
      this.tableLoading = true
      let data = {
        ...this.filters,
        pageNo: this.paginationData.pageNo,
        pageSize: this.paginationData.pageSize
      };
      // this.$api.ipsmlaboratorylList(data).then((res) => {
      //   this.tableLoading = false
      //   if (res.code == 200) {
      //     if (this.activeindex == 1) {
      //       this.paginationData.total = res.data.count;
      //       this.tableData = res.data.list;
      //     }
      //   } else {
      //     this.$message.error(res.message);
      //   }
      // });
    },
    // 批量删除
    abatchDelete(optyPl) {
      this.optyPl = optyPl
      this.paramsIds = []
      if (this.activeindex == 0 && this.optyPl == 'optyPl') {
        this.multipleSelection.map((item) => {
          this.Ids.push({
            id: item.id,
            name: item.name
          })
          this.paramsIds = this.Ids
        })
        this.$confirm('确定要删除当前科目吗，删除后将无法恢复，是否确定删除？', "提醒", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          cancelButtonClass: "btn-cancel",
          type: "warning"
        }).then(() => {
          axios.post(__PATH.BASE_URL_LABORATORY + 'subject/delete', this.paramsIds).then(res => {
            if (res.data.code == 200) {
              this.$message.success(res.data.msg)
              this.getTblleList()
            } else {
              this.$message.error(res.data.msg)
              this.getTblleList()
            }
          })
        })
      }
    },
    // 详情
    dblclick(row) {
      this.optType = 'subJectDetail';
      this.showSubjectForm = true;
      this.currentId = row.id.toString()
    },
    // 删除
    delData(row, deList) {
      console.log(row, 'RPEW');
      this.optType = deList
      if (this.optType == 'deList') {
        this.params.push({
          id: row.id,
          name: row.name
        })
        this.$confirm('确定要删除当前科目吗，删除后将无法恢复，是否确定删除？', "提醒", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          cancelButtonClass: "btn-cancel",
          type: "warning"
        }).then(() => {
          axios.post(__PATH.BASE_URL_LABORATORY + 'subject/delete', this.params).then(res => {
            if (res.data.code == 200) {
              this.$message.success(res.data.msg)
              this.getTblleList()
            } else {
              this.$message.error(res.data.msg)
              this.getTblleList()
              this.params = []
            }
          })
        })
      }
    },
    // 重置查询表单
    resetData() {
      this.filters.name = "";
      this.filters.dictLabel = "";
      this.paginationData = {
        pageNo: 1,
        pageSize: 15,
        total: 0,
      };
      this.getTblleList()
    },
    // 查询
    init() {
      this.getTblleList()
    },
    //  ----------------------------------------------------分页相关------------------------------------------------------------
    handleSizeChange(val) {
      this.paginationData.pageNo = 1;
      this.paginationData.pageSize = val;
      this.getTblleList()
    },
    handleCurrentChange(val) {
      this.paginationData.pageNo = val;
      this.getTblleList()
    },
    //  ------------------------------------------------操作表格中数据------------------------------------------------------------
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
  },
};
</script>
<style lang="scss" scoped>
// el-tree-node is-expanded is-focusable
::v-deep .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}

.span-ellipsis {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

::v-deep .el-tree-node__content {
  height: 32px;
}

.left_contentName {
  font-size: 16px;
  height: 40px;
  line-height: 40px;
  font-weight: 500;
  padding: 0px 10px;
  cursor: pointer;

  &.active {
    color: #3562db;
    background: linear-gradient(to right, #d9e1f8, #fff);
    border-radius: 4px 4px 4px 4px;
  }
}

// .left_contentName:active {
//   color: #3562db;
//   background: linear-gradient(to right, #d9e1f8, #fff);
// }
.role-content {
  height: 100%;
  display: flex;

  .role-content-left {
    width: 246px;
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    margin-right: 12px;

    .left_content {
      height: calc(100% - 60px);
      padding: 10px;

      // overflow: auto;
      .tree {
        margin-top: 10px;
        height: calc(100% - 40px);
        overflow: auto;
      }
    }
  }

  // /deep/ .el-message-box__btns>.el-button--default:nth-child(2) {
  //   color: #fff;
  //   border-color: #3562db;
  //   background: #3562db;
  //   font-weight: 400;
  //   font-size: 14px;
  //   padding: 8px 22px;
  //   font-family: PingFangSC-Regular
  // }

  .role-content-right {
    flex: 1;
    height: 100%;
    min-width: 0;
    padding: 10px;
    background: #fff;
    border-radius: 4px;

    .search-from {
      padding-bottom: 12px;

      &>div {
        margin-right: 10px;
      }

      &>button {
        margin-top: 12px;
      }
    }

    .middle_tools {
      margin-bottom: 10px;
    }

    .contentTable {
      height: calc(100% - 100px);
      display: flex;
      flex-direction: column;

      .contentTable-main {
        flex: 1;
        // overflow: auto;
      }

      .contentTable-footer {
        padding: 5px 0 0 0;
      }
    }

  }
}
</style>
