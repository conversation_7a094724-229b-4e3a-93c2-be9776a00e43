<template>
  <el-dialog custom-class="model-dialog" title="导出" :visible.sync="dialogVisibleExport" :before-close="closeDialog">
    <div v-loading="dialogTableLoading" style="min-height: 200px; background-color: #fff; padding: 10px; width: 100%;">
      <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">全选</el-checkbox>
      <el-checkbox-group v-model="checkedArr" style="margin-left: 20px;" @change="handleNameArrChange">
        <el-checkbox v-for="item in titles" :key="item.EnglishName" :label="item" style="margin-top: 10px; width: 20%;">{{ item.ChineseName }}</el-checkbox>
      </el-checkbox-group>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="getExportData(1)">导出全部记录</el-button>
      <el-button type="primary" :disabled="materRows.length < 1" @click="getExportData(0)">导出选中记录</el-button>
    </span>
  </el-dialog>
</template>
<script>
// import $ from 'jquery'
// import {
//   maintainExcel,
//   RecordExportExcel,
//   ProgressExportExcel
// } from "@assets/common/index.js";
// const mapping = [maintainExcel, RecordExportExcel, ProgressExportExcel ];
// 保养           //巡检记录           //巡检进度           //保养
export default {
  // 本组件包含1 预防工单 2 巡检记录 3 巡检进度   4 数据统计    5 奖惩制度 的导出功能
  name: 'recordExport',
  props: {
    dialogVisibleExport: {
      type: Boolean,
      default: false
    },
    exportType: {
      type: Number,
      default: null
    },
    startTime: {
      type: String,
      default: ''
    },
    endTime: {
      type: String,
      default: ''
    },
    compound: {
      type: String,
      default: ''
    },
    where: {
      type: String,
      default: ''
    },
    // eslint-disable-next-line vue/require-prop-type-constructor
    materRows: '',
    assetsTypeId: {
      type: String,
      default: ' '
    },
    companyCode: {
      type: String,
      default: ''
    },
    orderType: {
      type: String,
      default: '1'
    },
    formInline: {
      type: Object,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: '1'
    }
  },
  data() {
    return {
      dialogTableLoading: false,
      checkAll: false,
      isIndeterminate: true,
      titles: [],
      checkedArr: [],
      typeTitles: []
    }
  },
  watch: {
    materRows(n, o) {}
  },
  mounted() {
    this.getExportField()
  },
  methods: {
    // 获取转科、报废，资产字段接口   11111
    getExportField() {
      let params = {
        // exportType: this.exportType, // 1 预防工单 2 巡检记录 3 巡检进度   4 数据统计    5 奖惩制度  6意见反馈
      }
      let url = ' '
      switch (this.exportType) {
        case 1:
          url = 'preventionGetExportFields'
          break
        case 2:
          url = 'getRecordExportFields'
          break
        case 3:
          url = 'getProgressExportFields'
          break
        case 4:
          url = 'getSystemTeamStatisticsExportFields'
          break
        case 5:
          url = 'ipsmGetPenaltiesManagerExportFields'
          break
        case 6:
          url = 'getAdviseExportFields'
          break
        case 7: // 安全管控组织
          url = 'ipsmGetControlGroupInfoExportFields'
          break
        case 8: // 安全人员管理
          url = 'ipsmGetControlTeamUseExportFields'
          break
      }
      this.$api[url](params).then((res) => {
        for (var i in res.data.list) {
          this.titles.push({
            EnglishName: res.data.list[i],
            ChineseName: res.data.list[i].split('_')[1]
          })
        }
      })
    },
    // 导出
    getExportData(type) {
      // type   全部 选中
      if (this.dialogTableLoading) {
        return
      }
      if (this.checkedArr.length < 1) {
        this.$message.error('请至少选择一个导出字段')
        return
      }
      let field = []
      field = this.checkedArr.map((i) => {
        return i.EnglishName
      })
      let idArr = []
      idArr = this.materRows.map((o) => {
        if (this.where) {
          return o.planId
        } else {
          return o.maintainId ? o.maintainId : o.id ? o.id : o.teamId
        }
      })

      var params = {
        isAll: type,
        fields: field.join(','),
        ids: type == 0 ? idArr.join(',') : ' ',
        startPatrolTime: this.startTime,
        endPatrolTime: this.endTime,
        assetsTypeId: this.assetsTypeId || ' ',
        companyCode: this.companyCode,
        orderType: this.orderType,
        riskTypeId: 1,
        roleCode: 'BJSJTYY-YYGLY0-20221117173504',
        userTeamId: '1581497000007970816',
        controlGroupIds: '1581497000007970816'
      }
      this.dialogTableLoading = true
      let url = ' '
      switch (this.exportType) {
        case 1: // 预防工单
          url = 'PreventionExportExcel'
          params = { ...this.formInline, ...params }
          params.riskTypeId = this.formInline.assetsTypeId
          params.recordCode = this.formInline.maintainCode
          break
        case 2: // 巡检记录
          url = 'RecordExportExcel'
          params.compound = this.compound
          break
        case 3: // 巡检进度
          url = 'ProgressExportExcel'

          params.startTime = this.startTime
          params.endTime = this.endTime
          break
        case 4: // 数据统计
          url = 'systemTeamStatisticsExportExcel'
          params = { ...params, ...this.formInline }
          break
        case 5: // 奖惩制度
          url = 'ipsmPenaltiesManagerExportExcel'
          params = { ...params, ...this.formInline }
          break
        case 6: // 意见反馈
          params = { ...params, ...this.formInline }
          url = 'adviseExportExcel'
          break
        case 7: // 安全管理组织
          url = 'ipsmControlGroupInfoExportExcel'
          params = { ...params, ...this.formInline }
          break
        case 8: // 安全人员管理
          url = 'ipsmControlTeamUserExportExcel'
          params = { ...params, ...this.formInline }
          break
      }
      this.$api[url](params).then((res) => {
        if (res.status == 200 && !res.data.code) {
          let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          console.log(name)
          let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
          let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          // 释放这个临时的对象url
          window.URL.revokeObjectURL(url)
        } else {
          this.$message.error('导出失败')
        }
        this.closeDialog()
        this.dialogTableLoading = false
      })
    },
    closeDialog() {
      this.checkedArr = []
      this.$emit('closeDialog')
    },
    handleNameArrChange(val) {
      this.checkedArr = val
    },
    handleCheckAllChange(val) {
      this.checkedArr = val ? this.titles : []
      this.isIndeterminate = false
    }
  }
  // filters: {
  //   checkListFilter: function(value) {
  //     if (!value) return ' ';
  //     return value.split("-")[1];
  //   }
  // }
}
</script>
<style lang="scss" scoped>
.metera-dialog .el-dialog__body {
  height: 330px;
  overflow-y: auto;
}
</style>
