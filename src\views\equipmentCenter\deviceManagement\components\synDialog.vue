<template>
  <el-dialog v-dialogDrag title="选择" :visible.sync="synDialog" width="70%" :before-close="handleClose" custom-class="model-dialog" :close-on-click-modal="false">
    <div style="display: flex" class="outermost">
      <div class="left">
        <div class="toptip">
          <span class="green_line"></span>
          资产管理
        </div>
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="资产信息" name="zcxx">
            <div v-loading class="left_content">
              <el-tree
                ref="syTree"
                style="margin-top: 10px"
                :check-strictly="true"
                :data="treeData"
                :props="defaultProps"
                node-key="sysCode"
                :highlight-current="true"
                @node-click="handleNodeClick"
              >
              </el-tree>
            </div>
          </el-tab-pane>
          <el-tab-pane label="IOT设备" name="iot">
            <div v-loading class="left_content">
              <el-tree
                ref="iotTree"
                style="margin-top: 10px"
                :check-strictly="true"
                :data="treeData"
                :props="iotProps"
                :node-key="'id'"
                :highlight-current="true"
                @node-click="handleIotNodeClick"
              >
              </el-tree>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="right">
        <div style="display: flex">
          <el-input v-model="assetsName" :placeholder="activeName == 'zcxx' ? '设备名称' : '传感器名称'" style="width: 200px; margin-right: 10px"></el-input>
          <!-- <el-input placeholder="归属系统" style="width: 200px; margin-right: 10px"></el-input> -->
          <el-button type="primary" plain @click="reset">重置</el-button>
          <el-button type="primary" @click="inquiry">查询</el-button>
        </div>
        <!-- 选择 -->
        <div v-if="activeName == 'zcxx'" style="margin: 16px 0px; max-height: 42px; overflow: auto">
          <span>已选择</span>
          <span v-for="(item, index) in assetsNameList" :key="index" class="assetsName">{{ item }}</span>
        </div>
        <div v-if="activeName == 'iot'" style="margin: 16px 0px; max-height: 42px; overflow: auto">
          <span>已选择</span>
          <span v-for="(item, index) in sensorNameList" :key="index" class="assetsName">{{ item }}</span>
        </div>
        <div v-if="activeName == 'zcxx'">
          <el-table
            ref="table"
            v-loading="loading"
            :data="tableData"
            :height="tableHeight"
            :row-key="getRowKeys"
            :header-cell-style="{ 'text-align': 'center' }"
            tooltip-effect="dark"
            style="width: 100%"
            border
            stripe
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" align="center" :reserve-selection="true"> </el-table-column>
            <el-table-column type="index" label="序号" prop="" width="50" align="center">
              <template slot-scope="scope">
                <span>{{ (currentPage - 1) * pageSize + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="assetsName" label="设备名称" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="assetsCode" label="设备编码" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="manufacturerBrand" label="品牌" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="deviceModel" label="型号" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="spaceLocationName" label="所在区域" align="center" width="200" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="systemCode" label="数据来源" align="center" show-overflow-tooltip>
              <template slot-scope="scope">
                <span>{{ scope.row.systemCode == 'insp' ? '巡检系统' : '基础信息' }}</span>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            :current-page="currentPage"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            :page-size="pageSize"
            :page-sizes="[15, 30, 50]"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
        <div v-if="activeName == 'iot'">
          <el-table
            ref="iotTable"
            v-loading="loading"
            :data="iotTableData"
            :height="tableHeight"
            row-key="id"
            :header-cell-style="{ 'text-align': 'center' }"
            tooltip-effect="dark"
            style="width: 100%"
            border
            stripe
            @selection-change="handleIotSelectionChange"
          >
            <el-table-column type="selection" width="55" align="center" :reserve-selection="true"> </el-table-column>
            <el-table-column type="index" label="序号" prop="" width="50" align="center">
              <template slot-scope="scope">
                <span>{{ (iotCurrentPage - 1) * iotPageSize + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="assetName" label="传感器名称" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="assetCode" label="传感器ID" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="assetModel" label="传感器型号" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="assetBrand" label="传感器品牌" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="regionName" label="所在空间" align="center" width="200" show-overflow-tooltip> </el-table-column>
          </el-table>
          <el-pagination
            :current-page="iotCurrentPage"
            layout="total, sizes, prev, pager, next, jumper"
            :total="iotTotal"
            :page-size="iotPageSize"
            :page-sizes="[15, 30, 50]"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="handleClose">取消</el-button>
      <el-button type="primary" @click="qd">同步到本地</el-button>
    </span>
    <!-- 导入类型 -->
    <el-dialog
      v-dialogDrag
      title="资产导入"
      :visible.sync="importDialogVisible"
      :append-to-body="true"
      :modal-append-to-body="false"
      :before-close="handleAssetsImportClose"
      custom-class="model-dialog"
      :close-on-click-modal="false"
      width="40%"
      top="30vh"
    >
      <div class="content" style="padding: 10px; display: flex">
        <el-form ref="formInline" :model="formInline" :inline="true" class="form-inline" label-width="100px" :rules="rules">
          <el-form-item label="资产大类：" prop="assetCategoryCode">
            <el-select v-model.trim="formInline.assetCategoryCode" filterable placeholder="资产大类" @change="onAssetCategory">
              <el-option v-for="item in majorCategoriesList" :key="item.id" :label="item.dictName" :value="item.id"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="资产小类：" prop="assetSubcategoryCode">
            <el-cascader
              ref="assetSubcategoryCode"
              :key="assetIndex"
              v-model="formInline.assetSubcategoryCode"
              :props="assetPropsType"
              :options="subcategoryList"
              :collapse-tags="true"
              placeholder="资产小类"
              @change="handleAssetClick"
            ></el-cascader>
          </el-form-item>
          <el-form-item label="专业类别：" prop="professionalCategoryCode">
            <el-select v-model.trim="formInline.professionalCategoryCode" filterable placeholder="专业类别" @change="onMajorType">
              <el-option v-for="item in majorList" :key="item.id" :label="item.baseName" :value="item.id"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="系统类别：" prop="systemCategoryCode">
            <el-cascader
              ref="systemCategoryCode"
              :key="systemIndex"
              v-model="formInline.systemCategoryCode"
              :props="systemPropsType"
              :options="systemCodeList"
              :collapse-tags="true"
              placeholder="系统类别"
              @change="handleSystemClick"
            ></el-cascader>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="handleAssetsImportClose">取 消</el-button>
        <el-button type="primary" @click="submitAssetsImport">确 定</el-button>
      </span>
    </el-dialog>
  </el-dialog>
</template>
<script>
import { transData } from '@/util'
export default {
  props: {
    visible: {
      type: Boolean
    }
  },
  data() {
    return {
      treeData: [],
      activeName: 'zcxx',
      checkedData: '', // 树状图点击数据
      assetsName: '', // 资产名称
      loading: false,
      defaultProps: {
        children: 'children',
        label: 'sysName'
      },
      iotProps: {
        children: 'children',
        label: 'name'
      },
      importDialogVisible: false,
      pageSize: 15,
      currentPage: 1,
      total: 0,
      iotPageSize: 15,
      iotCurrentPage: 1,
      iotTotal: 0,
      tableData: [],
      iotTableData: [],
      multipleSelection: [],
      multipleIotSelection: [],
      assetsNameList: [], // 选择资产名称列表
      sensorNameList: [], // 选择资产名称列表
      formInline: {
        assetCategoryCode: '',
        assetSubcategoryCode: '',
        professionalCategoryCode: '',
        systemCategoryCode: []
      },
      assetPropsType: {
        children: 'children',
        label: 'dictName',
        value: 'id',
        checkStrictly: true,
        multiple: false
      },
      systemPropsType: {
        children: 'children',
        label: 'baseName',
        value: 'id',
        checkStrictly: true,
        multiple: false
      },
      majorCategoriesList: [], // 资产大类列表
      subcategoryList: [], // 资产小类列表
      majorList: [], // 专业类别列表
      systemCodeList: [], // 系统类别
      systemCategoryName: '',
      subcategoryAll: [], // 资产小类全部
      assetSubcategoryName: '', // 选择资产小类名称
      assetIndex: 0, // 资产小类
      systemIndex: 0, // 系统类别
      rules: {
        assetCategoryCode: [{ required: true, message: '请选择资产大类', trigger: 'change' }],
        assetSubcategoryCode: [{ required: true, message: '请选择资产小类', trigger: 'change' }],
        professionalCategoryCode: [{ required: true, message: '请选择专业类别', trigger: 'change' }]
      }
    }
  },
  computed: {
    synDialog: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    tableHeight() {
      return document.body.clientHeight - 525
    }
  },
  created() {
    this.getAssetsCountBySys()
  },
  mounted() {},
  methods: {
    getAssetsCountBySys() {
      this.$api.getAssetsCountBySys().then((res) => {
        if (res.code == '200') {
          this.treeData = res.data
          this.checkedData = this.treeData[0]
          this.getLersonnelList()
          this.$nextTick(() => {
            // id：绑定的 node-key
            this.$refs.syTree.setCurrentKey(this.checkedData.sysCode)
          })
        }
      })
    },
    // 树状图点击
    handleNodeClick(data) {
      this.currentPage = 1
      this.checkedData = data
      this.tableData = []
      this.$refs.syTree.setCurrentKey(this.checkedData.sysCode)
      this.getLersonnelList()
    },
    // iot树状图点击
    handleIotNodeClick(data) {
      this.iotCurrentPage = 1
      this.checkedData = data
      this.iotTableData = []
      this.$refs.iotTree.setCurrentKey(this.checkedData.id)
      this.getIotTableData()
    },
    getRowKeys(row) {
      return row.id
    },
    getLersonnelList() {
      let data = {
        assetsName: this.assetsName,
        current: this.currentPage,
        size: this.pageSize,
        systemCode: this.checkedData.sysCode
      }
      this.$api.getAssetsList(data).then((res) => {
        this.loading = true
        if (res.code == '200') {
          this.tableData = res.data.records
          this.total = res.data.total
        }
        this.loading = false
      })
    },
    getIotTableData() {
      let data = {
        assetsName: this.assetsName,
        currentPage: this.iotCurrentPage,
        pageSize: this.iotPageSize,
        id: this.checkedData.id
      }
      this.$api.getIotSensorList(data).then((res) => {
        this.loading = true
        if (res.code == '200') {
          this.iotTableData = res.data.list
          this.iotTotal = Number(res.data.count)
        }
        this.loading = false
      })
    },
    // 查询
    inquiry() {
      if (this.activeName == 'zcxx') {
        this.currentPage = 1
        this.getLersonnelList()
      } else {
        this.iotCurrentPage = 1
        this.getIotTableData()
      }
    },
    // 重置
    reset() {
      this.assetsName = ''
      this.inquiry()
    },
    handleClose(val) {
      this.$emit('update:visible', !this.visible)
      if (this.multipleSelection.lengrh && this.multipleSelection.length > 0) {
        this.$refs.table.clearSelection()
      }
      if (this.multipleIotSelection.lengrh && this.multipleIotSelection.length > 0) {
        this.$refs.iotTable.clearSelection()
      }
    },
    qd() {
      console.log('this.activeName', this.activeName)
      if (this.activeName === 'zcxx') {
        if (!this.multipleSelection.length) {
          return this.$message.error('请选择设备')
        }
        let assetDetailsList = this.multipleSelection
        assetDetailsList.forEach((item) => {
          item.nfcCode = item.nfc
        })
        this.$api.syncToLocalByAsset({ assetDetailsList: JSON.stringify(assetDetailsList), flag: '1' }).then((res) => {
          if (res.code == '200') {
            this.$message.success(res.message)
            this.$emit('synOK')
            this.$emit('update:visible', !this.visible)
          } else {
            this.$message.error(res.message)
          }
        })
        if (this.multipleSelection.lengrh && this.multipleSelection.length > 0) {
          this.$refs.table.clearSelection()
        }
      } else {
        if (!this.multipleIotSelection.length) {
          return this.$message.error('请选择传感器')
        }
        this.importDialogVisible = true
        // 资产大类接口
        this.$api.getDictMenuTree({ dictType: 'ZCFL', pid: '#' }).then((res) => {
          if (res.code == '200') {
            this.majorCategoriesList = res.data
          }
        })
        // 专业类别
        this.$api
          .getDeviceType({
            levelType: 1
          })
          .then((res) => {
            if (res.code == '200') {
              this.majorList = res.data
            }
          })
      }
    },
    // 监听事件
    handleSelectionChange(val) {
      this.multipleSelection = val
      this.assetsNameList = this.multipleSelection.map((item) => item.assetsName)
    },
    // iot 选择
    handleIotSelectionChange(val) {
      this.multipleIotSelection = val
      this.sensorNameList = this.multipleIotSelection.map((item) => item.assetName)
    },
    // 分页
    handleSizeChange(val) {
      if (this.activeName == 'zcxx') {
        this.pageSize = val
        this.getLersonnelList()
      } else {
        this.iotPageSize = val
        this.getIotTableData()
      }
    },
    handleCurrentChange(val) {
      if (this.activeName == 'zcxx') {
        this.currentPage = val
        this.getLersonnelList()
      } else {
        this.iotCurrentPage = val
        this.getIotTableData()
      }
    },
    // 获取iot资产字典
    getIotList() {
      this.$api.getDictionaryIotList().then((res) => {
        if (res.code == '200') {
          this.treeData = res.data
          this.checkedData = this.treeData[0]
          this.getIotTableData()
          this.$nextTick(() => {
            // id：绑定的 node-key
            this.$refs.iotTree.setCurrentKey(this.checkedData.id)
          })
        }
      })
    },
    // tab切换
    handleClick(tab, event) {
      this.treeData = []
      this.assetsNameList = [] // 选择资产名称列表
      this.sensorNameList = [] // 选择资产名称列表
      this.tableData = []
      if (tab.index == '0') {
        if (this.multipleSelection.lengrh && this.multipleSelection.length > 0) {
          this.$refs.table.clearSelection()
        }
        this.currentPage = 1
        this.getAssetsCountBySys()
      } else {
        if (this.multipleIotSelection.lengrh && this.multipleIotSelection.length > 0) {
          this.$refs.iotTable.clearSelection()
        }
        this.iotCurrentPage = 1
        this.getIotList()
      }
    },
    // 查询资产小类
    onAssetCategory(id) {
      this.subcategoryList = []
      let data = {
        current: 1,
        size: 1000000,
        dictTypeId: '27',
        pids: id
      }
      this.$api.selectByPage(data).then((res) => {
        if (res.code == '200') {
          this.subcategoryList = transData(res.data.records, 'id', 'pid', 'children')
          this.subcategoryAll = res.data.records
          ++this.assetIndex
        }
      })
    },
    // 选择资产小类
    handleAssetClick(type) {
      const names = []
      type.forEach((i) => {
        this.subcategoryAll.find((j) => {
          if (i == j.id) {
            names.push(j.dictName)
          }
        })
      })
      this.assetSubcategoryName = names.join(',')
    },
    // 选择下拉树 数据
    handleSystemClick(type) {
      const names = []
      type.forEach((i) => {
        this.systemAllList.find((j) => {
          if (i == j.id) {
            names.push(j.baseName)
          }
        })
      })
      this.systemCategoryName = names.join(',')
    },
    // 查询二级以下设备字典
    onMajorType(val) {
      this.systemCodeList = []
      let data = {
        startLevel: '2',
        parentId: val,
        levelType: '5'
      }
      this.$api.getDeviceType(data).then((res) => {
        if (res.code == '200') {
          this.systemCodeList = transData(res.data, 'id', 'parentId', 'children')
          this.systemAllList = res.data
          ++this.systemIndex
        }
      })
    },
    // 导入弹窗关闭
    handleAssetsImportClose() {
      this.importDialogVisible = false
    },
    // 获取资产大类名称
    getAssetsName(str, getArr) {
      let newObj = this[getArr].find((item) => str == item.id)
      return newObj?.dictName
    },
    // 获取专业类别名称
    getMajorName(str, getArr) {
      let newObj = this[getArr].find((item) => str == item.id)
      return newObj?.baseName
    },
    // 导入确定
    submitAssetsImport() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          let assetDetailsList = this.multipleIotSelection
          let data = {
            ...this.formInline,
            assetDetailsList: JSON.stringify(assetDetailsList),
            assetCategoryName: this.getAssetsName(this.formInline.assetCategoryCode, 'majorCategoriesList'), // 资产大类名称
            assetSubcategoryName: this.assetSubcategoryName, // 资产小类
            professionalCategoryName: this.getMajorName(this.formInline.professionalCategoryCode, 'majorList'), // 专业类别
            systemCategoryName: this.systemCategoryName, // 系统类别
            assetSubcategoryCode: this.formInline.assetSubcategoryCode ? this.formInline.assetSubcategoryCode.toString() : '',
            systemCategoryCode: this.formInline.systemCategoryCode ? this.formInline.systemCategoryCode.toString() : '',
            flag: '2'
          }
          this.$api.syncToLocalByAsset(data).then((res) => {
            if (res.code == '200') {
              this.importDialogVisible = false
              this.$message.success(res.message)
              setTimeout(() => {
                this.$emit('synOK')
                this.$emit('update:visible', !this.visible)
              }, 300)
            } else {
              this.importDialogVisible = false
              this.$message.error(res.message)
            }
            if (this.multipleIotSelection.lengrh && this.multipleIotSelection.length > 0) {
              this.$refs.iotTable.clearSelection()
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.outermost {
  width: 100%;
  max-height: 600px;
  border: 1px solid #eee;
}
.left {
  padding: 10px;
  width: 230px;
  margin-right: 10px;
  height: 100%;
  background-color: #fff;
  .left_content {
    width: 100%;
    height: 426px !important;
    overflow: scroll;
  }
}
.right {
  background-color: #fff;
  padding: 10px;
  width: calc(100% - 240px);
  height: 100%;
}
.el-pagination {
  // display: flex;
  // justify-content: flex-end;
  margin-top: 5px;
}
.model-dialog {
  padding: 10 !important;
}
::v-deep .el-select {
  width: 100px !important;
}
.el-select {
  width: 180px !important;
  margin-right: 10px;
}
::v-deep .model-dialog .el-dialog__body {
  overflow-y: hidden !important;
  max-height: calc(78vh - 130px) !important;
}
.assetsName {
  color: #3562db;
  margin-left: 10px;
}
</style>
