<template>
  <el-dialog title="查看附件" :visible="visible" :before-close="handleClosesubmit" width="60%">
    <el-table :data="tableData">
      <el-table-column label="序号" type="index" width="50"> </el-table-column>
      <el-table-column property="fileName" label="文件名"> </el-table-column>
      <el-table-column property="fileSize" label="大小（M）"> </el-table-column>
      <el-table-column property="option" label="操作" width="120">
        <template slot-scope="scope">
          <el-button type="text" @click="handleView(scope.row)">查看</el-button>
          <el-button type="text" @click="handleDownload(scope.row)"> 下载 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div slot="footer">
      <el-button @click="handleClosesubmit">关闭</el-button>
    </div>
  </el-dialog>
</template>
<script>
import mixins from '../mixins/index.js'
export default {
  mixins: [mixins],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: [String, Number],
      default: ''
    },
    isShare: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableData: []
    }
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.handleGetTableData()
        }
      },
      immediate: true
    }
  },
  methods: {
    handleGetTableData() {
      const func = this.isShare ? this.$api.fileManagement.shareListByArchiveId : this.$api.fileManagement.queryByArchiveId
      const parmas = this.isShare ? { archiveShareId: this.id } : { archiveId: this.id }
      func(parmas).then((res) => {
        this.tableData = res.data
      })
    },
    handleClosesubmit() {
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style></style>
