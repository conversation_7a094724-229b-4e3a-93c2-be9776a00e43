export const newRoutingList = [
    {
        path: '/UPS',
        component: 'Layout',
        name: 'UPS',
        redirect: '/UPS/operationalOverview',
        meta: {
            title: 'UPS运行监测',
            menuAuth: '/UPS'
        },
        children: []
    },
]
export const newRoutingListChildren = [
    {
        path: 'operationalOverview',
        name: 'operationalOverview',
        component: 'EmptyLayout',
        redirect: { name: 'operationalOverview' },
        meta: {
            title: '运行总览',
            menuAuth: '/runningMenu/operationalOverview',
            id: '1851826336280338432',
        },
        children: [
            {
                path: '',
                name: 'operationalOverview',
                component: 'operationalOverview',
                meta: {
                    title: '运行总览',
                    sidebar: false,
                    breadcrumb: false,
                    systemType: 'PDXT',
                    jumpAddress: '/distributionSystem/operationalOverview',
                }
            }
        ]
    },
    {
        path: 'operationalMonitoring',
        name: 'operationalMonitoring',
        component: 'EmptyLayout',
        redirect: { name: 'operationalMonitoring' },
        meta: {
            title: '运行监测',
            menuAuth: '/runningMenu/operationalMonitoring',
            id: '1851826773649690624',
        },
        children: [
            {
                path: '',
                name: 'operationalMonitoring',
                component: 'operationalMonitoring',
                meta: {
                    title: '运行监测',
                    sidebar: false,
                    breadcrumb: false,
                    systemType: 'PDXT',
                    jumpAddress: '/distributionSystem/operationalMonitoring',
                }
            }, {
                path: 'deviceDetails',
                name: 'deviceDetails',
                component: 'deviceDetails',
                meta: {
                    title: '监测详情',
                    sidebar: false,
                    breadcrumb: false,
                }
            }, {
                path: 'clusterSetting',
                name: 'clusterSetting',
                component: 'clusterSetting',
                meta: {
                    title: '设备配置',
                    sidebar: false,
                    breadcrumb: false,
                }
            }, {
                path: 'addNewForm',
                name: 'addNewForm',
                component: 'addNewForm',
                meta: {
                    title: '新建设备',
                    sidebar: false,
                    breadcrumb: false,
                }
            },
        ]
    },
    {
        path: 'airScenarioStrategy',
        name: 'airScenarioStrategy',
        component: 'EmptyLayout',
        redirect: { name: 'airScenarioStrategy' },
        meta: {
            title: '场景策略',
            menuAuth: '/runningMenu/airScenarioStrategy',
            id: '1851827038608084992',
        },
        children: [
            {
                path: '',
                name: 'airScenarioStrategy',
                component: 'airScenarioStrategy',
                meta: {
                    title: '场景策略',
                    sidebar: false,
                    breadcrumb: false,
                    systemType: 'ACAT',
                }
            },
            {
                path: 'airScenarioStrategyDetails',
                name: 'airScenarioStrategyDetails',
                component: 'airScenarioStrategyDetails',
                meta: {
                    title: '场景策略详情',
                    sidebar: false,
                    breadcrumb: false,
                    systemType: 'ACAT',
                    jumpAddress: '/airConditioningTerminal/airScenarioStrategy',
                }
            },
        ]
    },
    {
        path: 'airRunCalendar',
        name: 'airRunCalendar',
        component: 'EmptyLayout',
        redirect: { name: 'airRunCalendar' },
        meta: {
            title: '运行日历',
            menuAuth: '/runningMenu/airRunCalendar',
            id: '1851827173354291200',
        },
        children: [
            {
                path: '',
                name: 'airRunCalendar',
                component: 'airRunCalendar',
                meta: {
                    title: '运行日历',
                    sidebar: false,
                    breadcrumb: false,
                    systemType: 'ACAT',
                    jumpAddress: '/airConditioningTerminal/airRunCalendar',
                }
            }, {
                path: 'calendarDetails',
                name: 'calendarDetails',
                component: 'calendarDetails',
                meta: {
                    title: '运行日历详情',
                    sidebar: false,
                    breadcrumb: false,
                }
            },
            {
                path: 'calendarDetailsEquipment',
                name: 'calendarDetailsEquipment',
                component: 'calendarDetailsEquipment',
                meta: {
                    title: '运行日历设备详情',
                    sidebar: false,
                    breadcrumb: false,
                }
            },
        ]
    },
    {
        path: 'historicalDataSystem',
        name: 'historicalDataSystem',
        component: 'EmptyLayout',
        redirect: { name: 'historicalDataSystem' },
        meta: {
            title: '历史数据',
            menuAuth: '/runningMenu/historicalDataSystem',
            id: '1851826900196036608',
        },
        children: [
            {
                path: '',
                name: 'historicalDataSystem',
                component: 'historicalDataSystem',
                meta: {
                    title: '历史数据',
                    sidebar: false,
                    breadcrumb: false,
                    systemType: 'PDXT',
                    jumpAddress: '/distributionSystem/historicalDataSystem',
                }
            }
        ]
    },
]