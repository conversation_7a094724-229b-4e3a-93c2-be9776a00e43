<template>
  <div class="dialog-container">
    <el-dialog title="办理入住" :visible.sync="dialogVisible" top="10vh" width="60%" :close-on-click-modal="false" :before-close="handleCloseDialog">
      <div class="dialog-content">
        <el-form ref="checkInForm" :model="checkInRuleForm" :rules="checkInRules" label-width="100px" class="checkIn-ruleForm">
          <div class="info-box">
            <div class="a-title">申请信息</div>
            <div class="itemFormBox">
              <div class="item">
                <el-form-item label="申请人" prop="tenantId">
                  <el-input v-model="checkInRuleForm.tenantName" readonly placeholder="请选择申请人" @click.native="handleTenantClick"></el-input>
                </el-form-item>
              </div>
              <div class="item">
                <el-form-item label="婚姻状况" prop="marriage">
                  <el-radio-group v-model="checkInRuleForm.marriage" @change="handleMaritalStatusChange">
                    <el-radio label="0">未婚</el-radio>
                    <el-radio label="1">已婚</el-radio>
                  </el-radio-group>
                </el-form-item>
              </div>
            </div>
            <div class="itemFormBox copyFileBox">
              <div class="item">
                <el-form-item label="身份证复印件" label-width="140px">
                  <el-upload
                    action=""
                    data-type="copyOfIdCard"
                    list-type="picture-card"
                    accept=".rar, .zip, .doc, .docx, .pdf, .jpg, .jpeg, .png"
                    :file-list="checkInRuleForm.copyOfIdCard"
                    :http-request="handleHttpRequest.bind(this, 'copyOfIdCard')"
                    :on-remove="handleRemove.bind(this, 'copyOfIdCard')"
                    :on-change="handleFileChange.bind(this, 'copyOfIdCard')"
                    :beforeUpload="beforeAvatarUpload_20"
                    :on-error="handleUploadError"
                  >
                    <i class="el-icon-plus"></i>
                    <div slot="tip" class="el-upload__tip">支持扩展名：.rar .zip .doc .docx .pdf .jpg .jpeg .png，且不超过20M</div>
                    <div slot="file" slot-scope="{ file }" style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center">
                      <img v-if="formatFileIcon(file)" :src="formatFileIcon(file)" alt="" style="width: 100%; height: 100%" />
                      <img v-else :src="file.url" alt="" style="width: 100%; height: 100%" />
                      <span class="el-upload-list__item-actions">
                        <span class="el-upload-list__item-delete" @click="handleRemove('copyOfIdCard', file)">
                          <i class="el-icon-delete"></i>
                        </span>
                      </span>
                    </div>
                  </el-upload>
                </el-form-item>
              </div>
              <div class="item">
                <el-form-item label="学历学位复印件" label-width="140px">
                  <el-upload
                    action=""
                    list-type="picture-card"
                    data-type="copyOfDiploma"
                    accept=".rar, .zip, .doc, .docx, .pdf, .jpg, .jpeg, .png"
                    :file-list="checkInRuleForm.copyOfDiploma"
                    :http-request="handleHttpRequest.bind(null, 'copyOfDiploma')"
                    :on-remove="handleRemove.bind(null, 'copyOfDiploma')"
                    :on-change="handleFileChange.bind(this, 'copyOfDiploma')"
                    :beforeUpload="beforeAvatarUpload_20"
                    :on-error="handleUploadError"
                  >
                    <i class="el-icon-plus"></i>
                    <div slot="tip" class="el-upload__tip">支持扩展名：.rar .zip .doc .docx .pdf .jpg .jpeg .png，且不超过20M</div>
                    <div slot="file" slot-scope="{ file }" style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center">
                      <img v-if="formatFileIcon(file)" :src="formatFileIcon(file)" alt="" style="width: 100%; height: 100%" />
                      <img v-else :src="file.url" alt="" style="width: 100%; height: 100%" />
                      <span class="el-upload-list__item-actions">
                        <span class="el-upload-list__item-delete" @click="handleRemove('copyOfDiploma', file)">
                          <i class="el-icon-delete"></i>
                        </span>
                      </span>
                    </div>
                  </el-upload>
                </el-form-item>
              </div>
              <div class="item">
                <el-form-item label="职称复印件" label-width="140px">
                  <el-upload
                    action=""
                    list-type="picture-card"
                    data-type="copyOfProfessionalTitle"
                    accept=".rar, .zip, .doc, .docx, .pdf, .jpg, .jpeg, .png"
                    :file-list="checkInRuleForm.copyOfProfessionalTitle"
                    :http-request="handleHttpRequest.bind(null, 'copyOfProfessionalTitle')"
                    :on-remove="handleRemove.bind(null, 'copyOfProfessionalTitle')"
                    :on-change="handleFileChange.bind(this, 'copyOfProfessionalTitle')"
                    :beforeUpload="beforeAvatarUpload_20"
                    :on-error="handleUploadError"
                  >
                    <i class="el-icon-plus"></i>
                    <div slot="tip" class="el-upload__tip">支持扩展名：.rar .zip .doc .docx .pdf .jpg .jpeg .png，且不超过20M</div>
                    <div slot="file" slot-scope="{ file }" style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center">
                      <img v-if="formatFileIcon(file)" :src="formatFileIcon(file)" alt="" style="width: 100%; height: 100%" />
                      <img v-else :src="file.url" alt="" style="width: 100%; height: 100%" />
                      <span class="el-upload-list__item-actions">
                        <span class="el-upload-list__item-delete" @click="handleRemove('copyOfProfessionalTitle', file)">
                          <i class="el-icon-delete"></i>
                        </span>
                      </span>
                    </div>
                  </el-upload>
                </el-form-item>
              </div>
              <div v-if="checkInRuleForm.marriage === '0'" class="item">
                <el-form-item label="深圳居住证复印件" label-width="140px">
                  <el-upload
                    action=""
                    list-type="picture-card"
                    data-type="copyOfResidencePermit"
                    accept=".rar, .zip, .doc, .docx, .pdf, .jpg, .jpeg, .png"
                    :file-list="checkInRuleForm.copyOfResidencePermit"
                    :http-request="handleHttpRequest.bind(null, 'copyOfResidencePermit')"
                    :on-remove="handleRemove.bind(null, 'copyOfResidencePermit')"
                    :on-change="handleFileChange.bind(this, 'copyOfResidencePermit')"
                    :beforeUpload="beforeAvatarUpload_20"
                    :on-error="handleUploadError"
                  >
                    <i class="el-icon-plus"></i>
                    <div slot="tip" class="el-upload__tip">支持扩展名：.rar .zip .doc .docx .pdf .jpg .jpeg .png，且不超过20M</div>
                    <div slot="file" slot-scope="{ file }" style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center">
                      <img v-if="formatFileIcon(file)" :src="formatFileIcon(file)" alt="" style="width: 100%; height: 100%" />
                      <img v-else :src="file.url" alt="" style="width: 100%; height: 100%" />
                      <span class="el-upload-list__item-actions">
                        <span class="el-upload-list__item-delete" @click="handleRemove('copyOfResidencePermit', file)">
                          <i class="el-icon-delete"></i>
                        </span>
                      </span>
                    </div>
                  </el-upload>
                </el-form-item>
              </div>
              <div v-if="checkInRuleForm.marriage === '0'" class="item">
                <el-form-item label="父母身份证复印件" label-width="140px">
                  <el-upload
                    action=""
                    list-type="picture-card"
                    data-type="copyOfParentsIdCard"
                    accept=".rar, .zip, .doc, .docx, .pdf, .jpg, .jpeg, .png"
                    :file-list="checkInRuleForm.copyOfParentsIdCard"
                    :http-request="handleHttpRequest.bind(null, 'copyOfParentsIdCard')"
                    :on-remove="handleRemove.bind(null, 'copyOfParentsIdCard')"
                    :on-change="handleFileChange.bind(this, 'copyOfParentsIdCard')"
                    :beforeUpload="beforeAvatarUpload_20"
                    :on-error="handleUploadError"
                  >
                    <i class="el-icon-plus"></i>
                    <div slot="tip" class="el-upload__tip">支持扩展名：.rar .zip .doc .docx .pdf .jpg .jpeg .png，且不超过20M</div>
                    <div slot="file" slot-scope="{ file }" style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center">
                      <img v-if="formatFileIcon(file)" :src="formatFileIcon(file)" alt="" style="width: 100%; height: 100%" />
                      <img v-else :src="file.url" alt="" style="width: 100%; height: 100%" />
                      <span class="el-upload-list__item-actions">
                        <span class="el-upload-list__item-delete" @click="handleRemove('copyOfParentsIdCard', file)">
                          <i class="el-icon-delete"></i>
                        </span>
                      </span>
                    </div>
                  </el-upload>
                </el-form-item>
              </div>
              <div class="item">
                <el-form-item label="深圳无房证明" label-width="140px">
                  <el-upload
                    action=""
                    list-type="picture-card"
                    data-type="noHouse"
                    accept=".rar, .zip, .doc, .docx, .pdf, .jpg, .jpeg, .png"
                    :file-list="checkInRuleForm.noHouse"
                    :http-request="handleHttpRequest.bind(null, 'noHouse')"
                    :on-remove="handleRemove.bind(null, 'noHouse')"
                    :on-change="handleFileChange.bind(this, 'noHouse')"
                    :beforeUpload="beforeAvatarUpload_20"
                    :on-error="handleUploadError"
                  >
                    <i class="el-icon-plus"></i>
                    <div slot="tip" class="el-upload__tip">支持扩展名：.rar .zip .doc .docx .pdf .jpg .jpeg .png，且不超过20M</div>
                    <div slot="file" slot-scope="{ file }" style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center">
                      <img v-if="formatFileIcon(file)" :src="formatFileIcon(file)" alt="" style="width: 100%; height: 100%" />
                      <img v-else :src="file.url" alt="" style="width: 100%; height: 100%" />
                      <span class="el-upload-list__item-actions">
                        <span class="el-upload-list__item-delete" @click="handleRemove('noHouse', file)">
                          <i class="el-icon-delete"></i>
                        </span>
                      </span>
                    </div>
                  </el-upload>
                </el-form-item>
              </div>
              <div v-if="checkInRuleForm.marriage === '1'" class="item">
                <el-form-item label="结婚证复印件" label-width="140px">
                  <el-upload
                    action=""
                    list-type="picture-card"
                    data-type="copyOfMarriageCertificate"
                    accept=".rar, .zip, .doc, .docx, .pdf, .jpg, .jpeg, .png"
                    :file-list="checkInRuleForm.copyOfMarriageCertificate"
                    :http-request="handleHttpRequest.bind(null, 'copyOfMarriageCertificate')"
                    :on-remove="handleRemove.bind(null, 'copyOfMarriageCertificate')"
                    :on-change="handleFileChange.bind(this, 'copyOfMarriageCertificate')"
                    :beforeUpload="beforeAvatarUpload_20"
                    :on-error="handleUploadError"
                  >
                    <i class="el-icon-plus"></i>
                    <div slot="tip" class="el-upload__tip">支持扩展名：.rar .zip .doc .docx .pdf .jpg .jpeg .png，且不超过20M</div>
                    <div slot="file" slot-scope="{ file }" style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center">
                      <img v-if="formatFileIcon(file)" :src="formatFileIcon(file)" alt="" style="width: 100%; height: 100%" />
                      <img v-else :src="file.url" alt="" style="width: 100%; height: 100%" />
                      <span class="el-upload-list__item-actions">
                        <span class="el-upload-list__item-delete" @click="handleRemove('copyOfMarriageCertificate', file)">
                          <i class="el-icon-delete"></i>
                        </span>
                      </span>
                    </div>
                  </el-upload>
                </el-form-item>
              </div>
              <div v-if="checkInRuleForm.marriage === '1'" class="item">
                <el-form-item label="配偶身份证复印件" label-width="140px">
                  <el-upload
                    action=""
                    list-type="picture-card"
                    data-type="copyOfSpouseIdCard"
                    accept=".rar, .zip, .doc, .docx, .pdf, .jpg, .jpeg, .png"
                    :file-list="checkInRuleForm.copyOfSpouseIdCard"
                    :http-request="handleHttpRequest.bind(null, 'copyOfSpouseIdCard')"
                    :on-remove="handleRemove.bind(null, 'copyOfSpouseIdCard')"
                    :on-change="handleFileChange.bind(this, 'copyOfSpouseIdCard')"
                    :beforeUpload="beforeAvatarUpload_20"
                    :on-error="handleUploadError"
                  >
                    <i class="el-icon-plus"></i>
                    <div slot="tip" class="el-upload__tip">支持扩展名：.rar .zip .doc .docx .pdf .jpg .jpeg .png，且不超过20M</div>
                    <div slot="file" slot-scope="{ file }" style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center">
                      <img v-if="formatFileIcon(file)" :src="formatFileIcon(file)" alt="" style="width: 100%; height: 100%" />
                      <img v-else :src="file.url" alt="" style="width: 100%; height: 100%" />
                      <span class="el-upload-list__item-actions">
                        <span class="el-upload-list__item-delete" @click="handleRemove('copyOfSpouseIdCard', file)">
                          <i class="el-icon-delete"></i>
                        </span>
                      </span>
                    </div>
                  </el-upload>
                </el-form-item>
              </div>
              <div class="item">
                <el-form-item label="同住人资料" label-width="140px">
                  <el-upload
                    action=""
                    list-type="picture-card"
                    data-type="cohabitant"
                    accept=".rar, .zip, .doc, .docx, .pdf, .jpg, .jpeg, .png"
                    :file-list="checkInRuleForm.cohabitant"
                    :http-request="handleHttpRequest.bind(null, 'cohabitant')"
                    :on-remove="handleRemove.bind(null, 'cohabitant')"
                    :on-change="handleFileChange.bind(this, 'cohabitant')"
                    :beforeUpload="beforeAvatarUpload_20"
                    :on-error="handleUploadError"
                  >
                    <i class="el-icon-plus"></i>
                    <div slot="tip" class="el-upload__tip">支持扩展名：.rar .zip .doc .docx .pdf .jpg .jpeg .png，且不超过20M</div>
                    <div slot="file" slot-scope="{ file }" style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center">
                      <img v-if="formatFileIcon(file)" :src="formatFileIcon(file)" alt="" style="width: 100%; height: 100%" />
                      <img v-else :src="file.url" alt="" style="width: 100%; height: 100%" />
                      <span class="el-upload-list__item-actions">
                        <span class="el-upload-list__item-delete" @click="handleRemove('cohabitant', file)">
                          <i class="el-icon-delete"></i>
                        </span>
                      </span>
                    </div>
                  </el-upload>
                </el-form-item>
              </div>
              <div class="item">
                <el-form-item label="其他资料" label-width="140px">
                  <el-upload
                    action=""
                    list-type="picture-card"
                    data-type="otherInfo"
                    accept=".rar, .zip, .doc, .docx, .pdf, .jpg, .jpeg, .png"
                    :file-list="checkInRuleForm.otherInfo"
                    :http-request="handleHttpRequest.bind(null, 'otherInfo')"
                    :on-remove="handleRemove.bind(null, 'otherInfo')"
                    :on-change="handleFileChange.bind(this, 'otherInfo')"
                    :beforeUpload="beforeAvatarUpload_20"
                    :on-error="handleUploadError"
                  >
                    <i class="el-icon-plus"></i>
                    <div slot="tip" class="el-upload__tip">支持扩展名：.rar .zip .doc .docx .pdf .jpg .jpeg .png，且不超过20M</div>
                    <div slot="file" slot-scope="{ file }" style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center">
                      <img v-if="formatFileIcon(file)" :src="formatFileIcon(file)" alt="" style="width: 100%; height: 100%" />
                      <img v-else :src="file.url" alt="" style="width: 100%; height: 100%" />
                      <span class="el-upload-list__item-actions">
                        <span class="el-upload-list__item-delete" @click="handleRemove('otherInfo', file)">
                          <i class="el-icon-delete"></i>
                        </span>
                      </span>
                    </div>
                  </el-upload>
                </el-form-item>
              </div>
            </div>
            <div class="itemFormBox">
              <div class="item">
                <el-form-item label="附件文件" prop="name" label-width="140px">
                  <el-upload
                    action=""
                    data-type="tenantAttachmentUrl"
                    accept=".rar, .zip, .doc, .docx, .pdf, .jpg, .jpeg, .png"
                    :on-remove="handleRemove.bind(null, 'tenantAttachmentUrl')"
                    :on-change="handleFileChange.bind(this, 'tenantAttachmentUrl')"
                    :http-request="handleHttpRequest.bind(null, 'tenantAttachmentUrl')"
                    :beforeUpload="beforeAvatarUpload_10"
                    :file-list="checkInRuleForm.tenantAttachmentUrl"
                    :on-error="handleUploadError"
                  >
                    <el-button size="small" type="primary" icon="el-icon-upload2">上传文件</el-button>
                    <div slot="tip" class="el-upload__tip">支持扩展名：.rar .zip .doc .docx .pdf .jpg .jpeg .png，且不超过10M</div>
                  </el-upload>
                </el-form-item>
              </div>
            </div>
          </div>
          <div class="info-box">
            <div class="a-title">合同信息</div>
            <div class="itemFormBox">
              <div class="item">
                <el-form-item label="甲方:">
                  <span>中国医学科学院肿瘤医院深圳医院</span>
                </el-form-item>
              </div>
              <div class="item">
                <el-form-item label="乙方:">
                  <span>{{ checkInRuleForm.tenantName ? checkInRuleForm.tenantName : '--' }}</span>
                </el-form-item>
              </div>
            </div>
            <div class="itemFormBox">
              <div class="item">
                <el-form-item label="空间位置:">
                  <el-tooltip class="item" effect="dark" :content="roomInfo.spaceNames" placement="top-start">
                    <span class="roomName-span" v-if="roomInfo.spaceNames.length < 11">{{ roomInfo.spaceNames || '' }}</span>
                    <span class="roomName-span" v-else>{{ roomInfo.spaceNames ? `${roomInfo.spaceNames.substring(0, 11)}...` : '' }}</span>
                  </el-tooltip>
                </el-form-item>
              </div>
              <div class="item">
                <el-form-item label="房间号:" class="roomFormItem">
                  <el-tooltip class="item" effect="dark" :content="roomInfo.houseName" placement="top-start">
                    <span class="roomName-span" v-if="roomInfo.houseName.length < 11">{{ roomInfo.houseName || '' }}</span>
                    <span class="roomName-span" v-else>{{ roomInfo.houseName ? `${roomInfo.houseName.substring(0, 12)}...` : '' }}</span>
                  </el-tooltip>
                </el-form-item>
              </div>
            </div>
            <div class="itemFormBox">
              <div class="item">
                <el-form-item label="出租单价">
                  <span class="span">{{ roomInfo.hirePrice || '' }}</span
                  ><span>元/㎡月</span>
                </el-form-item>
              </div>
              <div class="item">
                <el-form-item label="出租租金">
                  <span class="span">{{ roomInfo.hireRent || '' }}</span
                  ><span>元</span>
                </el-form-item>
              </div>
            </div>
            <div class="itemFormBox">
              <div class="item">
                <el-form-item label="合同编号" prop="contractNum">
                  <el-input v-model="checkInRuleForm.contractNum" maxlength="50"></el-input>
                </el-form-item>
              </div>
              <div class="item item-rows">
                <el-form-item label="租期开始" prop="rentingStartTime">
                  <el-date-picker v-model="checkInRuleForm.rentingStartTime" :picker-options="pickerOptionsStart" type="date" value-format="yyyy-MM-dd" placeholder="选择日期">
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="租期结束" prop="rentingEndTime">
                  <el-date-picker v-model="checkInRuleForm.rentingEndTime" :picker-options="pickerOptionsEnd" type="date" value-format="yyyy-MM-dd" placeholder="选择日期">
                  </el-date-picker>
                </el-form-item>
              </div>
            </div>
            <div class="itemFormBox">
              <div class="item">
                <el-form-item label="附件文件" required>
                  <el-upload
                    action=""
                    data-type="contractAttachmentUrl"
                    accept=".rar, .zip, .doc, .docx, .pdf, .jpg, .jpeg, .png"
                    :on-remove="handleRemove.bind(null, 'contractAttachmentUrl')"
                    :http-request="handleHttpRequest.bind(null, 'contractAttachmentUrl')"
                    :on-change="handleFileChange.bind(this, 'contractAttachmentUrl')"
                    :beforeUpload="beforeAvatarUpload_10"
                    :file-list="checkInRuleForm.contractAttachmentUrl"
                    :on-error="handleUploadError"
                  >
                    <el-button size="small" type="primary" icon="el-icon-upload2">上传文件</el-button>
                    <div slot="tip" class="el-upload__tip">支持扩展名：.rar .zip .doc .docx .pdf .jpg .jpeg .png，且不超过10M</div>
                  </el-upload>
                </el-form-item>
              </div>
            </div>
          </div>
          <div class="info-box">
            <div class="a-title">入住回执单</div>
            <div class="itemFormBox">
              <div class="item">
                <el-form-item label="回执单" label-width="140px">
                  <el-upload
                    action=""
                    :limit="1"
                    data-type="receiptUrl"
                    accept=".rar, .zip, .doc, .docx, .pdf, .jpg, .jpeg, .png"
                    :on-remove="handleRemove.bind(null, 'receiptUrl')"
                    :on-change="handleFileChange.bind(this, 'receiptUrl')"
                    :http-request="handleHttpRequest.bind(null, 'receiptUrl')"
                    :beforeUpload="beforeAvatarUpload_20"
                    :file-list="checkInRuleForm.receiptUrl"
                    :on-exceed="handleExceed"
                    :on-error="handleUploadError"
                  >
                    <el-button size="small" type="primary" icon="el-icon-upload2">上传文件</el-button>
                    <div slot="tip" class="el-upload__tip">支持扩展名：.rar .zip .doc .docx .pdf .jpg .jpeg .png，且不超过20M</div>
                  </el-upload>
                </el-form-item>
              </div>
            </div>
            <div class="itemFormBox">
              <div class="item">
                <el-form-item label="入住登记确认单" label-width="140px">
                  <el-upload
                    action=""
                    :limit="1"
                    data-type="checkInUrl"
                    accept=".rar, .zip, .doc, .docx, .pdf, .jpg, .jpeg, .png"
                    :on-remove="handleRemove.bind(null, 'checkInUrl')"
                    :on-change="handleFileChange.bind(this, 'checkInUrl')"
                    :http-request="handleHttpRequest.bind(null, 'checkInUrl')"
                    :beforeUpload="beforeAvatarUpload_20"
                    :file-list="checkInRuleForm.checkInUrl"
                    :on-exceed="handleExceed"
                    :on-error="handleUploadError"
                  >
                    <el-button size="small" type="primary" icon="el-icon-upload2">上传文件</el-button>
                    <div slot="tip" class="el-upload__tip">支持扩展名：.rar .zip .doc .docx .pdf .jpg .jpeg .png，且不超过20M</div>
                  </el-upload>
                </el-form-item>
              </div>
            </div>
            <div class="itemFormBox">
              <div class="item">
                <el-form-item label="温馨提示确认单" label-width="140px">
                  <el-upload
                    action=""
                    :limit="1"
                    data-type="warmTipsUrl"
                    accept=".rar, .zip, .doc, .docx, .pdf, .jpg, .jpeg, .png"
                    :on-remove="handleRemove.bind(null, 'warmTipsUrl')"
                    :on-change="handleFileChange.bind(this, 'warmTipsUrl')"
                    :http-request="handleHttpRequest.bind(null, 'warmTipsUrl')"
                    :beforeUpload="beforeAvatarUpload_20"
                    :file-list="checkInRuleForm.warmTipsUrl"
                    :on-exceed="handleExceed"
                    :on-error="handleUploadError"
                  >
                    <el-button size="small" type="primary" icon="el-icon-upload2">上传文件</el-button>
                    <div slot="tip" class="el-upload__tip">支持扩展名：.rar .zip .doc .docx .pdf .jpg .jpeg .png，且不超过20M</div>
                  </el-upload>
                </el-form-item>
              </div>
            </div>
            <div class="itemFormBox">
              <div class="item">
                <el-form-item label="入住证明确认单" label-width="140px">
                  <el-upload
                    action=""
                    :limit="1"
                    data-type="checkInProveUrl"
                    accept=".rar, .zip, .doc, .docx, .pdf, .jpg, .jpeg, .png"
                    :on-remove="handleRemove.bind(null, 'checkInProveUrl')"
                    :on-change="handleFileChange.bind(this, 'checkInProveUrl')"
                    :http-request="handleHttpRequest.bind(null, 'checkInProveUrl')"
                    :beforeUpload="beforeAvatarUpload_20"
                    :file-list="checkInRuleForm.checkInProveUrl"
                    :on-exceed="handleExceed"
                    :on-error="handleUploadError"
                  >
                    <el-button size="small" type="primary" icon="el-icon-upload2">上传文件</el-button>
                    <div slot="tip" class="el-upload__tip">支持扩展名：.rar .zip .doc .docx .pdf .jpg .jpeg .png，且不超过20M</div>
                  </el-upload>
                </el-form-item>
              </div>
            </div>
            <div class="itemFormBox">
              <div class="item">
                <el-form-item label="其他证明材料" label-width="140px">
                  <el-upload
                    action=""
                    :limit="1"
                    data-type="otherProofsUrl"
                    accept=".rar, .zip, .doc, .docx, .pdf, .jpg, .jpeg, .png"
                    :on-remove="handleRemove.bind(null, 'otherProofsUrl')"
                    :on-change="handleFileChange.bind(this, 'otherProofsUrl')"
                    :http-request="handleHttpRequest.bind(null, 'otherProofsUrl')"
                    :beforeUpload="beforeAvatarUpload_20"
                    :file-list="checkInRuleForm.otherProofsUrl"
                    :on-exceed="handleExceed"
                    :on-error="handleUploadError"
                  >
                    <el-button size="small" type="primary" icon="el-icon-upload2">上传文件</el-button>
                    <div slot="tip" class="el-upload__tip">支持扩展名：.rar .zip .doc .docx .pdf .jpg .jpeg .png，且不超过20M</div>
                  </el-upload>
                </el-form-item>
              </div>
            </div>
          </div>
          <div class="info-box">
            <div class="a-title">入住确认</div>
            <div class="itemFormBox">
              <div class="item">
                <el-form-item label="入住开始">
                  <el-date-picker v-model="checkInRuleForm.liveDate" type="datetime" style="width: 100%" value-format="yyyy-MM-dd HH:mm:dd" placeholder="选择日期">
                  </el-date-picker>
                </el-form-item>
              </div>
              <div class="item">
                <el-form-item label="房屋照片">
                  <el-upload
                    action=""
                    list-type="picture-card"
                    :limit="9"
                    data-type="housePicUrl"
                    accept=".jpeg, .jpg, .png"
                    :file-list="checkInRuleForm.housePicUrl"
                    :http-request="handleHttpRequest.bind(null, 'housePicUrl')"
                    :on-remove="handleRemove.bind(null, 'housePicUrl')"
                    :on-change="handleFileChange.bind(this, 'housePicUrl')"
                    :beforeUpload="beforeAvatarUpload_20"
                    :on-exceed="handleExceed_9"
                    :on-error="handleUploadError"
                  >
                    <i class="el-icon-plus"></i>
                    <div slot="tip" class="el-upload__tip">支持扩展名：.jpg .jpeg .png，且不超过20M</div>
                  </el-upload>
                </el-form-item>
              </div>
            </div>
            <div class="itemFormBox">
              <div class="item">
                <el-form-item label="抄表类型">
                  <el-input v-model="checkInRuleForm.meterReadingType" disabled></el-input>
                </el-form-item>
              </div>
            </div>
            <div class="itemFormBox">
              <div class="item item-rows">
                <el-form-item label="电表数">
                  <el-input v-model="checkInRuleForm.electricityNum" placeholder="请输入" maxlength="50" @keyup.native="proving($event, 'electricityNum')"></el-input>
                </el-form-item>
                <el-form-item label="电表余额(元)">
                  <el-input
                    v-model="checkInRuleForm.electricitySurplusPrice"
                    placeholder="请输入"
                    maxlength="50"
                    @keyup.native="proving($event, 'electricitySurplusPrice')"
                  ></el-input>
                </el-form-item>
              </div>
              <div class="item">
                <el-form-item label="电表照片" style="width: 490px">
                  <el-upload
                    action=""
                    list-type="picture-card"
                    data-type="electricityPicUrl"
                    :limit="1"
                    accept=".jpeg, .jpg, .png"
                    :file-list="checkInRuleForm.electricityPicUrl"
                    :http-request="handleHttpRequest.bind(null, 'electricityPicUrl')"
                    :on-remove="handleRemove.bind(null, 'electricityPicUrl')"
                    :on-change="handleFileChange.bind(this, 'electricityPicUrl')"
                    :beforeUpload="beforeAvatarUpload_20"
                    :on-exceed="handleExceed"
                    :on-error="handleUploadError"
                  >
                    <i class="el-icon-plus"></i>
                    <div slot="tip" class="el-upload__tip">支持扩展名：.jpg .jpeg .png，且不超过20M</div>
                  </el-upload>
                </el-form-item>
              </div>
            </div>
            <div class="itemFormBox">
              <div class="item item-rows">
                <el-form-item label="水表数">
                  <el-input v-model="checkInRuleForm.waterNum" placeholder="请输入" maxlength="50" @keyup.native="proving($event, 'waterNum')"></el-input>
                </el-form-item>
                <el-form-item label="水表余额(元)">
                  <el-input v-model="checkInRuleForm.waterSurplusPrice" placeholder="请输入" maxlength="50" @keyup.native="proving($event, 'waterSurplusPrice')"></el-input>
                </el-form-item>
              </div>
              <div class="item">
                <el-form-item label="水表照片" style="width: 490px">
                  <el-upload
                    action=""
                    list-type="picture-card"
                    data-type="waterPicUrl"
                    :limit="1"
                    accept=".jpeg, .jpg, .png"
                    :file-list="checkInRuleForm.waterPicUrl"
                    :http-request="handleHttpRequest.bind(null, 'waterPicUrl')"
                    :on-remove="handleRemove.bind(null, 'waterPicUrl')"
                    :on-change="handleFileChange.bind(this, 'waterPicUrl')"
                    :beforeUpload="beforeAvatarUpload_20"
                    :on-exceed="handleExceed"
                    :on-error="handleUploadError"
                  >
                    <i class="el-icon-plus"></i>
                    <div slot="tip" class="el-upload__tip">支持扩展名：.jpg .jpeg .png，且不超过20M</div>
                  </el-upload>
                </el-form-item>
              </div>
            </div>
            <div class="itemFormBox">
              <div class="item item-rows">
                <el-form-item label="燃气表数">
                  <el-input v-model="checkInRuleForm.gasNum" placeholder="请输入" maxlength="50" @keyup.native="proving($event, 'gasNum')"></el-input>
                </el-form-item>
                <el-form-item label="燃气余额(元)">
                  <el-input v-model="checkInRuleForm.gasSurplusPrice" placeholder="请输入" maxlength="50" @keyup.native="proving($event, 'gasSurplusPrice')"></el-input>
                </el-form-item>
              </div>
              <div class="item">
                <el-form-item label="燃气表照片" style="width: 490px">
                  <el-upload
                    action=""
                    list-type="picture-card"
                    data-type="gasPicUrl"
                    :limit="1"
                    accept=".jpeg, .jpg, .png"
                    :file-list="checkInRuleForm.gasPicUrl"
                    :http-request="handleHttpRequest.bind(null, 'gasPicUrl')"
                    :on-remove="handleRemove.bind(null, 'gasPicUrl')"
                    :on-change="handleFileChange.bind(this, 'gasPicUrl')"
                    :beforeUpload="beforeAvatarUpload_20"
                    :on-exceed="handleExceed"
                    :on-error="handleUploadError"
                  >
                    <i class="el-icon-plus"></i>
                    <div slot="tip" class="el-upload__tip">支持扩展名：.jpg .jpeg .png，且不超过20M</div>
                  </el-upload>
                </el-form-item>
              </div>
            </div>
          </div>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="handleCloseDialog">取消</el-button>
        <el-button :loading="saveBtnLoding" :disabled="saveBtnLoding" type="primary" @click="saveDialog">确认</el-button>
      </span>
    </el-dialog>
    <selectApplicantDialog v-if="isSelectPers" selectMode="2" :selectDialogVisible="isSelectPers" @updateVisible="handSelectVisible" @advancedSearchFn="selectPersChange" />
  </div>
</template>
<script>
import pdfImgUrl from '@/assets/images/pdf.png'
import docImgUrl from '@/assets/images/doc_icon.png'
import rarImgUrl from '@/assets/images/rar.png'
import zipImgUrl from '@/assets/images/zip.png'
export default {
  components: {
    selectApplicantDialog: () => import('./selectApplicantDialog.vue')
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    roomInfo: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data() {
    return {
      pdfImgUrl,
      docImgUrl,
      rarImgUrl,
      zipImgUrl,
      isOverflow: false,
      saveBtnLoding: false,
      isSelectPers: false,
      pickerOptionsStart: {
        disabledDate: (time) => {
          if (this.checkInRuleForm.rentingEndTime) return time.getTime() > new Date(this.checkInRuleForm.rentingEndTime).getTime()
          else return
        }
      },
      pickerOptionsEnd: {
        disabledDate: (time) => {
          if (this.checkInRuleForm.rentingStartTime) return time.getTime() < new Date(this.checkInRuleForm.rentingStartTime).getTime()
          else return
        }
      },
      checkInRules: {
        tenantId: [{ required: true, message: '请选择人员', trigger: 'change' }],
        contractNum: [{ required: true, message: '请输入合同编号', trigger: 'blur' }],
        rentingStartTime: [{ required: true, message: '请选择租期开始时间', trigger: 'change' }],
        rentingEndTime: [{ required: true, message: '请选择租期结束时间', trigger: 'change' }]
      },
      checkInRuleForm: {
        tenantName: '', // 申请人名称
        tenantId: '', // 申请人ID
        marriage: '0', // 婚姻状况
        tenantNumber: '', // 租户工号
        tenantDeptId: '', // 租户科室ID
        tenantDeptName: '', // 租户科室名称
        tenantIdNumber: '', // 租户身份证号
        tenantPhone: '', // 租户本人手机号
        tenantSex: '', // 租户性别
        houseId: '', // 房间ID
        firstPartyName: '', // 甲方名称
        firstPartyCode: '', // 甲方code
        rentingStartTime: '', // 租期开始时间
        rentingEndTime: '', // 租期结束时间
        liveDate: '', // 入住时间
        meterReadingType: '入住抄表',
        electricityNum: '', // 电表度数
        electricitySurplusPrice: '', // 电表余额
        waterNum: '', // 水表度数
        waterSurplusPrice: '', // 水表余额
        gasNum: '', // 燃气表度数
        gasSurplusPrice: '', // 燃气余额
        contractAttachmentUrl: [], // 合同附件
        tenantAttachmentUrl: [], // 租户附件
        receiptUrl: [], // 回执单地址
        housePicUrl: [], // 房屋照片
        electricityPicUrl: [], // 电费照片
        gasPicUrl: [], // 燃气照片
        waterPicUrl: [], // 水表照片
        copyOfIdCard: [],
        copyOfDiploma: [],
        copyOfProfessionalTitle: [],
        copyOfResidencePermit: [],
        copyOfParentsIdCard: [],
        noHouse: [],
        cohabitant: [],
        copyOfMarriageCertificate: [],
        copyOfSpouseIdCard: [],
        otherInfo: [],
        checkInUrl: [],
        warmTipsUrl: [],
        checkInProveUrl: [],
        otherProofsUrl: []
      },
      personList: [], // 申请人数据
      isCheckIn: false,
      tenantInfo: {}
    }
  },
  mounted() {
    // this.getLersonnelList()
  },
  methods: {
    // 格式化文件icon
    formatFileIcon(file) {
      if (!file) return false
      let arr = file.name.split('.')
      if (['pdf', 'doc', 'docx', 'rar', 'zip'].includes(arr[arr.length - 1])) {
        switch (arr[arr.length - 1]) {
          case 'pdf':
            return this.pdfImgUrl
          case 'doc':
          case 'docx':
            return this.docImgUrl
          case 'rar':
            return this.rarImgUrl
          case 'zip':
            return this.zipImgUrl
          default:
            return ''
        }
      }
    },
    // 婚姻状况单选框事件
    handleMaritalStatusChange() {},
    handSelectVisible() {
      this.isSelectPers = false
    },
    handleTenantClick() {
      this.isSelectPers = true
    },
    // 选择人员事件
    selectPersChange(data) {
      this.handlePersonIdChange(data[0])
      this.handSelectVisible()
    },
    /** 弹窗提交 */
    saveDialog() {
      const userInfo = this.$store.state.user.userInfo.user
      let { electricityNum, electricitySurplusPrice, waterNum, waterSurplusPrice, gasNum, gasSurplusPrice } = this.checkInRuleForm
      let params = {
        ...this.checkInRuleForm,
        userId: userInfo.id,
        userName: userInfo.staffName,
        userIdCard: userInfo.umId || '',
        userDepartment: userInfo.deptName || '',
        userGender: userInfo.sex || '',
        userPost: userInfo.job || '',
        userEntryTime: userInfo.workEntryTime || '',
        userPhone: userInfo.phone || '',
        tenantInfo: this.tenantInfo,
        waterPowerRecordEntity: {
          type: '0',
          electricityPicUrl: this.checkInRuleForm.electricityPicUrl.length ? this.formatFileArr(this.checkInRuleForm.electricityPicUrl) : '', // 电费照片
          gasPicUrl: this.checkInRuleForm.gasPicUrl ? this.formatFileArr(this.checkInRuleForm.gasPicUrl) : '', // 燃气照片
          waterPicUrl: this.checkInRuleForm.waterPicUrl ? this.formatFileArr(this.checkInRuleForm.waterPicUrl) : '', // 水表照片
          electricityNum, // 电表度数
          electricitySurplusPrice, // 电表余额
          waterNum, // 水表度数
          waterSurplusPrice, // 水表余额
          gasNum, // 燃气表度数
          gasSurplusPrice // 燃气余额
        }
      }
      delete params.electricityPicUrl
      delete params.waterPicUrl
      delete params.gasPicUrl
      delete params.electricityNum
      delete params.electricitySurplusPrice
      delete params.waterNum
      delete params.waterSurplusPrice
      delete params.gasNum
      delete params.gasSurplusPrice
      params.contractAttachmentUrl = params.contractAttachmentUrl.length ? this.formatFileArr(params.contractAttachmentUrl) : ''
      params.tenantAttachmentUrl = params.tenantAttachmentUrl.length ? this.formatFileArr(params.tenantAttachmentUrl) : ''
      params.receiptUrl = params.receiptUrl.length ? this.formatFileArr(params.receiptUrl) : ''
      params.housePicUrl = params.housePicUrl.length ? this.formatFileArr(params.housePicUrl) : ''
      params.copyOfIdCard = params.copyOfIdCard.length ? this.formatFileArr(params.copyOfIdCard) : ''
      params.copyOfDiploma = params.copyOfDiploma.length ? this.formatFileArr(params.copyOfDiploma) : ''
      params.copyOfProfessionalTitle = params.copyOfProfessionalTitle.length ? this.formatFileArr(params.copyOfProfessionalTitle) : ''
      params.copyOfResidencePermit = params.copyOfResidencePermit.length ? this.formatFileArr(params.copyOfResidencePermit) : ''
      params.copyOfParentsIdCard = params.copyOfParentsIdCard.length ? this.formatFileArr(params.copyOfParentsIdCard) : ''
      params.noHouse = params.noHouse.length ? this.formatFileArr(params.noHouse) : ''
      params.cohabitant = params.cohabitant.length ? this.formatFileArr(params.cohabitant) : ''
      params.copyOfMarriageCertificate = params.copyOfMarriageCertificate.length ? this.formatFileArr(params.copyOfMarriageCertificate) : ''
      params.copyOfSpouseIdCard = params.copyOfSpouseIdCard.length ? this.formatFileArr(params.copyOfSpouseIdCard) : ''
      params.otherInfo = params.otherInfo.length ? this.formatFileArr(params.otherInfo) : ''
      params.checkInUrl = params.checkInUrl.length ? this.formatFileArr(params.checkInUrl) : ''
      params.warmTipsUrl = params.warmTipsUrl.length ? this.formatFileArr(params.warmTipsUrl) : ''
      params.checkInProveUrl = params.checkInProveUrl.length ? this.formatFileArr(params.checkInProveUrl) : ''
      params.otherProofsUrl = params.otherProofsUrl.length ? this.formatFileArr(params.otherProofsUrl) : ''
      params.firstPartyName = '中国医学科学院肿瘤医院深圳医院'
      params.firstPartyCode = userInfo.hospitalCode
      params.houseId = this.roomInfo.id
      if (this.isCheckIn) {
        const customMessage = '员工存在在租房屋，无法办理入住!'
        this.$message.error(customMessage)
        return false
      }
      if (!params.contractAttachmentUrl) {
        this.$message.error('请上传合同附件！')
        return false
      }
      this.$refs.checkInForm.validate((valid) => {
        if (valid) {
          this.saveBtnLoding = true
          this.$api.rentalHousingApi.saveMoveInto(params).then((res) => {
            this.saveBtnLoding = false
            if (res.code == 200) {
              this.$message.success(res.message)
              this.$emit('saveCheckInDialog')
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },
    //  格式化附件上传
    formatFileArr(arr) {
      if (arr && arr.length) {
        let arrJson = arr.map((item) => {
          return {
            url: item.uploadPath,
            name: item.file ? item.file.name : item.name
          }
        })
        return JSON.stringify(arrJson)
      } else {
        return ''
      }
    },
    // 获取人员列表
    getLersonnelList() {
      let params = {
        current: 1,
        size: 9999,
        sex: '',
        pmId: '',
        postId: '', // 岗位
        stationStatus: '',
        officeId: ''
      }
      this.$api.staffList(params).then((res) => {
        if (res.code == 200) {
          this.personList = res.data.records
        }
      })
    },
    /** 关闭弹窗 */
    handleCloseDialog() {
      this.saveBtnLoding = false
      this.$emit('closeCheckInDialog')
    },
    /** 申请人事件处理 */
    handlePersonIdChange(val) {
      if (val) {
        this.checkTenantInfo(val)
      } else {
        this.checkInRuleForm.tenantName = ''
        this.checkInRuleForm.tenantNumber = ''
        this.checkInRuleForm.tenantDeptId = ''
        this.checkInRuleForm.tenantDeptName = ''
        this.checkInRuleForm.tenantIdNumber = ''
        this.checkInRuleForm.tenantPhone = ''
        this.checkInRuleForm.tenantSex = ''
        this.tenantInfo = {}
      }
    },
    /** 校验租户是否入住或待入住 */
    checkTenantInfo(val) {
      let params = {
        tenantId: val.id
      }
      this.$api.rentalHousingApi.checkTenantMoveInto(params).then((res) => {
        if (res.code == 200) {
          if (!res.data) {
            // let person = this.personList.find((ele) => ele.id == val)
            this.checkInRuleForm.tenantName = val.staffName
            this.checkInRuleForm.tenantId = val.id
            this.checkInRuleForm.tenantNumber = val.staffNumber
            this.checkInRuleForm.tenantDeptId = val.officeId
            this.checkInRuleForm.tenantDeptName = val.officeName
            this.checkInRuleForm.tenantIdNumber = ''
            this.checkInRuleForm.tenantPhone = val.mobile
            this.checkInRuleForm.tenantSex = val.sex == 1 ? '0' : val.sex == 2 ? '1' : ''
            this.tenantInfo = val || {}
            this.isCheckIn = false
          } else {
            const customMessage = '员工存在在租房屋，无法办理入住!'
            this.$message.error(customMessage)
            this.isCheckIn = true
          }
        } else {
          this.$message.error(res.message)
        }
      })
    },
    /** 文件上传 */
    async handleHttpRequest(type, file) {
      await this.checkFile(file.file, this.checkInRuleForm[type])
      const params = new FormData()
      params.append('file', file.file)
      let res = await this.$api.uploadFile(params)
      if (res.code === '200') {
        this.$message({
          message: '上传成功',
          type: 'success'
        })
        const fileInfo = this.checkInRuleForm[type].find((it) => it.uid === file.file.uid)
        if (fileInfo) {
          fileInfo.uploadPath = res.data.fileKey
        }
      } else {
        this.$message.error(res.message)
      }
    },
    // 检测文件是否可以上传
    async checkFile(file, fileList) {
      if (/\s/.test(file.name)) {
        throw '文件名不能存在空格'
      }
      if (fileList.filter((x) => x.name === file.name).length > 1) {
        throw '已存在此文件，请重新选择'
      }
    },
    // 上传失败提示
    handleUploadError(err) {
      let errMsg = '上传失败'
      if (typeof err === 'string') {
        errMsg = err
      }
      this.$message.error(errMsg)
    },
    // 失踪同步文件列表
    handleFileChange(type, file) {
      if (file.status === 'fail') {
        // 失败的文件，500ms后删除，视觉效果会好一些
        window.clearTimeout(this.$timerId)
        this.$timerId = window.setTimeout(() => {
          let index = -1
          do {
            index = this.checkInRuleForm[type].findIndex((it) => it.status === 'fail')
            if (index > -1) {
              this.checkInRuleForm[type].splice(index, 1)
            }
          } while (index > -1)
          this.$timerId = -1
        }, 500)
      } else {
        const index = this.checkInRuleForm[type].findIndex((it) => it.uid === file.uid)
        if (index > -1) {
          this.checkInRuleForm[type].splice(index, 1, file)
        } else {
          this.checkInRuleForm[type].push(file)
        }
      }
    },
    /** 附件文件 移除 */
    handleRemove(type, file, fileList) {
      if (this.checkInRuleForm[type] && this.checkInRuleForm[type].length) {
        const ids = this.checkInRuleForm[type].findIndex((item) => item.uid === file.uid)
        this.checkInRuleForm[type].splice(ids, 1)
      }
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },
    handleExceed_9(files, fileList) {
      this.$message.warning(`当前限制选择 9 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },
    beforeAvatarUpload_10(file) {
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过 10MB!')
        return false
      }
      if (file.name.indexOf(',') != -1 || file.name.indexOf(' ') != -1) {
        this.$message.error('非法的文件名')
        return false
      }
    },
    beforeAvatarUpload_20(file) {
      const isLt20M = file.size / 1024 / 1024 < 20
      if (!isLt20M) {
        this.$message.error('上传文件大小不能超过 20MB!')
        return false
      }
      if (file.name.indexOf(',') != -1 || file.name.indexOf(' ') != -1) {
        this.$message.error('非法的文件名')
        return false
      }
    },
    /** 输入框输入限制 */
    proving(e, val) {
      e.target.value = e.target.value.toString().match(/^\d+(?:\.\d{0,2})?/) // 只能输入2位小数
      if (e.target.value.indexOf('.') < 0 && e.target.value != '') {
        // 输入替换，如输入05，直接替换为5，防止出现01，02这种情况
        e.target.value = parseFloat(e.target.value)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  height: 550px;
  box-sizing: border-box;
  overflow-y: auto;
}
.itemFormBox {
  display: flex;
  justify-content: space-between;
  color: #333;
  .item {
    width: 450px;
    display: flex;
    .el-form-item {
      width: 100%;
    }
    .roomFormItem {
      width: 300px;
    }
  }
  .item:nth-child(2) {
    width: 530px;
  }
  .item-rows {
    .el-form-item {
      width: calc(100% / 2);
      .el-date-editor {
        width: 150px;
      }
    }
  }
  .span {
    display: inline-block;
    width: 100px;
    text-align: center;
  }
}
.copyFileBox {
  flex-wrap: wrap;
  .item:nth-child(2) {
    width: 450px;
  }
}
.roomName-span {
  display: inline-block;
  max-width: 150px !important;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.a-title {
  font-size: 16px;
  font-weight: 600;
  color: #000;
  padding-left: 15px;
  margin-bottom: 15px;
  position: relative;
}
.a-title::after {
  content: '';
  width: 8px;
  height: 16px;
  border-radius: 0 8px 8px 0;
  background: #4387f7;
  position: absolute;
  left: 0px;
  top: 50%;
  transform: translateY(-50%);
}
.el-upload__tip {
  margin: 5px 0px 0px 0px;
  line-height: 1.5;
}
</style>
