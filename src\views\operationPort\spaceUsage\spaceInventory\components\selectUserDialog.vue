<template>
  <!-- 选择发布范围 -->
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="责任人选择"
    width="70%"
    :visible.sync="visible"
    custom-class="selectScopeDialog model-dialog"
    :before-close="closeDialog"
  >
    <div class="dialog-content">
      <div class="main-pre">
        <div class="pre-left">
          <dept-tree :selectKeys="detailed.deptId" @deptClick="deptClick" />
        </div>
        <div class="pre-right">
          <div class="search-from">
            <el-input v-model="searchFrom.staffName" placeholder="姓名" clearable style="width: 150px;"></el-input>
            <el-input v-model="searchFrom.mobile" placeholder="手机号" clearable style="width: 150px;"></el-input>
            <el-select v-model="searchFrom.postId"  placeholder="请选择消息状态" clearable style="width: 200px;">
              <el-option label="全部岗位" value=""></el-option>
              <el-option v-for="item in postList" :key="item.id" :label="item.postName" :value="item.id"></el-option>
            </el-select>
            <div style="display: inline-block;">
              <el-button type="primary" plain @click="resetForm">重置</el-button>
              <el-button type="primary" @click="searchForm">查询</el-button>
            </div>
          </div>
          <div class="table-content">
            <TablePage
              ref="table"
              v-loading="tableLoading"
              :showPage="true"
              row-key="id"
              :tableColumn="tableColumn"
              :data="tableData"
              height="calc(100% - 60px)"
              :pageData="pageData"
              @pagination="paginationChange"
              @selection-change="handleSelectionChange"
            />
          </div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <!-- <el-button type="primary" @click="confirm">确 定</el-button> -->
    </span>
  </el-dialog>
</template>

<script lang="jsx">
import deptTree from './deptTree.vue'
export default {
  name: 'selectUserDialog',
  components: {deptTree},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    detailed: { // 默认选中数据
      type: Object,
      default: () => {
        return {type: '1', userId: [], deptId: []}
      }
    }
  },
  data() {
    return {
      tableLoading: false,
      deptSelectIds: null, // 部门选择id
      preSelectIds: [], // 人员选择id
      searchFrom: {
        staffName: '', //  姓名
        mobile: '', // 手机号
        postId: '' // 岗位
      },
      postList: [], // 岗位列表

      tableData: [],
      multipleSelection: [],
      tableColumn: [
        // {
        //   type: 'selection',
        //   align: 'center',
        //   reserveSelection: true
        // },
        {
          width: 70,
          prop: '',
          label: '序号',
          reserveSelection: true,
          formatter: (scope) => {
            return (this.pageData.page - 1) * this.pageData.pageSize + scope.$index + 1
          }
        },
        {
          width: 120,
          prop: 'staffName',
          label: '姓名',
          reserveSelection: true
        },
        {
          width: 150,
          prop: 'mobile',
          label: '手机号码',
          reserveSelection: true
        },
        {
          prop: 'unitComName',
          label: '归属单位',
          reserveSelection: true
        },
        {
          prop: 'officeName',
          label: '所属部门',
          reserveSelection: true
        },
        {
          prop: 'postName',
          label: '岗位',
          reserveSelection: true
        },
        {
          width: 100,
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" style="color: #00CC8F" onClick={() => this.choicePersonnel(row.row)}>选择</span>
              </div>
            )
          }
        }
      ],
      pageData: {
        page: 1,
        pageSize: 15,
        total: 0
      }
    }
  },
  computed: {

  },
  created() {
    console.log(this.detailed, '--------------------')
    this.getStaffListByPage()
    this.postListFn()
  },
  methods: {
    // 部门选择
    deptClick(ids) {
      this.deptSelectIds = ids
      this.getStaffListByPage()
    },
    choicePersonnel(row) {
      this.$emit('selectFinish', row)
      this.$emit('update:visible', !this.visible)
    },
    confirm() {
      if (!this.preSelectIds.length) {
        this.$message({message: '请选择人员', type: 'error'})
        return
      }
      this.$emit('selectFinish', {
        msgScp: this.tabsActive,
        userId: this.preSelectIds,
        deptId: this.deptSelectIds
      })
      this.$emit('update:visible', !this.visible)
    },
    // 查询
    searchForm() {
      this.getStaffListByPage()
    },
    // 重置查询
    resetForm() {
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.deptSelectIds = null
      this.searchForm()
    },
    //  获取岗位列表
    postListFn() {
      this.$api.selectByList().then((res) => {
        if (res.code == 200) {
          this.postList = res.data
        }
      })
    },
    // 获取人员列表
    getStaffListByPage() {
      let param = {
        size: this.pageData.pageSize,
        current: this.pageData.page,
        officeId: this.deptSelectIds,
        ...this.searchFrom
      }
      let newArr = []
      this.tableLoading = true
      // this.$refs.table.clearSelection()
      this.$api.staffListByPage(param).then(res => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.pageData.total = res.data.total
          if (this.detailed.userId.length) {
            this.tableData.forEach(item => {
              if (this.detailed.userId.includes(item.id)) {
                newArr.push(item)
              }
            })
            console.log(newArr)
            this.$nextTick(() => {
              newArr.forEach(item => {
                this.$refs.table.toggleRowSelection(item)
              })
            })
          }
        }
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },

    // 多选
    handleSelectionChange(val) {
      this.preSelectIds = val.map(v => v.id)
      this.multipleSelection = val
    },
    paginationChange(pagination) {
      this.pageData.page = pagination.page
      this.pageData.pageSize = pagination.pageSize
      this.getStaffListByPage()
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    }
  }
}

</script>

<style lang="scss" scoped>
::v-deep .selectScopeDialog {
  .el-dialog__body {
    padding: 0;
  }

  .dialog-content {
    width: 100%;
    position: relative;
  }

  .el-tabs {
    position: absolute;
    top: 0;
    width: 100%;
    background: #fff;

    .el-tabs__item {
      padding: 0 24px;
    }
  }

  .main-dept {
    height: calc(100% - 80px);
    margin: 60px 20px 20px;
    padding: 10px;
    background: #fff;
    overflow: auto;
    border-radius: 4px;
  }

  .main-pre {
    height: calc(100% - 40px);
    margin: 20px;
    display: flex;

    .pre-left {
      width: 350px;
      background-color: #fff;
      border-radius: 4px;
      margin-right: 16px;
      padding: 10px 10px 10px 0;
      overflow: auto;
    }

    .pre-right {
      flex: 1;
      background-color: #fff;
      border-radius: 4px;
      padding: 0 10px 10px;

      .search-from {
        padding-bottom: 10px;

        & > div {
          margin-top: 12px;
          margin-right: 10px;
        }
      }

      .table-content {
        width: 100%;
        height: 500px;
      }
    }
  }
}
</style>
