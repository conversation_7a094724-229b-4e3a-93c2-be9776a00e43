<!--
 * @Author: hedd
 * @Date: 2024-01-17 15:52:50
 * @LastEditTime: 2024-01-17 16:55:12
 * @FilePath: \ihcrs_pc\src\views\monitor\components\securityOperationMonitor\rtspVideoDialog.vue
 * @Description:
-->
<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="视频播放"
    width="40%"
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="content">
      <div class="leftBox">
        <rtspCavas
          ref="rtspCavas"
          :rtspUrl="selectItem.rtsp"
          :videoName="selectItem.surveyEntityName"
          :hasCavas="selectItem.rtsp.indexOf('rtsp') != -1"
          :hasControl="true"
          :isLight="isLight"
          @toHiddenOperation="toHiddenRightBox"
        ></rtspCavas>
      </div>
      <div v-if="isLight" class="rightBox">
        <div class="title">云台控制</div>
        <div class="rightContent"><img src="@/assets/images/securityCenter/panel.png" alt="" /></div>
      </div>
    </div>
  </el-dialog>
</template>
<script lang="jsx">
import fullscreen from '@/components/rtspCavas/fullscreen.js'
export default {
  name: 'rtspVideoDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectItem: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      isLight: true,
      playerFullscreen: false // 播放器是否全屏
    }
  },
  mounted() {},
  methods: {
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    },
    // 控制右侧显示隐藏
    toHiddenRightBox(type) {
      if (type == 'screen') {
        this.toggleFullscreen()
      } else {
        this.isLight = !this.isLight
      }
    },
    toggleFullscreen() {
      const dom = document.querySelector('.content')
      if (this.playerFullscreen) {
        fullscreen.exit(dom)
      } else {
        fullscreen.request(dom, () => {
          this.playerFullscreen = false
        })
      }
      this.playerFullscreen = !this.playerFullscreen
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 400px;
  background: #fff;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  .leftBox,
  .rightBox {
    height: 100%;
  }
  .rightBox {
    background: #151f3c;
    width: 256px;
    margin-left: 2px;
    .title {
      font-size: 16px;
      padding: 10px 10px;
      color: #fff;
    }
    .rightContent {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
.model-dialog {
  padding: 0 !important;
}
::v-deep .el-dialog__body {
  padding: 0 !important;
}
</style>
