<template>
  <div class="content">
    <el-form ref="formInline" :model="formInline" label-width="100px" :rules="rules">
      <el-form-item label="人员姓名" prop="staffName">
        <el-input v-model="formInline.staffName" placeholder="请输入姓名"></el-input>
      </el-form-item>
      <el-form-item label="身份证号" prop="card">
        <el-input v-model="formInline.card" placeholder="请输入身份证号"></el-input>
      </el-form-item>
      <el-form-item label="出生日期">
        <el-date-picker v-model="formInline.birthDate" type="date" value-format="yyyy-MM-dd" placeholder="请选择出生日期" @change="birthDateChange"> </el-date-picker>
      </el-form-item>
      <br />
      <el-form-item label="年龄">
        <el-input v-model="age" show-word-limit placeholder="根据出生日期自动计算" disabled> </el-input>
      </el-form-item>
      <el-form-item label="籍贯">
        <el-input v-model="formInline.nativePlace" placeholder="请输入籍贯"></el-input>
      </el-form-item>
      <!-- <el-form-item label="民族">
        <el-select v-model="formInline.nation" placeholder="请选择民族" clearable>
          <el-option v-for="item in nationList" :key="item.code" :label="item.nation" :value="item.code"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="性别">
        <el-select v-model="formInline.sex" placeholder="请选择性别" clearable>
          <el-option v-for="item in sexList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <br />
      <el-form-item label="手机号码" prop="phone">
        <el-input v-model="formInline.phone" placeholder="请输入手机号码" maxlength="11"> </el-input>
      </el-form-item>
      <el-form-item label="办公电话" prop="phoneOffice">
        <el-input v-model="formInline.phoneOffice" placeholder="请输入办公电话" oninput="value=value.replace(/\D/g, '')"> </el-input>
      </el-form-item>
      <el-form-item label="职工工号">
        <el-input v-model="formInline.staffNum" placeholder="请输入职工工号"> </el-input>
      </el-form-item>
      <br />
      <el-form-item label="文化程度">
        <el-select v-model="formInline.education" placeholder="请选择文化程度" clearable filterable @change="educationChange">
          <el-option v-for="item in educationalLevelList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="角色权限" prop="role" class="ml-14">
        <el-select v-model="formInline.role" placeholder="请选择角色权限" clearable disabled @change="roleChange">
          <el-option v-for="item in roleList" :key="item.id" :label="item.roleName" :value="item.id"></el-option>
        </el-select>
      </el-form-item> -->
      <br />
      <el-form-item label="员工照片">
        <upload :fileList="photoList" modelName="hospitalBaseInfo" @input="uploadChangePhoto" @uploadRemove="uploadPhotoRemove"></upload>
      </el-form-item>
      <!-- <el-form-item label="签名">
        <upload :fileList="signList" :flieSizeType="1" modelName="hospitalBaseInfo" @input="uploadSignChange"
          @uploadRemove="uploadSighRemove"></upload>
      </el-form-item>
      <el-form-item label="印章">
        <upload :fileList="sealList" :flieSizeType="1" modelName="hospitalBaseInfo" @input="uploadSealChange"
          @uploadRemove="uploadSealRemove"></upload>
      </el-form-item> -->
    </el-form>
  </div>
</template>

<script>
import upload from '../components/upload.vue'
import { validateMobile, validatePhone, validateIdCard } from '@/assets/common/validate'
const validateMobileNum = (rule, value, callback) => {
  if (!validateMobile(value)) {
    callback(new Error('请输入正确的手机号'))
  } else {
    callback()
  }
}
const validateIdCardNum = (rule, value, callback) => {
  if (!validateIdCard(value)) {
    callback(new Error('请输入正确的身份证号'))
  } else {
    callback()
  }
}
const validatePhoneNum = (rule, value, callback) => {
  if (validatePhone(value) || validateMobile(value)) {
    callback()
  } else {
    callback(new Error('请输入正确的电话号码'))
  }
}
export default {
  name: 'memberBaseForm',
  components: {
    upload
  },
  data() {
    return {
      formInline: {
        staffName: '',
        card: '',
        // birthDate: "",
        nativePlace: '',
        nation: null,
        sex: '',
        phoneOffice: '',
        phone: '',
        staffNum: '',
        photoId: '',
        avatar: '',
        // signUrl: '', // 签名
        // sealUrl: '', // 印章
        //education: '', // 学历名称
        education: '', //学历id
        educationName: '' // 学历名称
        // roleName: '', // 角色权限
        // role: '',
      },
      age: '',
      rules: {
        staffName: {
          required: true,
          message: '请输入人员姓名',
          trigger: 'change'
        },
        card: [
          { validator: validateIdCardNum, trigger: 'blur' },
          { required: false, message: '请输入正确的身份证号', trigger: 'blur' }
        ],
        phoneOffice: [
          // { validator: validateMobileNum, trigger: "blur" },
          // { required: false, message: "请输入办公号码", trigger: "blur" },
        ],
        phone: [
          { validator: validatePhoneNum, trigger: 'blur' },
          { required: true, message: '请输入手机号码', trigger: 'blur' }
        ]
        // pmName: {
        //   required: true,
        //   message: "请选择所属单位",
        //   trigger: "change",
        // },
        // officeName: {
        //   required: true,
        //   message: "请选择所属科室",
        //   trigger: "change",
        // },
      },
      nationList: [],
      sexList: [
        { id: 1, name: '男' },
        { id: 0, name: '女' }
      ],
      roleList: [], // 角色权限
      photoList: [], // 照片
      signList: [], // 签名
      sealList: [], // 印章
      educationalLevelList: [], // 文化程度
      isDeptDialog: false,
      dialogTitle: '选择科室',
      personShow: '',
      psotAllList: []
    }
  },
  // computed: {
  //   age() {
  //     return this.birthDateChange
  //   }
  //   // // eslint-disable-next-line vue/return-in-computed-property
  //   // auditTitle() {
  //   //   if (!this.type) {
  //   //     return '隐患审核'
  //   //   } else if (this.isActive == '1') {
  //   //     return '隐患整改审核'
  //   //   } else if (this.isActive == '2') {
  //   //     return '隐患挂帐审核'
  //   //   }
  //   // }
  // },
  mounted() {
    this.init()
    if (this.$route.query.staffId) {
      this.getStaffDetailFn(this.$route.query.staffId)
    }
  },
  methods: {
    init() {
      // this.getNationListFn()
      this.getDictListFn()
      //this.getRoleList()
    },
    //  根据单位ID获取单位信息详情
    getStaffDetailFn(id) {
      this.$api.supplierAssess
        .getUserInfoById({
          staffId: id
        })
        .then((res) => {
          if (res.code == 200) {
            this.formInline = res.data
            this.formInline.role = Number(res.data.role)
            res.data.avatar
              ? (this.photoList = [
                  {
                    name: '',
                    url: this.$tools.imgUrlTranslation(res.data.avatar)
                  }
                ])
              : (this.photoList = [])
            if (this.formInline.birthDate) {
              let birthDate = new Date(this.formInline.birthDate)
              this.age = this.getAge(birthDate)
            }
            // res.data.signUrl
            //   ? this.signList = [
            //     {
            //       name: "",
            //       url: this.$tools.imgUrlTranslation(res.data.signUrl)
            //     }
            //   ]
            //   : (this.signList = []);
            // res.data.sealUrl
            //   ? this.sealList = [
            //     {
            //       name: "",
            //       url: this.$tools.imgUrlTranslation(res.data.sealUrl)
            //     }
            //   ]
            //   : (this.sealList = []);
          }
        })
    },
    //年龄改变
    birthDateChange(val) {
      this.$forceUpdate()
      if (val) {
        let birthDate = new Date(val)
        this.age = this.getAge(birthDate)
      }
    },
    getAge(birthDate) {
      // 创建一个 Date 对象表示当前日期
      const currentDate = new Date()
      // 获取当前年份
      const currentYear = currentDate.getFullYear()
      // 获取当前月份
      const currentMonth = currentDate.getMonth()
      // 获取当前日期
      const currentDay = currentDate.getDate()

      // 获取出生年份
      const birthYear = birthDate.getFullYear()
      // 获取出生月份
      const birthMonth = birthDate.getMonth()
      // 获取出生日期
      const birthDay = birthDate.getDate()

      // 计算年龄
      let age = currentYear - birthYear

      // 检查生日是否已过
      if (currentMonth < birthMonth || (currentMonth === birthMonth && currentDay < birthDay)) {
        age--
      }

      return age
    },
    // 角色权限
    getRoleList() {
      const params = {
        page: 1,
        pageSize: 9999
      }
      this.$api.getSysRoleInfo(params).then((res) => {
        if (res.code === '200') {
          this.roleList = res.data.records
        }
      })
    },
    // 民族列表
    getNationListFn() {
      this.$api.getNationList({}).then((res) => {
        if (res.code == 200) {
          this.nationList = res.data
        }
      })
    },
    getDictListFn() {
      // 文化程度
      this.$api.supplierAssess
        .getDictData({
          dictionaryCategoryId: 'EDUCATION_LEVEL_CATEGORY'
        })
        .then((res) => {
          if (res.code == 200) {
            this.educationalLevelList = res.data[0].children
          }
        })
    },
    validate(callback) {
      // 这个form是子组件内部 el-form 的 ref="contentForm"
      this.$refs.formInline.validate((valid) => {
        callback(valid)
      })
    },
    uploadChangePhoto(val, state) {
      if (state) {
        this.formInline.photoId = ''
        this.formInline.avatar = ''
      } else {
        this.formInline.photoId = val.uid
        this.formInline.avatar = val.fileHost + val.fileUrl
      }
    },
    uploadPhotoRemove() {
      this.photoList = []
      this.formInline.photoId = ''
      this.formInline.avatar = ''
    },
    uploadSealChange(val, state) {
      if (state) {
        this.formInline.sealUrl = ''
      } else {
        this.formInline.sealUrl = val.fileUrl
      }
    },
    uploadSealRemove() {
      this.sealList = []
      this.formInline.sealUrl = ''
    },
    uploadSignChange(val, state) {
      if (state) {
        this.formInline.signUrl = ''
      } else {
        this.formInline.signUrl = val.fileUrl
      }
    },
    uploadPicUrlChange(state, index) {
      if (state) {
        this.pictureList[index].picUrl = ''
      } else {
        let obj = this.$refs.upload[index].fileInfo
        console.log(obj, 'obj')
        this.pictureList[index].picUrl = obj.fileHost + obj.fileUrl
      }
    },
    uploadPicUrlRemove(index) {
      this.pictureList[index].fileList = []
      this.pictureList[index].picUrl = ''
    },
    uploadSighRemove() {
      this.signList = []
      this.formInline.signUrl = ''
    },
    // 文化程度改变
    educationChange(e) {
      if (e) {
        this.formInline.educationName = this.educationalLevelList.find((ele) => ele.id == e).name
      } else {
        this.formInline.educationName = ''
      }
    },
    // 角色权限改变
    roleChange(e) {
      this.formInline.roleName = this.roleList.find((ele) => ele.id == e).roleName
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  .el-input,
  .el-select,
  .el-date-editor {
    width: 379px;
  }
}
.el-date-editor {
  width: 379px !important;
}
.inputWid {
  width: 820px !important;
}
.el-form-item {
  display: inline-block;
  vertical-align: top;
  margin-right: 40px;
}
.map_search {
  padding-bottom: 10px;
}
.bmView {
  height: 100%;
  box-sizing: border-box;
}
</style>
