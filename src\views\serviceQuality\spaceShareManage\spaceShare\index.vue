<!-- 空间分摊 -->
<template>
  <PageContainer>
    <div slot="content" class="space-content" style="height: 100%">
      <div class="space-content-left">
        <div class="left_title">
          <span>空间结构</span>
          <div>
            <el-button type="primary" size="mini" :loading="autoFillLoading" @click="tableControl('autoFill')">自动填写</el-button>
            <el-button type="primary" size="mini" @click="tableControl('spaceShare')">空间分摊</el-button>
          </div>
        </div>
        <el-input v-model="filterText" placeholder="请输入关键字"></el-input>
        <div class="left_content">
          <el-tree
            ref="tree"
            v-loading="treeLoading"
            :check-strictly="true"
            :data="treeData"
            :props="defaultProps"
            :default-expanded-keys="idArr"
            :filter-node-method="filterNode"
            style="margin-top: 10px"
            node-key="id"
            :highlight-current="true"
            :expand-on-click-node="false"
            @node-click="nodeClick"
          >
            <template #default="{ node, data }">
              <el-tooltip effect="dark" :disabled="showTooltip" :content="node.label" placement="top">
                <div class="nodeLabel" @mouseover="onMouseOver(data.id)">
                  <span :ref="`nodeLabel${data.id}`">{{ node.label }}</span>
                  <span class="operate-btns">
                    <el-dropdown trigger="click" class="custom-tree-menu" @command="(key) => treeControl(key, data, node)">
                      <span class="more"><i class="el-icon-more rotate" /></span>
                      <el-dropdown-menu slot="dropdown" class="hide-arrow">
                        <el-dropdown-item v-if="data.ssmType == 1" command="editHospital">编辑医院</el-dropdown-item>
                        <el-dropdown-item v-if="data.ssmType == 2" command="editCampus">编辑院区</el-dropdown-item>
                        <el-dropdown-item v-if="data.ssmType == 3" command="editBuild">编辑建筑</el-dropdown-item>
                        <el-dropdown-item v-if="data.ssmType == 4" command="editFloor">编辑楼层</el-dropdown-item>
                        <el-dropdown-item command="record">操作记录</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </span>
                </div>
              </el-tooltip>
            </template>
          </el-tree>
        </div>
      </div>
      <div class="space-content-right">
        <div style="height: 100%; display: flex; flex-direction: column">
          <div class="search-from">
            <el-input
              v-model.trim="searchFrom.keywords"
              placeholder="空间名称"
              style="width: 300px"
              suffix-icon="el-icon-search"
              clearable
              maxlength="25"
              onkeyup="if(value.length>25)value=value.slice(0,25)"
            ></el-input>
            <el-button type="primary" plain style="margin-right: 10px" @click="resetForm">重置</el-button>
            <el-button type="primary" @click="searchForm">查询</el-button>
          </div>
          <div class="search-control">
            <el-button type="primary" :loading="dataSyncLoading" @click="tableControl('dataSync')">数据同步</el-button>
            <el-button type="primary" :disabled="!multipleSelection.length" @click="tableControl('batchEdit')">批量配置</el-button>
            <el-button type="primary" :disabled="!multipleSelection.length" @click="tableControl('batchEditType')">批量配置空间类型</el-button>
            <el-button v-if="isConfig" type="primary" style="float: right" @click="saveConfig">保存</el-button>
          </div>
          <div class="contentTable-main table-content">
            <el-table
              ref="table"
              v-loading="tableLoading"
              border
              style="width: 100%; height: 100%"
              :data="tableData"
              :height="tableHeight"
              stripe
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="60" align="center"></el-table-column>
              <!-- <el-table-column type="index" label="序号" width="70">
                <template slot-scope="scope">
                  <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
                </template>
              </el-table-column> -->
              <el-table-column label="房间编码" prop="modelCode" show-overflow-tooltip> </el-table-column>
              <el-table-column label="空间名称" prop="ssmName" show-overflow-tooltip> </el-table-column>
              <el-table-column label="位置" prop="allSpaceName" show-overflow-tooltip> </el-table-column>
              <el-table-column label="使用科室" prop="useDepartmentName" show-overflow-tooltip> </el-table-column>
              <el-table-column label="空间类型" prop="paceTypeName" show-overflow-tooltip> </el-table-column>
              <el-table-column label="建筑面积" prop="constructionArea" width="80">
                <template slot-scope="scope">
                  <el-input
                    v-if="scope.row.config"
                    v-model.trim="scope.row.constructionArea"
                    :maxlength="10"
                    oninput="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
                  ></el-input>
                  <span v-else>{{ scope.row.constructionArea }}</span>
                </template>
              </el-table-column>
              <el-table-column label="公摊面积" prop="sharedArea" width="80">
                <template slot-scope="scope">
                  <el-input
                    v-if="scope.row.config"
                    v-model.trim="scope.row.sharedArea"
                    :maxlength="10"
                    oninput="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
                    :disabled="scope.row.publicFlag == 1"
                  ></el-input>
                  <span v-else>{{ scope.row.sharedArea }}</span>
                </template>
              </el-table-column>
              <el-table-column label="总面积" prop="totalArea" width="100">
                <template slot-scope="scope">
                  <span>{{ (Number(scope.row.constructionArea) + Number(scope.row.sharedArea)).toFixed(2) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="是否公共" prop="publicFlag" width="90">
                <template slot-scope="scope">
                  <el-select v-if="scope.row.config" v-model="scope.row.publicFlag" style="width: 80px" @change="() => (scope.row.sharedArea = 0)">
                    <el-option label="否" value="0" />
                    <el-option label="是" value="1" />
                  </el-select>
                  <span v-else>{{ scope.row.publicFlag == 0 ? '否' : '是' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template slot-scope="scope">
                  <el-button type="text" @click="tableControl('edit', scope)">配置</el-button>
                  <el-button type="text" @click="tableControl('record', scope.row)">操作记录</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="contentTable-footer">
            <el-pagination
              :current-page="pagination.current"
              :page-sizes="pagination.pageSizeOptions"
              :page-size="pagination.size"
              :layout="pagination.layoutOptions"
              :total="pagination.total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            >
            </el-pagination>
          </div>
        </div>
      </div>
      <editSpaceDialog v-if="isEditSpaceDialog" :visible.sync="isEditSpaceDialog" :parentParams="parentParams" @change="treeChange" />
      <spaceTypeDialog v-if="isSpaceTypeDialog" :visible.sync="isSpaceTypeDialog" :selectItems="multipleSelection" />
      <shareCountDialog v-if="isShareCountDialog" :visible.sync="isShareCountDialog" />
    </div>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin.js'
import { transData } from '@/util'
export default {
  name: 'spaceShare',
  components: {
    editSpaceDialog: () => import('../components/editSpaceDialog.vue'),
    spaceTypeDialog: () => import('../components/spaceTypeDialog.vue'),
    shareCountDialog: () => import('../components/shareCountDialog.vue')
  },
  mixins: [tableListMixin],
  data() {
    return {
      showTooltip: false,
      isEditSpaceDialog: false,
      isSpaceTypeDialog: false,
      isShareCountDialog: false,
      filterText: '', // 树形结构筛选
      treeLoading: false,
      treeData: [],
      idArr: [],
      spaceIds: [], // 选中空间
      simNames: [], // 选中空间名称
      defaultProps: {
        label: 'ssmName',
        children: 'list'
      },
      searchFrom: {
        keywords: ''
      },
      tableLoading: false,
      tableData: [],
      multipleSelection: [], // 多选
      currentTreeNode: {},
      parentParams: {},
      dataSyncLoading: false,
      autoFillLoading: false
    }
  },
  computed: {
    isConfig() {
      return this.tableData.some((v) => v.config)
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    },
    isShareCountDialog(val) {
      if (!val) this.getSpacelistFn()
    },
    isSpaceTypeDialog(val) {
      if (!val) this.getSpacelistFn()
    }
  },
  created() {
    this.spaceTreeListFn()
  },
  methods: {
    saveConfig() {
      let params = []
      for (let i = 0; i < this.multipleSelection.length; i++) {
        if (this.multipleSelection[i].constructionArea === '') {
          this.$message({ message: '建筑面积不能为空', type: 'warning' })
          return
        }
        console.log
        params.push({
          ...this.multipleSelection[i],
          constructionArea: Number(this.multipleSelection[i].constructionArea),
          sharedArea: Number(this.multipleSelection[i].sharedArea),
          totalArea: Number(this.multipleSelection[i].constructionArea) + Number(this.multipleSelection[i].sharedArea)
        })
      }
      this.$api.SpaceConfigBatch(params).then((res) => {
        if (res.code == 200) {
          this.$message.success('配置成功')
          for (let i = 0; i < this.tableData.length; i++) {
            this.tableData[i].config = false
          }
          this.$refs.table.clearSelection()
          this.getSpacelistFn()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    tableControl(key, data) {
      const userInfo = this.$store.state.user.userInfo.user
      if (key == 'edit') {
        // 配置
        this.multipleSelection.push(data.row)
        this.tableData[data.$index].config = true
      } else if (key == 'record') {
        // 操作记录
        this.$router.push({
          name: 'operateRecord',
          query: {
            id: data.id,
            name: data.ssmName
          }
        })
      } else if (key == 'dataSync') {
        // 数据同步
        this.dataSyncLoading = true
        this.$api
          .SpaceDataSync()
          .then((res) => {
            this.$message({ message: res.msg, type: res.code == 200 ? 'success' : 'error' })
          })
          .finally(() => {
            this.dataSyncLoading = false
            this.getSpacelistFn()
          })
      } else if (key == 'batchEdit') {
        // 批量配置
        for (let i = 0; i < this.tableData.length; i++) {
          if (this.multipleSelection.map((v) => v.id).includes(this.tableData[i].id)) {
            this.tableData[i].config = true
          }
        }
      } else if (key == 'batchEditType') {
        // 批量配置空间类型
        this.isSpaceTypeDialog = true
      } else if (key == 'autoFill') {
        // 自动填写
        this.$confirm('自动填写会更新原空间结构建筑面积，请确认?', '自动填写确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.autoFillLoading = true
            this.$api
              .SpaceAutoFill()
              .then((res) => {
                if (res.code == 200) {
                  this.$message({ message: res.msg, type: 'success' })
                  this.getSpacelistFn()
                } else {
                  this.$confirm(res.data, res.msg ? res.msg : '自动填写失败', {
                    showCancelButton: false,
                    type: 'error'
                  })
                }
              })
              .finally(() => {
                this.autoFillLoading = false
              })
          })
          .catch(() => {
            this.$message({ type: 'info', message: '已取消' })
          })
      } else if (key == 'spaceShare') {
        // 空间分摊
        this.isShareCountDialog = true
      }
    },
    treeChange(data) {
      let params = {
        ...data,
        baseId: this.parentParams.baseId,
        constructionArea: Number(data.constructionArea),
        sharedArea: Number(data.sharedArea),
        totalArea: Number(data.constructionArea) + Number(data.sharedArea)
      }
      this.$api.SpaceConfigBatch([params]).then((res) => {
        if (res.code == 200) {
          this.isEditSpaceDialog = false
          this.$message({ message: this.parentParams.title + '成功', type: 'success' })
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 树行结构控制
    treeControl(key, data, node) {
      this.currentTreeNode = data
      let newObj = {
        editHospital: { title: '编辑医院' },
        editCampus: { title: '编辑院区' },
        editBuild: { title: '编辑建筑' },
        editFloor: { title: '编辑楼层' }
      }
      if (Object.keys(newObj).includes(key)) {
        this.parentParams = {
          title: newObj[key].title,
          name: data.allSpaceName,
          baseId: data.id
        }
        this.isEditSpaceDialog = true
      } else if (key == 'record') {
        // 操作记录
        this.$api.GetInfoByBaseId({ baseId: data.id }).then((res) => {
          if (res.code == 200) {
            // 操作记录
            this.$router.push({
              name: 'operateRecord',
              query: {
                id: res.data.id,
                name: data.ssmName
              }
            })
          }
        })
      }
    },
    // 获取空间列表
    getSpacelistFn() {
      let data = {
        simCode: this.spaceIds.join(),
        page: this.pagination.current,
        pageSize: this.pagination.size,
        keywords: this.searchFrom.keywords
      }
      this.tableLoading = true
      this.$api.GetRoomPage(data).then((res) => {
        if (res.code == 200) {
          for (let i = 0; i < res.data.records.length; i++) {
            res.data.records[i].config = false
          }
          this.tableData = res.data.records
          this.pagination.total = res.data.total
        } else {
          this.tableData = []
          this.pagination.total = 0
        }
        this.tableLoading = false
      })
    },
    filterNode(value, data) {
      if (!value) return true
      return data.ssmName.indexOf(value) !== -1
    },
    nodeClick(val) {
      const { tree } = this.$refs
      this.spaceIds = []
      this.simNames = []
      let checkedNode = tree.getNode(val.id)
      this.getTreeNode(checkedNode)
      this.pagination.current = 1
      this.pagination.size = 15
      this.getSpacelistFn()
    },
    getTreeNode(node) {
      if (node.level >= 1) {
        this.spaceIds.unshift(node.data.id)
        this.simNames.unshift(node.data.ssmName)
        this.getTreeNode(node.parent)
      } else {
        this.spaceIds.unshift('#')
      }
    },
    // 获取空间树
    spaceTreeListFn() {
      this.spaceIds = []
      this.treeLoading = true
      this.$api.getStructureTree().then((res) => {
        this.treeLoading = false
        if (res.code == 200) {
          res.data.map((tree) => {
            tree.ssmType == 1 || tree.ssmType == 2 ? this.idArr.push(tree.id) : ''
            return this.idArr
          })
          this.treeData = transData(res.data, 'id', 'pid', 'list')
          this.$nextTick(function () {
            this.$refs.tree.setCurrentKey(res.data[0].id)
          })
          this.spaceIds.push(res.data[0].id)
          this.getSpacelistFn()
        }
      })
    },
    // 判断当前页是否是最后一页
    isLastPage(deleteNum) {
      let deleteAfterPage = Math.ceil((this.pagination.total - deleteNum) / this.pagination.size)
      let currentPage = this.pagination.current > deleteAfterPage ? deleteAfterPage : this.pagination.current
      this.pagination.current = currentPage < 1 ? 1 : currentPage
    },
    // 重置
    resetForm() {
      this.pagination.size = 15
      this.pagination.current = 1
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.getSpacelistFn()
    },
    // 查询
    searchForm() {
      this.getSpacelistFn()
    },
    handleSizeChange(val) {
      this.pagination.size = val
      this.getSpacelistFn()
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.getSpacelistFn()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 树划入划出效果
    onMouseOver(id) {
      const parentWidth = this.$refs[`nodeLabel${id}`].parentNode.offsetWidth // 获取元素父级可视宽度
      const contentWidth = this.$refs[`nodeLabel${id}`].offsetWidth // 获取元素可视宽度
      this.showTooltip = contentWidth <= parentWidth
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: #e6effc !important;
}
::v-deep .el-tree-node__content {
  height: 36px !important;
  position: relative;
  .operate-btns {
    position: absolute;
    right: 2px;
    display: none;
    .el-icon-more:before {
      content: '\E794';
      color: #c0c4cc;
      font-size: 16px;
    }
    .rotate {
      cursor: pointer;
      margin-left: 5px;
      transform: rotate(90deg);
    }
    .rotate:focus {
      width: 20px;
      height: 20px;
      border-radius: 4em;
      background-color: rgba(130, 132, 138, 0.2);
    }
  }
  // 鼠标悬停时，展示
  &:hover,
  :focus-within {
    .operate-btns {
      display: inline;
    }
  }
}
.space-content {
  height: 100%;
  display: flex;
  .space-content-left {
    width: 280px;
    height: 100%;
    padding: 0px 10px 10px 10px;
    background: #fff;
    border-radius: 4px 0px 0px 4px;
    border-right: 1px solid #e4e7ed;
    .left_content {
      width: 100%;
      height: calc(100% - 95px);
      overflow: auto;
    }
    .left_title {
      padding: 16px 0px 16px 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      span {
        font-weight: bold;
        font-size: 16px;
        color: #333333;
        line-height: 22px;
      }
      .el-button--mini {
        padding: 4px 6px;
      }
    }
  }
  .space-content-right {
    height: 100%;
    min-width: 0;
    padding: 10px 20px 20px 20px;
    background: #fff;
    border-radius: 0px 4px 4px 0px;
    flex: 1;
    .search-from {
      & > div,
      .el-button {
        margin-right: 10px;
        margin-top: 10px;
      }
      & > .el-button:last-child {
        margin: 0px;
      }
    }
    .search-control {
      margin-bottom: 10px;
      & > .el-button {
        margin-top: 10px;
        margin-right: 10px;
        margin-left: 0px;
      }
      // & > .el-button:last-child {
      //   margin: 0px;
      // }
    }
    .contentTable-main {
      flex: 1;
      overflow: hidden;
      ::v-deep(.el-input) {
        .el-input__inner {
          padding: 0px 0px 0px 6px;
        }
      }
    }
    .contentTable-footer {
      padding: 10px 0 0;
    }
  }
  .content {
    width: 100%;
    max-height: 500px !important;
    overflow: auto;
    background-color: #fff !important;
  }
}
::v-deep .el-tree-node {
  white-space: normal;
  // outline: 0;
}
.nodeLabel {
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding-right: 20px;
}
</style>
