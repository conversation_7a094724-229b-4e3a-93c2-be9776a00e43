<template>
  <el-dialog :title="type === '1' ? '编辑文件夹' : '创建文件夹'" :visible="visible" width="560px"
    :before-close="handleClosesubmit">
    <el-form ref="form" label-position="right" label-width="100px" :model="formData" :rules="rules">
      <el-row :gutter="24" style="margin: 0">
        <el-col :md="24">
          <el-form-item label="文件夹名称" prop="folderName">
            <el-input v-model="formData.folderName" placeholder="请输入文件夹名称" maxlength="50" show-word-limit></el-input>
          </el-form-item>
        </el-col>
        <el-col :md="24">
          <el-form-item label="共享范围" prop="visibleRange">
            <el-radio-group v-model="formData.visibleRange" @change="handleRadioChange">
              <el-radio label="0">所有人</el-radio>
              <el-radio label="1">指定部门</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col v-if="formData.visibleRange === '1'" :md="24">
          <el-form-item label="指定部门" prop="accessAuth">
            <el-input v-model="formData.accessAuthName" placeholder="请选择指定部门" readonly clearable
              suffix-icon="el-icon-arrow-down" @focus="handleFocus"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="handleClosesubmit">取消</el-button>
      <el-button type="primary" @click="handleSubmit">
        {{ type === '1' ? '保存' : '创建' }}
      </el-button>
    </div>
    <deptDialog v-if="deptDialog" :visible.sync="deptDialog" title="指定部门" :disabledId="disabledId" :nature="'2'"
      :isNotmultiSector="true" @selectDept="selectDept" :selectedDept="selectedDept" />
  </el-dialog>
</template>
<script>
import { mapGetters } from 'vuex'
import DeptDialog from './deptDialog.vue'
export default {
  components: {
    DeptDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    currentNode: {
      type: Object,
      default: () => null
    },
    type: {
      type: String,
      default: '0'
    },
    folderType: {
      type: String,
      default: '1'
    }
  },
  data() {
    return {
      formData: { folderName: '', visibleRange: '0', accessAuth: '' },
      deptDialog: false,
      rules: {
        folderName: [
          {
            required: true,
            message: '请输入文件夹名称',
            trigger: ['change', 'blur']
          }
        ],
        visibleRange: [
          {
            required: true,
            message: '请选择共享范围',
            trigger: ['change', 'blur']
          }
        ],
        accessAuth: [
          {
            required: true,
            message: '请选择指定部门',
            trigger: ['blur', 'change']
          }
        ]
      },
      disabledId: '',
      selectedDept: [],//已选数组
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  watch: {
    visible: {
      handler(val) {
        if (val && this.type === '1') {
          this.$nextTick(() => {
            this.handleGetData()
          })
        }
      },
      immediate: true
    }
  },
  methods: {
    handleFocus() {
      this.selectedDept = []
      this.deptDialog = true
      if (this.formData.accessAuthName) {
        let idArr = this.formData.accessAuth.split(',')
        let nameArr = this.formData.accessAuthName.split(',')
        idArr.forEach((item, index) => {
          this.selectedDept.push({
            id: item,
            deptName: nameArr[index]
          })
        })
      }
      if (this.currentNode.visibleRange === '1') {
        this.disabledId = this.currentNode.accessAuth ? this.currentNode.accessAuth.split(',') : ''
      }
    },
    handleGetData() {
      this.$api.fileManagement.folderDetail({ id: this.currentNode.id }).then((res) => {
        const { folderName, folderId, accessAuth, visibleRange, accessAuthName } = res.data
        this.formData = {
          folderName,
          folderId,
          accessAuth,
          visibleRange,
          accessAuthName
        }
      })
    },
    // 选择科室
    selectDept(list) {
      this.formData.accessAuth = list.map((item) => item.id).join(',')
      this.formData.accessAuthName = list.map((item) => item.deptName).join(',')
    },
    handleRadioChange() {
      this.formData.accessAuth = ''
      this.formData.accessAuthName = ''
    },
    handleClosesubmit() {
      this.$emit('update:visible', false)
    },
    handleSubmit() {
      this.$refs.form.validate((res) => {
        if (res) {
          const isEdit = this.type === '1'
          const params = {
            ...this.formData,
            folderType: this.folderType
          }
          const { folderId } = this.formData
          const { id: parentId } = this.currentNode
          if (isEdit) {
            params.folderId = folderId
          } else {
            params.parentId = parentId
          }
          const func = isEdit ? this.$api.fileManagement.updateFolder : this.$api.fileManagement.insertFolder
          func(params).then((res) => {
            if (res.code === '200') {
              const text = isEdit ? '保存' : '创建'
              this.$message.success(text + '成功')
              this.handleClosesubmit()
              this.$emit('success')
            } else {
              this.$message.error('操作失败')
            }
          })
        }
      })
    }
  }
}
</script>
<style></style>
