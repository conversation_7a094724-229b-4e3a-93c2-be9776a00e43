<template>
  <el-dialog 
    v-if="dialogShow"
    :title="title" 
    width="60%" 
    :visible.sync="dialogShow" 
    custom-class="model-dialog"
  >
    <div style=" width: 100%; height: 100%;">
      <div slot="content" class="card-content">
        <div class="card-content-table table-content">
          <TablePage
            ref="table"
            v-loading="tableLoading"
            :showPage="true"
            :tableColumn="tableColumn"
            :data="tableData"
            height="calc(100% - 55px)"
            :pageData="pageData"
            :pageProps="pageProps"
            @pagination="paginationChange"
            @row-dblclick="control('detail', $event)"
          />
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'normalDialog',
  props: {

  },
  data() {
    return {
      dialogShow: false,
      queryParams: {
        departmentId: null
        // haveModel: '0'
      },
      total: 0,
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      pageProps: {
        page: 'current',
        pageSize: 'size',
        total: 'total'
      },
      tableColumn: [
        {
          prop: '',
          label: '序号',
          formatter: (scope) => {
            return (this.pageData.current - 1) * this.pageData.size + scope.$index + 1
          }
        },
        {
          prop: 'modelCode',
          label: '空间编码'
        },
        {
          prop: 'localSpaceName',
          label: '空间本地名称'
        },
        {
          prop: 'localSpaceCode',
          label: '本地编码'
        },
        {
          prop: 'spatialState',
          label: '空间状态'
        },
        {
          prop: 'area',
          label: '建筑面积（㎡）'
        },
        {
          prop: 'functionDictName',
          label: '功能类型'
        },
        {
          prop: 'dmName',
          label: '归属部门'
        },
        {
          prop: 'simName',
          label: '位置'
        },
        {
          prop: 'principalName',
          label: '空间责任人'
        }
      ],
      tableData: [],
      tableLoading: false,
      title: '空间总数详情'
    }
  },
  mounted() {

  },
  methods: {
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getList()
    },
    getEchartData(data) {
      this.dialogShow = true
      this.queryParams.departmentId = data.id
      this.pageData.current = 1
      this.getList()
    },
    getList() {
      this.tableLoading = true
      let data = {
        ...this.queryParams,
        current: this.pageData.current,
        size: this.pageData.size
      }
      this.$api.selectLeafSpaceById(data).then(res => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.pageData.total = res.data.total
          this.tableLoading = false
        }
      })
    },
    paginationSizeChange(val) {
      this.queryParams.pageSize = val
      this.queryParams.page = 1
      this.getList()
    },
    paginationCurrentChange(val) {
      this.queryParams.page = val
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .model-dialog{
  .el-dialog__body{
    height: 600px;
  }
}
@mixin spot {
  content: "";
  display: inline-block;
  width: 8px;
  height: 8px;
  background: #00bc6d;
  border-radius: 50%;
  margin-right: 5px;
}

.onLine::before {
  @include spot;

  background: #00bc6d;
}

.offLine::before {
  @include spot;

  background: #ccced3;
}

.onLine {
  color: #00bc6d;
}
// .offLine {
//   color: #ccced3;
// }
.card-content {
  height: 100%;
  padding: 0 0 0 4px;
  display: flex;
  flex-direction: column;

  .query_criteria {
    display: flex;
  }

  .card-content-table {
    height: 100%;
    flex: 1;
    overflow: auto;
    margin-top: 16px;

    ::v-deep .el-progress {
      .el-progress-bar__outer {
        border-radius: 0;
        height: 8px !important;
      }

      .el-progress-bar__inner {
        border-radius: 0;
      }
    }
  }

  .card-content-footer {
    padding: 10px 0 0;
  }
}
</style>
