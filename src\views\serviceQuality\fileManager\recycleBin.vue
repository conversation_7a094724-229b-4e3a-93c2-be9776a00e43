<!--  -->
<!-- 模板 -->
<template>
  <PageContainer>
    <div slot="content" class="monitor-setting">
      <div class="monitor-setting-right">
        <div class="right-heade">
          <el-input v-model="params.dataName" placeholder="输入名称搜索" suffix-icon="el-icon-search" clearable />
          <div style="display: inline-block">
            <el-button type="primary" plain @click="reset">重置</el-button>
            <el-button type="primary" @click="search">搜索</el-button>
          </div>
          <div>
            <el-button v-auth="'recycleBin:recover'" size="small" type="primary" :disabled="!selecTableList.length"
              @click="recoverAll">恢复</el-button>
            <el-button v-auth="'recycleBin:delete'" size="small" type="danger" :disabled="!selecTableList.length"
              @click="batchDel('deletAll')">彻底删除</el-button>
          </div>
        </div>
        <div class="right-content">
          <div class="table-content">
            <el-table height="100%" ref="multipleTable" v-loading="loading" :data="tableData"
              @selection-change="selectable" row-key="id">
              <el-table-column type="selection" width="55" align="center" :reserve-selection="true"> </el-table-column>
              <!-- <el-table-column prop="index" label="类型" align="center" show-overflow-tooltip> </el-table-column> -->
              <el-table-column prop="dataName" label="名称" align="center" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="fileName" label="附件" align="center" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="fileMenuName" label="文件位置" align="center" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="createName" label="上传人" align="center" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="createTime" label="上传时间" align="center" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span>{{formatDateTime(scope.row.createTime)}}</span>
                </template>
              </el-table-column>
              <el-table-column prop="updateName" label="修改人" align="center" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span>{{scope.row.updateName?scope.row.updateName:'-'}}</span>
                </template>
              </el-table-column>
              <el-table-column prop="updateTime" label="修改时间" align="center" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span>{{scope.row.updateTime?formatDateTime(scope.row.updateTime):'-'}}</span>
                </template>
              </el-table-column>
              <el-table-column prop="deleteTime" label="删除时间" align="center" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span>{{formatDateTime(scope.row.deleteTime)}}</span>
                </template>
              </el-table-column>
              <el-table-column prop="days" label="剩余时间" align="center" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span>{{scope.row.days}}天</span>
                </template>
              </el-table-column>
              <el-table-column show-overflow-tooltip align="center" label="操作">
                <template slot-scope="scope">
                  <el-button type="text" v-auth="'recycleBin:recover'" class="copy" @click="recover(scope.row)">恢复
                  </el-button>
                  <el-button v-auth="'recycleBin:delete'" type="text" class="delet" @click="batchDel(scope.row)"><span
                      style="color: red">彻底删除</span>
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="paginationClass">
              <div class="tipsClass">
                <i class="el-icon-info" style="color: #3664dd"></i>已选择{{ selecTableList.length }}项，支持跨页选择
                <el-button style="margin-left: 10px" @click="clearSelection" type="text">清空</el-button>
              </div>
              <div>
                <el-pagination :current-page="params.current" layout="-> ,total, sizes, prev, pager, next, jumper"
                  :total="total" :page-size="params.size" :page-sizes="[15, 30, 50]" @size-change="handleSizeChanges"
                  @current-change="handleCurrentChanges">
                </el-pagination>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 文件恢复弹框 -->
      <el-dialog title="选择位置" :visible.sync="dialogVisible" custom-class="ellipsisFileDialog" width="30%"
        :close-on-click-modal="false" :before-close="notyetRecover">
        <div style="margin-bottom: 30px" v-if="selecTableList.length > 1">
          <span
            class="ellipsis">{{ notyetRecoverList[0]?.dataName }}</span>等<span>{{ notyetRecoverList.length }}</span>个文件已无法恢复到原位置请重新选择恢复位置
        </div>
        <div style="margin-bottom: 30px" v-else>
          <span class="ellipsis">{{ rowFilePosition }}</span>文件已无法恢复到原位置请重新选择恢复位置
        </div>
        <span style="margin-right: 40px">文件位置</span>
        <el-cascader ref="cascader" v-model="positionFile" style="width: 60%" :options="treeData" :props="cascaderProps"
          @change="handleChange"></el-cascader>
        <span slot="footer" class="dialog-footer">
          <el-button type="text" @click="notyetRecover">暂不恢复</el-button>
          <el-button type="primary" @click="definiteRecovery">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </PageContainer>
</template>

<script>
  import {
    transData,
  } from '@/util'
  import Template from '../reportManagement/template.vue'
  export default {
    // 组件注册
    components: {
      Template
    },
    data() {
      // 数据定义
      return {
        treeData: [],
        cascaderProps: {
          label: 'ledgerName',
          value: 'ledgerId',
          checkStrictly: true
        },
        positionFile: ['ec20c1823f6411efad203cecef8c00bb'],
        value: '',
        dialogVisible: false,

        rowFilePosition: '',
        tableData: [],
        loading: false,
        total: 0,
        params: {
          size: 15,
          current: 1,
          dataName: "",

        },
        selecTableList: [],
        selectFileId: [],
        resultList: [],
        notyetRecoverList: []
      }
    },
    // 生命周期钩子：创建后
    created() {},
    // 生命周期钩子：载入后
    mounted() {
      this.tableList()
      this.getTreeList()
    },
    // 计算属性定义
    computed: {},
    // 监视器，用于监听数据的变化
    watch: {},
    // 方法定义，包括事件处理函数
    methods: {
      //格式化时间
      formatDateTime(timestamp) {
        const date = new Date(timestamp);
        const year = date.getFullYear();
        const month = ('0' + (date.getMonth() + 1)).slice(-2); // 月份是从0开始的
        const day = ('0' + date.getDate()).slice(-2);
        const hours = ('0' + date.getHours()).slice(-2);
        const minutes = ('0' + date.getMinutes()).slice(-2);
        const seconds = ('0' + date.getSeconds()).slice(-2);
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      },
      //选择文件位置
      handleChange(val) {
        this.positionFile = val
      },
      //获取下拉列表数据
      getTreeList() {
        this.$api
          .fileDirTree()
          .then((res) => {
            if (res.code === '200') {
              this.treeData = this.$tools.transData(res.data, 'ledgerId', 'parentLedgerId', 'children')
              this.tableList()
            } else {
              this.treeData = []
            }
          })

      },
      //暂不恢复
      notyetRecover() {
        this.$refs.multipleTable.clearSelection()
        this.dialogVisible = false
        this.positionFile = [this.treeData[0].ledgerId]
      },
      //选中清空
      clearSelection() {
        this.$refs.multipleTable.clearSelection()
      },
      //批量恢复
      recoverAll() {
        this.resultList = []
        this.$api.preRestoreFile({
          fileIds: this.selectFileId
        }).then((res) => {
          if (res.data.length == 0) {
            this.$message.success('恢复成功')
            this.clearSelection()
            this.tableList()
          } else {
            this.notyetRecoverList = res.data
            res.data.forEach((item) => {
              this.resultList.push(item.fileId)
            })
            this.dialogVisible = true
          }
        })
      },
      //确定恢复
      definiteRecovery() {
        this.$api.restoreFileNoParents({
          fileIds: this.resultList,
          fileMenuIds: this.positionFile.length == 1 ? this.positionFile[0] : this.positionFile[this
            .positionFile.length - 1]
        }).then((res) => {
          if (res.code == 200) {
            this.$message.success('恢复成功')
            this.clearSelection()
            this.tableList()
            this.dialogVisible = false
          }

        })
      },
      //恢复
      recover(row) {
        this.resultList = []
        this.rowFilePosition = ''
        this.rowFilePosition = row.dataName
        // this.dialogVisible = true
        this.$api.preRestoreFile({
          fileIds: [row.fileId]
        }).then((res) => {
          if (res.data.length == 0) {
            this.$message.success('恢复成功')
            this.tableList()
          } else {
            this.resultList = [row.fileId]
            this.dialogVisible = true
          }
        })
      },
      //彻底删除
      batchDel(row) {
        this.$confirm('彻底删除后内容将无法恢复, 是否继续?', '彻底删除', {
            confirmButtonText: '彻底删除',
            cancelButtonText: '取消',
            confirmButtonClass: 'btnClass',
            type: 'warning'
          })
          .then(() => {
            this.$api.completelyDelete({
              fileIds: row == 'deletAll' ? this.selectFileId : [row.fileId],
            }).then((res) => {
              if (res.code == 200) {
                this.clearSelection()
                this.tableList()
                this.$message({
                  type: 'success',
                  message: '删除成功!'
                })
              }
            })

          })
          .catch((action) => {
            this.$refs.multipleTable.clearSelection()
          })
      },
      //重置
      reset() {
        this.params.dataName = ''
        this.search()
      },
      //查询
      search() {
        this.total = 0
        this.params.current = 1
        this.tableList()
      },
      //选中列表数据
      selectable(val) {
        this.selectFileId = []
        val.forEach(element => {
          this.selectFileId.push(element.fileId)
        });
        this.selecTableList = val
      },
      //获取列表数据
      tableList() {
        this.$api
          .queryFileRecycleBinByPage({
            page: {
              current: this.params.current,
              size: this.params.size,
              total: this.total,
            },
            dataName: this.params.dataName
          }).then((res) => {
            if (res.code == 200) {
              this.tableData = res.data.records
              this.total = res.data.total
            }
          })
      },
      //分页
      handleSizeChanges(val) {
        this.params.size = val
        this.tableList()
      },
      //分页
      handleCurrentChanges(val) {
        this.params.current = val
        this.tableList()
      }
    },

  }

</script>
<style lang='scss' scoped>
  .monitor-setting {
    height: 100%;
    display: flex;

    .monitor-setting-right {
      height: 100%;
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: auto;

      .right-heade {
        padding: 0 200px 10px 10px !important;
        margin-left: 16px;
        background: #fff;
        border-radius: 4px;

        ::v-deep .el-input {
          width: 200px;
        }

        &>div {
          margin-right: 10px;
          margin-top: 10px;
        }
      }

      .right-content {
        margin-left: 16px;
        margin-top: 16px;
        background: #fff;
        padding: 10px;
        border-radius: 4px;
        flex: 1;
        overflow: hidden;

        .btns-group {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;

          &>div {
            display: flex;
          }

          .btns-group-control {
            >div {
              margin-left: 10px;
            }

            // & > div, & > button {
            //   margin-right: 10px;
            // }
          }
        }

        .table-content {
          height: calc(100% - 45px);

          ::v-deep .el-table .el-table__body-wrapper td.el-table__cell {
            border-right: 1px solid #ededf5;

            .tooltip-over-td {
              display: block;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }
    }
  }

  .paginationClass {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;

    .tipsClass {
      line-height: 32px;
    }
  }

  .ellipsis {
    width: 200px;
    /* 设置div的宽度 */
    white-space: nowrap;
    /* 保证文字在一行内显示 */
    overflow: hidden;
    /* 超出部分隐藏 */
    text-overflow: ellipsis;
    /* 使用省略号表示文字被截断 */
  }

  ::v-deep .ellipsisFileDialog {
    background: #fff;
    border-radius: 4px;

    .el-dialog__header {
      height: 56px;
      padding: 15px 20px;
      background: #fff;
      border-bottom: 1px solid $color-text-secondary;
      box-shadow: 0 0 12px 3px rgb(0 0 0 / 10%);

      .el-dialog__title,
      .dialog-title {
        font-size: 18px;
        font-family: "PingFang SC-Medium", "PingFang SC";
        font-weight: 500;
        color: $color-text;
      }

      .el-dialog__close {
        color: $color-text;
        font-weight: 600;
        font-size: 18px;
      }
    }

    .el-dialog__body {
      padding: 15px;
      // max-height: calc(78vh - 110px);
      // overflow-y: hidden;

    }

    .el-dialog__footer {
      max-height: 56px;
      padding: 10px 20px;
      background: #fff;
      box-shadow: 0 0 12px 3px rgb(0 0 0 / 10%);
    }
  }

</style>
<style lang='scss'>
  .btnClass {
    color: #fff;
    background-color: #d9001b !important;
    border-color: #d9001b !important;
  }

</style>
