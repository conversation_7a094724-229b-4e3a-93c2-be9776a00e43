<template>
  <PageContainer>
    <div slot="content" class="space-content">
      <div>
        <span class="ipt-title">公示名称：</span>
        <el-input v-model="select.title" placeholder="请输入" clearable class="left word-limit-input" maxlength="50" show-word-limit></el-input>
        <span class="ipt-title">公示范围：</span>
        <el-cascader v-model="select.typeCode" :show-all-levels="false" placeholder="请选择公示范围" :props="typeOptions" clearable class="el-input left"></el-cascader>
        <br class="ifBr" />
        <span class="ipt-title">公示状态：</span>
        <el-select v-model="select.status" class="el-input left" clearable placeholder="请选择">
          <el-option v-for="item in noticeType" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
        <span class="ipt-title">发布人：</span>
        <el-input v-model="select.issuerName" placeholder="请输入" clearable class="left word-limit-input" maxlength="20" show-word-limit style="margin-right: 40px"></el-input>
        <el-button type="primary" @click="queryList">查询</el-button>
        <el-button type="primary" plain @click="reset">重置</el-button>
      </div>
      <div class="top-search" style="margin: 16px 0; display: flex; justify-content: space-between">
        <div style="text-align: center">
          <el-button type="primary" icon="el-icon-plus" @click="addNotice">新建公示</el-button>
        </div>
      </div>
      <div class="table-list">
        <el-table v-loading="tableLoading" :data="tableData" border stripe height="calc(100% - 32px)" @row-dblclick="handleRowClick">
          <el-table-column type="index" label="序号" width="65">
            <template slot-scope="scope">
              <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="公示标题" prop="title" show-overflow-tooltip></el-table-column>
          <el-table-column prop="typeCode" show-overflow-tooltip label="公示类别">
            <template slot-scope="scope">
              {{ scope.row.typeCode === '0' ? '全院公示' : '部门公示' }}
            </template>
          </el-table-column>
          <el-table-column prop="overview" show-overflow-tooltip label="公示概述"></el-table-column>
          <el-table-column prop="status" show-overflow-tooltip label="公示状态">
            <template slot-scope="scope">
              <span v-if="scope.row.status == '0'" style="color: #f98784">草稿</span>
              <span v-if="scope.row.status == '1'" style="color: #2bbdbb">待发布</span>
              <span v-if="scope.row.status == '2'" style="color: #67c23a">已发布</span>
              <span v-if="scope.row.status == '3'" style="color: #909399">已撤销</span>
            </template>
          </el-table-column>
          <el-table-column prop="issuerName" show-overflow-tooltip label="发布人"></el-table-column>
          <el-table-column prop="issuerDate" show-overflow-tooltip label="发布时间"></el-table-column>
          <el-table-column show-overflow-tooltip width="260" label="操作">
            <template slot-scope="scope">
              <el-link type="primary" :underline="false" @click="LockNotice(scope.row)">查看</el-link>
              <el-link
                v-if="scope.row.status == '0' || scope.row.status == '1' || scope.row.status == '3'"
                type="success"
                :underline="false"
                style="margin-left: 10px"
                @click="issueNotice(scope.row)"
              >
                发布
              </el-link>
              <el-link
                v-if="scope.row.status == '0' || scope.row.status == '1' || scope.row.status == '3'"
                type="primary"
                :underline="false"
                style="margin-left: 10px"
                @click="addNotice(scope.row)"
              >
                编辑
              </el-link>
              <el-link v-if="scope.row.status != '2'" type="danger" :underline="false" style="margin-left: 10px" @click="delNotice(scope.row)"> 删除 </el-link>
              <el-link v-if="scope.row.status == '2'" type="warning" :underline="false" style="margin-left: 10px" @click="withdrawNotice(scope.row)">撤回</el-link>
            </template>
          </el-table-column>
        </el-table>
        <div class="table-page">
          <el-pagination
            class="pagination imas_page"
            :current-page="paginationData.currentPage"
            :page-sizes="[15, 30, 50, 100]"
            :page-size="paginationData.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="paginationData.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
      <noticeDetail ref="noticeDetail" :detailObj="detailObj"></noticeDetail>
    </div>
  </PageContainer>
</template>
<script>
import noticeDetail from './noticeDetail.vue'
export default {
  components: {
    noticeDetail
  },
  data() {
    let self = this
    return {
      activeNames: [''],
      select: {
        title: '',
        typeCode: '',
        status: '',
        issuerName: ''
      },
      tableData: [],
      tableHeight: '',
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      noticeType: [
        {
          id: '0',
          name: '草稿'
        },
        {
          id: '1',
          name: '待发布'
        },
        {
          id: '2',
          name: '已发布'
        },
        {
          id: '3',
          name: '已撤销'
        }
      ],
      leibie: '',
      tableLoading: false,
      row: {},
      dialogVisibleRole: false,
      statusList: [],
      typeOptions: {
        lazy: true,
        async lazyLoad(node, resolve) {
          if (node.level == 0) {
            const item = [
              {
                value: 0,
                label: '全院公告',
                leaf: true
              },
              {
                value: 1,
                label: '部门公告',
                leaf: false
              }
            ]
            resolve(item)
          } else if (node.level == 1) {
            const data = await self.getUnitListFn()
            resolve(data)
          } else {
            const data = await self.getDeptListFn(node.data.value)
            resolve(data)
          }
        }
      },
      detailObj: {}
    }
  },
  mounted() {
    this.getNoticeList()
  },
  methods: {
    async getUnitListFn() {
      try {
        const res = await this.$api.getUnitList({})
        if (res.code == 200) {
          const item = res.data.map((i) => {
            return {
              label: i.unitComName,
              value: i.umId,
              leaf: false
            }
          })
          return item
        }
      } catch (error) {
        console.error('获取单位列表失败:', error)
      }
    },
    async getDeptListFn(unitId) {
      try {
        const res = await this.$api.departList({
          current: 1,
          size: 999,
          unitId
        })
        if (res.code == 200) {
          const item = res.data.records.map((i) => {
            return {
              label: i.deptName,
              value: i.id,
              leaf: true
            }
          })
          return item
        }
      } catch (error) {
        console.error('获取单位列表失败:', error)
      }
    },
    getNoticeList() {
      this.tableLoading = true
      const params = {
        title: this.select.title,
        typeCode: this.select.typeCode ? (this.select.typeCode == '0' ? '0' : '1') : '',
        officeId: '',
        status: this.select.status,
        issuerName: this.select.issuerName,
        pageNum: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize
      }
      if (params.typeCode == '1') {
        params.officeId = this.select.typeCode[this.select.typeCode.length - 1]
      }
      this.$api.rentalHousingApi
        .noticeDataList(params)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data.records
            this.paginationData.total = res.data.total
          } else {
            this.$message.error(res.message || '获取失败')
          }
        })
        .catch(() => {
          this.tableLoading = this.$store.state.loadingShow
        })
    },
    issueNotice(row) {
      this.$confirm('确认发布这条公示?', '提醒', { type: 'warning' }).then((res) => {
        this.$api.rentalHousingApi
          .noticePublish({
            id: row.noticeId
          })
          .then((res) => {
            if (res.code == 200) {
              this.$message.success(res.message)
            } else {
              this.$message.error(res.message)
            }
            this.getNoticeList()
          })
      })
    },
    withdrawNotice(row) {
      this.$confirm('确认撤回这条公示?', '提醒', { type: 'warning' }).then((res) => {
        this.$api.rentalHousingApi
          .noticeWithdraw({
            id: row.noticeId
          })
          .then((res) => {
            if (res.code == 200) {
              this.$message.success(res.message)
            } else {
              this.$message.error(res.message)
            }
            this.getNoticeList()
          })
      })
    },
    delNotice(row) {
      this.$confirm('确认删除这条公示?', '提醒', { type: 'warning' }).then((res) => {
        this.$api.rentalHousingApi
          .noticeDelete({
            id: row.noticeId
          })
          .then((res) => {
            if (res.code == 200) {
              this.$message.success(res.message)
            } else {
              this.$message.error(res.message)
            }
            this.getNoticeList()
          })
      })
    },
    // 查看公示
    LockNotice(row) {
      this.detailObj = row
      this.$refs.noticeDetail.dialogVisibleRole = true
    },
    handleRowClick(row, column, event) {
      this.LockNotice(row)
    },
    // 查询列表
    queryList(i) {
      if (i) {
        this.paginationData.currentPage = 1
        this.paginationData.pageSize = 15
      }
      this.getNoticeList()
    },
    reset() {
      this.select.title = ''
      this.select.typeCode = ''
      this.select.status = ''
      this.select.issuerName = ''
      this.getNoticeList()
    },
    addNotice(row) {
      if (row) {
        this.$router.push({
          path: '/addPublicity',
          query: {
            id: row.noticeId
          }
        })
      } else {
        this.$router.push({
          path: '/addPublicity'
        })
      }
    },
    handleChange(val) {},
    handleSelectionChange(val) {
      this.multipleSelection = val
      this.queryList()
    },
    handleSizeChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      this.queryList()
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.queryList()
    }
  }
}
</script>
<style lang="scss" scoped>
.space-content {
  height: 100%;
  overflow: auto;
  padding: 10px 20px 20px 20px;
  background: #fff;
  border-radius: 0px 4px 4px 0px;
  .table-list {
    height: calc(100% - 168px);
  }
}
.ipt-title {
  width: 94px;
  display: inline-block;
  text-align: right;
  white-space: nowrap;
  margin-top: 20px;
}
.left {
  margin-right: 130px;
  margin-top: 20px;
}
.left:last-child {
  margin-right: 30px;
}
.el-input,
.el-select {
  width: calc((100% / 3) - 94px - 8.5%);
}
.ifBr {
  display: none;
}
.operation-text {
  width: 40px;
  display: inline-block;
  margin-right: 5px;
  color: #fff;
  text-align: center;
}
.dialog-contnet {
  .caption {
    height: 36px;
    line-height: 36px;
    border-bottom: 1px solid #ddd;
    .caption-text {
      display: inline-block;
      margin-right: 40px;
    }
  }
  .preview {
    .ql-editor {
      max-height: 328px;
      overflow: auto;
    }
  }
}
</style>
