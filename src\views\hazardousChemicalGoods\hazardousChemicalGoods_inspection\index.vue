<template>
  <PageContainer v-loading="blockLoading">
    <div slot="content" class="sino-part-right">
      <div class="right_search">
        <div class="right_search_input">
          <el-input v-model="searchInfo.inspectionTableName" placeholder="检查表名称" clearable size="medium"> </el-input>
          <el-input v-model="searchInfo.inspectorName" placeholder="检查人"></el-input>
          <el-date-picker
            v-model="searchInfo.date"
            type="daterange"
            unlink-panels
            size="medium"
            style="width: 260px"
            range-separator="至"
            start-placeholder="检查日期起"
            end-placeholder="检查日期止"
            value-format="yyyy-MM-dd"
            @change="handleDatePickerChange"
          >
          </el-date-picker>
          <el-button type="primary" plain size="medium" style="margin-left: 15px" @click="handleResetData">重置</el-button>
          <el-button type="primary" size="medium" @click="handleQuery">查询</el-button>
        </div>
      </div>
      <div class="right_table_box">
        <el-table
          ref="table"
          key="id"
          v-loading="tableLoading"
          :data="tableData"
          :header-cell-style="$tools.setHeaderCell(3)"
          height="100%"
          :border="true"
          style="width: 100%"
          :cell-style="{ padding: '8px 0' }"
          stripe
          title="双击查看详情"
        >
          <template slot="empty">
            <p class="tableEmptyLabel">{{ $store.state.tableLabel }}</p>
          </template>
          <el-table-column type="index" width="100" label="序号" fixed="left" :align="'center'">
            <template slot-scope="scope">
              <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="inspectionTableName" label="检查表名称" :align="'center'" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="checkSide" label="检查端" :align="'center'" min-width="120" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ String(scope.row.checkSide) === '0' ? 'PC' : String(scope.row.checkSide) === '1' ? 'APP' : '' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="inspectionDate" label="检查日期" :align="'center'" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="inspectorName" label="检查人" :align="'center'" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="assistantInspectors" label="同行人" :align="'center'" min-width="120" show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-if="scope.row.assistantInspectors">
                <span v-for="(item, index) in JSON.parse(scope.row.assistantInspectors)" :key="index">{{
                  `${item.peerReviewerName}${index == JSON.parse(scope.row.assistantInspectors).length - 1 ? '' : '、'}`
                }}</span>
              </span>
              <span v-else></span>
            </template>
          </el-table-column>
          <el-table-column prop="resultYes" label="检查结果" :align="'center'" min-width="120" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ `是: ${scope.row.resultYes || 0}项，否: ${scope.row.resultNo || 0}项` }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="createDate" label="填表时间" :align="'center'" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="name" label="操作" width="180" fixed="left" :align="'center'">
            <template slot-scope="scope">
              <el-link type="primary " :underline="false" @click="dblclick(scope.row)">查看</el-link>
            </template>
          </el-table-column>
        </el-table>
        <div class="table-pagination">
          <el-pagination
            :current-page="paginationData.currentPage"
            :page-sizes="[15, 30, 50, 100]"
            :page-size="paginationData.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="paginationData.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
export default {
  data() {
    return {
      blockLoading: false,
      tableLoading: false,
      delDialogVisible: false,
      panelType: 'list',
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      treeFilterText: '',
      treeData: [
        {
          id: '#',
          label: '专项检查表',
          children: []
        }
      ],
      tableData: [],
      searchInfo: {
        inspectionTableName: '',
        hospitalCode: '',
        inspectorName: '',
        date: [],
        startTime: '',
        endTime: ''
      },
      adminType: '',
      delId: '',
      ruleForm: {
        delReason: ''
      },
      rules: {
        delReason: [{ required: true, message: '请输入删除原因', trigger: 'blur' }]
      }
    }
  },
  watch: {
    treeFilterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    /** 重置 */
    handleResetData() {
      this.searchInfo = {
        inspectionTableName: '',
        inspectionTableId: '',
        hospitalCode: '',
        inspectorName: '',
        date: [],
        startTime: '',
        endTime: ''
      }
      this.paginationData.currentPage = 1
      this.getTableData()
    },
    /** 查询 */
    handleQuery() {
      this.paginationData.currentPage = 1
      this.getTableData()
    },
    /** 查看详情 */
    dblclick(row) {
      sessionStorage.setItem('checkInfo', JSON.stringify(row))
      this.$router.push({
        name: 'hsc_checkTableDetail',
        query: {
          id: row.id
        }
      })
    },
    /** 获取列表数据 */
    getTableData() {
      let params = {
        ...this.searchInfo,
        currentPage: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize,
        confirmationStatus: 1
      }
      delete params.date
      if (this.searchInfo.inspectionTableId === '#') {
        params.inspectionTableId = ''
      }
      this.tableLoading = true
      this.$api.getCheckTableData(params).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.tableData = res.data.list
          this.paginationData.total = res.data.sum
        } else {
          this.$message.error(res.message)
        }
      })
    },
    /** 时间事件 */
    handleDatePickerChange(val) {
      if (val && val.length) {
        this.searchInfo.startTime = `${val[0]} 00:00:00`
        this.searchInfo.endTime = `${val[1]} 23:59:59`
      } else {
        this.searchInfo.startTime = ''
        this.searchInfo.endTime = ''
      }
    },
    /** 条数变化 事件 */
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.getTableData()
    },
    /** 页数变化 事件 */
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getTableData()
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .container-content {
  background-color: #fff;
  padding: 20px;
}
.sino-part-right {
  height: 100%;
  .right_search {
    display: flex;
    justify-content: space-between;
    .el-input,
    .el-select {
      width: 200px;
      margin-right: 15px;
    }
  }
  .right_table_box {
    margin-top: 15px;
    height: calc(100% - 125px);
    .table-pagination {
      text-align: right;
      margin-top: 15px;
    }
  }
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  span:first-child {
    width: 180px;
    white-space: nowrap;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.el-tooltip {
  display: inline-block;
  width: 180px;
}
.el-form-item {
  width: 100%;
}
::v-deep .del-input .el-input__inner {
  padding-right: 52px;
}
::v-deep .el-dialog {
  max-height: 750px;
  min-width: 200px;
  min-height: 200px;
}
::v-deep .el-dialog__body {
  box-sizing: border-box;
  margin: 0px;
}
::v-deep .el-dialog__footer {
  position: relative;
}
::v-deep .el-input__inner {
  height: 32px;
}
</style>
