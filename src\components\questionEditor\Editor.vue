<template>
  <div>
    <quill-editor class="editor" v-model="content" ref="myQuillEditor" :options="editorOption"
      @blur="onEditorBlur($event)" @focus="onEditorFocus($event)" @change="onEditorChange($event)"></quill-editor>
    <!-- <el-upload
      class="avatar-uploader"
      style="width:0;height:0"
      :action="serverUrl"
      name="multipartFile"
      :headers="header"
      :data="ruleForm"
      :show-file-list="false"
      accept=".jpg, .jpeg, .png, .JPG, .JPEG, .bmp"
      :on-success="uploadSuccess"
      :on-error="uploadError"
      :before-upload="beforeUpload"
    ></el-upload>-->
    <el-upload class="avatar-uploader" style="width:0;height:0" action="string" name="multipartFile"
      :show-file-list="false" accept=".jpg, .jpeg, .png, .JP<PERSON>, .JPEG, .bmp" :before-upload="beforeUpload"
      :http-request="httpRequest"></el-upload>
  </div>
</template>
<script>
// 工具栏配置
const toolbarOptions = [
  ["bold", "italic"], // 加粗 斜体 下划线 删除线
  [{ header: 1 }, { header: 2 }], // 1、2 级标题
  [{ list: "ordered" }, { list: "bullet" }], // 有序、无序列表
  [{ size: ["small", false, "large", "huge"] }], // 字体大小
  [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题
  [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色
  [{ align: [] }], // 对齐方式
  ["clean"], // 清除文本格式
  ["image"], // 链接、图片、视频
];

import { quillEditor } from "vue-quill-editor";
import axios from "axios";

// import {uploadImg} from "../common/api";

export default {
  props: {
    /*编辑器的内容*/
    value: {
      type: String,
    },
    /*图片大小*/
    maxSize: {
      type: Number,
      default: 4000, //kb
    },
  },

  components: {
    quillEditor,
  },

  data() {
    return {
      content: this.value,
      obj: {},
      photoFile: "",
      quillUpdateImg: false, // 根据图片上传状态来确定是否显示loading动画，刚开始是false,不显示
      editorOption: {
        theme: "snow", // or 'bubble'
        placeholder: "请输入文本内容",
        modules: {
          toolbar: {
            container: toolbarOptions, // 工具栏
            handlers: {
              image: function (value) {
                if (value) {
                  document.querySelector(".avatar-uploader input").click();
                } else {
                  this.quill.format("image", false);
                }
              },
            },
          },
        },
      },
      serverUrl: `${process.env.IUMS_HOST}textImageController/addImage`, // 这里写你要上传的图片服务器地址
      ruleForm: {},
      header: {
        // 'Access-Control-Allow-Headers':'*'
        // token: sessionStorage.token
      }, // 有的图片服务器要求请求头需要有token
      routeInfo: {}
    };
  },
  mounted() {
    this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
    // this.serverUrl = `?unitCode=${this.$store.state.loginInfo.unitCode}&hospitalCode=${this.$store.state.loginInfo.hospitalCode}`
    // this.ruleForm.unitCode = this.$store.state.loginInfo.unitCode
    // this.ruleForm.hospitalCode = this.$store.state.loginInfo.hospitalCode
  },
  methods: {
    onEditorBlur() {
      //失去焦点事件
    },
    onEditorFocus() {
      //获得焦点事件
    },
    onEditorChange() {
      //内容改变事件
      this.$emit("input", this.content);
    },
    httpRequest(item) {
      ;
      let formData = new FormData();
      formData.append("file", item.file);
      axios({
        method: "post",
        url: __PATH.BASE_URL + "minio/upload",
        data: formData,
        headers: {
          token: this.routeInfo.token,
        },
      })
        .then((res) => {
          if (res.data.code == 200) {
            let quill = this.$refs.myQuillEditor.quill;
            let length = quill.getSelection().index;
            quill.insertEmbed(length, "image", res.data.data.viewAddress);
            // 调整光标到最后
            quill.setSelection(length + 1);
          } else {
            this.$message.error("图片插入失败");
          }
        })
        .catch(() => {
          this.$message.error("上传失败");
        });
    },
    // 富文本图片上传前
    beforeUpload(file) {
      // 显示loading动画
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        this.$message.error("上传头像图片大小不能超过 5MB!");
      }
      this.quillUpdateImg = true;
      return isLt5M;
    },

    uploadSuccess(res, file) {
      // res为图片服务器返回的数据
      // 获取富文本组件实例
      let quill = this.$refs.myQuillEditor.quill;
      if (res.code == 200) {
        // 获取光标所在位置
        let length = quill.getSelection().index;
        // 插入图片  res.data.imgurl为服务器返回的图片地址
        // quill.insertEmbed(length, "image", res.data.imgurl);
        quill.insertEmbed(
          length,
          "image",
          res.data.signature + "&id=" + res.data.id + "iums="
        );
        // 调整光标到最后
        quill.setSelection(length + 1);
      } else {
        this.$message.error("图片插入失败");
      }
      // loading动画消失
      this.quillUpdateImg = false;
    },
    // 富文本图片上传失败
    uploadError() {
      // loading动画消失
      this.quillUpdateImg = false;
      this.$message.error("图片插入失败");
    },
  },
};
</script>

<style>
.editor em {
  font-style: italic !important;
}

.editor strong {
  font-weight: bold !important;
}

.editor {
  line-height: normal !important;
  height: 300px;
}

.ql-snow .ql-tooltip[data-mode="link"]::before {
  content: "请输入链接地址:";
}

.ql-snow .ql-tooltip.ql-editing a.ql-action::after {
  border-right: 0px;
  content: "保存";
  padding-right: 0px;
}

.ql-snow .ql-tooltip[data-mode="video"]::before {
  content: "请输入视频地址:";
}

.ql-snow .ql-picker.ql-size .ql-picker-label::before,
.ql-snow .ql-picker.ql-size .ql-picker-item::before {
  content: "14像素";
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="small"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="small"]::before {
  content: "10像素";
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="large"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="large"]::before {
  content: "18像素";
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="huge"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="huge"]::before {
  content: "32像素";
}

.ql-snow .ql-picker.ql-header .ql-picker-label::before,
.ql-snow .ql-picker.ql-header .ql-picker-item::before {
  content: "文本";
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
  content: "标题1";
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
  content: "标题2";
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
  content: "标题3";
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="4"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
  content: "标题4";
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="5"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
  content: "标题5";
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="6"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
  content: "标题6";
}

.ql-snow .ql-picker.ql-font .ql-picker-label::before,
.ql-snow .ql-picker.ql-font .ql-picker-item::before {
  content: "标准字体";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="serif"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="serif"]::before {
  content: "衬线字体";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="monospace"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="monospace"]::before {
  content: "等宽字体";
}
</style>