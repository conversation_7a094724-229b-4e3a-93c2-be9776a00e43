<template>
  <div>
    <!-- 服务的空间 -->
    <template v-if="serviceSpaceDialogShow">
      <serviceSpaceDialog
        :serviceSpaceDialogShow="serviceSpaceDialogShow"
        :spaceData="spaceData"
        @submitServiceSpaceDialog="submitServiceSpaceDialog"
        @closeServiceSpaceDialog="closeServiceSpaceDialog"
      />
    </template>
    <!-- 关联摄像机 -->
    <template v-if="cameraDialogShow">
      <cameraDialog :cameraDialogShow="cameraDialogShow" :cameraDialogData="cameraDialogData" :limitSelectLength="limitSelectLength" @submitCameraDialog="submitCameraDialog" @closeCameraDialog="closeCameraDialog" />
    </template>
    <!-- 关联设备资产 -->
    <template v-if="equipmentAssetsDialogShow">
      <equipmentAssetsDialog
        :dialogShow="equipmentAssetsDialogShow"
        :dialogData="equipmentAssetsDialogData"
        @submitDialog="submitEquipmentAssetsDialog"
        @closeDialog="closeEquipmentAssetsDialog"
      />
    </template>
  </div>
</template>
<script>
import serviceSpaceDialog from './serviceSpaceDialog'
import cameraDialog from './cameraDialog'
import equipmentAssetsDialog from './equipmentAssetsDialog'
export default {
  name: 'monitoringParamsGather',
  components: {
    serviceSpaceDialog,
    cameraDialog,
    equipmentAssetsDialog
  },
  props: {
    serviceSpaceDialogShow: {
      type: Boolean,
      default: false
    },
    cameraDialogShow: {
      type: Boolean,
      default: false
    },
    equipmentAssetsDialogShow: {
      type: Boolean,
      default: false
    },
    spaceData: {
      type: Array,
      default: () => []
    },
    cameraDialogData: {
      type: String,
      default: ''
    },
    limitSelectLength: {
      type: Number,
      default: 4
    },
    equipmentAssetsDialogData: {
      type: Object,
      default: () => {}
    },
    assetsClickType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  },
  methods: {
    // 服务空间事件------------start
    submitServiceSpaceDialog(data) {
      this.$emit('submitDialog', {
        ...data,
        serviceSpaceDialogShow: false
      })
    },
    closeServiceSpaceDialog() {
      this.$emit('submitDialog', {
        serviceSpaceDialogShow: false
      })
    },
    // 服务空间事件------------end
    // 摄像机事件------------start
    submitCameraDialog(data) {
      this.$emit('submitDialog', {
        cameraDialogShow: false,
        imsVidiconList: data.map((e) => {
          return {
            imsVidiconId: e.icmId,
            imsVidiconName: e.icmName
          }
        }),
        // imsVidiconId: data.icmId.toString(),
        // imsVidiconName: data.icmName.toString(),
        type: 'camera'
      })
    },
    closeCameraDialog() {
      this.$emit('submitDialog', {
        cameraDialogShow: false
      })
    },
    // 摄像机事件------------end
    // 设备资产事件------------start
    submitEquipmentAssetsDialog(data) {
      const assetsData = this.assetsClickType === 'relation' ? 'assetsData' : 'assetsServiceData'
      this.$emit('submitDialog', {
        equipmentAssetsDialogShow: false,
        [assetsData]: data,
        type: this.assetsClickType === 'relation' ? 'assets' : ''
      })
    },
    closeEquipmentAssetsDialog() {
      this.$emit('submitDialog', {
        equipmentAssetsDialogShow: false
      })
    }
  }
}
</script>
