<template>
  <PageContainer>
    <div slot="content" class="targetLibrary-body">
      <div class="targetLibrary-content-title">指标库</div>
      <div class="targetLibrary-content">
        <div class="targetLibrary-content-left">
          <div class="search">
            <el-input v-model="filterText" placeholder="请输入分类" clearable suffix-icon="el-icon-search"> </el-input>
          </div>
          <div v-loading class="left_content">
            <el-tree
              ref="tree"
              v-loading="treeLoading"
              :data="treeData"
              :props="defaultProps"
              node-key="node"
              :highlight-current="true"
              :filter-node-method="filterNode"
              @node-click="handleNodeClick"
            ></el-tree>
          </div>
        </div>
        <div class="targetLibrary-content-right">
          <div style="height: 100%">
            <div class="search-from">
              <div>
                <el-input
                  v-model.trim="keyWord"
                  placeholder="请输入指标名称"
                  style="width: 200px"
                  clearable
                  maxlength="25"
                  onkeyup="if(value.length>25)value=value.slice(0,25)"
                ></el-input>
                <el-button type="primary" plain style="margin-left: 10px" @click="resetForm">重置</el-button>
                <el-button type="primary" @click="searchForm">查询</el-button>
              </div>
            </div>
            <div class="contentTable">
              <div class="contentTable-main table-content">
                <el-table v-loading="tableLoading" border style="width: 100%" :data="tableData" :height="tableHeight" :row-class-name="tableRowClassName">
                  <el-table-column prop="name" label="指标名称" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="type" label="指标类型" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <span> {{ getTypeName(scope.row.type) }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="nodeName" label="指标分类" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="dataType" label="数据类型" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <span> {{ scope.row.dataType == 0 ? '数值' : scope.row.dataType == 1 ? '百分比' : '时长' }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="remark" label="参考值" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="illustrate" label="指标说明" show-overflow-tooltip></el-table-column>
                </el-table>
              </div>
              <div class="contentTable-footer">
                <el-pagination
                  :current-page="pagination.current"
                  :page-sizes="pagination.pageSizeOptions"
                  :page-size="pagination.size"
                  :layout="pagination.layoutOptions"
                  :total="pagination.total"
                  @size-change="paginationSizeChange"
                  @current-change="paginationCurrentChange"
                >
                </el-pagination>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin.js'
import { transData } from '@/util'
export default {
  name: 'targetLibrary',
  components: {},
  mixins: [tableListMixin],
  data() {
    return {
      filterText: '',
      keyWord: '', // 查询关键字
      treeData: [],
      checkedData: {}, // 选中tree数据
      defaultProps: {
        label: 'nodeName',
        children: 'nodeList'
      },
      expanded: [],
      filters: {
        dictName: ''
      },
      tableLoading: false,
      tableData: [],
      treeLoading: false,
      treeLevel: 0, // 层级
      nodeName: '',
      nodeCode: '',
      nodeType: ''
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  mounted() {
    this.getTreeData()
    this.getDataList()
  },
  methods: {
    getTypeName(type) {
      if (type == 0) {
        return '组织架构'
      } else if (type == 1) {
        return '设备'
      } else if (type == 2) {
        return '空间'
      } else if (type == 3) {
        return '值班考勤'
      } else if (type == 4) {
        return '业务'
      }
    },
    // 过滤节点
    filterNode(value, data) {
      if (!value) return true
      return data.nodeName.indexOf(value) !== -1
    },
    getTreeData() {
      this.treeLoading = true
      this.$api
        .getLibraryTreeList({})
        .then((res) => {
          this.treeLoading = false
          if (res.code == 200) {
            this.treeData = res.data ? res.data : []
          } else {
            this.$message.error(res.message)
          }
        })
        .catch((err) => {
          this.treeLoading = false
        })
    },
    getDataList() {
      this.tableLoading = true
      let data = {
        nodeCode: this.treeLevel == 1 ? '' : this.nodeCode,
        nodeName: this.treeLevel == 1 ? '' : this.nodeName,
        name: this.keyWord,
        page: this.pagination.current,
        pageSize: this.pagination.size,
        type: this.nodeType
      }
      this.$api
        .getLibraryPageList(data)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data ? res.data.records : []
            this.pagination.total = res.data ? res.data.total : 0
          } else if (res.message) {
            this.tableData = []
            this.pagination.total = 0
            this.$message.error(res.message)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    handleNodeClick(val, node) {
      this.treeLevel = node.level
      this.nodeName = val.nodeName
      this.nodeCode = val.node
      this.nodeType = this.treeLevel == '1' ? val.node : val.type
      this.getDataList()
    },
    // table隔行变色
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex % 2 == 1) {
        return 'warning-row'
      } else {
        return 'success-row'
      }
    },
    // 重置
    resetForm() {
      this.keyWord = ''
      this.pagination.size = 15
      this.pagination.current = 1
      this.getDataList()
    },
    // 查询
    searchForm() {
      this.pagination.current = 1
      this.getDataList()
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}
::v-deep .el-tree-node__content {
  height: 32px;
}
.targetLibrary-body {
  height: 100%;
  width: 100%;
  background: #fff;
  .targetLibrary-content-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 13px 16px;
  }
  .targetLibrary-content {
    display: flex;
    margin-top: 16px;
    padding: 0 24px;
    height: calc(100% - 80px);
    .targetLibrary-content-left {
      width: 246px;
      height: 100%;
      background: #fff;
      border: 1px solid #e5e6eb;
      .search {
        padding: 16px 12px;
      }
      .left_content {
        width: 100%;
        height: calc(100% - 65px);
        border-radius: 4px;
        overflow: scroll;
      }
    }
    .targetLibrary-content-right {
      height: 100%;
      min-width: 0;
      padding: 0 20px;
      background: #fff;
      border-radius: 4px;
      flex: 1;
      .search-from {
        padding-bottom: 12px;
        display: flex;
        justify-content: space-between;
        & > div {
          margin-right: 10px;
        }
        & > button {
          margin-top: 12px;
        }
      }
      .contentTable {
        height: calc(100% - 50px);
        display: flex;
        flex-direction: column;
        .contentTable-main {
          flex: 1;
          overflow: auto;
        }
        .contentTable-footer {
          padding: 10px 0 0;
        }
      }
    }
    .content {
      width: 100%;
      max-height: 500px !important;
      overflow: auto;
      background-color: #fff !important;
    }
  }
}
.record {
  color: #3562db !important;
}
::v-deep .el-tree-node {
  white-space: normal;
  // outline: 0;
}
::v-deep .warning-row {
  background-color: #f5f5f5 !important;
}
</style>
