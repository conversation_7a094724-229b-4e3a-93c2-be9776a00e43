<template>
  <PageContainer>
    <div slot="header">
      <div class="searchForm">
        <div class="search-box">
          <div class="btns">
            <div :class="['date-btn', dateType == 'day' ? 'active-btn' : '']" @click="changeDateType('day')">今日</div>
            <div :class="['date-btn', dateType == 'month' ? 'active-btn' : '']" @click="changeDateType('month')">本月</div>
            <div :class="['date-btn', dateType == 'year' ? 'active-btn' : '']" @click="changeDateType('year')">本年</div>
            <div :class="['date-btn', dateType == 'all' ? 'active-btn' : '']" @click="changeDateType('all')">全部</div>
            <div :class="['date-btn', dateType == 'custom' ? 'active-btn' : '']" @click="changeDateType('custom')">自定义</div>
          </div>
          <!-- <el-date-picker v-show="dateType == 'custom'" v-model="beginGatherTime" type="datetime" placeholder="选择开始时间" value-format="yyyy-MM-dd HH:mm:ss"> </el-date-picker>
          <el-date-picker v-show="dateType == 'custom'" v-model="endGatherTime" type="datetime" placeholder="选择结束时间" value-format="yyyy-MM-dd HH:mm:ss"> </el-date-picker> -->

          <el-date-picker
            v-if="dateType == 'custom'"
            v-model="customDate"
            type="datetimerange"
            start-placeholder="开始日期"
            range-separator="至"
            end-placeholder="结束日期"
            :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>

          <div v-show="dateType == 'custom'" style="margin-left: 16px">
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button type="primary" plain @click="reset">重置</el-button>
          </div>
        </div>
        <div>
          <el-button type="primary" plain @click="goManualEntry">手动录入</el-button>
          <el-button type="primary" plain @click="gobatchImport">批量导入</el-button>
        </div>
      </div>
    </div>
    <div slot="content" ref="contentRef">
      <div class="cards">
        <div class="left-card">
          <img src="@/assets/images/operationPort/waste-icon2.png" />
          <span>收集重量</span>
          <span>{{ sumData.gatherWeigh }}</span>
          <span>kg</span>
        </div>
        <div class="right-card">
          <img src="@/assets/images/operationPort/waste-icon1.png" />
          <span>收集数量</span>
          <span>{{ sumData.count }}</span>
          <span>袋（件）</span>
        </div>
      </div>
      <div class="table-box">
        <el-table v-loading="tableLoading" :data="tableData" border style="width: 100%" height="90%">
          <el-table-column label="所属科室" prop="officeName" :resizable="false" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="医废数量" prop="count" width="100" :resizable="false" align="center"></el-table-column>
          <el-table-column label="医废编码" prop="barCode" width="180" :resizable="false" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="医废类型" prop="wasteType" width="100" :resizable="false" align="center"></el-table-column>
          <el-table-column label="收集时间" prop="gatherTime" width="180" :resizable="false" align="center"></el-table-column>
          <el-table-column label="收集重量" prop="gatherWeigh" width="80" :resizable="false" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.gatherWeigh }}kg</span>
            </template>
          </el-table-column>
          <el-table-column label="收集人员" prop="receivedPersonName" width="180" :resizable="false" align="center"></el-table-column>
          <el-table-column label="收集人员签字" width="120" :resizable="false" align="center">
            <template slot-scope="scope">
              <span style="color: #5188fc; cursor: pointer" @click="showPicture(scope.row)">查看图片</span>
            </template>
          </el-table-column>
          <el-table-column label="科室人员签字" width="120" :resizable="false" align="center">
            <template slot-scope="scope">
              <span style="color: #5188fc; cursor: pointer" @click="showPicture2(scope.row)">查看图片</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100" :resizable="false" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.inventoryStatus == 1 || scope.row.inventoryStatus == 4">已收集</span>
              <span v-else-if="scope.row.inventoryStatus == 2">已入站</span>
              <span v-else-if="scope.row.inventoryStatus == 3">已出站</span>
            </template>
          </el-table-column>
          <el-table-column label="医废追溯" width="80" :resizable="false" align="center">
            <template slot-scope="scope">
              <div>
                <span style="color: #5188fc; cursor: pointer" @click="selectConfigRowData(scope.row.id)">追溯</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="pageNo"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
        <imgCarousel :dialogVisibleImg="dialogVisibleImg" :imgArr="imgArr" @closeDialog="closeDialogImg"></imgCarousel>
        <template v-if="retrospectShow">
          <retrospect ref="retrospect" :dialogShow="retrospectShow" :detailId="detailId" @retrospectCloseDialog="retrospectCloseDialog"></retrospect>
        </template>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import moment from 'moment'
import store from '@/store/index'
import imgCarousel from '../spaceManage/common/imgCarousel.vue'
import retrospect from '../spaceManage/components/retrospect.vue'
export default {
  name: 'collectionRecord',
  components: {
    imgCarousel,
    retrospect
  },
  // beforeRouteEnter(to, from, next) {
  //   let names = []
  //   to.matched.map((v, i) => {
  //     if (i > 0) {
  //       v.components.default.name && names.push(v.components.default.name)
  //     }
  //   })
  //   // 进入页面时，先将当前页面的 name 信息存入 keep-alive 全局状态
  //   setTimeout(() => {
  //     next((vm) => {
  //       vm.$store.commit('keepAlive/add', names)
  //     })
  //   }, 0)
  // },
  data() {
    return {
      moment,
      dateType: 'day',
      customDate: [],
      beginGatherTime: '',
      endGatherTime: '',
      tableLoading: false,
      tableData: [],
      pageNo: 1,
      pageSize: 15,
      total: 0,
      imgArr: [],
      dialogVisibleImg: false,
      retrospectShow: false,
      sumData: {
        count: 0,
        gatherWeigh: 0
      }
    }
  },
  created() {},
  mounted() {
    this.getTableData()
    this.getSum()
  },
  methods: {
    changeDateType(type) {
      this.dateType = type
      if (type != 'custom') {
        // this.beginGatherTime = ''
        // this.endGatherTime = ''
        this.customDate = []
        this.getTableData()
        this.getSum()
      }
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.pageNo = 1
      this.getTableData()
    },
    getTableData() {
      this.tableLoading = true
      let params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        dateType: this.dateType,
        // beginGatherTime: this.beginGatherTime,
        // endGatherTime: this.endGatherTime
        beginGatherTime: this.customDate[0] ? moment(this.customDate[0]).format('YYYY-MM-DD HH:mm:ss') : '',
        endGatherTime: this.customDate[1] ? moment(this.customDate[1]).format('YYYY-MM-DD HH:mm:ss') : ''
      }
      if (params.dateType == 'custom') {
        params.dateType = 'all'
      }
      // if (params.beginGatherTime) {
      //   let arr = params.beginGatherTime.split('+')
      //   params.beginGatherTime = arr[0] + '+' + encodeURIComponent(arr[1])
      // }
      // if (params.endGatherTime) {
      //   let arr = params.endGatherTime.split('+')
      //   params.endGatherTime = arr[0] + '+' + encodeURIComponent(arr[1])
      // }
      this.$api.getCollectionRecord(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.data.rows
        this.total = res.data.total
      })
    },
    handleCurrentChange(val) {
      this.pageNo = val
      this.getTableData()
    },
    goManualEntry() {
      this.$router.push({ path: '/wasteManage/inoutManage/manualEntry' })
    },
    gobatchImport() {
      this.$router.push({ path: '/wasteManage/inoutManage/batchImport' })
    },
    showPicture2(row) {
      if (row.officeSignature) {
        // let str = row.ossFilePrefix + row.officeSignature
        let str = this.$tools.imgUrlTranslation(row.officeSignature)
        this.imgArr.push(str)

        this.dialogVisibleImg = true
      } else {
        this.$message.error('无科室人员签名')
      }
    },
    showPicture(row) {
      if (row.receivedSignature) {
        // let str = row.ossFilePrefix + row.receivedSignature
        let str = this.$tools.imgUrlTranslation(row.receivedSignature)
        this.imgArr.push(str)
        this.dialogVisibleImg = true
      } else {
        this.$message.error('无收集人员签名')
      }
    },
    closeDialogImg() {
      this.dialogVisibleImg = false
      this.imgArr = []
    },
    selectConfigRowData(id) {
      this.detailId = id
      this.retrospectShow = true
    },
    retrospectCloseDialog() {
      this.retrospectShow = false
    },
    reset() {
      this.dateType = 'day'
      // this.beginGatherTime = ''
      // this.endGatherTime = ''
      this.customDate = []
      this.getTableData()
      this.getSum()
    },
    getSum() {
      let params = {
        dateType: this.dateType,
        // beginGatherTime: this.beginGatherTime,
        // endGatherTime: this.endGatherTime
        beginGatherTime: this.customDate[0] ? moment(this.customDate[0]).format('YYYY-MM-DD HH:mm:ss') : '',
        endGatherTime: this.customDate[1] ? moment(this.customDate[1]).format('YYYY-MM-DD HH:mm:ss') : ''
      }
      if (params.dateType == 'custom') {
        params.dateType = 'all'
      }
      this.$api.getCollectionRecordSum(params).then((res) => {
        this.sumData = res.data
      })
    },
    handleSearch() {
      this.getTableData()
      this.getSum()
    }
  }
}
</script>
<style lang="scss" scoped>
.searchForm {
  display: flex;
  height: 80px;
  padding: 0 16px;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .el-input {
    width: 200px;
    margin-right: 8px;
  }

  > div {
    margin-right: 20px;
  }
}

.search-box {
  display: flex;
  align-items: center;
}

.container-content > div {
  height: 100%;
  margin-top: 16px;
}

.el-table {
  margin-bottom: 12px;
}

::v-deep .el-date-editor .el-range__icon {
  line-height: 24px;
}

.statistics {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  height: 78px;
}

.statistics .item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  flex: 1;
  background-color: rgb(53 98 219 / 6%);
  margin-right: 8px;
  border-radius: 4px;
}

.statistics .item span:nth-child(2) {
  font-size: 18px;
  color: #3562db;
  font-weight: 700;
}

.statistics .hover-style {
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgb(32 56 114 / 13%);
  border-bottom: 3px solid #3562db;
}

.statistics .pointer-style {
  cursor: pointer;
}

.statistics .pure {
  background-color: #fff;
}

.statistics .pure span:nth-child(2) {
  color: #121f3e;
}

::v-deep .el-dialog {
  height: 80vh;
  margin-top: 10vh;
}

::v-deep .el-dialog__wrapper {
  overflow: hidden;
}

::v-deep .el-dialog__body {
  height: 93%;
}

::v-deep .el-table__row:hover {
  cursor: pointer;
}

.btns {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.btns .date-btn {
  width: 100px;
  height: 32px;
  border: 1px solid #3562db;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  color: #3562db;
  margin-right: 12px;
  cursor: pointer;
}

.active-btn {
  background-color: #3562db;
  color: #fff !important;
}

.cards {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.cards > div {
  width: 49%;
  background-color: #fff;
  border-radius: 4px;
  height: 90px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cards img {
  width: 45px;
  height: 45px;
}

.cards span:nth-child(2) {
  margin: 0 16px;
}

.cards span:nth-child(3) {
  font-size: 28px;
  font-weight: 700;
}

.cards span:nth-child(4) {
  font-size: 14px;
  color: #ccced3;
  margin-left: 5px;
  transform: translateY(3px);
}

.table-box {
  background-color: #fff;
  height: 69vh;
  padding: 16px;
}
</style>
