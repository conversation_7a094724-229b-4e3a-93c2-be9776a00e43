<script>
import { UsingStatusOptions } from '@/views/operationPort/constant'
import SelectPeopleDialog from '@/views/operationPort/exerciseManage/exercisePlan/components/SelectPeople.vue'
export default {
  name: 'TimeOutConfigEdit',
  components: { SelectPeopleDialog },
  props: {
    id: Number,
    visible: Boolean
  },
  events: ['update:visible', 'success'],
  data: function() {
    return {
      formModel: {
        ruleName: '',
        sendTime: '',
        businessFormId: [],
        ruleValue: undefined,
        remindUserIds: '',
        remindUserNames: '',
        remindUserPhones: '',
        remindMode: ['1'],
        status: 1,
        remindTarget: ['1'],
        remark: ''
      },
      rules: {
        ruleName: [{ required: true, message: '请输入规则名称' }],
        businessFormId: [{ required: true, type: 'array', message: '请选择生效项目类型', trigger: 'change' }],
        ruleValue: [{ required: true, message: '请选择超时设置', trigger: 'blur' }],
        sendTime: [{ required: true, message: '请选择发送时间', trigger: 'change' }],
        remindTarget: [{ required: true, type: 'array', message: '请选择提醒目标', trigger: 'change' }],
        remindMode: [{ required: true, type: 'array', message: '请选择提醒方式', trigger: 'change' }],
        remindUserNames: [{ required: true, message: '请选择人员', trigger: 'change' }]
      },
      loadingStatus: false,
      personDialogShow: false,
      businessOptions: [],
      statusOptions: UsingStatusOptions
    }
  },
  computed: {
    title: function() {
      if (this.readonly) {
        return '查看提醒规则'
      } else {
        return this.id ? '编辑提醒规则' : '新建提醒规则'
      }
    },
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
    toFetch() {
      return this.dialogVisible && this.id
    },
    // 是否有其他人被选中
    otherChecked() {
      return this.formModel.remindTarget.includes('3')
    }
  },
  watch: {
    toFetch(val) {
      val && this.getDetail()
    },
    otherChecked(val) {
      // 如果不选择3，其他。则需要清空人员信息
      if (!val) {
        this.formModel.remindUserPhones = ''
        this.formModel.remindUserIds = ''
        this.formModel.remindUserNames = ''
      }
    }
  },
  created() {
    this.getOptions()
  },
  methods: {
    getOptions() {
      this.$api.SporadicProject.selectUnUseBusinessForm()
        .then((res) => {
          console.log(res)
          if (res.code === '200') {
            this.businessOptions = res.data
            // this.formModel.remark = res.data.remark
          } else {
            throw res.msg
          }
        })
        .catch((msg) => this.$message.error(msg || '获取生效项目类型失败'))
    },
    getDetail() {
      this.loadingStatus = true
      this.$api.SporadicProject.selectReminderConfigOne({ id: this.id })
        .then((res) => {
          if (res.code === '200') {
            this.formModel.ruleName = res.data.ruleName
            this.formModel.status = +res.data.status
            this.formModel.ruleValue = res.data.ruleValue
            this.formModel.remark = res.data.remark
            this.formModel.sendTime = res.data.sendTime.replace(/:00$/, '')
            // 多选
            if (res.data.remindMode) {
              this.formModel.remindMode = res.data.remindMode.split(',')
            }
            if (res.data.remindTarget) {
              this.formModel.remindTarget = res.data.remindTarget.split(',')
            }
            if (res.data.businessFormId) {
              this.formModel.businessFormId = res.data.businessFormId.split(',')
            }
            // 根据是否选中其他，回填通知用户信息
            if (this.formModel.remindTarget.includes('3')) {
              this.formModel.remindUserNames = res.data.remindUserNames
              this.formModel.remindUserPhones = res.data.remindUserPhones
              this.formModel.remindUserIds = res.data.remindUserIds
            }
          } else {
            throw res.msg
          }
        })
        .catch((msg) => this.$message.error(msg || '获取供应商详情失败'))
        .finally(() => (this.loadingStatus = false))
    },
    onDialogClosed() {
      this.$refs.formRef.resetFields()
    },
    onSubmit() {
      this.$refs.formRef
        .validate()
        .catch(() => Promise.reject())
        .then(() => {
          // config request data
          const params = {
            ruleName: this.formModel.ruleName,
            businessFormId: this.formModel.businessFormId.join(),
            status: this.formModel.status,
            ruleValue: this.formModel.ruleValue,
            remindTarget: this.formModel.remindTarget.join(),
            remindUserIds: this.formModel.remindUserIds,
            remindUserPhones: this.formModel.remindUserPhones,
            remindUserNames: this.formModel.remindUserNames,
            remindMode: this.formModel.remindMode.join(),
            sendTime: this.formModel.sendTime + ':00',
            remark: this.formModel.remark
          }
          // 填充表单名称
          params.businessFormName = this.formModel.businessFormId
            .map(id => this.businessOptions.find(it => it.businessId === id).businessName)
            .join()
          // 设置loading
          this.loadingStatus = true
          // do request
          if (this.id) {
            params.id = this.id
            return this.$api.SporadicProject.updateReminderConfig(params)
          } else {
            return this.$api.SporadicProject.saveReminderConfig(params)
          }
        })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('操作成功')
            this.$emit('success')
            this.dialogVisible = false
          } else {
            throw res.msg || '操作失败'
          }
        })
        .catch((msg) => {
          msg && this.$message.error(msg)
        })
        .finally(() => {
          this.loadingStatus = false
        })
    },
    // 人员弹窗
    submitPersonDialog(list) {
      this.formModel.remindUserIds = list.map((item) => item.id).join()
      this.formModel.remindUserNames = list.map((item) => item.staffName).join()
      this.formModel.remindUserPhones = list.map((item) => item.mobile).join()
      this.closePersonDialog()
    },
    closePersonDialog() {
      this.personDialogShow = false
    },
    showPersonDialogFn() {
      if (this.otherChecked) {
        this.personDialogShow = true
      }
    }
  }
}
</script>
<template>
  <div>
    <el-dialog
      v-dialog-drag
      class="component tag-edit"
      :title="title"
      width="750px"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      :visible.sync="dialogVisible"
      custom-class="model-dialog"
      @closed="onDialogClosed"
    >
      <el-form ref="formRef" :model="formModel" :rules="rules" label-width="110px">
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="规则名称" prop="ruleName">
              <el-input v-model="formModel.ruleName" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生效项目类型" prop="businessFormId">
              <el-select v-model="formModel.businessFormId" placeholder="请选择" multiple>
                <el-option v-for="item of businessOptions" :key="item.businessId" :value="item.businessId" :label="item.businessName" :disabled="!!item.state"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="formModel.status" placeholder="请选择">
                <el-option v-for="item of statusOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="超时设置" prop="ruleValue">
              <div class="oneLineBox">
                <span>若当前节点下的审批人超过</span>
                <el-input-number
                  v-model="formModel.ruleValue"
                  controls-position="right"
                  :min="1"
                  placeholder="请输入"
                  :controls="false"
                  :precision="0"
                  style="width: 80px; margin: 0 10px"
                ></el-input-number>
                <span>天未处理，则进行超时通知。</span>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <div class="oneLineBox">
              <el-form-item label="提醒目标" prop="remindTarget">
                <el-checkbox-group v-model="formModel.remindTarget">
                  <el-checkbox label="1">当前节点处理人</el-checkbox>
                  <el-checkbox label="2">流程发起人</el-checkbox>
                  <el-checkbox label="3">其他</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item
                v-if="otherChecked"
                label=""
                prop="remindUserNames"
                label-width="10px"
              >
                <div @click="showPersonDialogFn">
                  <el-input
                    :value="formModel.remindUserNames"
                    readonly
                    style="margin-left: 10px; top: -4px"
                    placeholder="请选择"
                  ></el-input>
                </div>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="24">
            <el-form-item label="提醒方式" prop="remindMode">
              <el-checkbox-group v-model="formModel.remindMode">
                <el-checkbox label="1">系统消息</el-checkbox>
                <el-checkbox label="2">
                  短信
                  <el-tooltip class="item" effect="dark" content="若提醒目标未配置手机号，则无法进行通知" placement="top">
                    <el-button type="text" icon="el-icon-info"></el-button>
                  </el-tooltip>
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="发送时间" prop="sendTime">
              <div class="oneLineBox">
                <span>每天</span>
                <el-time-select
                  v-model="formModel.sendTime"
                  :picker-options="{
                    start: '00:00',
                    step: '00:15',
                    end: '23:59'
                  }"
                  :editable="false"
                  style="width: 140px; margin: 0 10px"
                  placeholder="选择时间"
                >
                </el-time-select>
                <span>发送通知</span>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="formModel.remark" type="textarea" :rows="4"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button type="primary" plain @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="onSubmit">确认</el-button>
      </template>
    </el-dialog>
    <template v-if="personDialogShow">
      <SelectPeopleDialog :personDialogShow="personDialogShow" @submitPersonDialog="submitPersonDialog" @closePersonDialog="closePersonDialog" />
    </template>
  </div>
</template>
<style lang="scss">
.component.tag-edit {
  .el-form {
    background-color: #fff;
    padding: 10px 16px 0;
    .el-form-item {
      .el-select {
        width: 100%;
      }
      .el-form-item__content .el-input-group {
        vertical-align: middle;
      }
    }
    .oneLineBox {
      display: flex;
      align-items: center;
    }
  }
}
</style>
