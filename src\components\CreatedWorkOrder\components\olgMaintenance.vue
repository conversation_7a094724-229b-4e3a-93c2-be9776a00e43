<template>
  <div class="dialog-content">
    <div class="content-top">
      <div>
        <div class="work-order-label">报警ID :</div>
        <div class="work-order-value">{{ alarmId }}</div>
      </div>
      <div>
        <div class="work-order-label">来电号码 :</div>
        <div class="work-order-value">
          <el-input v-model="formInline.needPhone" :readonly="dealType === 'deal'" placeholder="请输入来电号码"></el-input>
        </div>
        <div class="work-order-label">开单时间 :</div>
        <div class="work-order-value">{{ kd_time }}</div>
        <el-button style="float: right" plain type="primary" @click="placeOrder()"><i class="el-icon-plus"></i> 再建一单</el-button>
      </div>
    </div>
    <div class="content-footer">
      <el-row :gutter="20" :type="$store.state.settings.mode == 'mobile' ? '' : 'flex'" style="align-items: stretch">
        <el-col :md="8">
          <ContentCard title="服务事项" :required="true" :cstyle="{ height: '100%' }">
            <span slot="title-right" :title="selectService" class="select-servive">{{ selectService }}</span>
            <div slot="content" class="footer-left">
              <el-input v-model="filterText" placeholder="请输入关键词" style="margin: 10px 0"></el-input>
              <el-tree ref="tree" class="tree" :filter-node-method="filterNode" node-key="id" :data="itemTreeData" :props="defaultProps" @node-click="handleNodeClick"></el-tree>
            </div>
          </ContentCard>
        </el-col>
        <el-col :md="16">
          <ContentCard title="工单信息" :cstyle="{ height: '100%', 'overflow-y': 'auto' }">
            <div slot="content" class="footer-right">
              <el-form ref="formInline" class="form-data" :model="formInline" :rules="rules" :inline="true" label-width="auto" size="medium">
                <div class="formRow">
                  <el-form-item label="服务时间：" prop="appointmentType">
                    <el-radio v-model="formInline.appointmentType" label="0">立刻</el-radio>
                    <el-radio v-model="formInline.appointmentType" label="1">预约</el-radio>
                  </el-form-item>
                </div>
                <div class="formRow">
                  <el-form-item v-if="formInline.appointmentType === '1'" label="服务时间：" prop="appointmentDate">
                    <el-date-picker
                      v-model="formInline.appointmentDate"
                      :picker-options="pickerOptions"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      popper-class="timePicker"
                      type="datetime"
                      placeholder="选择日期时间"
                    >
                    </el-date-picker>
                  </el-form-item>
                </div>
                <div class="formRow" style="width: 100%">
                  <el-form-item style="width: 70%; flex: auto" label="紧急程度：" prop="urgencyDegree">
                    <el-radio v-model="formInline.urgencyDegree" label="2" @change="urgencyChange('2')"><el-tag color="#3562DB" size="small">一般</el-tag></el-radio>
                    <el-radio v-model="formInline.urgencyDegree" label="0"><el-tag color="#FF9435" size="small">紧急事故</el-tag></el-radio>
                    <el-radio v-model="formInline.urgencyDegree" label="1"><el-tag color="#FA403C" size="small">紧急催促</el-tag></el-radio>
                  </el-form-item>
                  <el-form-item v-if="formInline.urgencyDegree !== '2'" style="width: auto; flex: auto" label="" prop="noticeBtn">
                    <el-checkbox v-model="formInline.noticeBtn" @change="noticeBtnChange">通知</el-checkbox>
                  </el-form-item>
                </div>
                <table v-if="selectNoticePeopleRow.length" class="maint-table" style="table-layout: fixed">
                  <tbody>
                    <tr>
                      <td style="width: 42%">联系人</td>
                      <td style="width: 42%">人员电话</td>
                      <td style="width: 16%">操作</td>
                    </tr>
                    <tr v-for="(item, index) in selectNoticePeopleRow" :key="index">
                      <td>
                        <div :title="item.name" class="one-line">{{ item.name }}</div>
                      </td>
                      <td>
                        <div :title="item.phone" class="one-line">{{ item.phone }}</div>
                      </td>
                      <td>
                        <div class="one-line scope-del" @click="noticePeopleDel(item.id)">删除</div>
                      </td>
                    </tr>
                  </tbody>
                </table>
                <div class="formRow">
                  <el-form-item label="所属科室：" prop="sourceDept">
                    <el-select v-model="formInline.sourceDept" filterable placeholder="请选择">
                      <el-option v-for="item in sourcesDeptOptions" :key="item.value" :label="item.label" :value="item.value + '_' + item.label"> </el-option>
                    </el-select>
                  </el-form-item>
                </div>
                <div class="formRow">
                  <el-form-item label="服务地点：" prop="localtionName">
                    <el-input v-model="formInline.localtionName" readonly="readonly" placeholder="请选择服务地点" @focus="getLocaltion()"></el-input>
                  </el-form-item>
                </div>
                <div class="formRow">
                  <el-form-item label="工号：" prop="callerJobNum">
                    <el-input v-model.trim="formInline.callerJobNum" placeholder="请输入工号"></el-input>
                  </el-form-item>
                </div>
                <div class="formRow">
                  <el-form-item label="电话：" prop="sourcesPhone">
                    <el-input v-model.trim="formInline.sourcesPhone" placeholder="请输入电话"></el-input>
                  </el-form-item>
                </div>
                <div class="formRow">
                  <el-form-item label="联系人：" prop="callerName">
                    <el-input v-model.trim="formInline.callerName" placeholder="请输入联系人"></el-input>
                  </el-form-item>
                </div>
                <div class="formRow" style="width: 85%">
                  <el-form-item label="申报描述：" prop="questionDescription">
                    <el-input
                      v-model.trim="formInline.questionDescription"
                      maxlength="500"
                      placeholder="请输入描述，限制五百字"
                      type="textarea"
                      onkeyup="if(value.length>500)value=value.slice(0,500)"
                      show-word-limit
                    ></el-input>
                  </el-form-item>
                </div>
                <div class="formRow" style="width: 100%">
                  <el-form-item style="width: 70%; flex: auto" label="服务部门：" prop="designateDeptCode">
                    <el-select v-model="formInline.designateDeptCode" placeholder="请选择服务部门" @change="deptChange">
                      <el-option v-for="item in teamsOptions" :key="item.id" :label="item.team_name" :value="item.id + '_' + item.team_name"> </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item style="width: 30%; flex: auto" label="" prop="designCheck">
                    <el-checkbox v-model="formInline.designCheck" @change="designPerson">指派工人</el-checkbox>
                  </el-form-item>
                </div>
                <table v-if="selectTeamPeopleRow.length" class="maint-table" style="table-layout: fixed">
                  <tbody>
                    <tr>
                      <td style="width: 42%">服务人员</td>
                      <td style="width: 42%">人员电话</td>
                      <td style="width: 16%">操作</td>
                    </tr>
                    <tr v-for="(item, index) in selectTeamPeopleRow" :key="index">
                      <td>
                        <div :title="item.member_name" class="one-line">{{ item.member_name }}</div>
                      </td>
                      <td>
                        <div :title="item.phone" class="one-line">{{ item.phone }}</div>
                      </td>
                      <td>
                        <div class="one-line scope-del" @click="peopleDel(item.id)">删除</div>
                      </td>
                    </tr>
                  </tbody>
                </table>
                <div class="formRow" style="width: 100%">
                  <el-form-item label="申报属性：" prop="typeSources">
                    <el-radio v-if="workOrderDetail.olgTaskManagement?.typeSources !== '3'" v-model="formInline.typeSources" label="1">医务报修</el-radio>
                    <el-radio v-if="workOrderDetail.olgTaskManagement?.typeSources !== '3'" v-model="formInline.typeSources" label="2">外委巡查</el-radio>
                    <el-radio v-if="workOrderDetail.olgTaskManagement?.typeSources !== '3'" v-model="formInline.typeSources" label="4">领导巡查</el-radio>
                    <el-radio v-else v-model="formInline.typeSources" label="3">巡检来源</el-radio>
                  </el-form-item>
                </div>
                <div class="formRow repair-work">
                  <el-form-item label="" prop="repairWork">
                    <el-checkbox v-model="formInline.repairWork" true-label="2" false-label="1">返修工单</el-checkbox>
                  </el-form-item>
                </div>
              </el-form>
            </div>
          </ContentCard>
        </el-col>
      </el-row>
    </div>
    <!-- 选择班组人员弹框 -->
    <template v-if="changeNoticePeopleShow">
      <noticePeople ref="changeNoticePeople" :changeNoticePeopleShow="changeNoticePeopleShow" @peopleSure="noticePeopleSure" @closeDialog="closeNoticePeopleDialog"></noticePeople>
    </template>
    <!-- 选择班组人员弹框 -->
    <template v-if="changeTeamsPeopleShow">
      <teamsPeople
        ref="changeTeamsPeople"
        :selectTeamsData="selectTeamsData"
        :changeTeamsPeopleShow="changeTeamsPeopleShow"
        @peopleSure="peopleSure"
        @closeDialog="closePeopleDialog"
      ></teamsPeople>
    </template>
    <!-- 选择服务地点弹框 -->
    <template v-if="changeLocationShow">
      <Location ref="changeLocation" :changeLocationShow="changeLocationShow" @localSure="locationSure" @closeDialog="closeLocationDialog"></Location>
    </template>
    <!-- 创建工单 -->
    <template v-if="workOrderDealShow">
      <CreatedWorkOrder
        :workOrderDealShow.sync="workOrderDealShow"
        :workTypeCode="olgTaskManagement.workTypeCode"
        :workTypeName="olgTaskManagement.workTypeName"
        :alarmId="alarmId"
        :spaceId="spaceId"
        :projectCode="projectCode"
        :dealType="dealType"
        :workTypeId="workTypeId"
      ></CreatedWorkOrder>
    </template>
  </div>
</template>
<script>
import noticePeople from '../common/noticePeople.vue'
import teamsPeople from '../common/teamsPeople.vue'
import Location from '../common/Location.vue'
import moment from 'moment'
import { listToTree } from '@/util'
export default {
  name: 'olgMaintenance',
  components: {
    noticePeople,
    teamsPeople,
    Location
  },
  props: {
    workOrderDetail: {
      type: Object,
      default() {
        return {}
      }
    },
    alarmId: {
      type: String,
      default: ''
    },
    spaceId: {
      type: String,
      default: ''
    },
    projectCode: {
      type: String,
      default: ''
    },
    dealType: {
      type: String,
      default: ''
    },
    routerFrom: {
      type: String,
      default: 'local'
    }
  },
  data() {
    return {
      itemTreeData: [],
      defaultProps: {
        label: 'name',
        children: 'children'
      },
      filterText: '',
      selectService: '',
      rules: {
        callerName: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
        designateDeptCode: [{ required: true, message: '请选择服务部门', trigger: 'blur' }]
      },
      formInline: {
        needPhone: '',
        appointmentType: '0',
        appointmentDate: '',
        urgencyDegree: '2',
        sourceDept: '',
        localtion: '',
        localtionName: '',
        noticeBtn: false,
        designCheck: false,
        typeSources: '1',
        callerJobNum: '',
        callerName: '',
        sourcesPhone: '',
        designateDeptCode: '',
        questionDescription: '',
        repairWork: '',
        designatePersonCode: '',
        designatePersonName: '',
        designatePersonPhone: '',
        designatePersonUserId: '',
        personHidden: '',
        typeThree: '',
        typeNameThree: '',
        typeTwo: '',
        typeNameTwo: '',
        typeOne: '',
        typeNameOne: '',
        isDispatching: '0'
      },
      kd_time: '',
      selectRowId: '', // tree 选中id
      teamsOptions: [], // 班组
      sourcesDeptOptions: [],
      changeNoticePeopleShow: false, // 选择 应急联系人弹框
      selectNoticePeopleRow: [],
      changeTeamsPeopleShow: false,
      selectTeamPeopleRow: [],
      selectTeamsData: {},
      changeLocationShow: false, // 服务地点弹窗
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now()
        }
      },
      workOrderDealShow: false,
      olgTaskManagement: {},
      workTypeId: ''
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    if (this.workOrderDetail) {
      this.formInit()
    }
  },
  mounted() {
    this.getItemTreeData()
    this.getSourcesDeptOptions() // 获取科室数据
    // 处理模式 初始化查询班组  新增模式由服务事项带出班组
    if (this.dealType === 'add') {
      console.log(this.$store.state.user.userInfo)
      // 带入新增该人员的信息
      this.formInline.callerName = this.$store.state.user.userInfo.user.staffName
      this.formInline.callerJobNum = this.$store.state.user.userInfo.user.staffNum
      this.formInline.sourcesPhone = this.$store.state.user.userInfo.user.userName
      if (this.spaceId) {
        this.getAreaDataByAreaCode(this.spaceId) // 根据区域编码获取区域数据
      }
    }
  },
  methods: {
    // 表单提交
    saveForm() {
      const formData = JSON.parse(JSON.stringify(this.formInline))
      if (!this.selectService) {
        return this.$message.warning('请选择三级服务事项！')
      }
      const personWorkPerson = []
      if (formData.designatePersonCode) {
        const designatePersonCode = formData.designatePersonCode.split(',')
        const designatePersonName = formData.designatePersonName.split(',')
        const designatePersonPhone = formData.designatePersonPhone.split(',')
        designatePersonCode.map((e, index) => {
          personWorkPerson.push([designatePersonName[index], designatePersonPhone[index], '', e].toString())
        })
      }
      Object.assign(formData, {
        userId: this.$store.state.user.userInfo.user.staffId,
        userName: this.$store.state.user.userInfo.user.staffName,
        officeName: '',
        needPhone1: formData.needPhone,
        typeThree1: formData.typeThree,
        typeNameThree1: formData.typeNameThree,
        typeOne1: formData.typeOne,
        typeNameOne1: formData.typeNameOne,
        typeTwo1: formData.typeTwo,
        typeNameTwo1: formData.typeNameTwo,
        appointmentType1: formData.appointmentType,
        appointmentDate1: formData.appointmentDate,
        urgencyDegree1: formData.urgencyDegree,
        localtionName1: formData.localtionName,
        designateDeptCode1: formData.designateDeptCode,
        typeSources1: formData.typeSources,
        repairWork1: formData.repairWork,
        num: 1
      })
      if (this.dealType === 'deal') {
        Object.assign(formData, {
          operSource: 'newCreate',
          operType: this.workOrderDetail.operType,
          workNum: this.workOrderDetail.olgTaskManagement.workNum,
          workTypeCode: this.workOrderDetail.olgTaskManagement.workTypeCode,
          type: this.workOrderDetail.olgTaskManagement.type,
          id: this.workOrderDetail.olgTaskManagement.id,
          sourceDept: formData.sourceDept ? formData.sourceDept.split('_')[1] : '',
          sourcesDept1: this.formInline.sourceDept,
          personWorkPerson: personWorkPerson.length ? personWorkPerson.toString() : ''
        })
        this.placeAndCancelOrder(formData)
      } else if (this.dealType === 'add') {
        Object.assign(formData, {
          id: '',
          sysForShort: this.alarmId,
          workNum: this.workOrderDetail.olgTaskManagement.workNum,
          workSources: this.workOrderDetail.olgTaskManagement.workSources,
          operType: this.workOrderDetail.operType,
          taskType: this.workOrderDetail.olgTaskManagement.taskType,
          workTypeCode: this.workOrderDetail.olgTaskManagement.workTypeCode,
          workTypeName: this.workOrderDetail.olgTaskManagement.workTypeName,
          type: this.workOrderDetail.olgTaskManagement.type,
          tempDate: this.workOrderDetail.tempDate,
          isSubmit: '0',
          sourceDept: formData.sourceDept ? formData.sourceDept.split('_')[1] : '',
          sourcesDept1: this.formInline.sourceDept ?? this.workOrderDetail.olgTaskManagement.sourcesDept + '_' + this.workOrderDetail.olgTaskManagement.sourcesDeptName,
          sourcesDeptName: this.workOrderDetail.olgTaskManagement.sourcesDeptName === 'undefined' ? '' : this.workOrderDetail.olgTaskManagement.sourcesDeptName,
          noNum: this.$store.state.user.userInfo.user.staffNumber,
          personWorkPerson: personWorkPerson.length ? personWorkPerson.toString() : ''
        })
        this.placeAndCancelSave(formData)
      }
    },
    placeAndCancelOrder(formData) {
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          this.$api.placeAndCancelOrder(formData).then((res) => {
            if (res.success) {
              this.$message({
                message: res.msg,
                type: 'success'
              })
              this.$emit('save', false)
            } else {
              this.$message({
                message: res.msg,
                type: 'warning'
              })
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    placeAndCancelSave(formData) {
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          this.$api.placeAndCancelSave(formData).then((res) => {
            let msg = res.msg
            msg = msg.replace('<br/>', '')
            if (res.success) {
              this.$message({
                message: msg,
                type: 'success'
              })
              this.$emit('save', {
                workNum: res.body.workNum,
                workType: this.workOrderDetail.olgTaskManagement.workTypeCode,
                workTypeName: this.workOrderDetail.olgTaskManagement.workTypeName,
                limAlarmId: this.alarmId
              })
            } else {
              this.$message({
                message: msg,
                type: 'warning'
              })
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    formInit() {
      if (this.dealType === 'deal') {
        this.formInline.needPhone = this.workOrderDetail.olgTaskManagement.needPhone
        this.formInline.appointmentType = this.workOrderDetail.olgTaskManagement.appointmentType || '0'
        this.formInline.appointmentDate = this.workOrderDetail.olgTaskManagement.appointmentDate
        this.formInline.urgencyDegree = this.workOrderDetail.olgTaskManagement.urgencyDegree || '2'
        this.formInline.localtion = this.workOrderDetail.olgTaskManagement.olgTaskDetailList[0].localtion
        this.formInline.localtionName = this.workOrderDetail.olgTaskManagement.olgTaskDetailList[0].localtionName
        this.formInline.callerJobNum = this.workOrderDetail.olgTaskManagement.callerJobNum
        this.formInline.callerName = this.workOrderDetail.olgTaskManagement.callerName
        this.formInline.sourcesPhone = this.workOrderDetail.olgTaskManagement.sourcesPhone
        this.formInline.questionDescription = this.workOrderDetail.olgTaskManagement.questionDescription
        this.formInline.typeSources = this.workOrderDetail.olgTaskManagement.typeSources || '1'
        this.formInline.repairWork = this.workOrderDetail.olgTaskManagement.repairWork
        this.selectService = this.workOrderDetail.olgTaskManagement.olgTaskDetailList[0].itemServiceName || ''
        this.formInline.typeThree = this.workOrderDetail.olgTaskManagement.olgTaskDetailList[0].itemServiceCode
        this.formInline.typeNameThree = this.workOrderDetail.olgTaskManagement.olgTaskDetailList[0].itemServiceName
        this.formInline.typeOne = this.workOrderDetail.olgTaskManagement.olgTaskDetailList[0].itemTypeCode
        this.formInline.typeNameOne = this.workOrderDetail.olgTaskManagement.olgTaskDetailList[0].itemTypeName
        this.formInline.typeTwo = this.workOrderDetail.olgTaskManagement.olgTaskDetailList[0].itemDetailCode
        this.formInline.typeNameTwo = this.workOrderDetail.olgTaskManagement.olgTaskDetailList[0].itemDetailName
        this.kd_time = this.workOrderDetail.kd_time
        // 过滤班组
        this.selectRowId = this.formInline.typeOne
        this.getTeamsByWorkTypeCode('Echo')
        const spaceIds = this.formInline.localtion ? this.formInline.localtion.split(',') : []
        if (spaceIds.length) {
          this.getAreaDataByAreaCode(spaceIds[spaceIds.length - 1])
        }
      } else if (this.dealType === 'add') {
        this.kd_time = moment().format('YYYY-MM-DD HH:mm:ss')
      }
    },
    // 服务事项赋值
    handleNodeClick(data, node) {
      if (data.level !== '3') {
        return this.$message.warning('请选择三级服务事项！')
      }
      if (data.level === '3') {
        this.selectService = data.name
        this.selectRowId = node.parent.parent.data.id
        this.formInline.typeThree = data.id
        this.formInline.typeNameThree = data.name
        this.formInline.typeTwo = node.parent.data.id
        this.formInline.typeNameTwo = node.parent.data.name
        this.formInline.typeOne = node.parent.parent.data.id
        this.formInline.typeNameOne = node.parent.parent.data.name
      } else if (data.level === '2') {
        this.selectRowId = node.parent.data.id
        this.formInline.typeTwo = data.id
        this.formInline.typeNameTwo = data.name
      } else {
        this.selectRowId = node.data.id
        this.formInline.typeOne = data.id
        this.formInline.typeNameOne = data.name
      }
      this.formInline.designateDeptCode = ''
      this.selectTeamsData = {}
      this.formInline.isDispatching = '0'
      this.selectTeamPeopleRow = []
      this.formInline.designCheck = false
      this.getTeamsByWorkTypeCode()
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    // 再建一单
    placeOrder() {
      this.olgTaskManagement = {
        workTypeCode: this.workOrderDetail.olgTaskManagement.workTypeCode,
        workTypeName: this.workOrderDetail.olgTaskManagement.workTypeName
      }
      if (this.dealType === 'deal') {
        this.workTypeId = this.workOrderDetail.olgTaskManagement.id
      }
      this.workOrderDealShow = true
      // id workTypeName workTypeCode 'newCreate'
    },
    // 通知联系人
    noticeBtnChange() {
      this.changeNoticePeopleShow = true
    },
    urgencyChange() {
      this.selectNoticePeopleRow = []
      this.formInline.num = 0
      this.formInline.noticeBtn = ''
    },
    deptChange() {
      this.selectTeamPeopleRow = []
      this.setFormPeopleData([])
    },
    // 指派工人
    designPerson() {
      if (!this.formInline.designateDeptCode) {
        this.$nextTick(() => {
          this.formInline.designCheck = !this.formInline.designCheck
        })
        return this.$message({
          message: '请选择服务部门！',
          type: 'warning'
        })
      }
      this.selectTeamsData = {
        type: '2',
        id: this.formInline.designateDeptCode.split('_')[0],
        deptName: this.formInline.designateDeptCode.split('_')[1]
      }
      this.formInline.isDispatching = '1'
      this.changeTeamsPeopleShow = true
    },
    // 获取服务事项
    getItemTreeData() {
      const params = {
        workTypeCode: this.workOrderDetail.olgTaskManagement.workTypeCode,
        free1: this.projectCode
        // free1: '73e7aab447b34971b9ae6d8dae034aa3'
      }
      this.$api.getItemTreeData(params).then((res) => {
        this.itemTreeData = listToTree(res, 'id', 'parent')
        if (this.dealType === 'add' && this.itemTreeData.length) {
          // 服务事项默认选中第一个
          this.formInline.typeOne = this.itemTreeData[0].id
          this.formInline.typeNameOne = this.itemTreeData[0].name
          this.formInline.typeTwo = this.itemTreeData[0].children[0]?.id ?? ''
          this.formInline.typeNameTwo = this.itemTreeData[0].children[0]?.name ?? ''
          this.formInline.typeThree = this.itemTreeData[0].children[0]?.children[0]?.id ?? ''
          this.formInline.typeNameThree = this.itemTreeData[0].children[0]?.children[0]?.name ?? ''
          this.selectService = this.itemTreeData[0].children[0]?.children[0]?.name ?? ''
          // 过滤班组
          this.selectRowId = this.itemTreeData[0].id
          this.getTeamsByWorkTypeCode('Echo')
        }
      })
    },
    getSourcesDeptOptions() {
      this.$api.getAllOffice().then((res) => {
        if (res.success) {
          this.sourcesDeptOptions = res.body.result
        }
      })
    },
    // 获取班组
    getTeamsByWorkTypeCode(flag) {
      const params = {
        localtionId: '',
        workTypeCode: this.workOrderDetail.olgTaskManagement.workTypeCode,
        matterId: this.selectRowId
      }
      this.$api.getDataByTypeTeam(params).then((res) => {
        if (res.code === '200') {
          this.teamsOptions = res.data.list
          // 默认选中第一个班组
          if (flag === 'Echo' && this.teamsOptions.length) {
            this.formInline.designateDeptCode = this.teamsOptions[0]?.id + '_' + this.teamsOptions[0]?.team_name
          }
        }
      })
    },
    // 获取服务地点 start
    getLocaltion() {
      this.changeLocationShow = true
    },
    locationSure(item) {
      // console.log(item)
      this.formInline.localtionName = item.name
      this.formInline.localtion = item.id
      this.changeLocationShow = false
    },
    getAreaDataByAreaCode(spaceId) {
      const params = {
        id: spaceId
        // id: '1574997404942348290'
      }
      this.$api.lookUpDataById(params).then((res) => {
        // this.$api.lookUpDataById(params, __PATH.USER_CODE).then((res) => {
        if (res.code === 200) {
          const data = res.data
          this.formInline.localtionName = data.simName.replace(/>/g, '') + (data.ssmName ?? '')
          const simCode = data.simCode.split(',')
          this.formInline.localtion = simCode[2] + '_' + simCode[3] + '_' + simCode[4] + '_' + data.id
          // 科室数据加载完后再回显
          const timer = setInterval(() => {
            if (this.sourcesDeptOptions.length > 0) {
              this.formInline.sourceDept = data.dmId ? data.dmId + '_' + data.dmName : ''
              clearInterval(timer)
            }
          }, 200)
        }
      })
    },
    // end
    closeLocationDialog() {
      this.changeLocationShow = false
    },
    // 删除 应急联系人 start
    noticePeopleDel(id) {
      this.selectNoticePeopleRow = this.selectNoticePeopleRow.filter((e) => e.id !== id)
      this.setNoticePeople(this.selectNoticePeopleRow)
    },
    closeNoticePeopleDialog() {
      this.changeNoticePeopleShow = false
      this.setNoticePeople(this.changeNoticePeopleShow)
    },
    noticePeopleSure(item) {
      this.changeNoticePeopleShow = false
      this.selectNoticePeopleRow = item
      this.setNoticePeople(item)
    },
    // end
    setNoticePeople(selection) {
      if (selection.length === 0 || JSON.stringify(selection) === '{}') {
        this.formInline.noticeBtn = ''
        this.formInline.num = 0
      } else {
        const person = selection.map((item) => {
          return item.name + ',' + item.phone + ',' + item.wechat + ',' + item.staffId
        })
        this.formInline.personHidden = person.toString()
        this.formInline.num = selection.length
      }
    },
    // 服务人员 start
    closePeopleDialog() {
      this.changeTeamsPeopleShow = false
      this.setFormPeopleData(this.selectTeamPeopleRow)
    },
    peopleSure(item) {
      this.changeTeamsPeopleShow = false
      this.selectTeamPeopleRow = item
      this.setFormPeopleData(this.selectTeamPeopleRow)
    },
    // 根据id删除人员
    peopleDel(id) {
      this.selectTeamPeopleRow = this.selectTeamPeopleRow.filter((e) => e.id !== id)
      this.setFormPeopleData(this.selectTeamPeopleRow)
    },
    // 选中人员 提交数据重组   服务人员 end
    setFormPeopleData(selection) {
      if (selection.length) {
        // 注释eslint
        // eslint-disable-next-line
        const name = Array.from(selection, ({ member_name }) => member_name)
        const code = Array.from(selection, ({ id }) => id)
        const phone = Array.from(selection, ({ phone }) => phone)
        const userId = Array.from(selection, ({ user_id }) => user_id)
        this.formInline.designatePersonCode = code.toString()
        this.formInline.designatePersonName = name.toString()
        this.formInline.designatePersonPhone = phone.toString()
        this.formInline.designatePersonUserId = userId.toString()
      } else {
        this.formInline.designatePersonCode = ''
        this.formInline.designatePersonName = ''
        this.formInline.designatePersonPhone = ''
        this.formInline.designatePersonUserId = ''
        this.formInline.designCheck = ''
      }
    }
  }
}
</script>
<style lang="scss" type="text/css" scoped>
.dialog-content {
  .content-top {
    padding: 6px 15px 6px 5px;
    background: #fff;
    > div {
      display: flex;
      height: 32px;
      line-height: 32px;
      margin-bottom: 10px;
      font-family: 'PingFang SC-Regular', 'PingFang SC';
      .work-order-label {
        width: 80px;
        color: $color-text;
        text-align: right;
      }
      ::v-deep .el-input__inner {
        width: 200px;
      }
      .work-order-value {
        flex: 1;
        padding-left: 10px;
        color: $color-text;
        font-weight: 600;
      }
    }
  }
  .content-footer {
    padding: 10px;
    height: calc(100% - 82px);
    ::v-deep .card-title {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    ::v-deep .card-body {
      height: calc(100% - 38px);
      .footer-left {
        height: 100%;
        margin-right: 3%;
        .el-tree {
          height: calc(100% - 52px);
          overflow: auto;
        }
      }
    }
    .select-servive {
      font-family: PingFangSC-Regular, 'PingFang SC';
      font-size: 14px;
      font-weight: 600;
      color: $color-primary;
      padding-left: 15px;
    }
    .footer-right {
      ::v-deep .form-data {
        height: calc(100% - 30px);
        overflow-y: scroll;
        .formRow {
          display: flex;
          width: 75%;
          .el-form-item {
            flex: 1;
            display: flex;
            margin-bottom: 20px;
          }
          .el-form-item__content {
            flex: 1;
            .el-select {
              width: 100%;
            }
          }
        }
        .repair-work {
          width: 95%;
          .el-form-item__content {
            text-align: right;
          }
        }
      }
      .maint-table {
        width: 70%;
        margin: 0 0 20px 80px;
        border: 1px solid $color-text-secondary;
        border-collapse: collapse;
        td {
          padding: 5px 0 5px 10px;
          border: 1px solid $color-text-secondary;
          height: 25px;
          line-height: 25px;
          vertical-align: middle;
        }
        tr:first-child {
          background-color: $color-text-secondary;
        }
        td:first-child {
          width: 35%;
        }
        .one-line {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .scope-del {
          color: $color-primary;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
