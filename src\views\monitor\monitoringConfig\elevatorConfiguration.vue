<!--
 * @Author: hedd
 * @Date: 2023-04-23 18:44:30
 * @LastEditTime: 2024-07-05 17:20:15
 * @FilePath: \ihcrs_pc\src\views\monitor\monitoringConfig\elevatorConfiguration.vue
 * @Description:
-->
<template>
  <monitorSettingOld :projectCode="projectCode" :requestHttp="requestHttp"></monitorSettingOld>
</template>
<script>
import monitorSettingOld from './monitorSettingOld'
import { monitorTypeList } from '@/util/dict.js'
export default {
  name: 'elevatorConfiguration',
  components: { monitorSettingOld },
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    // 进入页面时，先将当前页面的 name 信息存入 keep-alive 全局状态
    setTimeout(() => {
      next((vm) => {
        vm.$store.commit('keepAlive/add', names)
      })
    }, 0)
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['elevatorMonitorForm'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      requestHttp: __PATH.VUE_IEMC_ELEVATOR_API,
      projectCode: monitorTypeList.find((item) => item.projectName == '电梯监测').projectCode
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
