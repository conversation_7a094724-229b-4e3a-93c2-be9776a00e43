<!-- 运行监测 -->
<template>
    <ContentCard :title="item.componentTitleShow" :scrollbarHover="true" :cstyle="{ width: '100%', height: '100%' }"
        class="drag_class" :hasMoreOper="['edit']"
        @more-oper-event="(val) => $emit('all-more-Oper', val, item.componentName, item.chartType)">
        <div slot="content" style="width: 100%; height: 100%; display: flex">
            <echarts :ref="`operatingStatistics${item.componentDataType}`"
                :domId="`operatingStatistics${item.componentDataType}`" width="100%" height="100%" />

            <!-- <div style="width: 100%; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 50px">
          <img src="@/assets/images/newMonitor/no-chat.png" />
          <span>暂无数据</span>
        </div> -->
        </div>
    </ContentCard>
</template>

<script>
import moment from 'moment'
import * as echarts from 'echarts'
export default {
    name: 'operatingStatistics',
    props: {
        item: {
            type: Object,
            default: () => { }
        },
        systemCode: {
            type: String,
            default: ''
        }
    },
    data() {
        return {}
    },
    mounted() {
        setTimeout(() => {
            this.getTopTenData()
        }, 100)
    },
    methods: {
        getTopTenData(id) {
            let payload = {
                sysOfCode: this.systemCode,
                sysOf1Code: "SSS",
                groupId: id || "",
            }
            this.$api.getAssetsCountBySysCode(payload).then((res) => {
                if (res.code === "200") {
                    this.appendEchartsData(res.data)
                } else {
                    this.hasChart = false
                }
            })
        },
        appendEchartsData(data) {
            const baseData = {
                color: ['#d9001b', '#3562db'],
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    top: 'center',
                    right: '10%',
                    orient: 'vertical',
                },
                series: [
                    {
                        type: 'pie',
                        radius: ['100%', '70%'],
                        center: ['30%', '50%'],
                        avoidLabelOverlap: false,
                        label: {
                            show: true,  // 显示标签
                            position: 'center',  // 标签位置为中心
                            formatter: () => {
                                return [
                                    '{total|总数}',
                                    '{count|' + data.totalCount + '}'
                                ].join('\n');  // 使用多个文本行
                            },
                            rich: {
                                total: {
                                    fontSize: '16',  // 字体大小
                                    color: '#333',   // 字体颜色
                                    padding: [5, 0] // 上边距设置为10
                                },
                                count: {
                                    fontSize: '20',  // 字体大小（加大）
                                    color: '#333',   // 字体颜色
                                    fontWeight: 'bold', // 加粗
                                    padding: [0, 0]   // 控制上下内边距，调整位置
                                }
                            },
                        },
                        labelLine: {
                            show: false  // 隐藏指示线
                        },
                        data: []
                    }
                ]
            }
            if (data && data.data.length > 0) {
                data.data.forEach((item) => {
                    baseData.series[0].data.push(item)
                })
            }
            this.$refs[`operatingStatistics${this.item.componentDataType}`].init(baseData)
        }
    }
}
</script>
<style lang="scss" scoped>
.data-btns {
    position: absolute;
    right: 7%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &>span {
        width: 44px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        margin: 0 4px;
        background-color: #f6f5fa;
        font-size: 14px;
        font-family: 'PingFang SC-Medium', 'PingFang SC';
        border: none;
        border-radius: 2px;
        color: #7f848c;
        cursor: pointer;
    }

    .active-btn {
        background-color: #e6effc !important;
        color: #3562db !important;
        border-color: #e6effc !important;
    }
}

.operation-list {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
}
</style>