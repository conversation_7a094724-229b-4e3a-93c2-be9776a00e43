<template>
  <div class="whole">
    <div class="tit">
      <div class="top">
        <div style="display: flex">
          <el-input v-model.trim="templateName" placeholder="请输入问卷名称" class="ipt" style="width: 200px"></el-input>
          <el-button type="primary" @click="searchForm">查询</el-button>
          <el-button type="primary" plain @click="resetForm">重置</el-button>
        </div>
        <el-button type="primary" icon="el-icon-plus" @click="dialogVisible = true">新增</el-button>
      </div>
      <ul v-loading="loading" class="quest">
        <li v-for="item in dataList" :key="item.id" class="questList">
          <div class="t">
            <div style="display: flex; align-items: center">
              <div style="width: 300px">编号：{{ item.id }}</div>
              <div>创建时间：{{ item.createTime }}</div>
            </div>
            <div style="diaplay: flex">
              <el-button type="primary" plain size="mini" @click="publish(item)">发布</el-button>
              <el-button type="primary" plain size="mini" @click="dele(item)">删除</el-button>
              <el-button type="primary" plain size="mini" @click="reproduce(item)">复制</el-button>
            </div>
          </div>
          <div>
            <div style="display: flex; justify-content: space-between; padding: 15px 0 0 15px">
              <p style="width: 41%">
                名称：<span style="color: #3562db">{{ item.name }}</span>
              </p>
              <p style="width: 24%">
                状态：<span style="color: #ff6461">{{ transformationState(item) }}</span>
              </p>
              <p style="width: 30%">已回收：{{ item.collectedNum }}份</p>
            </div>
            <div>
              <BreadcrumbNavBar @quest="quest(item)"></BreadcrumbNavBar>
            </div>
          </div>
        </li>
      </ul>
      <el-pagination
        :current-page="currentPage"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        :page-size="pageSize"
        :page-sizes="[15, 30, 50]"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <el-dialog title="新建问卷" :visible.sync="dialogVisible" width="40%" :before-close="handleClose" custom-class="model-dialog">
      <div class="outermost" style="background-color: #fff">
        <el-form ref="ruleForm" :model="ruleForm" :rules="rules">
          <el-form-item label="问卷名称" label-width="120px" prop="questName">
            <el-input v-model.trim="ruleForm.questName" placeholder="请输入问卷名称" style="width: 200px"></el-input>
          </el-form-item>
          <el-form-item label="问卷模板类型" label-width="120px" prop="templetType">
            <el-select v-model="ruleForm.templetType" placeholder="请选择问卷模板类型" @focus="getTempleList" @change="change">
              <el-option v-for="item in selectList" :key="item.id" :label="item.dictName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div v-if="show">
          <el-table v-loading="loading" :data="tableData" height="300px" border stripe highlight-current-row @selection-change="selectable">
            <el-table-column type="selection" width="55" align="center"> </el-table-column>
            <el-table-column prop="index" label="序号" align="center" show-overflow-tooltip>
              <template slot-scope="scope">
                <span>{{ (currentPage - 1) * pageSize + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="模板名称" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="type" label="问卷类型" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="templateDesc" label="描述" align="center" show-overflow-tooltip> </el-table-column>
          </el-table>
          <el-pagination
            style="margin-top: 3px"
            :current-page="currentPage"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            :page-size="pageSize"
            :page-sizes="[15, 30, 50]"
            @size-change="handleSizeChanges"
            @current-change="handleCurrentChanges"
          ></el-pagination>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import BreadcrumbNavBar from './component/BreadcrumbNavBar/BreadcrumbNavBar.vue'

export default {
  components: {
    BreadcrumbNavBar
  },
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        questName: '',
        templetType: ''
      },
      rules: {
        questName: [{ required: true, message: '请输入问卷名称', trigger: 'change' }],
        templetType: [{ required: true, message: '请选择问卷模板类型', trigger: 'change' }]
      },
      questionStatus: {
        design: '设计',
        publish: '收集',
        recovery: '完成',
        0: '暂停',
        1: '收集'
      },
      selectList: '',
      tableData: [],
      templetId: '',
      loading: false,
      pageSize: 15,
      currentPage: 1,
      total: 0,
      show: false,
      templateName: '',
      selection: '',
      dataList: [],
      flag: -1,

      menuList: [
        {
          labelName: '1、问卷名称',
          path: '/degreeSatisfaction/questManagement/questionCreate',
          class: 'unLine-heigth',
          index: 1
        },
        {
          labelName: '2、问卷题目',
          path: '/degreeSatisfaction/questManagement/questionDesign',
          class: 'unLine-heigth',
          index: 2
        },
        {
          labelName: '3、问卷设置',
          path: '/degreeSatisfaction/questManagement/questionSetting',
          class: 'unLine-heigth',
          index: 3
        },
        {
          labelName: '4、问卷传播',
          path: '/degreeSatisfaction/questManagement/propagation',
          class: 'unLine-heigth',
          index: 4
        },
        {
          labelName: '5、问卷回收',
          path: '/degreeSatisfaction/questManagement/recoveryQues',
          class: 'unLine-heigth',
          index: 5
        },
        {
          labelName: '6、单题分析',
          path: '/degreeSatisfaction/questManagement/questionAnalysis',
          class: 'unLine-heigth',
          index: 6
        },
        {
          labelName: '7、交叉分析',
          path: '/degreeSatisfaction/questManagement/questionTotalAnalysis',
          class: 'unLine-heigth',
          index: 7
        }
      ]
    }
  },

  created() {
    this.glList()
  },
  methods: {
    // 把问卷状态标识改为文字
    transformationState(row) {
      if (row.status === 'publish') {
        return this.questionStatus[row.statusRun]
      } else {
        return this.questionStatus[row.status]
      }
    },
    // 发布
    publish(row) {
      const params = {
        questionId: row.id,
        userId: this.$store.state.user.userInfo.userId
      }
      this.$api.getPaperQuestions(params).then((res) => {
        if (res.status == 200) {
          if (res.data.questions.length == 0) {
            this.$message.error('请先进行添加问题')
          } else {
            this.$api.publishPvq({ id: row.id }).then((res) => {
              if (res.status == 200) {
                this.$message({
                  message: res.message,
                  type: 'success'
                })
                this.glList()
              } else {
                this.$message.error(res.message)
              }
            })
          }
        }
      })
    },
    // 复制
    reproduce(row) {
      let params = {
        id: row.id,
        userId: this.$store.state.user.userInfo.userId,
        userName: this.$store.state.user.userInfo.username,
        pvqName: row.name, // 问卷名称
        updateUser: this.$store.state.user.userInfo.username
      }
      this.$api.copyPvq(params).then((res) => {
        if (res.status == 200) {
          this.$message({
            message: res.message,
            type: 'success'
          })
          this.ruleForm.questName = ''
          this.ruleForm.templetType = ''
          this.dialogVisible = false
          this.glList()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 删除
    dele(row) {
      this.$confirm('删除后将无法恢复，是否确定删除?', '信息提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$api.deletePvq({ questionId: row.id }, { 'operation-type': 3, 'operation-id': row.id, 'operation-name': row.name}).then((res) => {
            if (res.status == 200) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.glList()
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    quest(row) {
      localStorage.setItem('questId', row.id)
      localStorage.setItem('localData', JSON.stringify(row))
    },
    selectable(val) {
      
      
      this.selection = val
      console.log(this.selection,'this.selection');
    },
    change(val) {
      this.templetId = val
      this.tableList()
    },
    tableList() {
      let params = {
        userId: this.$store.state.user.userInfo.userId,
        userName: this.$store.state.user.userInfo.username,
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        type: this.templetId,
        isTemplate: '1'
      }
      this.$api.listPvq(params).then((res) => {
        if (res.status == 200) {
          res.data.list.forEach((item) => {
            this.selectList.forEach((item2) => {
              if (item.type == item2.id) {
                item.type = item2.dictName
              }
            })
          })
          this.tableData = res.data.list
          this.total = res.data.total
          this.show = true
        }
      })
    },
    glList() {
      let params = {
        userId: this.$store.state.user.userInfo.userId,
        userName: this.$store.state.user.userInfo.username,
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        name: this.templateName,
        isTemplate: '0'
      }
      this.$api.listPvq(params).then((res) => {
        if (res.status == 200) {
          this.dataList = res.data.list
          this.total = res.data.total
        }
      })
    },
    getTempleList() {
      this.$api.queryDictTypeTree().then((res) => {
        if (res.status == 200) {
          this.selectList = res.data
        }
      })
    },
    handleClose() {
      this.ruleForm.questName = ''
      this.ruleForm.templetType = ''
      this.dialogVisible = false
      this.show = false
      this.$nextTick(() => {
        this.$refs['ruleForm'].clearValidate()
      })
    },
    confirm(formName) {
      let params = {
        userId: this.$store.state.user.userInfo.userId,
        userName: this.$store.state.user.userInfo.username,
        pvqName: this.ruleForm.questName, // 问卷名称
        updateUser: this.$store.state.user.userInfo.username
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.selection.length != 1) {
            return this.$message.error('模板只能选择一个')
          } else {
            params.templateId = this.selection[0].id
          }
          this.$api.copyPvq(params, {'operation-type': 1}).then((res) => {
            if (res.status == 200) {
              this.$message({
                message: res.message,
                type: 'success'
              })
              this.ruleForm.questName = ''
              this.ruleForm.templetType = ''
              this.dialogVisible = false
              this.show = false
              this.$nextTick(() => {
                this.$refs['ruleForm'].clearValidate()
              })
              this.glList()
            } else {
              this.$message.error(res.message)
            }
          })
        } else {
          return false
        }
      })
    },
    searchForm() {
      this.currentPage = 1
      this.glList()
    },
    resetForm() {
      this.templateName = ''
      this.currentPage = 1
      this.glList()
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.glList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.glList()
    },
    handleSizeChanges(val) {
      this.pageSize = val
      this.tableList()
    },
    handleCurrentChanges(val) {
      this.currentPage = val
      this.tableList()
    }
  }
}
</script>

<style lang="scss" scoped>
.whole {
  width: 100%;
  height: 100%;
  padding: 15px;

  .tit {
    // width: 100%;
    height: 100%;
    background-color: #fff;
    border-radius: 5px;
  }

  .top {
    display: flex;
    justify-content: space-between;
    padding: 10px 10px 0;

    .ipt {
      width: 200px;
      margin-right: 10px;
    }
  }
}

.quest {
  height: calc(100% - 110px);
  overflow-y: scroll;
  padding: 0 10px;
  margin-top: 15px;
  border-bottom: 1px solid #eee;
  font-size: 14px;

  .questList {
    // padding: 0 10px;
    width: 100%;
    height: 150px;
    border: 1px solid #eee;
    margin-bottom: 10px;

    .t {
      padding: 5px 15px;
      display: flex;
      justify-content: space-between;
      align-content: center;
      border-bottom: 1px solid #eee;
      background: #faf9fc;
      font-weight: 500;
      color: #121f3e;

      div {
        line-height: center;
      }
    }
  }
}

.el-pagination {
  padding: 0 10px;
}

::v-deep .line-heigth {
  font-weight: 500 !important;
}

.unLine-heigth {
  font-weight: normal;
}

.navigate-bar-container {
  padding: 20px;
}

.breadcrumbLabel {
  color: #3562db;
  cursor: pointer;
}

span ::v-deep .el-breadcrumb__separator {
  color: #3562db;
}

.outermost {
  width: 100%;
  max-height: 600px;
  border: 1px solid #eee;
  padding: 10px;
  overflow: hidden;
}

::v-deep .model-dialog .el-dialog__body {
  overflow: hidden;
}
</style>
