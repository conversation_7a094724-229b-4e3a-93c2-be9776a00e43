<template>
  <div>
    <el-row>
      <el-col :span="8">
        <div class="item">
          <span class="item-title">使用期限</span>
          <span class="item-content">{{ detailsInfo.serviceLife || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">资产原值</span>
          <span class="item-content">{{ detailsInfo.money || '--' }}</span>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="item">
          <span class="item-title">折旧方式</span>
          <span class="item-content">{{ detailsInfo.depreciationTypeName || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">经费来源</span>
          <span class="item-content">
            <el-popover
              placement="top"
              width="200"
              trigger="click">
              <div>
                <div style="display: flex">
                  <span style="width: 80px">财政拨款：</span>
                  <span>{{ detailsInfo.fiscalAppropriation }}元</span>
                </div>
                <div style="display: flex">
                  <span style="width: 80px">教学经费：</span>
                  <span>{{ detailsInfo.educationFunds }}元</span>
                </div>
                <div style="display: flex">
                  <span style="width: 80px">科研经费：</span>
                  <span>{{ detailsInfo.researchFunds }}元</span>
                </div>
                <div style="display: flex">
                  <span style="width: 80px">自筹金额：</span>
                  <span>{{ detailsInfo.selfRaisedFunds }}元</span>
                </div>
                <div style="display: flex">
                  <span style="width: 80px">专项经费：</span>
                  <span>{{ detailsInfo.specialFunds }}元</span>
                </div>
                <div style="display: flex">
                  <span style="width: 80px">其他：</span>
                  <span>{{ detailsInfo.otherFunds }}元</span>
                </div>
              </div>
              <el-button slot="reference" class="view-btn">查看</el-button>
            </el-popover>
          </span>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="item">
          <span class="item-title">资产净值</span>
          <span class="item-content">{{ detailsInfo.residualValue || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">科室分摊</span>
          <span class="item-content">
            <el-popover
              placement="top"
              width="200"
              trigger="click">
              <div>
                <div v-for="(item, index) in departmentShare" :key="index">
                  <span style="width: 80px">{{ item.name }}:</span>
                  <span>{{ item.value || '--' }}%</span>
                </div>
              </div>
              <el-button slot="reference" class="view-btn">查看</el-button>
            </el-popover>
          </span>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
export default {
  name: 'DepreciationInfo',
  props: ['detailsInfo', 'departmentShare'],
  data() {
    return {}
  },
  mounted() {},
  methods: {}
}
</script>

<style scoped lang="scss">
  .item {
    margin-bottom: 12px;
    font-size: 14px;
    display: flex;
    // height: 20px;
    // line-height: 20px;
    .item-title {
      color: #909399;
      min-width: 110px;
    }
    .item-content {
      color: #121f3e;
      .view-btn {
        font-size: 12px;
        height: 20px !important;
        min-width: 50px !important;
        width: 50px !important;
        padding: 0 !important;
      }
    }
  }
</style>
