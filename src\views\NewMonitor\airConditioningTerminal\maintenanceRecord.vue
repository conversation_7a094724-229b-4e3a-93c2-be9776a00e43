<template>
    <PageContainer>
        <div slot="header" v-show="showTable == false">
            <div class="searchForm">
                <div class="control-btn-header">
                    <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 1 }" plain
                        @click="timeTypeChange(1)">今日</el-button>
                    <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 2 }" plain
                        @click="timeTypeChange(2)">本周</el-button>
                    <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 3 }" plain
                        @click="timeTypeChange(3)">本月</el-button>
                    <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 4 }" plain
                        @click="timeTypeChange(4)">本年</el-button>
                    <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 5 }" plain
                        style="margin-right: 10px" @click="timeTypeChange(5)">自定义</el-button>
                    <el-date-picker v-model="requestInfo.dataRange" type="daterange" unlink-panels
                        :disabled="requestInfo.timeType != 5" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd" clearable
                        @change="timeListData" />
                    <div style="display: inline-block">
                        <el-button type="primary" plain @click="reset">重置</el-button>
                        <el-button type="primary" @click="search">查询</el-button>
                    </div>
                </div>
            </div>
        </div>
        <div slot="content" ref="contentRef" class="table-box">
            <div class="tableContainer" v-show="showTable == false">
                <TablePage ref="table" v-loading="tableLoading" :showPage="true" :tableColumn="tableColumn" height="420"
                    :data="tableData" :pageData="pageData" @pagination="paginationChange" :pageProps="pageProps" />
            </div>
            <div class="tableContainer" v-show="showTable == true" style="overflow-y: auto;height:500px;">
                <p style="font-size: 16px;">
                    <span @click="taskReturn" style="cursor: pointer;">&lt;</span> 任务名称：
                    <el-link type="primary" @click="taskJump()"> {{ taskName }}
                    </el-link>
                </p>
                <ContentCard title="基础信息">
                    <div slot="content">
                        <div class="heade-info">
                            <p class="heade-p">
                                <span>{{ pointName }}点名称：</span>
                                <span>{{ particulars.inspectionPointName }}</span>
                            </p>
                            <p class="heade-p">
                                <span>所属区域：</span>
                                <span>{{ particulars.inspectionPointType == '1' ? particulars.simName :
                                    particulars.inspectionPointType
                                        == '2' ? particulars.regionName : '' }}</span>
                            </p>
                            <p class="heade-p">
                                <span>备注说明：</span>
                                <span>{{ particulars.assetsRemarks }}</span>
                            </p>
                        </div>
                    </div>
                </ContentCard>
                <ContentCard :title="`${pointName}内容`">
                    <div slot="content">
                        <el-table v-loading="tableLoading" :data="projectList" height="200" style="width: 100%;"
                            element-loading-background="rgba(0, 0, 0, 0.2)">
                            <el-table-column fixed type="index" width="70" label="序号"> </el-table-column>
                            <el-table-column :prop="projectType == '1' ? 'detailName' : 'detailName'"
                                show-overflow-tooltip :label="`${pointName}项目`"></el-table-column>
                            <el-table-column :prop="projectType == '1' ? 'content' : 'standardRequirements'"
                                show-overflow-tooltip :label="`${pointName}要点`"></el-table-column>
                            <el-table-column prop="deptName" show-overflow-tooltip :label="`${pointName}内容`">
                                <template slot-scope="scope">
                                    <span>{{ formate(scope.row) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="contentStandard" show-overflow-tooltip :label="`${pointName}结果`">
                                <template slot-scope="scope">
                                    <div v-if="scope.row.isNum == '4'" style="white-space: normal">
                                        <span v-for="item in scope.row.contentStandard" :key="item.fileKey" class="link"
                                            @click="handleDownload(item)">{{ item.sourceName }}</span>
                                    </div>
                                    <span v-else>{{ scope.row.contentStandard }}</span>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </ContentCard>
                <ContentCard :title="`${pointName}执行`">
                    <div slot="content">
                        <div class="heade-info">
                            <p class="heade-p">
                                <span>{{ pointName }}情况：</span>
                                <span>{{ pointName == '2' ? (excute.carryOutFlag == '1' ? '已保养' : '未保养') :
                                    excute.carryOutFlag == '1'
                                        ? '已巡'
                                        : '未巡' }}</span>
                            </p>
                            <p class="heade-p">
                                <span>执行人员：</span>
                                <span>{{ excute.implementPersonName || '-' }}</span>
                            </p>
                            <p class="heade-p">
                                <span>实际{{ pointName }}时间：</span>
                                <span>{{ excute.excuteTime || '-' }}</span>
                            </p>

                            <p class="heade-p">
                                <span>定位状态：</span>
                                <span>{{ excute.spyScan }}</span>
                            </p>
                        </div>
                    </div>
                </ContentCard>
                <ContentCard :title="`${pointName}结果`">
                    <div slot="content" class="result">
                        <div class="heade-info">
                            <p class="heade-p">
                                <span>{{ pointName }}结果：</span>
                                <span>{{ result.state == '2' ? '合格' : result.state == '3' ? '不合格' : result.state == '4'
                                    ? '异常报修' :
                                    pointName
                                        == '2' ? '未保养' : '未巡' }}</span>
                            </p>
                            <br>
                            <p class="heade-p">
                                <span>{{ pointName }}情况说明：</span>
                                <span>{{ result.desc }}</span>
                            </p>
                            <p class="heade-p" style="display: flex;">
                                <span>图片：</span>
                                <span>
                                    <div v-if="result.attachmentUrlList.length > 0" class="resultImgBox">
                                        <div v-for="(item, index) in result.attachmentUrlList" :key="index">
                                            <el-image style="max-height: 120px; max-width: 100px; margin-right: 5px"
                                                :src="item" fit="scale-down" :preview-src-list="[item]"></el-image>
                                        </div>
                                    </div>
                                    <div v-else>暂无</div>
                                </span>
                            </p>
                            <p class="heade-p" style="display: flex;">
                                <span>语音：</span>
                                <span>
                                    <span v-if="!result.callerTapeUrl">暂无</span>
                                    <div v-else>
                                        <audio ref="player" style="height: 40px" :src="result.callerTapeUrl"
                                            preload="true" controls="controls"></audio>
                                    </div>
                                </span>
                            </p>
                        </div>
                    </div>
                </ContentCard>
            </div>
        </div>
    </PageContainer>
</template>
<script lang="jsx">
import moment from 'moment'
moment.locale('zh-cn')
export default {
    name: 'emergencyTeam',
    props: {
        selectiveType: {
            type: Number,
            default: ''
        },
    },
    data() {
        return {
            selectType: null,
            particulars: [],
            taskName: "",
            systemCode: "",
            planTypeId: "",
            projectList: [],
            projectType: '', // 1:专业 0:日常
            excute: {},
            result: {
                attachmentUrlList: []
            },
            detailsData: {},
            showTable: false,
            tableLoading: false,
            requestInfo: {
                projectCode: '',
                dataRange: [moment().format('YYYY-MM-DD 00:00:00'), moment().format('YYYY-MM-DD 23:59:59')], // 时间范围
                timeType: 1 // 1: 当天 2: 本月 3: 本年 4: 自定义
            },
            hasChart: false,

            tableData: [],
            pageData: {
                pageNo: 1,
                pageSize: 15,
                total: 0
            },
            pageProps: {
                page: 'pageNo',
                pageSize: 'pageSize',
                total: 'total'
            },
        }
    },
    computed: {
        pointName() {
            switch (this.selectiveType) {
                case 2:
                    return '保养';
                case 5:
                    return '年检';
                default:
                    return '';
            }
        },
        tableColumn() {
            return [
                {
                    prop: '',
                    label: '序号',
                    formatter: (scope) => {
                        return (this.pageData.pageNo - 1) * this.pageData.pageSize + scope.$index + 1
                    },
                    width: 60
                },
                {
                    prop: 'taskName',
                    label: '任务名称'
                },
                {
                    prop: 'taskStartTime',
                    label: this.selectiveType == 2 ? "应保时间" : "应年检时间",
                    formatter: (scope) => {
                        return `${scope.row.taskStartTime}`
                    }
                },
                {
                    prop: 'excuteTime',
                    label: this.selectiveType == 2 ? "实际维保时间" : "实际年检时间"
                },
                {
                    prop: 'carryOutFlag',
                    label: this.selectiveType == 2 ? "维保情况" : "年检情况",
                    formatter: (row) => {
                        switch (row.row.carryOutFlag) {
                            case '0':
                                return '未执行';
                            case '1':
                                return '已执行';
                            default:
                                return ''; // 处理未定义的情况
                        }
                    }
                },
                {
                    prop: 'state',
                    label: this.selectiveType == 2 ? "维保结果" : "年检结果",
                    formatter: (row) => {
                        switch (row.row.state) {
                            case '2':
                                return '合格';
                            case '3':
                                return '不合格';
                            case '4':
                                return '报修';
                            default:
                                return ''; // 处理未定义的情况
                        }
                    }
                },
                {
                    prop: 'implementPersonName',
                    label: '执行人员',
                },
                {
                    prop: 'guaranteeCode',
                    label: '工单编号'
                },
                {
                    prop: 'details',
                    label: this.selectiveType == 2 ? "保养情况说明" : "年检情况说明",
                },
                {
                    prop: 'operation',
                    label: '操作',
                    width: 100,
                    render: (h, row) => {
                        return (
                            <div class="operationBtn">
                                <span class="operationBtn-span" onClick={() => this.handleListEvent(row.row)}>
                                    详情
                                </span>
                            </div>
                        )
                    }
                }
            ]
        },

    },
    watch: {
        selectiveType(newVal) {
            this.selectType = newVal;
            console.log(this.selectType, 'newVal');
        }
    },
    mounted() {
        this.getWbList()
    },
    methods: {
        // 日期选择
        timeTypeChange(type) {
            let newObj = {
                1: [moment().format('YYYY-MM-DD 00:00:00'), moment().format('YYYY-MM-DD 23:59:59')],
                2: [moment().startOf('week').format('YYYY-MM-DD 00:00:00'), moment().endOf('week').format('YYYY-MM-DD 23:59:59')],
                3: [moment().startOf('month').format('YYYY-MM-DD 00:00:00'), moment().endOf('month').format('YYYY-MM-DD 23:59:59')],
                4: [moment().startOf('year').format('YYYY-MM-DD 00:00:00'), moment().endOf('year').format('YYYY-MM-DD 23:59:59')],
                5: []
            }
            this.requestInfo.dataRange = newObj[type]
            this.requestInfo.timeType = type
            this.pageData.pageSize = 15
            this.pageData.page = 1
        },
        timeListData(val) {
            this.requestInfo.dataRange[0] = val[0] + ' 00:00:00'
            this.requestInfo.dataRange[1] = val[1] + ' 23:59:59'
        },
        // 获取维保数据列表
        getWbList() {
            let param = {
                taskPointId: this.$route.query.id,
                systemIdentificationClassification: this.selectiveType,
                startData: this.requestInfo.dataRange[0],
                endData: this.requestInfo.dataRange[1],
                pageSize: this.pageData.pageSize,
                pageNo: this.pageData.pageNo
            }
            this.tableLoading = true
            this.tableData = []
            this.$api
                .getTaskByTaskPointId(param)
                .then((res) => {
                    if (res.code == 200) {
                        this.tableData = res.data.list
                        this.pageData.total = res.data.count
                    }
                    this.tableLoading = false
                })
                .catch(() => {
                    this.tableLoading = false
                })
        },
        // 分页
        paginationChange(pagination) {
            Object.assign(this.pageData, pagination)
            this.getWbList()
        },
        // 重置查询
        reset() {
            this.requestInfo.timeType = 1
            this.requestInfo.dataRange = [moment().format('YYYY-MM-DD 00:00:00'), moment().format('YYYY-MM-DD 23:59:59')] // 时间范围
            this.getWbList()
        },
        // 查询
        search() {
            this.getWbList()
        },
        // 查看详情
        handleListEvent(row) {
            this.showTable = true
            this.taskName = row.taskName
            this.systemCode = row.systemCode
            this.planTypeId = row.planTypeId
            this.getTaskPointReleaseDetail(row.taskPointReleaseId)
        },
        // 维保详情接口
        getTaskPointReleaseDetail(id) {
            this.tableLoading = true
            this.$api.getTaskPointReleaseDetail({ id: id }).then((res) => {
                if (res.code == '200') {
                    if (res.data.project.projectdetailsReleaseList) {
                        this.projectType = res.data.project.equipmentTypeId
                        res.data.project.projectdetailsReleaseList.forEach((item) => {
                            item.contentStandard = item.isNum === '4' ? (item.contentStandard ? JSON.parse(item.contentStandard) : []) : item.contentStandard
                        })
                        this.projectList = res.data.project.projectdetailsReleaseList
                    }
                    this.excute = res.data.excute
                    res.data.result.attachmentUrlList = res.data.result.attachmentUrlList.length > 0 ? res.data.result.attachmentUrlList.map((i) => this.$tools.imgUrlTranslation(i)) : []
                    this.result = res.data.result
                    this.particulars = JSON.parse(res.data.taskPoint.particulars)
                }
                this.tableLoading = false
            })
        },
        formate(row) {
            if (this.projectType == '1') {
                // 数值
                if (row.isNum == '0') {
                    return row.rangeStart + '-' + row.rangeEnd + (row.einheitName || '')
                } else if (['1', '2', '4'].includes(row.isNum)) {
                    // 无 || 文本 || 文件上传
                    return '无'
                } else if (row.isNum == '3') {
                    // 选项
                    const option = JSON.parse(row.termJson)
                    const contTexts = option.map((i) => i.contText)
                    return contTexts.join('、')
                }
            } else {
                return row.inspectionBasis
            }
        },
        // 返回
        taskReturn() {
            this.showTable = false
        },
        // 跳转页面
        taskJump() {
            let path = ''
            if (this.selectiveType == 2) {
                path = '/MaintenanceManagement/taskManagement'
            } else {
                path = '/annualManagement/taskManagement'
            }
            this.$router.push({
                path: path,
                query: {
                    taskName: this.taskName,
                    systemCode: this.systemCode,
                    planTypeId: this.planTypeId,
                }
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.searchForm {
    display: flex;
    height: 50px;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-left: -13px;

    .control-btn-header {
        padding: 0;

        &>div {
            margin-right: 10px;
            margin-top: 10px;
        }

        .btn-item {
            border: 1px solid #3562db;
            color: #3562db;
            font-family: none;
        }

        .btn-active {
            color: #fff;
            background: #3562db;
        }
    }
}

.result {
    .resultImgBox {
        display: flex;
        align-items: center;
        flex-wrap: wrap;

        img {
            width: 80px;
            height: 80px;
            margin-left: 10px;
        }
    }
}

.container-content>div {
    height: 100%;
    margin-top: 16px;
}

.el-table {
    margin-bottom: 12px;
}

::v-deep .el-table__row:hover {
    cursor: pointer;
}

.table-box {
    background-color: #fff;
    height: 100%;

    .tableContainer {
        height: 100%;

        .el-table {
            height: calc(100% - 96px) !important;
        }
    }

    .heade-info {
        padding: 0 0 14px 16px;

        .heade-p {
            display: inline-block;
            width: 25%;
            font-size: 14px;
            line-height: 35px;

            em {
                font-style: normal;
            }
        }
    }
}

.diaContent {
    width: 100%;
    max-height: 550px !important;
    overflow: auto;
    background-color: #fff !important;

    .el-input,
    .el-select,
    .el-cascader {
        width: 300px;
    }
}

.form-item {
    display: inline-block;
    margin-right: 20px;
}

.inputWidth {
    width: 820px;
}

.ml-16 {
    margin-left: 16px;
}

.dialog .el-dialog {
    width: 60% !important;
}
</style>