<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="maintain-list">
        <div class="maintain-list-item">
          <label class="list-item" style="display: flex">
            <span class="label">模板名称：</span>
            <span class="value" style="width: calc(100% - 100px); text-overflow: ellipsis; white-space: nowrap; overflow: hidden; display: inline-block">{{
              formInline.taskBookName
            }}</span>
          </label>
        </div>
        <div class="maintain-list-item">
          <label class="list-item">
            <span class="label">模板分类：</span>
            <span class="value">{{ formInline.taskBookSortName }}</span>
          </label>
        </div>
        <div class="maintain-list-item">
          <label class="list-item">
            <span class="label">模板类型：</span>
            <span class="value">{{ formInline.taskBookTypeName }}</span>
          </label>
        </div>
        <div class="maintain-list-block">
          <label class="list-item" style="display: flex">
            <span class="label">模板说明：</span>
            <span class="value">{{ formInline.projectExplain }}</span>
          </label>
        </div>
      </div>
      <template>
        <div style="margin-bottom: 20px">
          <div class="content-table">
            <div class="table-title">
              <span class="title"> <i></i> {{ systemType == '2' ? '保养' : '巡检' }}内容 </span>
              <span class="line"></span>
            </div>
            <div v-if="formInline.equipmentTypeId == '1'" class="inspection-content">
              <div v-for="(item, index) in maintainProjectdetails" :key="index" class="content-block">
                <div class="content-line"></div>
                <div class="porject-name">
                  <div class="porject porject-index">
                    <span class="index-icon">
                      <img :src="icon" alt />
                    </span>
                    <span class="index-text">{{ index + 1 }}</span>
                  </div>
                  <div class="porject porject-input">
                    {{ systemType == '2' ? '保养' : '巡检' }}项目：
                    <span class="gray">{{ item.detailName }}</span>
                  </div>
                </div>
                <div class="content-block">
                  <div v-for="(e, i) in item.maintainProjectdetailsTermList" :key="i" class="termContent">
                    <div class="termContent-input">
                      {{ systemType == '2' ? '保养' : '巡检' }}选项：
                      <span class="gray options">{{ termTypeOptions[parseInt(e.isNum)] }}</span>
                    </div>
                    <div class="termContent-input">
                      {{ systemType == '2' ? '保养' : '巡检' }}要点：
                      <span class="gray">{{ e.content }}</span>
                    </div>
                    <div v-if="!readonly" class="termContent-input">
                      <span class="termContent-button button-add" @click="addrow(item.maintainProjectdetailsTermList)">
                        <i class="el-icon-plus"></i>
                        <span>添加行</span>
                      </span>
                      <span v-if="i != 0" class="termContent-button button-detele" @click="deteleRow(item.maintainProjectdetailsTermList, i)">
                        <i class="el-icon-delete"></i>
                        <span>删除</span>
                      </span>
                    </div>
                    <template>
                      <div v-if="e.isNum == '0'" class="termContent-tools tools-number">
                        <div class="termContent-number">
                          正常范围：
                          <span class="gray">{{ e.rangeStart }}</span>
                          至
                          <span class="gray">{{ e.rangeEnd }}</span>
                          <span>
                            &nbsp;单位：
                            <span class="gray">{{ e.einheitName ? e.einheitName : '无' }}</span>
                          </span>
                        </div>
                        <span v-if="unitOptions[parseInt(e.einheitCode)] !== '无'">{{ unitOptions[parseInt(e.einheitCode)] }}</span>
                      </div>
                      <div v-if="e.isNum == '3'" class="termContent-tools tools-radio">
                        <el-table stripe :data="e.termJson" :cell-style="{ padding: '8px' }" style="width: 730px">
                          <el-table-column prop="contText" label="选项文字" width="450">
                            <template slot-scope="scope">
                              <div class="radio-text">
                                <el-input
                                  v-model.trim="scope.row.contText"
                                  show-word-limit
                                  :maxlength="50"
                                  :readonly="readonly"
                                  style="width: 300px; display: inline-block; margin-right: 15px"
                                ></el-input>
                                <span v-if="!readonly" style="margin-right: 15px" :class="['sion-icon', 'el-icon-plus']" @click="addTabelRow(e)"></span>
                                <span
                                  v-if="!readonly && scope.$index != 0"
                                  style="color: #fc2c61"
                                  :class="['sion-icon', 'el-icon-delete', scope.$index == 0 ? 'icon-disabled' : '']"
                                  @click="radioDeteleRow(e.termJson, scope.$index)"
                                ></span>
                              </div>
                            </template>
                          </el-table-column>
                          <el-table-column label="默认" align="center">
                            <template slot-scope="scope">
                              <el-radio v-model="scope.row.isDefault" label="0" :disabled="readonly" @change.native="selectRadio(scope.row, scope.$index, e)">&nbsp;</el-radio>
                            </template>
                          </el-table-column>
                          <el-table-column v-if="!readonly" label="上移 下移" width="100">
                            <template slot-scope="scope">
                              <span :class="['sion-icon', 'el-icon-top', scope.$index == 0 ? 'icon-disabled' : '']" @click="upRow(scope.row, scope.$index, e.termJson)"></span>
                              <span
                                :class="['sion-icon', 'el-icon-bottom', scope.$index == e.termJson.length - 1 ? 'icon-disabled' : '']"
                                @click="downRow(scope.row, scope.$index, e.termJson)"
                              ></span>
                            </template>
                          </el-table-column>
                        </el-table>
                      </div>
                    </template>
                  </div>
                </div>
              </div>
              <!-- <el-pagination
                  class="pagination"
                  layout="total, prev, pager, next"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage"
                  :total="total"
                  :page-size="15"
                ></el-pagination> -->
            </div>
            <div v-else class="inspection-content" style="margin-top: 10px">
              <div class="content-block">
                <div v-for="(item, index) in tableData" :key="index">
                  <div class="project-list">
                    <div class="porject porject-input">
                      {{ systemType == '2' ? '保养' : '巡检' }}内容：
                      <span class="gray">{{ item.content || '暂无' }}</span>
                    </div>
                    <div class="porject porject-input">
                      标准要求：
                      <span class="gray">{{ item.standardRequirements || '暂无' }}</span>
                    </div>
                    <div class="porject porject-input">
                      {{ systemType == '2' ? '保养' : '巡检' }}依据：
                      <span class="gray">{{ item.inspectionBasis || '暂无' }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">关闭</el-button>
    </div>
  </PageContainer>
</template>
<script>
import icon from '@/assets/images/inspectionCenter/ic-document.png'
export default {
  name: 'maintenanceTemplateDetail',
  async beforeRouteLeave(to, from, next) {
    if (!['maitemplateManagement', 'templateManagement', 'comInstemplateManagement', 'vp_templateManagement'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      icon,
      tableData: [
        {
          index: 0,
          content: '',
          standardRequirements: '',
          inspectionBasis: ''
        }
      ],
      pageOrigin: '', // 保存页面来源，是日常保养，还是日常巡检
      title: '',
      readonly: true,
      formInline: {
        taskBookName: '',
        taskBookType: '',
        projectExplain: '',
        taskBookTypeName: '',
        taskBookSortName: '', // 模板分类
        id: '', // 模板id，修改必传
        equipmentTypeId: ''
      },
      typeList: [],
      workTypeCodeList: [],
      maintainProjectdetails: [
        {
          detailName: '', // 项目名称
          id: 0,
          maintainProjectdetailsTermList: [
            {
              isNum: '10', // 巡检选项
              content: '', // 巡检要点
              id: 0,
              rangeStart: '', // 数值范围
              rangeEnd: '',
              einheitCode: '', // 单位code
              einheitName: '', // 单位名称
              termJson: [{ contText: '', isDefault: '0' }] // 单选选项文字
            }
          ]
        }
      ],
      unitOptions: [],
      termTypeOptions: [],
      query: {},
      id: '',
      blockLoading: false,
      typeArr: [],
      typeTreeData: [],
      propsType: {
        children: 'children',
        label: 'typeName',
        value: 'typeId',
        checkStrictly: true
      },
      currentPage: 1,
      total: 0,
      systemType: '' // 系统标识,
    }
  },
  activated() {
    this.initEvent()
  },
  mounted() {
    const routerNameList = ['templateManagement', 'maitemplateManagement', 'comInstemplateManagement', 'vp_templateManagement']
    if (!this.$store.state.keepAlive.list.some((e) => routerNameList.includes(e))) {
      this.initEvent()
    }
  },
  methods: {
    // // 获取任务类型
    // _findDictionaryTableList() {
    //   let data = {
    //     dictCode: '',
    //     dictName: '',
    //     pageNo: 1,
    //     pageSize: 9999,
    //     dictType: 'task_book_type'
    //   }
    //   this.$api.findDictionaryTableList(data).then((res) => {
    //     const { code, data, message } = res
    //     if (code == 200) {
    //       this.taskBookTypeArr = data.list
    //     } else {
    //       this.$message.error(message)
    //     }
    //   })
    // },
    initEvent() {
      Object.assign(this.$data, this.$options.data())
      this.query = this.$route.query
      this.systemType = this.$route.meta.type
      // 任务书类型、巡检选项、单位
      // this._findDictionaryTableList()
      this._getDictValue('inspection_options') // 巡检选项
      this._getDictValue('engineeringUnit') // 单位
      // 修改，详情，获取模板详情
      this._getTaskBookDetails()
    },
    // 获取巡检选项/单位
    _getDictValue(types) {
      this.$api.getDictValueList({ dictType: types }).then((res) => {
        if (types == 'inspection_options') {
          res.data.forEach((o) => {
            this.termTypeOptions[parseInt(o.dictValue)] = o.dictLabel
          })
        } else {
          res.data.forEach((o) => {
            this.unitOptions[parseInt(o.dictValue)] = o.dictLabel
          })
        }
      })
    },
    // 查询详情
    _getTaskBookDetails() {
      let userInfo = this.$store.state.user.userInfo.user
      let data = {
        staffId: userInfo.staffId,
        userName: userInfo.staffName,
        id: this.query.rowId
      }
      this.$api.getTaskBookDetails(data).then((res) => {
        const { code, data, message } = res
        if (code == 200) {
          this.maintainProjectdetails = data.maintainProjectdetails
          this.formInline.taskBookName = data.projectName
          this.formInline.taskBookTypeName = data.equipmentTypeName
          this.formInline.taskBookSortName = data.dictTypeName
          this.formInline.projectExplain = data.projectExplain
          this.formInline.equipmentTypeId = data.equipmentTypeId
          if (data.equipmentTypeName) {
            const newData = []
            data.maintainProjectdetails.forEach((i) => {
              const item = {}
              item.index = i.id
              item.content = i.detailName
              item.standardRequirements = i.standardRequirements
              item.inspectionBasis = i.inspectionBasis
              newData.push(item)
            })
            this.tableData = newData
          }
        }
      })
    },
    hangdleChange(val) {
      this.formInline.equipmentTypeId = val[val.length - 1]
      this.formInline.equipmentTypeName = this.typeList.find((item) => {
        return item.typeId == this.formInline.equipmentTypeId
      }).typeName
    },
    // 点击单选按钮
    selectRadio(row, index, val) {
      for (var i = 0; i < val.termJson.length; i++) {
        if (index == i) {
          val.termJson[i].isDefault = '0'
        } else {
          val.termJson[i].isDefault = '1'
        }
      }
    },
    checkEmpty(arr) {}
  }
}
</script>
<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  padding: 10px;
  overflow-y: auto;
}
.project-list {
  display: flex;
  align-items: center;
  padding: 5px 0;
  > div {
    margin-right: 20px;
  }
}
.content-table {
  padding: 0 30px;
}
.maintain-list {
  padding-left: 30px;
  margin: 20px 0 30px;
}
.project-textarea textarea {
  height: 120px;
}
.sion-icon {
  cursor: pointer;
  padding-left: 3px;
  color: rgb(44 199 197 / 100%);
}
.icon-disabled {
  cursor: not-allowed;
}
.form-inline {
  .el-input,
  .el-select,
  .el-cascader {
    width: 300px;
  }
}
.inspection-content {
  width: 1200px;
  padding: 20px 0 0 42px;
  font-size: 14px;
  font-weight: 400;
  color: rgb(96 98 102 / 100%);
  .content-block {
    margin-bottom: 20px;
    .porject-name {
      .porject {
        display: inline-block;
      }
      .porject-index {
        width: 50px;
        .index-icon {
          position: relative;
          right: 8px;
          top: 1px;
        }
      }
      .porject-input {
        width: 810px;
        .el-input {
          display: inline-block;
          width: 90%;
        }
      }
      .porject-button {
        width: 200px;
        padding-left: 30px;
      }
    }
    .termContent {
      padding-left: 52px;
      margin-top: 20px;
      .termContent-input {
        display: inline-block;
        width: auto;
        margin-right: 30px;
        .el-input {
          display: inline-block;
          width: 400px;
        }
        .termContent-button {
          height: 42px;
          line-height: 42px;
          color: rgb(44 199 197 / 100%);
          margin-right: 20px;
          cursor: pointer;
        }
        .button-detele {
          color: rgb(252 44 97 / 100%);
        }
      }
      .termContent-tools {
        padding-left: 75px;
        margin-top: 20px;
        .termContent-number {
          display: inline-block;
          // margin-right: 20px;
          .el-input,
          .el-select {
            display: inline-block;
            width: 100px;
            margin-left: 10px;
          }
        }
      }
      .termContent-radio {
        .radio-text {
          .el-input {
            display: inline-block;
            width: 300px;
          }
        }
      }
    }
  }
}
.maintain-list-item {
  display: inline-block;
  width: 33%;
  overflow: hidden;
  text-overflow: ellipsis;
  .list-item {
    display: block;
    height: 40px;
    line-height: 40px;
    font-size: 15px;
    .label {
      display: inline-block;
      width: 100px;
      text-align: right;
      font-weight: 400;
      color: rgb(96 98 102 / 100%);
    }
    .value {
      font-weight: 400;
      color: rgb(144 147 153 / 100%);
    }
  }
}
.maintain-list-block {
  line-height: 20px;
  .list-item {
    font-size: 15px;
  }
  .label {
    display: inline-block;
    width: 100px;
    vertical-align: top;
    text-align: right;
    font-weight: 400;
    color: rgb(96 98 102 / 100%);
  }
  .value {
    display: inline-block;
    width: 880px;
    font-weight: 400;
    color: rgb(144 147 153 / 100%);
  }
}
.gray {
  font-weight: 400;
  color: rgb(144 147 153 / 100%);
}
.options {
  display: inline-block;
  width: 60px;
}
.content-table .table-title {
  .title {
    width: 80px;
    padding: 0;
    font-size: 14px;
    color: #606266;
    i {
      display: inline-block;
      width: 8px;
      height: 16px;
      border-radius: 0 8px 8px 0;
      background: #3562db;
      margin-right: 8px;
      position: relative;
      top: 2px;
    }
  }
  .line {
    display: inline-block;
    width: calc(100% - 85px);
    height: 1px;
    border-top: 1px dashed #dcdfe6;
    position: relative;
    top: -2px;
    left: 2px;
  }
}
</style>
