<template>
  <div class="wholeTable">
    <div class="wholeTable-main table-content">
      <!-- <el-table ref="table" v-loading="loading" :resizable="false" border :data="tableData" :default-sort="{prop: 'alarmLevel', order: 'descending'}" :height="tableHeight" style="width: 100%;" @selection-change="tableSelectChange" @sort-change="tableSortChange"> -->
      <el-table ref="table" v-loading="loading" :resizable="false" border :data="tableData" :height="tableHeight" style="width: 100%;" @selection-change="tableSelectChange" @sort-change="tableSortChange">
        <el-table-column type="selection" width="50" align="center"></el-table-column>
        <el-table-column prop="incidentName" label="事件类型" show-overflow-tooltip></el-table-column>
        <el-table-column prop="alarmObjectName" label="对象" show-overflow-tooltip></el-table-column>
        <el-table-column prop="alarmStartTime" label="报警时间" sortable="custom" width="170" >
          <template slot-scope="scope">{{ scope.row.alarmStartTime }}</template>
        </el-table-column>
        <el-table-column prop="alarmLevel" label="报警等级" sortable="custom" width="110">
          <span slot-scope="scope" class="alarmLevel" :style="{background: alarmLevelItem[scope.row.alarmLevel].color}">
            {{alarmLevelItem[scope.row.alarmLevel].text}}
          </span>
        </el-table-column>
        <el-table-column prop="alarmSpaceName" label="位置" show-overflow-tooltip></el-table-column>
        <el-table-column prop="alarmSource" label="报警来源" show-overflow-tooltip></el-table-column>
        <el-table-column prop="alarmStatus" label="报警处理状态" width="120">
          <div slot-scope="scope" class="alarmStatus" :style="{color: scope.row.alarmStatus == 0 ? '#FA403C' : (scope.row.alarmStatus == 1 ? '#fd9434' : '#999')}">
            <span class="alarmStatusIcon" :style="{background: scope.row.alarmStatus == 0 ? '#FA403C' : (scope.row.alarmStatus == 1 ? '#ff9435' : '#999')}"></span>
            {{ scope.row.alarmStatus == 0 ? '未处理' : (scope.row.alarmStatus == 1 ? '处理中' : '已关闭') }}
          </div>
        </el-table-column>
        <el-table-column prop="alarmAffirm" label="警情确认" width="100">
          <template slot-scope="scope">{{alarmAffirmItem[scope.row.alarmAffirm] || '-'}}</template>
        </el-table-column>
        <!-- 0：非经典案例，1：经典案例 -->
        <el-table-column prop="classic" label="经典案例" width="100">
          <span slot-scope="scope" style="display: flex; align-items: center; cursor: pointer; user-select: none;" @click="$emit('operating', 'collect', scope.row)">
            <svg-icon v-if="scope.row.classic == 1" name="collect_icon" class="collectIcon" />
            <svg-icon v-else name="collect_linear_icon" class="collectIcon" />
            {{ scope.row.classic == 1 ? '已存' : '未存' }}
          </span>
        </el-table-column>
        <!-- 0：不屏蔽，1：屏蔽中 -->
        <el-table-column prop="shield" label="屏蔽状态" width="120">
          <div slot-scope="scope" style="display: flex; align-items: center; cursor: pointer; user-select: none;">
            <el-switch :value="scope.row.shield == 1" :width="36" active-color="#3562DB" inactive-color="#CCCED3" @change="(val) => {$emit('operating', 'shielded', scope.row)}" />
            <span style="margin-left: 4px;">{{ scope.row.shield == 1 ? '已屏蔽' : '未屏蔽' }}</span>
          </div>
        </el-table-column>
        <el-table-column label="操作" width="250">
          <template slot-scope="scope">
            <!-- dispatch(派单) confirmAlarm(确警) remark(备注) shielded(屏蔽) close(关闭) viewDetails(查看详情) -->
            <el-button v-if="!scope.row.workNum" type="text" @click="$emit('operating', 'dispatch', scope.row)">派单</el-button>
            <el-button v-if="![1, 2, 3, 4].includes(scope.row.alarmAffirm)" type="text" @click="$emit('operating', 'confirmAlarm', scope.row)">警情确认</el-button>
            <!-- 已关闭只能查看备注 -->
            <el-button type="text" @click="$emit('operating', 'remark', scope.row)">备注</el-button>
            <!-- 未处理关闭前先走警情确认流程 -->
            <el-button v-if="scope.row.alarmStatus != 2" type="text" @click="$emit('operating', 'close', scope.row)">关闭</el-button>
            <el-button type="text" @click="$emit('operating', 'alarmDetails', scope.row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="wholeTable-footer">
      <el-pagination
        :current-page="pagination.current"
        :page-sizes="pagination.pageSizeOptions"
        :page-size="pagination.size"
        :layout="pagination.layoutOptions"
        :total="pagination.total"
        @size-change="paginationSizeChange"
        @current-change="paginationCurrentChange">
      </el-pagination>
    </div>
  </div>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin.js'
export default {
  name: 'wholeTable',
  mixins: [tableListMixin],
  props: {
    params: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      loading: false,
      alarmLevelItem: {
        0: { text: '通知', color: '#3562DB' },
        1: { text: '一般', color: '#3562DB' },
        2: { text: '紧急', color: '#FF9435' },
        3: { text: '重要', color: '#FA403C' }
      },
      alarmAffirmItem: { 0: '未确认', 1: '真实报警', 2: '误报', 3: '演练', 4: '调试' },
      tableData: [],
      timeOrType: '' // 排序 0.按紧急降序  1.按紧急升序  2.按时间降序  3.按时间升序
    }
  },
  created() {
    this.getDataList()
  },
  methods: {
    // 清除选择
    clearSelect() {
      this.$refs.table.clearSelection()
    },
    // 存为经典案例
    collectAlarmRecords(row) {
      let param = {
        alarmId: row.id,
        classic: row.classic == 1 ? 0 : 1
      }
      this.$api.CollectAlarmRecords(param).then((res) => {
        if (res.code == 200) {
          this.getDataList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 获取全部报警记录
    getDataList(page = this.pagination.current) {
      let { projectCode, incidentType, alarmLevel, alarmSpaceId, dataRange, alarmStatus, objectId } = this.params
      let params = {
        timeOrType: this.timeOrType,
        pageNo: page,
        pageSize: this.pagination.size,
        projectCode: projectCode.toString(),
        alarmStatus, objectId,
        incidentType, alarmLevel, alarmSpaceId,
        startTime: dataRange ? dataRange[0] : '',
        endTime: dataRange ? dataRange[1] : ''
      }
      this.loading = true
      if (page == 1) this.pagination.current = 1
      this.$api.GetAllAlarmRecord(params).then((res) => {
        this.loading = false
        if (res.code == 200) {
          this.tableData = res.data ? res.data.records : []
          this.pagination.total = res.data ? res.data.total : 0
        } else {
          this.tableData = []
          this.pagination.total = 0
        }
      }).catch((err) => {
        this.loading = false
      })
    },
    // table排序
    tableSortChange(column) {
      if (column.prop === 'alarmStartTime') {
        this.timeOrType = column.order ? (column.order == 'ascending' ? 3 : 2) : ''
      } else {
        this.timeOrType = column.order ? (column.order == 'ascending' ? 1 : 0) : ''
      }
      this.getDataList()
    },
    // table选择
    tableSelectChange(val) {
      this.$emit('getSelectList', val)
    }
  }
}
</script>
<style lang="scss" scoped>
.wholeTable {
  height: calc(100% - 16px);
  background: #fff;
  border-radius: 4px;
  padding: 16px;
  display: flex;
  margin-top: 16px;
  flex-direction: column;
  overflow: auto;
  .wholeTable-main {
    flex: 1;
    overflow: auto;
  }
  .wholeTable-footer {
    padding: 10px 0 0;
  }
  .alarmLevel {
    padding: 3px 6px;
    border-radius: 4px;
    color: #fff;
    line-height: 14px;
  }
  .alarmStatus {
    position: relative;
    display: inline-block;
    padding-left: 12px;
    .alarmStatusIcon {
      display: inline-block;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      border-radius: 100%;
    }
  }
  .collectIcon {
    font-size: 16px;
    margin-right: 4px;
  }
}
</style>
