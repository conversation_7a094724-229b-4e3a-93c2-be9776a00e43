<template>
  <el-dialog
    v-if="environmentDialogVisible"
    v-dialogDrag
    custom-class="polling-dialog"
    :modal="false"
    :close-on-click-modal="false"
    :visible.sync="environmentDialogVisible"
    :before-close="() => environmentDialogVisible = false"
  >
    <span slot="title">
      环境监测
    </span>
    <div class="polling-content">
      <div class="environment_type">
        <p>{{ dataObj.paramName }}</p>
        <b>{{ dataObj.paramValue }}<span>{{ dataObj.unit }}</span></b>
        <div v-if="dataObj.warnId" :style="{ backgroundColor:dataObj.warnColor }" class="status_btn">{{ dataObj.warnName }}</div>
      </div>
      <div class="date_time">
        <el-date-picker
          v-model="queryParmas.currentDate"
          class="my-date-picker"
          popper-class="diabloPopperClass"
          type="date"
          range-separator="至"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          :picker-options="pickerOptions"
          :clearable="false"
          value-format="yyyy-MM-dd"
        >
        </el-date-picker>
        <div class="dialog_btn reset" @click="reset">重置</div>
        <div class="dialog_btn query" @click="getenvironmentalMonitoringEcharts">查询</div>
      </div>
      <div v-if="!environmentalMonitoringShow" class="echart-null">
        <img src="@/assets/images/null.png" alt="" />
        <div>暂无数据~</div>
      </div>
      <div v-else style="width: 100%; height: calc(100% - 107px);">
        <div id="environmentalMonitoring"></div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import moment from 'moment'
import * as echarts from 'echarts'
export default {
  data() {
    return {
      environmentalMonitoringShow: true,
      environmentDialogVisible: false,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      },
      queryParmas: {
        projectCode: null,
        surveyCode: null,
        surveyName: null,
        paramCode: null,
        currentDate: null
      },
      dataObj: {}
    }
  },
  mounted() {

  },
  methods: {
    getData(item) {
      this.$api.queryEnvMonitorByParamId({...item, paramCode: item.paramId}).then(res => {
        this.dataObj = {
          ...res.data,
          paramName: item.paramName
        }
        this.queryParmas.projectCode = item.projectCode
        this.queryParmas.surveyCode = item.surveyCode
        this.queryParmas.surveyName = res.data.surveyName
        this.queryParmas.paramCode = item.paramId
        this.reset()
      })
    },
    reset() {
      this.queryParmas.currentDate = moment().format('YYYY-MM-DD')
      this.getenvironmentalMonitoringEcharts()
    },
    getenvironmentalMonitoringEcharts() {
      this.$api.queryEnvMonitorDetail(this.queryParmas).then(res => {
        this.environmentDialogVisible = true
        this.environmentalMonitoringShow = !!res.data.length
        let data = res.data.map(ele => {
          return {
            name: ele.date.slice(11, 16),
            value: ele.paramValue
          }
        })
        this.$nextTick(() => {
          this.setenvironmentalMonitoringEcharts(data)
        })
      })

    },
    // 能耗分析柱状图
    setenvironmentalMonitoringEcharts(data) {
      if (!data.length) return
      const getchart = echarts.init(document.getElementById('environmentalMonitoring'))
      console.log(getchart)
      let textColor = '#86909C'
      let option = {
        color: ['#3562DB'],
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: data.map(x => x.name),
          triggerEvent: true,
          axisLine: {
            show: false
          },
          axisLabel: {
            color: textColor,
            fontSize: 12
          }
        },
        yAxis: {
          type: 'value',
          name: '单位：m³',
          nameTextStyle: {
            color: textColor
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: textColor,
            fontSize: 13
          },
          // y轴轴线颜色
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              color: 'rgba(255,255,255,0.2)'
            }
          }
        },
        dataZoom: [
          {
            type: 'slider',
            realtime: true,
            start: 0,
            end: 20,  // 数据窗口范围的结束百分比。范围是：0 ~ 100。
            height: 5, // 组件高度
            left: 5, // 左边的距离
            right: 5, // 右边的距离
            bottom: 10, // 下边的距离
            show: data.length > 6,  // 是否展示
            showDetail: false, // 拖拽时是否展示滚动条两侧的文字
            zoomLock: true,         // 是否只平移不缩放
            moveOnMouseMove: false, // 鼠标移动能触发数据窗口平移
            // zoomOnMouseWheel: false, // 鼠标移动能触发数据窗口缩放
            // 下面是自己发现的一个问题，当点击滚动条横向拖拽拉长滚动条时，会出现文字重叠，导致效果很不好，以此用下面四个属性进行设置，当拖拽时，始终保持显示六个柱状图，可结合自己情况进行设置。添加这个属性前后的对比见**图二**
            startValue: 0, // 从头开始。
            endValue: 6,  // 最多六个
            minValueSpan: 6,  // 放大到最少几个
            maxValueSpan: 6  //  缩小到最多几个
          },
          {
            type: 'inside',  // 支持内部鼠标滚动平移
            start: 0,
            end: 20,
            zoomOnMouseWheel: false,  // 关闭滚轮缩放
            moveOnMouseWheel: true, // 开启滚轮平移
            moveOnMouseMove: true  // 鼠标移动能触发数据窗口平移
          }
        ],
        series: [
          {
            data: data.map(x => x.value),
            type: 'line',
            itemStyle: {
              color: '#F4DB67'
            },
            barWidth: 10,
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: '#F4DB67'
                }, {
                  offset: 1,
                  color: 'rgba(244, 219, 103, .1)'
                }])
              }
            }
          }
        ],
        grid: { // 让图表占满容器
          top: '30px',
          left: '16px',
          right: '16px',
          bottom: '20px',
          containLabel: true
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
#environmentalMonitoring {
  width: 100%;
  height: 100%;
  z-index: 2;
}
.echart-null {
  margin: 0 auto;
  height: calc(100% - 110px);
  width: 50%;
  text-align: center;
  color: #8a8c8f;

  img {
    max-width: 100%;
    max-height: calc(100% - 20px);
  }

  div {
    font-size: 14px;
  }
}
.my-date-picker{
  ::v-deep .el-input__prefix{
    color: #2181F4;
  }
  ::v-deep .el-input__inner{
    color: #fff;
    opacity: .6;
  }
}
::v-deep .polling-dialog {
  width: 617px;
  height: 414px;
  background: url("~@/assets/images/monitor/environment-dialog-bg.png") no-repeat;
  background-size: 100% 100%;

  .el-dialog__header {
    padding: 20px 10px 10px 26px;

    span {
      font-size: 20px;
      font-family: "HarmonyOS Sans SC-Medium", "HarmonyOS Sans SC";
      font-weight: 500;
      color: #dce9ff;
    }

    .el-dialog__headerbtn .el-dialog__close {
      color: #7cd0ff;
      font-size: 20px;
    }
  }
  .el-dialog__body{
    height: calc(100% - 56px);
    padding: 20px 20px;
  }
  .polling-content {
    height: 100%;
    .environment_type{
      display: flex;
      align-items: center;
      margin-bottom: 32px;
      p{
        font-size: 16px;
        font-family: Alibaba PuHuiTi-Regular, Alibaba PuHuiTi;
        font-weight: 400;
        color: #A2B7D9;
        line-height: 19px;
        margin: 0;
      }
      b{
        font-size: 28px;
        font-family: DIN-Bold, DIN;
        font-weight: bold;
        color: #EEFBFE;
        line-height: 33px;
        margin: 0 10px;
        span{
          font-size: 14px;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: 400;
          color: #CCCED3;
          line-height: 16px;
        }
      }
      .status_btn{
        padding: 5px;
        background: #F53F3F;
        border-radius: 2px;
        font-size: 12px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #FFFFFF;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .date_time{
      display: flex;
      margin-bottom: 10px;
    }

    .el-input {
      border-radius: 4px;

      .el-input__inner {
        background: rgb(3 23 81 / 50%);
        border: 1px solid #193382;
        color: #fff;
      }

      .el-input-group__append,
      .el-input-group__prepend {
        padding: 0 10px;
        background: rgb(3 23 81 / 50%);
        border-color: #193382;
        color: #fff;
      }
    }
  }
}
</style>
