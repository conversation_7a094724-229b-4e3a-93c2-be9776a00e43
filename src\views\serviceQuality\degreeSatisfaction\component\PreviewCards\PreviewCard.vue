<template>
  <div>
    <el-card class="box-card" shadow="hover">
      <div slot="header">
        <el-row>
          <el-col :span="24" class="header-right">
            <el-button v-for="(btn, index) in buttonArrays" :key="index" type="primary" plain @click="doConfirmBeforeEdit(btn.callbackName, btn.message, btn.callbackParams)">{{
              transformation(btn.textNameFlag, btn.textName)
            }}</el-button>
          </el-col>
        </el-row>
      </div>
      <slot></slot>
    </el-card>
  </div>
</template>

<script>
import notice from '../../utils/notice'
import utils from '../../utils/utils'
// import axiosConter from '../../axios/centralControl'
export default {
  props: {
    currentPreviewSubject: {
      type: Object
    }
  },
  data() {
    return {
      selectedPreviewSubject: {}, // 当前操作的题目
      buttonArrays: [
        {
          textName: '编辑',
          message: '编辑',
          callbackName: 'handleEditQuestionItem',
          callbackParams: ''
        },
        {
          textName: '删除',
          message: '删除',
          callbackName: 'delItem',
          callbackParams: ''
        },
        {
          textName: '上移',
          message: '上移',
          callbackName: 'handleMoveUpQuestion',
          callbackParams: 'moveQuestionSubjectUp'
        },
        {
          textName: '下移',
          message: '下移',
          callbackName: 'handleMoveDownQuestion',
          callbackParams: 'moveQuestionSubjectDown'
        }
        // {
        //   textNameFlag: true,
        //   textName: "页面逻辑",
        //   message: "设置页面逻辑",
        //   callbackName: "openPageLogic",
        //   callbackParams: ""
        // }
      ]
    }
  },
  created() {
    this.selectedPreviewSubject = this.currentPreviewSubject
  },
  methods: {
    transformation(textNameFlag, name) {
      if (textNameFlag) {
        if (this.selectedPreviewSubject.logicType) {
          name = '修改页面逻辑'
        }
      }
      return name
    },
    openPageLogic() {
      // 把当前题目数据抛送给 父组件
      notice.$emit('openPopup', this.selectedPreviewSubject)
    },
    /**
     * 编辑当前题目
     */
    handleEditQuestionItem() {
      const children = this.$children
      const selectedPreviewSubject = this.selectedPreviewSubject
      notice.$emit('showQuestionDialog', selectedPreviewSubject)
    },
    /**
     * 删除当前题目前的提示
     */
    delItem() {
      this.$confirm('此操作将永久删除该条题目信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.handleDeleteQuestionItem()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    /**
     * 删除当前选中的题目
     */
    handleDeleteQuestionItem() {
      const params = {
        id: this.selectedPreviewSubject.id,
        pvqId: localStorage.getItem('questId') // 获取问卷id
      }
      this.$api.deleteQuestion(params).then((res) => {
        if (res.status == 200) {
          notice.$emit('getCurrentQuestionAllSubject')
          this.$parent.getPaperQuestions()
        }
      })
    },
    /**
     * 对当前题目实现上移
     * @funName： moveQuestionSubjectUp/moveQuestionSubjectDown标志调取后台的接口名称
     */
    handleMoveUpQuestion() {
      // moveDownQuestion
      const params = {
        questionId: this.selectedPreviewSubject.id
      }
      console.log(params, 'params')
      this.$api.moveUpQuestion(params).then((res) => {
        if (res.status == 200) {
          this.$message({
            message: res.message,
            type: 'success'
          })
          this.$parent.getPaperQuestions()
        }
      })
    },
    // 降序
    handleMoveDownQuestion() {
      // moveDownQuestion
      const params = {
        questionId: this.selectedPreviewSubject.id
      }
      console.log(params, 'params')
      this.$api.moveDownQuestion(params).then((res) => {
        if (res.status == 200) {
          this.$message({
            message: res.message,
            type: 'success'
          })
          this.$parent.getPaperQuestions()
        }
      })
    },
    /**
     * 对题目操作前的提示信息，此操作仅在问卷处于非设计阶段用于展示提示消息
     * @callback：点击确定之后的回调函数
     * @funName： 回调函数需要的参数
     */
    doConfirmBeforeEdit(callback, labelName, params) {
      console.log(callback, '4', params, 'paramsparamsparams')
      const ss = this.$event
      // 获取问卷状态，状态分为design: "设计",publish: "收集",recovery: "完成",
      const pvqStatus = utils.getLocalStorage('localData', 'status')
      if (pvqStatus === 'publish') {
        // 获取问卷状态为publish时的状态，状态分为 0: "暂停",1: "收集"
        const statusRun = utils.getLocalStorage('localData', 'statusRun')
        if (statusRun == 1) {
          this.doConfirmMessage('问卷处于收集状态，无法' + labelName + '！')
        } else {
          this.doConfirmMessage('问卷已暂停，无法' + labelName + '！')
        }
      } else if (pvqStatus === 'recovery') {
        this.doConfirmMessage('问卷已结束，无法' + labelName + '！')
      } else {
        this[callback](params)
      }
    },
    /**
     * 展示提示消息确认框
     * @messageInfo：提示信息
     */
    doConfirmMessage(messageInfo) {
      this.$confirm(messageInfo, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
.box-card {
  border-radius: 0;

  .header-right {
    text-align: right;
  }

  .el-button {
    padding: 6px 10px;
  }
}

@media screen and (max-width: 1366px) {
  .box-card .el-button {
    padding: 0 15px;
  }
}

.el-card.is-hover-shadow:focus,
.el-card.is-hover-shadow:hover {
  border: 1px solid #dbdbdb;
}

::v-deep .el-card__header {
  padding: 5px !important;
}
</style>
