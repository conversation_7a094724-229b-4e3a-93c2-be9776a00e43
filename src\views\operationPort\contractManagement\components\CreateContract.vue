<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <el-form ref="form" label-position="right" label-width="140px" :model="formData" :rules="rules">
        <ContentCard title="基本信息">
          <div slot="content" class="footer-role">
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="9">
                <el-form-item label="合同名称" prop="archiveName">
                  <el-input v-model="formData.archiveName" maxlength="50" placeholder="请输入合同名称"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="9">
                <el-form-item label="位置" prop="folderId">
                  <el-cascader v-model="formData.folderId" :options="options"
                    :props="{ emitPath: false, checkStrictly: true, children: 'children', label: 'label', value: 'id' }"
                    clearable></el-cascader>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </ContentCard>
        <ContentCard title="合同信息">
          <div slot="content" class="footer-role">
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="9">
                <el-form-item label="合同编号" prop="archiveNumber">
                  <el-input v-model="formData.archiveNumber" placeholder="请输入合同编号" maxlength="50" clearable></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="9">
                <el-form-item label="关联合同">
                  <VirtualListSelect v-model="formData.relatedIds" collapse-tags :options="contractList" multiple
                    :propsOptions="propsOptions" />
                </el-form-item>
              </el-col>
              <el-col :md="9">
                <el-form-item label="合同分类" prop="archiveModel">
                  <el-select v-model="formData.archiveModel">
                    <el-option v-for="item in contractClassification" :key="item.value" :value="item.value"
                      :label="item.label" clearable></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :md="9">
                <el-form-item label="合同类别" prop="category">
                  <el-select v-model="formData.category">
                    <el-option v-for="item in categoryList" :key="item.value" :value="item.value" :label="item.label"
                      clearable></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :md="9">
                <el-form-item label="供应商" prop="supplier">
                  <el-input v-model="formData.supplier" placeholder="请输入供应商" maxlength="50" clearable></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="9">
                <el-form-item label="签订部门">
                  <el-cascader v-model="formData.departmentId" placeholder="请选择签订部门" :options="deptList" :props="{
                      value: 'id',
                      label: 'deptName',
                      checkStrictly: true,
                      emitPath: false
                    }" clearable filterable size="small" @change="selectDept"></el-cascader>
                </el-form-item>
              </el-col>
              <el-col :md="9">
                <el-form-item label="经办人">
                  <el-input v-model="formData.handledBy" placeholder="请输入经办人" maxlength="50" clearable></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="9">
                <el-form-item label="合同金额（元）" prop="amount">
                  <el-input v-model="formData.amount" placeholder="请输入合同金额（元）" clearable
                    @input="(value) => (formData.amount = value.replace(/[^\d.]/g, '').replace(/(\.\d{2}).+/, '$1'))"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="9">
                <el-form-item label="保证金额（元）">
                  <el-input v-model="formData.bond" placeholder="请输入保证金额（元）" clearable
                    @input="(value) => (formData.bond = value.replace(/[^\d.]/g, '').replace(/(\.\d{2}).+/, '$1'))"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="9">
                <el-form-item label="签订日期" prop="archiveDate">
                  <el-date-picker v-model="formData.archiveDate" value-format="timestamp" placeholder="请选择签订日期"
                    clearable></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :md="9">
                <el-form-item label="合同有效期">
                  <el-date-picker v-model="formData.effectiveDate" value-format="timestamp" placeholder="请选择合同有效期"
                    clearable></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :md="9">
                <el-form-item label="合同生效日期" prop="startDate">
                  <el-date-picker v-model="formData.startDate" :pickerOptions="pickerOptions" value-format="timestamp"
                    placeholder="请选择合同生效日期" clearable></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :md="9">
                <el-form-item label="合同结束日期">
                  <el-date-picker v-model="formData.endDate" :pickerOptions="endPickerOptions" value-format="timestamp"
                    placeholder="请选择合同生效日期" clearable></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :md="24">
                <el-form-item label="合同存放位置">
                  <el-input v-model="formData.saveLocation" type="textarea" maxlength="200" show-word-limit
                    placeholder="请输入合同存放位置"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="24">
                <el-form-item label="备注">
                  <el-input v-model="formData.remark" type="textarea" maxlength="200" show-word-limit
                    placeholder="请输入备注"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="24">
                <el-form-item label="附件">
                  <el-upload class="upload-demo" :action="''" :on-success="handleOnSuccess"
                    :before-upload="beforeUpload" multiple :http-request="httpRequset" :show-file-list="false">
                    <el-button size="small" type="secondary">
                      <em class="el-icon-upload2"></em>
                      点击上传
                    </el-button>
                    <div slot="tip" class="el-upload__tip">文件大小限制20M</div>
                  </el-upload>
                </el-form-item>
              </el-col>
            </el-row>
            <div class="tableBox">
              <el-table :data="formData.archiveFileList" highlight-current-row>
                <el-table-column label="序号" type="index" width="50"> </el-table-column>
                <el-table-column property="fileName" label="文件名"> </el-table-column>
                <el-table-column property="fileSize" label="大小（M）"> </el-table-column>
                <el-table-column property="option" label="操作" width="120">
                  <template slot-scope="scope">
                    <el-button type="text" @click="handleDelete(scope)"> 删除 </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </ContentCard>
      </el-form>
    </div>
    <div slot="footer">
      <el-button type="primary" @click="handleSave">保存</el-button>
      <el-button type="primary" plain @click="$router.go(-1)">取消</el-button>
    </div>
  </PageContainer>
</template>
<script>
import mixins from '@/views/operationPort/dossierManager/mixins/index.js'
import { transData } from '@/util'
import dictMixin from '../mixins/index.js'
export default {
  name: 'CreateDocuments',
  mixins: [mixins, dictMixin],
  data() {
    const that = this
    return {
      rules: {
        folderId: [{ required: true, message: '请选择位置', trigger: ['change', 'blur'] }],
        archiveName: [
          {
            required: true,
            message: '请输入文档名称',
            trigger: ['change', 'blur']
          }
        ],
        archiveNumber: [{ required: true, message: '请输入文号', trigger: ['change', 'blur'] }],
        archiveModel: [
          {
            required: true,
            message: '请选择所属分类',
            trigger: ['change', 'blur']
          }
        ],
        category: [
          {
            required: true,
            message: '请选择合同类别',
            trigger: ['change', 'blur']
          }
        ],
        supplier: [
          {
            required: true,
            message: '请输入供应商',
            trigger: ['change', 'blur']
          }
        ],
        amount: [
          {
            required: true,
            message: '请输入合同金额',
            trigger: ['change', 'blur']
          }
        ],
        archiveDate: [
          {
            required: true,
            message: '请选择签订日期',
            trigger: ['change', 'blur']
          }
        ],
        startDate: [
          {
            required: true,
            message: '请选择合同生效日期',
            trigger: ['change', 'blur']
          }
        ]
      },
      pickerOptions: {
        disabledDate(date) {
          return that.formData.endDate && date.getTime() >= that.formData.endDate
        }
      },
      endPickerOptions: {
        disabledDate(date) {
          return that.formData.startDate && date.getTime() <= that.formData.startDate
        }
      },
      formData: {
        archiveName: '',
        folderId: '',
        archiveNumber: '',
        relatedIds: '',
        archiveModel: '',
        saveLocation: '',
        remark: '',
        archiveFileList: [],
        category: '',
        supplier: '',
        departmentId: '',
        handledBy: '',
        amount: '',
        bond: '',
        archiveDate: '',
        effectiveDate: '',
        startDate: '',
        endDate: ''
      },
      options: [],
      contractList: [],
      deptList: [],
      propsOptions: {
        label: 'archiveName',
        value: 'archiveId'
      }
    }
  },
  computed: {
    Authorization() {
      return getToken()
    }
  },
  created() {
    this.handleGetTreeData()
    this.handleGetContractData()
    this.getDeptList()
  },
  methods: {
    findNameById(tree, targetIds) {
      const names = []
      function search(node) {
        if (targetIds.includes(node.id)) {
          names.push(node.deptName)
        }
        if (node.children) {
          node.children.forEach((child) => search(child))
        }
      }
      tree.forEach((node) => search(node))
      return names.join(',')
    },
    selectDept(e) {
      this.formData.departmentName = this.findNameById(this.deptList, [e])
    },
    // 获取部门列表
    getDeptList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == '200') {
          this.deptList = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    handleGetContractData() {
      this.$api.fileManagement.queryAllContractPage({ current: 1, size: 99999999 }).then((res) => {
        this.contractList = res.data.records
      })
    },
    handleSave() {
      this.$refs.form.validate((res) => {
        if (res) {
          const params = {
            ...this.formData,
            archiveType: '0',
            relatedIds: this.formData.relatedIds.toString()
          }
          this.$api.fileManagement.insertContract(params).then((res) => {
            if (res.code === '200') {
              this.$message.success('保存成功')
              this.handleBack()
            } else {
              this.$message.error('保存失败')
            }
          })
        }
      })
    },
    /**
     * @description 递归删除值为空数组的child属性，避免级联选择器显示后一级空列表
     * @param {Array} optionList 数据源
     * @param {String} childName 需要删除的child属性名 如 chhildren  childList等等
     */
    handleRemoveEmptyChild(optionList, childName) {
      for (let i = 0; i < optionList.length; i++) {
        if (optionList[i][childName].length !== 0) {
          this.handleRemoveEmptyChild(optionList[i][childName], childName)
        } else {
          delete optionList[i][childName]
        }
      }
    },
    handleGetTreeData() {
      const isMine = this.$route.path === '/dossierManager/myCreateDocuments'
      this.$api.fileManagement.selectFolderTree({ folderType: '0', isMine }).then((res) => {
        this.handleRemoveEmptyChild(res.data, 'children')
        this.options = res.data
      })
    },
    handleBack() {
      this.$router.go(-1)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep {
  .container-content {
    overflow: auto !important;
    .box-card:first-child {
      padding-bottom: 0px;
    }
    .box-card:last-child {
      padding-top: 0px;
    }
  }
  .el-date-editor,
  .el-cascader {
    width: 100%;
  }
}
.tableBox {
  padding-left: 140px;
}
::v-deep .upload-demo {
  display: flex;
  .el-upload__tip {
    margin-top: 0;
    margin-left: 8px;
    color: #8c8c8c;
  }
}
::v-deep .el-select {
  width: 100%;
}
</style>
