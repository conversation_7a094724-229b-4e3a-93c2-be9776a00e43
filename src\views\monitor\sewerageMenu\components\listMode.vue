<template>
  <div ref="listMode" class="listMode">
    <div v-if="requestInfo.orderBy && requestInfo.orderBy == 2" v-loading="loading" class="type-main">
      <ContentCard
        v-for="(gather, gatherIndex) in listData"
        :key="gather.name"
        :title="gather.entityTypeName"
        :style="{ borderBottom: gatherIndex == 0 ? '3px solid #F6F5FA' : '' }"
      >
        <div slot="content" class="type-content">
          <div v-for="item in gather.entityTypeList" :key="item.surveyEntityCode" class="card-content">
            <div class="card-heade" @click="goToDetails(item)">
              <div class="heade-info">
                <p class="heade-name">{{ item.surveyEntityName }}</p>
                <span
                  v-for="alarm in item.policeHistoryGroup"
                  :key="alarm.alarmLevel"
                  class="info-icon-box"
                  :style="{ display: !alarmType[alarm.alarmLevel] ? 'none' : '' }"
                  @click.stop="viewAlarm(alarm)"
                >
                  <svg-icon v-if="[0, 1, 2, 3].includes(alarm.alarmLevel)" :name="alarmType[alarm.alarmLevel]" class="info-icon" />
                </span>
              </div>
              <div v-if="item.entityTypeId != '2887' && item.surveyStatus" class="heade-control">
                <p class="btn-item" :class="{ 'btn-active': item.surveyStatus == 1 }">自动</p>
                <p class="btn-item" :class="{ 'btn-active': item.surveyStatus == 0 }">手动</p>
                <!-- <svg-icon name="on_icon" class="on-icon" /> -->
                <!-- <svg-icon v-else-if="item.status == 1" name="off_icon" class="on-icon" />
                <svg-icon v-else-if="item.status == 6" name="break_icon" class="on-icon" style="cursor: no-drop;" /> -->
              </div>
            </div>
            <div class="card-main">
              <div v-if="gather.entityTypeName == '集水坑'" class="card-img">
                <img v-if="item.liquidStatus == 1" src="../../../../assets/images/monitor/gyw_img.png" alt="高液位" />
                <img v-else-if="item.liquidStatus == 0" src="../../../../assets/images/monitor/zc_img.png" alt="正常" />
                <img v-else src="../../../../assets/images/monitor/dyw_img.png" alt="低液位" />
              </div>
              <div v-else class="card-img">
                <img v-if="item.status == 0" src="../../../../assets/images/monitor/sb_img_kai.png" alt="水泵开" />
                <img v-else src="../../../../assets/images/monitor/sb_img_guan.png" alt="水泵关" />
              </div>
              <div class="scroll-y" style="padding: 24px 0; width: calc(100% - 60px)">
                <div class="main-left">
                  <div v-for="v in item.parameterList" :key="v.parameterId" class="left-item">
                    <p class="item-label">{{ v.parameterName }}</p>
                    <p class="item-value" style="margin-top: 9px">
                      <span style="font-size: 20px; font-weight: 600" :style="{ color: v.color }">{{ v.quoteValue || '-' }}</span>
                      <span style="font-size: 12px; color: #ccced3; margin-left: 4px">{{ v.parameterUnit || '' }}</span>
                    </p>
                  </div>
                </div>
                <div v-if="item.controlParameterList" class="main-right">
                  <div v-for="v in item.controlParameterList" :key="v.parameterId" class="right-item">
                    <p class="item-label">{{ v.parameterName }}</p>
                    <p class="item-value" style="margin-top: 9px; padding-right: 16px">
                      <span style="font-size: 18px; font-weight: bold">{{ v.parameterValue || '-' }}</span>
                      <span style="font-size: 12px; color: #121f3e; font-weight: bold">{{ v.parameterUnit || '' }}</span>
                      <span v-if="item.surveyStatus == 0" class="control-btn">
                        <i class="el-icon-caret-top"></i>
                        <i class="el-icon-caret-bottom"></i>
                      </span>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ContentCard>
    </div>
    <div v-else v-loading="loading" class="relation-main">
      <div v-for="item in listData" :key="item.surveyEntityCode" class="card-content">
        <div class="card-heade" @click="goToDetails(item)">
          <p class="heade-name">{{ item.surveyEntityName }}</p>
          <span
            v-for="alarm in item.policeHistoryGroup"
            :key="alarm.alarmLevel"
            class="info-icon-box"
            :style="{ display: !alarmType[alarm.alarmLevel] ? 'none' : '' }"
            @click.stop="viewAlarm(alarm)"
          >
            <svg-icon v-if="[0, 1, 2, 3].includes(alarm.alarmLevel)" :name="alarmType[alarm.alarmLevel]" class="info-icon" />
          </span>
        </div>
        <div class="card-main">
          <div class="card-main-heade">
            <div v-if="item.entityTypeName == '集水坑'" class="card-img">
              <img v-if="item.liquidStatus == 1" src="../../../../assets/images/monitor/gyw_img.png" alt="高液位" />
              <img v-else-if="item.liquidStatus == 0" src="../../../../assets/images/monitor/zc_img.png" alt="正常" />
              <img v-else src="../../../../assets/images/monitor/dyw_img.png" alt="低液位" />
            </div>
            <div v-else class="card-img">
              <img v-if="item.status == 0" src="../../../../assets/images/monitor/sb_img_kai.png" alt="水泵开" />
              <img v-else src="../../../../assets/images/monitor/sb_img_guan.png" alt="水泵关" />
            </div>
            <div class="scroll-y" style="padding: 8px 0; width: calc(100% - 60px)">
              <div class="main-left">
                <div v-for="v in item.parameterList" :key="v.parameterId" class="left-item">
                  <p class="item-label">{{ v.parameterName }}</p>
                  <p class="item-value" style="margin-top: 9px">
                    <span style="font-size: 20px; font-weight: 600" :style="{ color: v.color }">{{ v.quoteValue || '-' }}</span>
                    <span style="font-size: 12px; color: #ccced3; margin-left: 4px">{{ v.parameterUnit || '' }}</span>
                  </p>
                </div>
              </div>
              <div class="main-right">
                <div v-for="v in item.controlParameterList" :key="v.parameterId" class="right-item">
                  <p class="item-label">{{ v.parameterName }}</p>
                  <p class="item-value" style="margin-top: 9px; padding-right: 16px">
                    <span style="font-size: 18px; font-weight: bold">{{ v.parameterValue || '-' }}</span>
                    <span style="font-size: 12px; color: #121f3e; font-weight: bold">{{ v.parameterUnit || '' }}</span>
                    <span v-if="item.surveyStatus == 0" class="control-btn">
                      <i class="el-icon-caret-top"></i>
                      <i class="el-icon-caret-bottom"></i>
                    </span>
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div v-if="item.children" class="scroll-y" style="padding: 16px 0; width: 100%">
            <div v-for="(v, i) in item.children" :key="v.surveyEntityCode + i" class="main-footer-item" @click="goToDetails(v)">
              <div class="footer-item-heade">
                <p class="item-heade-name">{{ v.surveyEntityName }}</p>
                <!-- <svg-icon name="on_icon" class="on-icon" /> -->
              </div>
              <div class="footer-item-main">
                <div class="card-img">
                  <img v-if="v.status == 0" src="../../../../assets/images/monitor/sb_img_kai.png" alt="水泵开" />
                  <img v-else src="../../../../assets/images/monitor/sb_img_guan.png" alt="水泵关" />
                </div>
                <div class="item-main-left">
                  <div v-for="(list, index) in arrTrans(v.parameterList)" :key="index + 'list'">
                    <p v-for="j in list" :key="j.parameterId" class="item-item-value">
                      <span>{{ j.quoteValue || '-' }}</span>
                      <span style="font-size: 12px; color: #ccced3; margin-left: 4px">{{ j.parameterUnit || '' }}</span>
                    </p>
                  </div>
                </div>
                <div class="item-main-right">
                  <p v-if="v.surveyStatus" class="item-item-value">{{ v.surveyStatus == 1 ? '自动' : '手动' }}</p>
                  <p v-if="v.status && v.status != 0" class="item-item-value" :style="{ color: v.status == 0 ? '#121F3E' : '#FA403C' }">{{ v.status == 0 ? '运行' : '停止' }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="listMode-footer">
      <el-pagination
        :current-page="pagination.current"
        :page-sizes="pagination.pageSizeOptions"
        :page-size="pagination.size"
        :layout="pagination.layoutOptions"
        :total="pagination.total"
        @size-change="paginationSizeChange"
        @current-change="paginationCurrentChange"
      />
    </div>
    <alarm-dialog v-if="alarmDialog" :visible.sync="alarmDialog" :alarmTypeItem="alarmTypeItem" />
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import tableListMixin from '@/mixins/tableListMixin.js'
import alarmDialog from '../../airMenu/components/alarmDialog'
import { arrTrans } from '@/util'
export default {
  name: 'listMode',
  components: {
    alarmDialog
  },
  mixins: [tableListMixin],
  data() {
    return {
      alarmDialog: false,
      loading: false,
      requestInfo: {},
      initList: [],
      listData: [],
      alarmType: {
        3: 'urgent_icon',
        2: 'serious_icon',
        1: 'commonly_icon',
        0: 'tips_icon'
      },
      alarmTypeItem: {}
    }
  },
  computed: {
    arrTrans() {
      return (list) => {
        return arrTrans(3, list)
      }
    },
    ...mapGetters({
      socketIemcMsgs: 'socket/socketIemcMsgs'
    })
  },
  watch: {
    socketIemcMsgs(data) {
      let itemData = JSON.parse(data)
      let newList = JSON.parse(JSON.stringify(this.listData))
      if (this.requestInfo.orderBy == 1) {
        newList.forEach((item) => {
          if (item.surveyEntityCode == itemData.surveyEntityCode) {
            Object.assign(item, itemData)
          }
        })
      } else {
        newList.forEach((item) => {
          item.entityTypeList.forEach((v) => {
            if (v.surveyEntityCode == itemData.surveyEntityCode) {
              Object.assign(v, itemData)
            }
          })
        })
      }
      this.listData = this.setData(newList)
    }
  },
  created() {},
  methods: {
    // 查看报警记录
    viewAlarm(item) {
      this.alarmTypeItem = { ...item, projectCode: this.requestInfo.projectCode }
      this.alarmDialog = true
    },
    // 获取检测项列表
    init(params) {
      this.pagination.current = 1
      this.requestInfo = params
      this.getDataList()
    },
    // 获取检测项列表
    getDataList() {
      let params = {
        ...this.requestInfo,
        page: this.pagination.current,
        pageSize: this.pagination.size
      }
      this.loading = true
      this.listData = []
      this.$api.GetRealWaterMonitoringList(params).then((res) => {
        this.loading = false
        if (res.code == 200) {
          this.initList = res.data.list ? res.data.list : []
          this.listData = this.setData(this.initList)
          this.pagination.total = res.data.totalCount ? res.data.totalCount : 0
        }
      })
    },
    setData(list) {
      if (this.requestInfo.orderBy == 1) {
        // this.listData = this.$tools.transData(list, 'surveyEntityCode', 'serviceEntityId', 'children')
        // this.listData.forEach((item) => {
        //   item.parameterList.forEach((v) => {
        //     v.quoteValue = v.parameterUnit ? v.parameterValue : v.configName
        //     v.color = v.parameterId == '2860' ? (v.quoteValue == '高液位' || v.quoteValue == '低液位' ? '#FA403C' : '#00BC6D') : '#121f3e'
        //   });
        //   (item?.children ?? []).forEach((i) => {
        //     i.parameterList = i.parameterList.filter(j => j.parameterId != 2606 && j.parameterId !=  2765)
        //     i.parameterList.forEach((v) => {
        //       v.quoteValue = v.parameterUnit ? v.parameterValue : v.configName
        //     })
        //   })
        //   // item.controlParameterList.forEach((v) => {
        //   //   v.quoteValue = v.parameterUnit ? v.parameterValue : v.configName
        //   // })
        // })
        list.forEach((item) => {
          // 过滤子集 id为2765和2606的检测项
          if (item.serviceEntityId) {
            item.parameterList = item.parameterList.filter((j) => j.parameterId != 2606 && j.parameterId != 2765)
          }
          item.parameterList.forEach((v) => {
            v.quoteValue = v.parameterUnit ? v.parameterValue : v.configName
            v.color = v.parameterId == '2860' ? (v.quoteValue == '高液位' || v.quoteValue == '低液位' ? '#FA403C' : '#00BC6D') : '#121f3e'
          })
          // item.controlParameterList.forEach((v) => {
          //   v.quoteValue = v.parameterUnit ? v.parameterValue : v.configName
          // })
        })
        return this.$tools.transData(list, 'surveyEntityCode', 'serviceEntityId', 'children')
      } else {
        list.forEach((item) => {
          item.entityTypeList.forEach((j) => {
            j.parameterList.forEach((v) => {
              v.quoteValue = v.parameterUnit ? v.parameterValue : v.configName
              v.color = v.parameterId == '2860' ? (v.quoteValue == '高液位' || v.quoteValue == '低液位' ? '#FA403C' : '#00BC6D') : '#121f3e'
            })
            // item.controlParameterList.forEach((v) => {
            //   v.quoteValue = v.parameterUnit ? v.parameterValue : v.configName
            // })
          })
        })
        return list
      }
    },
    goToDetails(item) {
      this.$router.push({
        path: '/sewerageMenu/sewerageMonitor/monitorDetails',
        query: {
          surveyCode: item.surveyEntityCode,
          surveyName: item.surveyEntityName,
          projectCode: this.requestInfo.projectCode,
          assetId: item.assetId
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.listMode {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  ::v-deep .el-loading-mask {
    left: 16px;
  }
  p {
    margin: 0;
  }
  .type-main {
    overflow: auto;
    flex: 1;
    margin: 16px 0 0 16px;
    background: #fff;
    ::v-deep .box-card {
      width: 100%;
      padding: 16px 0;
      .card-title {
        padding: 0 16px;
      }
      .card-body {
        width: 100%;
        margin-top: 0;
      }
    }
    .type-content {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
    }
    .card-content {
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      margin-top: 16px;
      margin-left: 16px;
      max-width: calc(100% - 32px);
      display: flex;
      flex-direction: column;
      .card-heade {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        box-shadow: 0 0 5px 0 rgb(18 31 62 / 12%);
        cursor: pointer;
        .heade-info {
          display: flex;
          align-items: center;
          .heade-name {
            font-size: 15px;
            color: #121f3e;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .info-icon-box {
            cursor: pointer;
            .info-icon {
              margin-left: 7px;
              font-size: 18px;
            }
          }
        }
        .heade-control {
          display: flex;
          align-items: center;
          margin-left: 8px;
          .btn-item {
            cursor: no-drop;
            margin-right: 8px;
            padding: 3px 0;
            text-align: center;
            min-width: 42px;
            font-size: 14px;
            line-height: 14px;
            color: #7f848c;
            background: #f6f5fa;
            border-radius: 2px;
            border: 1px solid #ededf5;
          }
          .btn-active {
            background: #3562db;
            color: #fff;
            border-color: #3562db;
          }
        }
      }
      .card-main {
        padding: 0 16px 0 24px;
        min-height: 60px;
        display: flex;
        box-sizing: content-box;
        align-items: center;
        flex: 1;
      }
    }
  }
  .relation-main {
    overflow: auto;
    width: 100%;
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    .card-content {
      max-width: calc(100% - 32px);
      background: #fff;
      border-radius: 4px;
      margin-top: 16px;
      margin-left: 16px;
      p {
        margin: 0;
      }
      .card-heade {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        box-shadow: 0 0 5px 0 rgb(18 31 62 / 12%);
        cursor: pointer;
        .heade-name {
          font-size: 15px;
          color: #121f3e;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .info-icon-box {
          cursor: pointer;
          .info-icon {
            margin-left: 7px;
            font-size: 18px;
          }
        }
      }
      .card-main {
        padding: 16px;
        .card-main-heade {
          border-bottom: 1px solid #e4e7ed;
          display: flex;
          box-sizing: content-box;
          align-items: center;
        }
        .main-footer-item {
          padding: 16px;
          background: #faf9fc;
          border-radius: 4px;
          margin-left: 16px;
          cursor: pointer;
          .footer-item-heade {
            padding-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .item-heade-name {
              font-size: 14px;
              font-weight: 500;
              color: #121f3e;
              line-height: 14px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              margin-right: 10px;
            }
          }
          .footer-item-main {
            display: flex;
            align-items: center;
            .item-main-left {
              margin-top: -10px;
              display: flex;
              height: 90px;
            }
            .item-main-right {
              height: 90px;
              margin-top: -10px;
            }
          }
        }
        .main-footer-item:first-child {
          margin-left: 0;
        }
      }
    }
  }
  .on-icon {
    z-index: 1001;
    font-size: 20px;
    cursor: pointer;
  }
  .scroll-y {
    display: flex;
    box-sizing: content-box;
    align-items: center;
    overflow-x: auto;
  }
  .card-img {
    width: 60px;
    height: 60px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .main-left {
    display: flex;
    .left-item {
      margin-left: 20px;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
    }
  }
  .main-right {
    border-left: 1px solid #dcdfe6;
    margin-left: 20px;
    display: flex;
    .right-item {
      margin-left: 20px;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
    }
  }
  .item-item-value {
    margin-top: 8px !important;
    font-size: 16px;
    color: #121f3e;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 18px;
    margin-left: 16px !important;
    height: 18px;
    span {
      display: inline-block;
    }
  }
  .item-label {
    font-size: 14px;
    color: #414653;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .item-value {
    position: relative;
    color: #121f3e;
    line-height: 20px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    .control-btn {
      position: absolute;
      display: flex;
      flex-direction: column;
      color: #c0c4cc;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      font-size: 14px;
      i {
        cursor: pointer;
      }
      i:hover {
        color: #3562db;
      }
      .el-icon-caret-top,
      .el-icon-caret-bottom {
        margin-top: -2px;
        margin-bottom: -2px;
      }
    }
  }
  .listMode-footer {
    padding: 10px 0 10px 16px;
    ::v-deep .el-pagination {
      .btn-next,
      .btn-prev {
        background: transparent;
      }
      .el-pager li {
        background: transparent;
      }
    }
  }
}
</style>
