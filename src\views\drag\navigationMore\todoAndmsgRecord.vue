<template>
  <PageContainer>
    <div slot="header">
      <div class="search-header">
        <div class="backBar">
          <span style="cursor: pointer" @click="$router.go(-1)">
            <i class="el-icon-arrow-left"></i>
            {{ pageTitle }}
          </span>
        </div>
        <div class="search-from">
          <div>
            <el-date-picker
              v-model="searchFrom.dataRange"
              type="datetimerange"
              unlink-panels
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="yyyy-MM-dd HH:mm:ss"
              :picker-options="pickerOptions"
            >
            </el-date-picker>
            <el-button type="primary" style="margin-left: 10px" plain @click="resetForm">重置</el-button>
            <el-button type="primary" @click="searchForm">查询</el-button>
          </div>
          <div>
            <el-button type="primary" @click="readClick">一键已读</el-button>
          </div>
        </div>
      </div>
    </div>
    <div slot="content" class="msg-table">
      <TablePage
        ref="table"
        v-loading="loading"
        border
        row-key="msgId"
        :tableColumn="tableColumn"
        :showPage="true"
        :data="tableData"
        :pageData="pageData"
        :pageProps="pageProps"
        height="calc(100% - 40px)"
        @pagination="paginationChange"
        @selection-change="handleSelectionChange"
      >
      </TablePage>
      <todoItemDialog v-if="todoItemDialogShow" :visible.sync="todoItemDialogShow" :itemData="itemData" @closeTodoDialog="closeTodoDialog" />
      <template v-if="alarmDetailShow">
        <AlarmDetailDialog ref="alarmDetail" :alarmId="alarmDetail.alarmId" :visible.sync="alarmDetailShow" @update:visible="closeAlarmDialog" />
      </template>
      <template v-if="workOrderDetailCenterShow">
        <el-dialog :visible.sync="workOrderDetailCenterShow" custom-class="detailDialog main" :close-on-click-modal="false" :before-close="workOrderDetailCloseDialog">
          <template slot="title">
            <span class="dialog-title">{{ dialogTitle }}</span>
          </template>
          <component :is="iomsComponent" :rowData="detailObj" />
        </el-dialog>
      </template>
      <WorkOrderDetailDialog v-if="workOrderDetailShow" sourceType="alarm" :visible.sync="workOrderDetailShow" :alarmDetail="alarmDetail" :dialogDetail="workOderDialogData" />
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import { msgIconList } from '@/util/dict.js'
export default {
  name: 'todoAndmsgRecord',
  components: {
    todoItemDialog: () => import('../components/dialog/todoItemDialog.vue'),
    workOrderDetailYS: () => import('@/views/serviceQuality/transport/components/workOrderDetailList.vue'),
    workOrderDetailWX: () => import('@/views/serviceQuality/maintenance/components/workOrderDetailList.vue'),
    workOrderDetailBJ: () => import('@/views/serviceQuality/cleaning/components/workOrderDetailList.vue'),
    workOrderDetailSSP: () => import('@/views/serviceQuality/snapshot/components/workOrderDetailList.vue')
  },
  // beforeRouteEnter(to, from, next) {
  //   const selectPage = to.query.page
  //   next((vm) => {
  //     vm.$route.meta.title = selectPage == 'todo' ? '待办管理' : '消息管理'
  //     console.log(vm.$route.meta)
  //     // vm.$store.commit('keepAlive/add', names)
  //   })
  // },
  beforeRouteLeave(to, from, next) {
    this.$store.commit('settings/setCurrentRouteMetaTitle', '')
    next()
  },
  data() {
    return {
      pageTitle: '',
      alarmDetailShow: false,
      alarmDetail: {},
      workOrderDetailCenterShow: false, // 工单详情弹窗
      dialogTitle: '', // 弹窗标题
      detailObj: {}, // 工单详情传递数据
      iomsComponent: 'workOrderDetailWX', // 工单详情组件(维修)
      workOrderDetailShow: false, // 工单详情弹窗
      workOderDialogData: [], // 工单详情传递数据
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      // 搜索条件
      searchFrom: {
        dataRange: [] // 时间范围
      },
      loading: false,
      tableData: [],
      tableColumn: [
        {
          type: 'selection',
          align: 'center'
        },
        {
          prop: '',
          label: '序号',
          width: 80,
          reserveSelection: true,
          formatter: (scope) => {
            return (this.pageData.page - 1) * this.pageData.pageSize + scope.$index + 1
          }
        },
        {
          prop: 'publishTime',
          label: '时间',
          reserveSelection: true
        },
        {
          prop: 'msgSysType',
          label: '类型',
          reserveSelection: true,
          formatter: (scope) => {
            if (scope.row.msgType == 0 || scope.row.msgType == 1) {
              return scope.row.msgSysTypeName
            } else {
              return '工作提醒'
            }
          }
        },
        {
          prop: 'msgTitle',
          label: '标题',
          reserveSelection: true
        },
        {
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" style="color: #3562db" onClick={() => this.handleListEvent('detail', row.row)}>
                  详情
                </span>
                <span class="operationBtn-span" style="color: #fa403c" onClick={() => this.handleListEvent('del', row.row)}>
                  删除
                </span>
                {row.row.readStatus == 0 ? (
                  row.row.msgType != 2 ? (
                    <span class="operationBtn-span" style="color: black" onClick={() => this.handleListEvent('read', row.row)}>
                      标记已读
                    </span>
                  ) : (
                    <span class="operationBtn-span" style="color: black" onClick={() => this.handleListEvent('read', row.row)}>
                      标记已完成
                    </span>
                  )
                ) : row.row.msgType != 2 ? (
                  <span class="operationBtn-span" style="color: #999;cursor: auto;">
                    已读
                  </span>
                ) : (
                  <span class="operationBtn-span" style="color: #999" onClick={() => this.handleListEvent('read', row.row)}>
                    标记未完成
                  </span>
                )}
              </div>
            )
          }
        }
      ],
      multipleSelection: [],
      pageData: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      pageProps: {
        page: 'page',
        pageSize: 'pageSize',
        total: 'total'
      },
      todoItemDialogShow: false,
      itemData: {},
      pageMsgType: 0
    }
  },
  created() {
    const pageArr = [
      {
        type: 'todo',
        name: '待办管理',
        msgType: 1
      },
      {
        type: 'msg',
        name: '消息管理',
        msgType: 0
      }
    ]
    const selectPage = pageArr.find((item) => {
      return item.type == this.$route.query.page
    })
    this.pageTitle = selectPage.name
    this.$route.meta.title = this.pageTitle
    this.$store.commit('settings/setCurrentRouteMetaTitle', selectPage.name)
    this.pageMsgType = selectPage.msgType
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    // 初始化组件数据
    getDataList() {
      let { dataRange } = this.searchFrom
      let params = {
        page: this.pageData.page,
        pageSize: this.pageData.pageSize,
        userId: this.$store.state.user.userInfo.user.staffId,
        msgType: this.pageMsgType,
        msgSysId: 0,
        startTime: dataRange[0],
        endTime: dataRange[1]
      }
      this.loading = true
      this.$api
        .getMessageByUserId(params)
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            this.tableData = res.data ? res.data.list : []
            this.pageData.total = res.data ? res.data.total : 0
          } else {
            this.tableData = []
            this.pageData.total = 0
          }
        })
        .catch((err) => {
          this.loading = false
        })
    },
    // 列表事件
    handleListEvent(type, row) {
      if (row.msgType == 2) {
        if (type == 'detail') {
          this.itemData = row
          // todolist对应弹窗
          this.todoItemDialogShow = true
        } else if (type == 'read') {
          this.$confirm('是否改变该工作提醒的完成状态?', '提示', {
            cancelButtonClass: 'el-button--primary is-plain',
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.changeTodoStatus(row)
          })
        }
      } else {
        if (type == 'detail') {
          this.viewMsgDetail(row)
        } else if (type == 'read') {
          this.updateReadStatus(row.msgId)
        }
      }
      if (type == 'del') {
        this.$confirm('是否删除该消息?', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.delMessage(row)
        })
      }
      console.log(type, row)
    },
    // 查看详情
    viewMsgDetail(item) {
      const selectTypeData = msgIconList[item.msgCatId]
      // 通知公告
      if (selectTypeData.type == 'system') {
        this.$router.push({
          path: '/drag/noticeDetail',
          query: {
            msgId: item.msgId,
            msgSysId: item.msgSysId
          }
        })
        return
      }
      const params = {
        userId: this.$store.state.user.userInfo.user.staffId,
        msgId: item.msgId,
        msgSysId: item.msgSysId
      }
      this.$api.getImserverMsgInfo(params).then((res) => {
        if (res.code == 200) {
          const data = res.data
          let msgBody = {}
          try {
            msgBody = JSON.parse(data.msgBody)
          } catch (error) {
            return this.$message.error('消息体解析失败，请联系管理员')
          }
          console.log(msgBody)
          if (!Object.keys(msgBody).length) {
            return this.$message.warning('此消息暂无详情数据')
          }
          const selectTypeData = msgIconList[data.msgCatId]
          const elements = selectTypeData?.elements?.split(',') ?? []
          // 遍历msgBody对象保留elements中的属性
          for (const key in msgBody) {
            if (!elements.includes(key)) {
              delete msgBody[key]
            }
          }
          // msgBody的元素数量不等于elements的元素数量，说明有查询属性缺失
          if (Object.keys(msgBody).length != elements.length) {
            return this.$message.warning('消息数据缺失，请联系管理员')
          }
          if (selectTypeData.type == 'ioms') {
            // 确警工单
            if (selectTypeData.typeCode == 16) {
              this.workOderDialogData = [
                {
                  workTypeName: msgBody.type,
                  id: msgBody.workNum,
                  active: true
                }
              ]
              this.workOrderDetailShow = !this.workOrderDetailShow
            } else {
              this.detailObj = {
                id: msgBody.workNum
              }
              this.dialogTitle = `${msgBody.type}（${msgBody.flowType}）`
              if (selectTypeData?.component) {
                this.iomsComponent = selectTypeData?.component
                this.workOrderDetailCenterShow = true
              } else {
                this.$message.warning('该工单类型暂未开放')
              }
            }
          } else if (selectTypeData.type == 'icis') {
            this.$router.push({
              path: '/InspectionManagement/taskManagement/taskDetail',
              query: {
                taskId: msgBody.id,
                type: 'detail',
                systemType: selectTypeData.typeCode
              }
            })
          } else if (selectTypeData.type == 'warn') {
            // this.alarmDetail = msgBody
            // this.alarmDetailShow = true
            // 报警详情新页面
            this.$router.push({
              path: '/allAlarm/alarmDetail',
              query: {
                alarmId: msgBody.alarmId
              }
            })
          } else if (selectTypeData.type == 'life') {
            // 寿命提醒功能 跳转设备详情
            const { id, assetsId, configType } = msgBody
            this.$router.push({
              name: 'addDevice',
              query: {
                alarmId: msgBody.alarmId,
                type: 'details',
                configType,
                id,
                assetsId
              }
            })
          } else if (selectTypeData.type == 'approve') {
            // 审批消息 跳转审批详情
            const { projectCode, processInstanceId, status } = msgBody
            this.$router.push({
              path: '/operation/flowFormDetail',
              query: {
                detailId: projectCode,
                flowId: processInstanceId,
                handleType: 'detail',
                // handleType: status == 'finished' ? 'detail' : 'handle',
                status
              }
            })
          } else {
            this.$message.warning('该消息类型暂未开放')
          }
          console.log(msgBody, elements)
        }
      })
    },
    // 改变工作提醒状态
    changeTodoStatus(row) {
      const params = {
        msgId: row.msgId,
        readStatus: row.readStatus == 0 ? 1 : 0,
        userId: this.$store.state.user.userInfo.user.staffId
      }
      this.$api.finishToDoListLevel(params).then((res) => {
        if (res.code == 200) {
          this.$message.success('状态修改成功')
          this.searchForm()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 删除消息
    delMessage(row) {
      const params = {
        msgId: row.msgId,
        userId: this.$store.state.user.userInfo.user.staffId
      }
      this.$api.deleteMsgByUserId(params).then((res) => {
        if (res.code == 200) {
          this.$message.success('删除成功')
          const { page, pageSize, total } = this.pageData
          this.pageData.page = this.$tools.resetTablePage(page, pageSize, total)
          this.searchForm()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 一键已读
    readClick() {
      if (this.multipleSelection.length) {
        this.$confirm('是否将选中的消息标记为已读?', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.updateReadStatus()
        })
      } else {
        this.$confirm('是否将所有消息标记为已读?', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.onekeyreadpc()
        })
      }
    },
    // 一键已读
    onekeyreadpc() {
      const params = {
        userId: this.$store.state.user.userInfo.user.staffId,
        msgType: this.pageMsgType,
        msgSysId: 0
      }
      this.$api.onekeyreadpc(params).then((res) => {
        if (res.code == 200) {
          this.$message.success('消息阅读成功')
          this.searchForm()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 批量已读
    updateReadStatus(msgId = '') {
      const params = {
        userId: this.$store.state.user.userInfo.user.staffId,
        msgId: msgId || this.multipleSelection.map((item) => item.msgId).join(','),
        msgSysId: 0
      }
      this.$api.updateReadStatus(params).then((res) => {
        if (res.code == 200) {
          this.$message.success('消息阅读成功')
          this.searchForm()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getDataList()
    },
    // 重置查询
    resetForm() {
      // this.handleClear()
      Object.assign(this.searchFrom, {
        dataRange: [] // 时间范围
      })
      this.searchForm()
    },
    // 查询
    searchForm() {
      this.pageData.page = 1
      this.getDataList()
    },
    closeTodoDialog() {
      this.todoItemDialogShow = false
      this.searchForm()
    },
    // 工单详情弹窗关闭
    workOrderDetailCloseDialog() {
      this.workOrderDetailCenterShow = false
      this.messageInit()
    },
    // 报警详情弹窗关闭
    closeAlarmDialog() {
      this.messageInit()
    }
  }
}
</script>
<style lang="scss" scoped>
.search-header {
  padding: 10px 8px 10px 15px;
  .search-from {
    display: flex;
    justify-content: space-between;
    padding-bottom: 12px;
    & > div {
      margin-top: 12px;
      margin-right: 10px;
    }
    .el-range-editor.el-input__inner {
      padding: 0 15px;
    }
  }
  .backBar {
    color: #121f3e;
    height: 30px;
    border-bottom: 1px solid #dcdfe6;
  }
}
.msg-table {
  height: calc(100% - 15px);
  margin-top: 15px;
  padding: 15px;
  background: #fff;
}
</style>
