<template>
  <div class="tab_content">
    <div class="right-heade">
      <div class="search-box">
        <el-input v-model="formData.fileName" placeholder="编号/名称/备注" suffix-icon="el-icon-search" clearable />
        <el-cascader
          v-model="formData.ownerDeptId"
          placeholder="签订部门"
          :options="deptList"
          :props="{
            value: 'id',
            label: 'deptName',
            checkStrictly: true,
            emitPath: false
          }"
          clearable
          filterable
          size="small"
        ></el-cascader>
        <el-date-picker
          v-model="formData.datetimerange"
          value-format="timestamp"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleDateChange"
        >
        </el-date-picker>
        <div>
          <el-button type="primary" @click="search">查询</el-button>
          <el-button type="primary" plain @click="reset">重置</el-button>
        </div>
      </div>
      <div>
        <el-button type="danger" size="small" @click="handleCancelSharing">结束借阅</el-button>
      </div>
    </div>
    <div class="right-content">
      <div class="table-content">
        <TablePage
          ref="tablePage"
          v-loading="tableLoading"
          v-scrollHideTooltip
          :tableColumn="tableColumn"
          :data="tableData"
          border
          height="100%"
          :showPage="true"
          :pageData="pageData"
          :pageProps="{
            page: 'current',
            pageSize: 'size',
            total: 'total'
          }"
          @pagination="paginationChange"
          @selection-change="handleSelectionChange"
        >
        </TablePage>
      </div>
    </div>
    <FileSharing v-if="sharevisible" title="合同借阅" :visible.sync="sharevisible" update :current="current" @success="handleQueryTablelist" />
  </div>
</template>
<script lang="jsx">
import tableListMixin from '@/mixins/tableListMixin.js'
import FileSharing from '@/views/operationPort/dossierManager/components/FileSharing.vue'
import { transData } from '@/util'
export default {
  components: {
    FileSharing
  },
  mixins: [tableListMixin],
  data() {
    return {
      tableColumn: [
        {
          type: 'selection',
          width: 50
        },
        {
          prop: 'archiveName',
          label: '合同名称',
          align: 'center'
        },
        {
          prop: 'archiveNumber',
          label: '合同编号',
          align: 'center'
        },
        {
          prop: 'folderName',
          label: '文件夹',
          align: 'center'
        },
        {
          prop: 'departmentName',
          label: '签订部门',
          align: 'center'
        },
        {
          prop: 'lendRangeDept',
          label: '借阅范围（部门）',
          align: 'shareDeptName'
        },
        {
          prop: 'lendRangeUser',
          label: '借阅范围（成员）',
          align: 'shareMemberName'
        },
        {
          prop: 'shareStartDate',
          label: '借阅开始',
          align: 'center'
        },
        {
          prop: 'shareEndDate',
          label: '借阅结束',
          align: 'center'
        },
        {
          prop: 'handler',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" style="color: #3562DB" onClick={() => this.handleShare(row.row)}>
                  调整借阅
                </span>
                <span class="operationBtn-span" style="color: #3562DB" onClick={() => this.handleCancelSharing(row.row)}>
                  结束借阅
                </span>
              </div>
            )
          }
        }
      ],
      formData: {
        startTime: null,
        endTime: null,
        archiveInfo: '',
        datetimerange: []
      },
      tableLoading: false,
      tableData: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      checkedData: [],
      sharevisible: false,
      current: {},
      deptList: []
    }
  },
  created() {
    this.handleQueryTablelist()
    this.getDeptList()
  },
  methods: {
    handleDateChange(e) {
      this.formData.startTime = e && e[0]
      this.formData.endTime = e && e[1]
    },
    // 获取部门列表
    getDeptList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == '200') {
          this.deptList = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    handleQueryTablelist() {
      const parmas = {
        ...this.formData,
        ...this.pageData,
        archiveType: '0'
      }
      delete parmas.datetimerange
      this.$api.fileManagement.shareManagerListByPage(parmas).then((res) => {
        this.tableData = res.data.records
        this.pageData.total = res.data.total
      })
    },
    search() {
      this.pageData.current = 1
      this.handleQueryTablelist()
    },
    reset() {
      Object.keys(this.formData).forEach((key) => {
        this.formData[key] = ''
        if (key === 'datetimerange') {
          this.formData[key] = []
        }
        if (['startTime', 'endTime'].includes(key)) {
          this.formData[key] = null
        }
      })
      this.search()
    },
    handleSelectionChange(e) {
      this.checkedData = e
    },
    // 分页事件
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getTableList()
    },
    handleShare(data) {
      console.log(data)
      this.current = data
      this.sharevisible = true
    },
    handleCancelSharing(row) {
      const isTrue = row instanceof PointerEvent
      const rows = this.checkedData
      if (isTrue) {
        if (rows.length < 1) {
          this.$message.warning('请至少选择一条数据！')
          return
        }
      }
      this.$confirm('结束后合同在借阅空间不可见', '结束借阅', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      })
        .then(() => {
          const idList = isTrue ? rows.map((item) => item.archiveShareId) : [row.archiveShareId]
          this.$api.fileManagement.cancelShare({ idList }).then(() => {
            this.$message.success('取消成功')
            this.handleQueryTablelist()
          })
        })
        .catch(() => {})
    }
  }
}
</script>
<style lang="scss" scoped>
.tab_content {
  width: 100%;
  height: calc(100% - 40px);
  .right-heade {
    border-radius: 4px;
    ::v-deep .el-input {
      width: 200px;
    }
    .search-box {
      display: flex;
      margin-bottom: 10px;
      & > div {
        margin-right: 10px;
        margin-top: 10px;
      }
    }
  }
  .right-content {
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
    width: 100%;
    height: calc(100% - 90px);
    margin-top: 16px;
    .btns-group {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      & > div {
        display: flex;
      }
      .btns-group-control {
        > div {
          margin-left: 10px;
        }
        // & > div, & > button {
        //   margin-right: 10px;
        // }
      }
    }
    .table-content {
      height: calc(100% - 45px);
      ::v-deep .el-table .el-table__body-wrapper td.el-table__cell {
        border-right: 1px solid #ededf5;
        .tooltip-over-td {
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>
