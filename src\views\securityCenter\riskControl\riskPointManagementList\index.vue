<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%;">
      <div class="role-content-left">
        <div class="toptip">
          <span class="green_line"></span>
          风险分类
        </div>
        <div class="left_content">
          <div style="display: flex; justify-content: space-around;">
            <div :class="[activeTitle == '1' ? 'tree-title-active' : '']" class="tree-title" @click="activeTitleChange(1)">风险大类</div>
            <div :class="[activeTitle == '2' ? 'tree-title-active' : '']" class="tree-title" @click="activeTitleChange(2)">风险位置</div>
          </div>
          <div style="display: flex; justify-content: space-around;">
            <div :class="[activeTitle == '3' ? 'tree-title-active' : '']" class="tree-title" @click="activeTitleChange(3)">风险等级</div>
            <div :class="[activeTitle == '4' ? 'tree-title-active' : '']" class="tree-title" @click="activeTitleChange(4)">责任部门</div>
          </div>
          <el-input v-show="activeTitle == 1" v-model.trim="filterText" style="margin-top: 10px;" clearable placeholder="输入关键字进行过滤"></el-input>
          <el-input v-show="activeTitle == 2" v-model.trim="filterText2" style="margin-top: 10px;" clearable placeholder="输入关键字进行过滤"></el-input>
          <el-input v-show="activeTitle == 3" v-model.trim="filterText3" style="margin-top: 10px;" clearable placeholder="输入关键字进行过滤"></el-input>
          <el-input v-show="activeTitle == 4" v-model.trim="filterText4" style="margin-top: 10px;" clearable placeholder="输入关键字进行过滤"></el-input>
          <div>
            <el-tree
              v-if="activeTitle == 4"
              ref="tree4"
              style="margin-top: 10px;"
              :check-strictly="true"
              :data="groupRiskArrData"
              :props="defaultProps4"
              node-key="id"
              :highlight-current="true"
              :filter-node-method="filterNode4"
              :default-expanded-keys="expanded"
              @node-click="handleNodeClick"
            ></el-tree>
          </div>
          <div>
            <el-tree
              v-if="activeTitle == 3"
              ref="tree3"
              style="margin-top: 10px;"
              :check-strictly="true"
              :data="riskLevelList"
              :props="defaultProps"
              node-key="dictCode"
              :highlight-current="true"
              :filter-node-method="filterNode3"
              :default-expanded-keys="expanded"
              @node-click="handleNodeClick"
            ></el-tree>
          </div>
          <div>
            <el-tree
              v-if="activeTitle == 2"
              ref="tree2"
              node-key="id"
              :highlight-current="true"
              :filter-node-method="filterNode2"
              :default-expanded-keys="expanded"
              style="margin-top: 10px; overflow: auto;"
              :check-strictly="true"
              :data="riskLocalArrData"
              :props="defaultProps2"
              @node-click="handleNodeClick"
            ></el-tree>
          </div>
          <el-tree
            v-if="activeTitle == 1"
            ref="tree"
            style="margin-top: 10px;"
            :check-strictly="true"
            :data="treeData"
            :props="defaultProps"
            node-key="dictCode"
            :highlight-current="true"
            :filter-node-method="$tools.filterNode"
            :default-expanded-keys="expanded"
            @node-click="handleNodeClick"
          ></el-tree>
        </div>
      </div>
      <div class="role-content-right">
        <div style="height: 100%;">
          <div class="search-from">
            <el-input v-model.trim="filters.compoundQuery" clearable placeholder="风险名称" style="width: 200px;"></el-input>
            <el-input v-model.trim="filters.responsiblePersonName" clearable placeholder="责任人" style="width: 200px;"></el-input>
            <el-button type="primary" @click="resetData">重置</el-button>
            <el-button type="primary" @click="searchClick()">查询</el-button>
          </div>
          <div class="middle_tools">
            <el-button type="primary" icon="el-icon-plus" @click="addDate">新增</el-button>
            <el-button type="primary" icon="el-icon-edit" :disabled="multipleSelection.length != 1" @click="upData">编辑</el-button>
            <el-button type="primary" icon="el-icon-delete" :disabled="multipleSelection.length != 1" @click="delData">删除</el-button>
            <!-- <el-button type="primary" icon="el-icon-edit" :disabled="!multipleSelection.length !=0" @click="assignTeam >分配班组</el-button> -->
            <!-- <div
            class="print-template" style="display:inline-block;cursor: pointer;font-size:22px;font-family:'DIN-Bold';font-weight:500;color:#6B9DFF;margin:0 20px;" @click="confirmDialogShow"
          >{{labelName}}</div> -->
            <!-- <el-button :disabled="!labelName || multipleSelection.length < 1" type="primary" icon="el-icon-printer" @click="print">打印</el-button> -->
            <el-button type="primary" :disabled="multipleSelection.length != 1" @click="goYanpan(1)">研判</el-button>
            <el-button type="primary" icon="el-icon-upload2" @click="exportClickExport">导出</el-button>
            <el-upload
              action="string"
              :on-change="handleChange"
              :file-list="templateList"
              :show-file-list="false"
              accept=".xls,.xlsx"
              style="display: inline-block;"
              :http-request="importTemplate">
              <el-button type="primary" icon="el-icon-download">导入</el-button>
            </el-upload>
            <el-button type="primary" icon="el-icon-download" @click="lecDownload">LEC模板下载</el-button>
            <el-button type="primary" icon="el-icon-download" @click="lscDownload">LS模板下载</el-button>
            <el-button type="primary" icon="el-icon-picture-outline" :disabled="multipleSelection.length != 1" @click="confirmDialogShow">查看二维码</el-button>
            <el-button type="primary" icon="el-icon-download" @click="exportQRCode">导出二维码</el-button>
            <el-button type="primary" icon="el-icon-download" :disabled="!multipleSelection.length >= 1" @click="riskExportWord">导出风险告知卡</el-button>
          </div>
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table v-loading="tableLoading" :data="tableData" :height="tableHeight" border stripe @row-dblclick="dblclick" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" fixed></el-table-column>
                <el-table-column type="index" width="80" label="序号" align="center">
                  <template slot-scope="scope">
                    <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="riskName" label="风险点名称" min-width="100" show-overflow-tooltip></el-table-column>
                <el-table-column prop="riskPlace" label="风险点位点(区域)或系统" show-overflow-tooltip width="180"></el-table-column>
                <el-table-column prop="riskLevel" label="风险等级" min-width="80" show-overflow-tooltip width="120">
                  <template slot-scope="scope">
                    <div v-if="scope.row.riskLevel == 1" class="table-block-text" style="background: rgb(255 0 0);" @click="look(scope.row)">重大风险</div>
                    <div v-if="scope.row.riskLevel == 2" class="table-block-text" style="background: rgb(255 97 0);" @click="look(scope.row)">较大风险</div>
                    <div v-if="scope.row.riskLevel == 3" style="background: rgb(255 255 0); color: #606266;" class="table-block-text" @click="look(scope.row)">一般风险</div>
                    <div v-if="scope.row.riskLevel == 4" style="background: rgb(0 0 255);" class="table-block-text" @click="look(scope.row)">低风险</div>
                    <div v-if="scope.row.riskLevel == 5" style="color: #606266;" class="table-block-text" @click="goYanpan(2, scope.row)">未研判</div>
                  </template>
                </el-table-column>
                <el-table-column prop="taskTeamName" show-overflow-tooltip label="责任部门" width="100"></el-table-column>
                <el-table-column prop="responsiblePersonName" show-overflow-tooltip label="责任人"></el-table-column>
                <el-table-column prop="urgentContactPhone" show-overflow-tooltip label="应急联系电话" min-width="130"></el-table-column>
                <!-- <el-table-column prop="urgentPhone" show-overflow-tooltip label="应急电话" min-width="100"></el-table-column> -->
                <!-- <el-table-column prop="riskElement" show-overflow-tooltip label="主要风险因素"  min-width="110" ></el-table-column> -->
                <!-- <el-table-column prop="operationElement" show-overflow-tooltip label="安全操作要点"  min-width="110" ></el-table-column> -->
                <!-- <el-table-column prop="controlElement" show-overflow-tooltip label="主要风险管控措施"  min-width="150" ></el-table-column> -->
                <!-- <el-table-column prop="handleElement" show-overflow-tooltip label="应急处理措施"  min-width="120" ></el-table-column> -->
                <el-table-column prop="attachmentUrl" show-overflow-tooltip label="图片" min-width="100">
                  <template slot-scope="scope">
                    <span style="color: #5188fc; cursor: pointer;" @click="showPicture(scope.row, 'attachmentUrl')">查看图片</span>
                  </template>
                </el-table-column>
                <el-table-column prop="processUrl" show-overflow-tooltip label="预案流程图" min-width="100">
                  <template slot-scope="scope">
                    <span style="color: #5188fc; cursor: pointer;" @click="showPicture(scope.row, 'processUrl')">查看图片</span>
                  </template>
                </el-table-column>
                <el-table-column prop="registerPerson" show-overflow-tooltip label="登记人"></el-table-column>
                <el-table-column prop="registerTime" label="登记时间" width="180"></el-table-column>
                <!-- <el-table-column prop="status" show-overflow-tooltip label="状态" min-width="60">
              <template slot-scope="scope">
                <span>{{scope.row.status == 0?'启用':'禁用'}}</span>
              </template>
            </el-table-column> -->
                <!-- <el-table-column label="操作" width="100">
              <template slot-scope="scope">
                <el-switch
                  @change="switchChange(scope.row)"
                  v-model="scope.row.status == 0"
                  active-color="#5188fc"
                  inactive-color="#DCDFE6"
                ></el-switch>
              </template>
            </el-table-column> -->
              </el-table>
            </div>
            <div class="contentTable-footer">
              <el-pagination
                :current-page="paginationData.currentPage"
                :page-sizes="[15, 30, 50, 100]"
                :page-size="paginationData.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="paginationData.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              ></el-pagination>
            </div>
          </div>
        </div>
      </div>
      <!-- 导出 -->
      <riskExport
        ref="dialog-export"
        :exportType="checkedData"
        :activeTitle="activeTitle"
        :materRows="multipleSelection"
        :dialogVisibleExport="dialogVisibleExport"
        :filters="filters"
        @closeDialog="closeDialogExport"
      ></riskExport>
      <!-- 研判 -->
      <conductJudged :id="id" :dialogVisible="dialogVisibleDialog2" :type="type" @closeDialog="closeDialogList2" @closeDialogRole="closeDialogRole2"></conductJudged>
      <imgCarousel :dialogVisibleImg="dialogVisibleImg" :imgArr="imgArr" @closeDialog="closeDialogImg"></imgCarousel>
      <!-- 查看二维码 -->
      <el-dialog custom-class="model-dialog" title="查看二维码" class="selectTemplate" style="text-align: left;" :visible.sync="confirmDialog" width="50%">
        <div v-loading="imgLoading" style="background-color: #fff; padding: 10px; width: 100%;">
          <div class="contentItem" style="text-align: center; margin: 0 auto;">
            <img style="width: 300px;" :src="'data:image/png;base64' + img" alt />
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="confirmDialog = false">关闭</el-button>
        </span>
      </el-dialog>
      <!-- 研判记录 -->
      <listDialog :dialogVisible="dialogVisibleDialog" :dataList="dataList" @closeDialog="closeDialogList"></listDialog>
    </div>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin.js'
import imgCarousel from '@/components/imgCarousel/imgCarousel'
import conductJudged from './components/conductJudged.vue'
import riskExport from './components/riskExport.vue'
import listDialog from './components/listDialog.vue'
import { transData } from '@/util'
import axios from 'axios'
export default {
  name: 'RiskPointManagementList',
  components: {
    imgCarousel,
    conductJudged,
    riskExport,
    listDialog
  },
  mixins: [tableListMixin],
  data() {
    return {
      templateList: [],
      imgLoading: false,
      loading: false,
      importFileDialog: false,
      id: '',
      type: 'edit',
      treeData: [],
      tableCode: 1,
      defaultProps: {
        children: 'children',
        label: 'dictLabel',
        value: 'dictValue'
      },
      defaultProps2: {
        children: 'children',
        label: 'gridName',
        value: 'id'
      },
      defaultProps4: {
        children: 'children',
        label: 'teamName',
        value: 'id'
      },
      riskLevelList: [],
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      tableData: [],
      treeLoading: false,
      tableLoading: false,
      filters: {
        compoundQuery: '',
        responsiblePersonName: ''
      },
      multipleSelection: [],
      checkedData: {},
      filterText: '',
      filterText2: '',
      filterText3: '',
      filterText4: '',
      expanded: [],
      riskType: '', // 风险大类
      companyCode: '', // 等级
      repairPlaceId: '', // 区域
      controlGroupIds: '', // 小组
      dialogVisible: false,
      dialogVisibleExport: false,
      dialogVisibleDialog: false,
      dialogVisibleDialog2: false,
      dataList: [],
      groupRiskArrData: [], // 负责班组
      riskLocalArrData: [], // 风险位置
      teamList: [],
      labelName: '打印模板预览', // 风险点标签名称
      confirmDialog: false, // 风险点标签模板弹窗
      labelList: [], // 风险点标签模板弹窗
      imgArr: [],
      dialogVisibleImg: false,
      activeTitle: 1,
      fileList: [],
      img: '',
      downLoading: false
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    },
    filterText2(val) {
      this.$refs.tree2.filter(val)
    },
    filterText3(val) {
      this.$refs.tree3.filter(val)
    },
    filterText4(val) {
      this.$refs.tree4.filter(val)
    }
  },
  mounted() {},
  created() {
    this.getDictValue()
    this.getTreeData()
  },
  methods: {
    getDictValue() {
      this.$api
        .ipsmGetDictValue({
          dictType: 'risk_level_judge',
          isShowParent: 0
        })
        .then((res) => {
          if (res.code == 200) {
            this.riskLevelList = res.data
          }
        })
      // 获取分配班组
      this.groupRiskArrData = []
      this.$api.ipsmGetControlGroupInfoList({}).then((res) => {
        res.data.list.map((v) => {
          if (v.parent != '#') {
            this.groupRiskArrData.push(v)
          }
        })
      })
      // 获取风险位置
      this.$api.ipsmGetGridList({}).then((res) => {
        let treeList = transData(res.data, 'id', 'parentId', 'children')
        this.riskLocalArrData = treeList
      })
    },
    searchClick() {
      this.paginationData.currentPage = 1
      this.getTableData(true)
    },
    // --------------新增，修改，详情
    dblclick(val) {
      this.$router.push({
        name: 'ipsmAddRisk',
        query: { type: 'check', id: val.id }
      })
    },
    addDate() {
      this.$router.push({
        name: 'ipsmAddRisk',
        query: { type: 'add', dictValue: this.checkedData.dictValue }
      })
    },
    upData() {
      this.$router.push({
        name: 'ipsmAddRisk',
        query: {
          type: 'edit',
          id: this.multipleSelection[0].id
        }
      })
    },
    delData(val) {
      this.$confirm('确认删除?', '提醒', { type: 'warning' }).then((res) => {
        this.$api
          .ipsmRiskManageDeleteRisk({
            id: this.multipleSelection[0].id
          })
          .then((res) => {
            if (res.code == 200) {
              // 删除最后一页的最后一条数据时跳转回最后一页的上一页
              this.paginationData.currentPage = this.$tools.paginationData(this.paginationData.total, this.paginationData.pageSize, this.paginationData.currentPage)
              this.$message.success(res.message)
            } else {
              this.$message.error(res.message)
            }
            // this.getTreeData();
            this.getTableData()
          })
      })
    },
    // 状态修改
    switchChange(val) {
      // this.tableLoading = true
      // this.$http
      //   .riskManageRiskUpdateStatus({
      //     id: val.id,
      //     status: val.status == 0 ? 1 : 0
      //   })
      //   .then((res) => {
      //     if (res.code == 200) {
      //       this.$message.success(res.message)
      //     } else {
      //       this.$message.warning(res.message)
      //     }
      //     this.getTableData()
      //   })
    },
    assignTeam() {
      // this.dialogVisible = true
    },
    closeDialog(val) {
      // this.dialogVisible = false
    },
    closeDialogImg() {
      this.dialogVisibleImg = false
      this.imgArr = []
    },
    showPicture(row, type) {
      if (row.attachmentUrl.length > 0) {
        this.$api
          .ipsmGetPictureUrls({
            repairAttachmentUrl: type == 'attachmentUrl' ? row.attachmentUrl : row.processUrl
          })
          .then((res) => {
            res.data.repairAttachmentUrl.forEach(i => {
              this.imgArr.push(this.$tools.imgUrlTranslation(i))
            })
          })
        this.dialogVisibleImg = true
      } else {
        this.$message.error('该记录无图片')
      }
    },
    filterNode2(value, data) {
      if (!value) return true
      return data.gridName.indexOf(value) !== -1
    },
    filterNode3(value, data) {
      if (!value) return true
      return data.dictLabel.indexOf(value) !== -1
    },
    filterNode4(value, data) {
      if (!value) return true
      return data.teamName.indexOf(value) !== -1
    },
    /**
     * @description: 分配班组
     * @param {type}
     * @return {type}
     */
    closeDialogRole() {
      // if (this.teamList.length == 0) {
      //   return this.$message.error('请选择要分配的班组')
      // }
      // this.dialogVisible = false
      // let riskIds = []
      // this.multipleSelection.map((v) => {
      //   riskIds.push(v.id)
      // })
      // let teamId = []
      // let teamName = []
      // this.teamList.map((v) => {
      //   teamName.push(v.text)
      //   teamId.push(v.id)
      // })
      // let data = {
      //   riskIds: riskIds.toString(),
      //   teamId: teamId.toString(),
      //   teamName: teamName.toString()
      // }
      // this.$http.riskMAnageAssignTaskTeam(data).then((res) => {
      //   if (res.code == 200) {
      //     this.$message.success(res.message)
      //   } else {
      //     this.$message.error(res.message)
      //   }
      //   this.getTableData()
      // })
    },
    closeDialogRole2() {
      this.type = 'edit'
      this.dialogVisibleDialog2 = false
      this.getTableData()
    },
    look(row) {
      this.$api
        .ipsmGetRiskJudgeRecord({
          riskId: row.id
        })
        .then((res) => {
          if (res.code == 200) {
            this.dataList = res.data.riskJudgeRecordHistory
          } else {
            this.$message.error(res.message)
          }
        })
      this.dialogVisibleDialog = true
    },
    // 研判
    goYanpan(i, row) {
      this.type = 'add2'
      if (i == 2) {
        this.multipleSelection[0] = row
      }
      this.id = this.multipleSelection[0].id
      this.dialogVisibleDialog2 = true
    },
    closeDialogList() {
      this.dialogVisibleDialog = false
    },
    closeDialogList2() {
      this.type = 'edit'
      this.id = ''
      this.dialogVisibleDialog2 = false
    },
    // 勾选
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 班组勾选
    teamTableChange(val) {
      // this.teamList = val
    },
    // 获取风险分类
    getTreeData() {
      this.treeLoading = true
      this.$api
        .ipsmGetDictValue({
          dictType: 'risk_order_type',
          isShowParent: 0
        })
        .then((res) => {
          if (res.code == '200' && res.data.length > 0) {
            let arr = transData(res.data, 'dictCode', 'parentCode', 'children')
            this.treeData = arr
            this.expanded = [this.treeData[0].dictCode]
            this.riskType = [this.treeData[0].dictValue]
            this.checkedData = this.treeData[0]
            this.$nextTick(() => {
              this.$refs.tree.setCurrentKey(this.checkedData)
            })
            this.getTableData()
          }
          this.treeLoading = false
        })
    },
    // 风险分类选择
    activeTitleChange(i) {
      this.activeTitle = i
      let ref = ''
      switch (i) {
        case 1:
          this.riskType = [this.treeData[0].dictValue]
          this.checkedData = this.treeData[0]
          ref = 'tree'
          break
        case 2:
          this.repairPlaceId = [this.riskLocalArrData[0].id]
          this.checkedData = this.riskLocalArrData[0]
          ref = 'tree2'
          break
        case 3:
          this.riskLevel = [this.riskLevelList[0].dictValue]
          this.checkedData = this.riskLevelList[0]
          ref = 'tree3'
          break
        case 4:
          this.controlGroupIds = [this.groupRiskArrData[0].id]
          this.checkedData = this.groupRiskArrData[0]
          ref = 'tree4'
          break
      }
      this.$nextTick(() => {
        this.$refs[ref].setCurrentKey(this.checkedData)
      })
      this.getTableData()
    },
    // 树状图点击
    handleNodeClick(data) {
      // this.riskType = data.dictValue;
      // this.tableCode = Number(data.dictValue);
      // this.paginationData.currentPage = 1;
      this.checkedData = data
      this.tableCode = this.activeTitle == 1 ? this.checkedData.dictValue : ''
      // this.$refs.tree.setCurrentKey(this.checkedData);
      this.getTableData()
    },
    getTableData(type) {
      let data = {
        queryType: 1,
        pageSize: this.paginationData.pageSize,
        currentPage: this.paginationData.currentPage,
        riskName: this.filters.compoundQuery,
        responsiblePersonName: this.filters.responsiblePersonName, // 责任人
        riskType: this.activeTitle == 1 ? this.checkedData.dictValue : '',
        repairPlaceId: this.activeTitle == 2 ? this.checkedData.id : '',
        riskLevel: this.activeTitle == 3 ? this.checkedData.dictValue : '',
        controlGroupIds: this.activeTitle == 4 ? this.checkedData.id : ''
      }
      this.tableLoading = true
      // riskManageFindList
      this.$api.ipsmRiskManageFindList(data).then((res) => {
        this.tableLoading = this.$store.state.loadingShow
        if (res.code == 200) {
          this.tableDataList = res.data
          this.tableData = res.data.list
          this.paginationData.total = parseInt(res.data.sum)
        } else if (res.message) {
          this.$message.error(res.message)
        }
      })
    },
    resetData() {
      this.filters.compoundQuery = ''
      this.filters.responsiblePersonName = ''
      this.searchClick()
    },
    handleSizeChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getTableData()
    },
    // 导出相关
    closeDialogExport() {
      this.dialogVisibleExport = false
    },
    exportClickExport() {
      this.dialogVisibleExport = true
    },
    // 导出二维码
    exportQRCode() {
      this.downLoading = true
      let userInfo = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      // let unitCode = baseInfo.unitCode
      // let hospitalCode = baseInfo.hospitalCode
      let riskIdList = []
      this.multipleSelection.map((item) => {
        riskIdList.push(item.id)
      })
      let data = {
        unitCode: userInfo.unitCode || 'BJSYGJ',
        hospitalCode: userInfo.hospitalCode || 'BJSJTYY',
        controlGroupIds: userInfo.controlGroupIds,
        hospitalName: userInfo.hospitalName,
        platformFlag: 1,
        unitName: userInfo.unitName,
        userId: userInfo.userId,
        userName: userInfo.name,
        positionType: userInfo.positionType,
        roleCode: userInfo.roleCode,
        ids: riskIdList.join(',')
      }
      axios({
        method: 'get',
        url: __PATH.VUE_AQ_URL + 'riskManageController/exportQRCode',
        params: data,
        responseType: 'blob',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      }).then((res) => {
        console.log(res, 'res')
        let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
        console.log(name)
        let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
        let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
        // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
        let a = document.createElement('a')
        a.href = url
        a.download = decodeURI(name)
        a.click()
        // 释放这个临时的对象url
        window.URL.revokeObjectURL(url)
        this.downLoading = false
      })
      // let url =  `${
      //   __PATH.IDPS_URL
      // }riskManageController/exportQRCode?unitCode=${unitCode}&hospitalCode=${hospitalCode}`;
      // location.href = url;
    },
    // 导出风险告知卡
    riskExportWord() {
      this.downLoading = true
      // let baseInfo = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      // let unitCode = baseInfo.unitCode
      // let hospitalCode = baseInfo.hospitalCode
      let riskIdList = []
      this.multipleSelection.map((item) => {
        riskIdList.push(item.id)
      })
      let riskId = riskIdList.join(',')
      const userInfo = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      let data = {
        unitCode: userInfo.unitCode || 'BJSYGJ',
        hospitalCode: userInfo.hospitalCode || 'BJSJTYY',
        controlGroupIds: userInfo.controlGroupIds,
        hospitalName: userInfo.hospitalName,
        platformFlag: 1,
        unitName: userInfo.unitName,
        userId: userInfo.userId,
        userName: userInfo.name,
        positionType: userInfo.positionType,
        roleCode: userInfo.roleCode,
        riskIds: riskId
      }
      axios({
        method: 'get',
        url: __PATH.VUE_AQ_URL + 'riskManageController/exportWord',
        params: data,
        responseType: 'blob',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      }).then((res) => {
        console.log(res, 'res')
        if (res.status == 200 && !res.data.code) {
          let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
          let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          // 释放这个临时的对象url
          window.URL.revokeObjectURL(url)
          if (res.data.code == 500) {
            this.$message.error(res.data.message)
          } else if (res.status == 200 && !res.data.code) {
            this.$message.success('导出成功')
          } else {
            this.$message.error('导出失败')
          }
        }
        this.downLoading = false
      })
      // let url =  `${
      //   __PATH.IDPS_URL
      // }riskManageController/exportWord?unitCode=${unitCode}&hospitalCode=${hospitalCode}&riskIds=${riskId}`;
      // location.href = url;
    },
    // 查看二维码
    confirmDialogShow() {
      this.confirmDialog = true
      this.imgLoading = true
      this.$api
        .ipsmQueryQRCode({
          riskId: this.multipleSelection[0].id,
          riskName: this.multipleSelection[0].riskName
        })
        .then((res) => {
          this.img = res.data.imgUrl
          this.imgLoading = false
        })
    },
    // 打印风险点标签
    print() {
      // if (this.multipleSelection.length < 1) {
      //   this.$message.error('请至少选择一条数据进行操作')
      // } else {
      //   let vm = this
      //   let _idarr = []
      //   let baseInfo = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      //   let unitCode = baseInfo.unitCode
      //   let hospitalCode = baseInfo.hospitalCode
      //   this.multipleSelection.forEach(function (item) {
      //     _idarr.push(item.id)
      //   })
      //   let param = `${__PATH.IDPS_URL}riskController/exportRiskPdf?unitCode=${unitCode}&hospitalCode=${hospitalCode}&ids=${_idarr.join(',')}`
      //   location.href = param
      // }
    },
    // -----------------------------------添加图片-----------------
    cancelFile() {
      this.importFileDialog = false
      this.fileList = []
    },
    // 限制文件大小
    beforeAvatarUpload(file) {
      const isLt50M = file.size / 1024 / 1024 < 50
      if (!isLt50M) {
        this.$message.error('上传图片大小不能超过 50MB!')
        return false
      }
      if (file.name.indexOf(',') != -1) {
        this.$message.error('非法的文件名')
        return false
      }
    },
    /**
     * 删除文件
     */
    handleRemove(file, fileList) {
      this.fileList = []
    },
    /**
     * 文件上传成功
     */
    handlePreview(file) {},
    /**
     * 图片文件
     */
    httpRequest(item) {
      this.fileList.push(item.file)
    },
    handleExceed(files, fileList) {
      this.$message.warning('最多选择一个文件')
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`)
    },
    lecDownload() {
      let baseInfo = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      // let param = `${__PATH.VUE_AQ_URL}riskManageController/exportLecTemplateExcel?unitCode=${baseInfo.unitCode}&hospitalCode=${baseInfo.hospitalCode}`
      // location.href = param
      const data = {
        unitCode: baseInfo.unitCode,
        hospitalCode: baseInfo.hospitalCode
      }
      axios({
        method: 'get',
        url: __PATH.VUE_AQ_URL + 'riskManageController/exportLecTemplateExcel',
        params: data,
        responseType: 'arraybuffer',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      }).then(res => {
        if (res.status == 200 && !res.data.code) {
          let name = '风险点LEC模板.xls'
          let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
          // let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
          let url = URL.createObjectURL(blob)
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          // 释放这个临时的对象url
          window.URL.revokeObjectURL(url)
          if (res.data.code == 500) {
            this.$message.error(res.data.message)
          } else if (res.status == 200 && !res.data.code) {
            this.$message.success('下载成功')
          } else {
            this.$message.error('下载失败')
          }
        }
      })
    },
    lscDownload() {
      let baseInfo = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      // let param = `${__PATH.VUE_AQ_URL}riskManageController/exportLsTemplateExcel?unitCode=${baseInfo.unitCode}&hospitalCode=${baseInfo.hospitalCode}`
      // location.href = param
      const data = {
        unitCode: baseInfo.unitCode,
        hospitalCode: baseInfo.hospitalCode
      }
      axios({
        method: 'get',
        url: __PATH.VUE_AQ_URL + 'riskManageController/exportLsTemplateExcel',
        params: data,
        responseType: 'arraybuffer',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      }).then(res => {
        if (res.status == 200 && !res.data.code) {
          let name = '风险点LS模板.xls'
          let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
          // let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
          let url = URL.createObjectURL(blob)
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          // 释放这个临时的对象url
          window.URL.revokeObjectURL(url)
          if (res.data.code == 500) {
            this.$message.error(res.data.message)
          } else if (res.status == 200 && !res.data.code) {
            this.$message.success('下载成功')
          } else {
            this.$message.error('下载失败')
          }
        }
      })
    },
    handleChange(file, fileList) {
      this.templateList = [file]
    },
    importTemplate() {
      let baseInfo = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      let fomrData = new FormData()
      this.templateList.forEach(i => {
        fomrData.append('file', i.raw)
      })
      fomrData.append('unitCode', baseInfo.unitCode)
      fomrData.append('hospitalCode', baseInfo.hospitalCode)
      fomrData.append('userId', baseInfo.id)
      fomrData.append('userName', baseInfo.name)
      axios({
        method: 'post',
        url: __PATH.VUE_AQ_URL + 'riskManageController/importRiskExcelData',
        data: fomrData,
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      }).then(res => {
        if (res.data.code == '200') {
          this.$message.success(res.data.message)
          this.getTableData()
        } else {
          this.$message.error(res.data.message)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
// el-tree-node is-expanded is-focusable
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}
::v-deep .el-tree-node__content {
  height: 32px;
}
.role-content {
  height: 100%;
  display: flex;
  .role-content-left {
    width: 268px;
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    margin-right: 12px;
    .left_content {
      height: calc(100% - 60px);
      padding: 10px;
      overflow: auto;
    }
  }
  .role-content-right {
    flex: 1;
    height: 100%;
    min-width: 0;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    .search-from {
      padding-bottom: 12px;
      & > div {
        margin-right: 10px;
      }
      & > button {
        margin-top: 12px;
      }
    }
    .middle_tools {
      margin-bottom: 10px;
      .el-button {
        margin: 0 10px 10px 0;
      }
      .print-template {
        display: inline-block;
        cursor: pointer;
        font-size: 22px;
        font-family: DIN-Bold;
        font-weight: 500;
        color: #6b9dff;
        margin-right: 40px;
      }
    }
    .contentTable {
      height: calc(100% - 150px);
      display: flex;
      flex-direction: column;
      .contentTable-main {
        height: calc(100% - 40px);
        flex: 1;
        overflow: auto;
      }
      .contentTable-footer {
        padding: 10px 0 0;
      }
    }
  }
}
.tree-title-active {
  background: #5188fc;
  border-color: #5188fc;
  color: #fff;
}
.table-block-text {
  width: 80px;
  line-height: 24px;
  text-align: center;
  cursor: pointer;
  color: #fff;
}
.tree-title {
  border: 1px solid #ebeef5;
  width: 40%;
  margin-bottom: 5px;
  line-height: 30px;
  border-radius: 4px;
  text-align: center;
  color: #5188fc;
  cursor: pointer;
}
.tree-title-active {
  background: #5188fc;
  border-color: #5188fc;
  color: #fff;
}
::v-deep .el-tree-node__label {
  display: inline-block;
  white-space: normal;
  text-align: left;
}
::v-deep .el-tree-node__content {
  height: auto;
}
</style>
