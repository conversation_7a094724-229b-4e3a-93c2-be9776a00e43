<template>
  <el-dialog
    v-dialogDrag
    custom-class="model-dialog"
    :modal="true"
    :close-on-click-modal="false"
    :title="detail.handleType == 'add' ? '新增配置' : '编辑配置'"
    :visible="visible"
    width="60%"
    :before-close="dialogBeforeClose"
  >
    <div class="dialog-content">
      <el-form ref="form" :model="form" label-width="120px" :rules="rules">
        <div class="table-title">
          <span class="title"> <i></i>基本信息</span>
        </div>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="作业类型" prop="workTypeId">
              <el-select v-model="form.workTypeId" multiple placeholder="请选择" @change="workTypeChange">
                <el-option v-for="(item, index) in jobTypeList" :key="index" :label="item.name" :value="item.id" :disabled="item.status == 0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="巡检模板" prop="templateId">
              <el-select v-model="form.templateId" placeholder="请选择巡检模版">
                <el-option v-for="item in templateList" :key="item.id" :label="item.templateName" :value="item.id"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <div class="table-title">
          <span class="title"> <i></i>执行信息</span>
        </div>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="周期类型" prop="cycleType">
              <el-select v-model="form.cycleType" placeholder="请选择周期类型" :disabled="detail.handleType == 'edit'" @change="changeCycleType">
                <el-option v-for="item in cycleList" :key="item.cycleType" :label="item.label" :value="item.cycleType"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.cycleType == '8' || form.cycleType == '5'" label="开始日期" prop="startDate">
              <el-date-picker v-model="form.startDate" value-format="yyyy-MM-dd" type="date" :disabled="detail.handleType == 'edit'" placeholder="请选择开始日期"> </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.cycleType == '3'" label="开始月份" prop="startMonth">
              <el-select v-model="form.startMonth" placeholder="请选择" :disabled="detail.handleType == 'edit'">
                <el-option v-for="item in startMonthArr" :key="item.id" :label="item.name" :value="item.id"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.cycleType == '3'" label="开始日期" prop="startDay">
              <el-select v-model="form.startDay" placeholder="请选择" :disabled="detail.handleType == 'edit'">
                <el-option v-for="item in startDateArr" :key="item.id" :label="item.name" :value="item.id"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item v-if="form.cycleType == '0' || form.cycleType == '2'" label="开始日期" prop="startDate">
              <el-select v-model="form.startDate" placeholder="请选择" :disabled="detail.handleType == 'edit'">
                <el-option v-for="item in startDateArr" :key="item.id" :label="item.name" :value="item.id"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="时间" prop="timeInterval">
              <el-time-picker
                v-model="form.timeInterval"
                value-format="HH:mm:ss"
                is-range
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                placeholder="选择时间范围"
                @change="timeChange"
              >
              </el-time-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.cycleType == '6'" label="频次" prop="frequency">
              <el-input v-model="form.frequency" type="number" style="width: 180px" placeholder="请输入频次" max="12" min="1">
                <template slot="append">次</template>
              </el-input>
              <el-radio-group v-model="form.customTimeConfig" fill="#3a62d8" size="small" style="vertical-align: top; margin-left: 8px">
                <el-radio-button v-if="checkFrequencyType() != false" label="2">自定义</el-radio-button>
                <el-radio-button label="1">最小间隔</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.cycleType != '8' && form.customTimeConfig == '1'" label="最小间隔" prop="minInterval">
              <el-input v-model="form.minInterval" type="number" placeholder="请输入最小间隔" min="1">
                <template slot="append">{{ form.cycleType == '6' ? '分钟' : '天' }}</template>
              </el-input>
            </el-form-item>
            <el-form-item v-if="form.cycleType == '6' && form.customTimeConfig == '2'" label="自定义时间" label-width="100px" class="red-star">
              <el-button type="primary" @click="openCustomTimeDialog">配置</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.cycleType != '6'" label="完成期限" prop="finalTime">
              <el-input v-model="form.finalTime" type="number" min="1" placeholder="请输入完成期限">
                <template slot="append">天</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="'巡检部门'" prop="maintainDeptId">
              <el-select v-model="form.maintainDeptId" placeholder="请选择部门" clearable multiple filterable style="width: 100%">
                <el-option v-for="item in deptList" :key="item.id" :label="item.deptName" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-dialog
        v-dialogDrag
        title="自定义时间"
        :modal="false"
        custom-class="model-dialog"
        append-to-body
        :visible.sync="customDialogVisible"
        width="25%"
        :before-close="handleClose"
      >
        <div v-for="(item, index) in customTimeArr" :key="index" class="time-picker-box">
          <span>{{ index + 1 }}</span>
          <el-time-picker
            v-model="item.customtime"
            value-format="HH:mm:ss"
            is-range
            start-placeholder="开始时间"
            range-separator="至"
            end-placeholder="结束时间"
            placeholder="选择时间范围"
          >
          </el-time-picker>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button v-if="detail.handleType != 'detail'" type="primary" plain @click="resetCustom">重 置</el-button>
          <el-button v-if="detail.handleType != 'detail'" type="primary" @click="customDialogConfirm">确 定</el-button>
          <el-button v-if="detail.handleType == 'detail'" type="primary" @click="handleClose">关 闭</el-button>
        </span>
      </el-dialog>
    </div>
    <div slot="footer">
      <el-button @click="dialogBeforeClose">取 消</el-button>
      <el-button type="primary" @click="submit">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import moment from 'moment'
export default {
  model: {
    prop: 'visible',
    event: 'update:visible'
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    detail: {
      type: Object,
      default: () => {
        return {
          handleType: 'add',
          configId: ''
        }
      }
    }
  },
  data() {
    return {
      form: {
        workTypeId: [],
        templateId: '',
        cycleType: '8',
        frequency: '',
        customTimeConfig: '1',
        startDate: '',
        startMonth: '',
        startDay: '',
        finalTime: '',
        minInterval: '',
        customTimeJson: [],
        maintainDeptId: [],
        timeInterval: ['', '']
      },
      rules: {
        workTypeId: [{ required: true, message: '请选择作业类型', trigger: 'change' }],
        templateId: [{ required: true, message: '请选择模板', trigger: 'change' }],
        cycleType: [{ required: true, message: '请选择周期类型', trigger: 'change' }],
        frequency: [{ required: true, message: '请输入执行频率', trigger: 'blur' }],
        startDate: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
        startDay: [{ required: true, message: '请选择结束日期', trigger: 'change' }],
        startMonth: [{ required: true, message: '请选择开始月份', trigger: 'change' }],
        timeInterval: [{ required: true, message: '请选择时间', trigger: 'change' }],
        minInterval: [{ required: true, message: '请输入最小间隔', trigger: 'blur' }],
        finalTime: [{ required: true, message: '请输入完成期限', trigger: 'blur' }],
        maintainDeptId: [{ required: true, message: '请选择所属部门', trigger: 'change' }]
      },
      jobTypeList: [],
      templateList: [],
      cycleList: [
        {
          cycleType: '8',
          label: '单次'
        },
        {
          cycleType: '6',
          label: '每日'
        },
        {
          cycleType: '0',
          label: '每周'
        },
        {
          cycleType: '2',
          label: '每月'
        },
        {
          cycleType: '3',
          label: '季度'
        },
        {
          cycleType: '5',
          label: '全年'
        }
      ],
      deptList: [],
      deptTree: {
        value: 'id',
        label: 'deptName',
        multiple: true,
        checkStrictly: true
      },
      startDateArr: [],
      startMonthArr: [
        {
          id: '1',
          name: '第一个月'
        },
        {
          id: '2',
          name: '第二个月'
        },
        {
          id: '3',
          name: '第三个月'
        }
      ],
      pickerOptions: {},
      customTimeArr: [],
      customDialogVisible: false
    }
  },
  mounted() {
    this.getDeptList()
    this.getWorkList()
    this.getTemplateList()
    if (this.detail.configId) {
      this.getDetail()
    }
  },
  methods: {
    timeChange(val) {
      console.log(val)
    },
    initCycle(val) {
      if (val == '0') {
        const dateName = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        this.startDateArr = []
        for (let i = 0; i < 7; i++) {
          const item = {
            id: i + 1 + '',
            name: '每' + dateName[i]
          }
          this.startDateArr.push(item)
        }
      } else if (val == '2') {
        this.startDateArr = []
        for (let i = 0; i < 30; i++) {
          const item = {
            id: i + 1 + '',
            name: '每月' + (i + 1) + '日'
          }
          this.startDateArr.push(item)
        }
      } else if (val == '3') {
        this.startDateArr = []
        for (let i = 0; i < 30; i++) {
          const item = {
            id: i + 1 + '',
            name: '每月' + (i + 1) + '日'
          }
          this.startDateArr.push(item)
        }
      }
    },
    // 获取详情
    getDetail() {
      this.$api.workMaintainConfigDetail({ id: this.detail.configId }).then((res) => {
        if (res.code == '200') {
          this.form = res.data
          let arr = res.data.workTypeId.split(',').map((el) => {
            return parseInt(el, 10)
          })
          this.form.workTypeId = arr
          this.$set(this.form, 'timeInterval', [res.data.createStartTime, res.data.createEndTime])
          this.form.maintainDeptId = res.data.maintainDeptId.split(',')
          this.initCycle(this.form.cycleType)
        }
      })
    },
    // 获取作业类型
    getWorkList() {
      this.$api
        .businessFormList({
          name: '',
          assignmentType: '0',
          page: 1,
          pageSize: 999
        })
        .then((res) => {
          if (res.code === '200') {
            this.jobTypeList = res.data.records
          } else {
            throw res.message
          }
        })
    },
    // 作业类型更改
    workTypeChange(val) {
      let arr = []
      val.forEach((item) => {
        let name = this.jobTypeList.find((el) => el.id === item).name
        arr.push(name)
      })
      this.form.workTypeName = arr.join(',')
    },
    // 获取巡检模板
    getTemplateList() {
      let param = {
        templateName: '',
        templateType: '',
        size: 999,
        currentPage: 1
      }
      this.$api
        .workMaintainList(param)
        .then((res) => {
          if (res.code == 200) {
            this.templateList = res.data.list
          }
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    // 获取巡检部门
    getDeptList() {
      this.$api.getDeptList().then((res) => {
        if (res.code == 200) {
          this.deptList = res.data
        }
      })
    },
    // 改变周期类型
    changeCycleType(val) {
      this.form.startDate = ''
      this.form.frequency = ''
      this.initCycle(val)
    },
    // 周期更改
    checkFrequencyType() {
      let arr = [0, 2, 3, 5]
      if (arr.includes(this.form.cycleType)) {
        this.form.customTimeConfig = '1'
        return false
      } else {
        return true
      }
    },
    openCustomTimeDialog() {
      if (!this.form.frequency || Number(this.form.frequency) > 12 || this.form.frequency == '0') {
        return this.$message.warning('请填写正确的频次')
      }
      if (Number(this.form.frequency) != this.customTimeArr.length) {
        this.customTimeArr = []
      }
      if (this.customTimeArr.length == 0) {
        let arr = []
        for (let i = 0; i < Number(this.form.frequency); i++) {
          arr.push({ customStartTime: '' })
        }
        this.customTimeArr = arr
      }
      this.customDialogVisible = true
    },
    handleClose() {
      this.customDialogVisible = false
    },
    resetCustom() {
      let customLength = this.customTimeArr.length
      let arr = []
      for (let i = 0; i < customLength; i++) {
        arr.push({ customtime: '' })
      }
      this.customTimeArr = arr
      this.form.customTimeJson = []
    },
    customDialogConfirm() {
      let arr = []
      this.customTimeArr.forEach((item) => {
        if (item.customtime) {
          arr.push({
            startTime: item.customtime[0],
            endTime: item.customtime[1]
          })
        }
      })
      this.form.customTimeJson = arr
      this.customDialogVisible = false
    },
    dialogBeforeClose() {
      this.$emit('update:visible', false)
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.form.cycleType != '8' && this.form.cycleType != '5' && this.form.cycleType != '0') {
            if (this.form.cycleType == '2') {
              if (moment(this.form.startDate) > moment().date()) {
                this.$message({
                  type: 'error',
                  message: '开始日期必须大于今天'
                })
                return false
              }
            } else {
              if (moment(this.form.startDate) < moment()) {
                this.$message({
                  type: 'error',
                  message: '开始日期必须大于今天'
                })
                return false
              }
            }
          }
          let nameArr = []
          this.form.maintainDeptId.forEach((item) => {
            this.deptList.forEach((i) => {
              if (item == i.id) {
                nameArr.push(i.deptName)
              }
            })
          })
          let params = this.form
          params.workTypeId = this.form.workTypeId.join(',')
          params.createStartTime = this.form.timeInterval[0]
          params.createEndTime = this.form.timeInterval[1]
          params.maintainDeptId = this.form.maintainDeptId.join(',')
          params.maintainDeptName = nameArr.join(',')
          let startDate = ''
          if (this.form.cycleType == '8' || this.form.cycleType == '5') {
            startDate = moment(this.form.startDate).format('YYYY-MM-DD')
          } else if (this.form.cycleType == '0' || this.form.cycleType == '2') {
            startDate = this.form.startDate
          } else if (this.form.cycleType == '3') {
            startDate = this.form.startMonth + '-' + this.form.startDay
          }
          params.startDate = startDate
          // if (this.form.customTimeConfig != '1') {
          //   params.customTimeJson = JSON.stringify(this.form.customTimeJson)
          // }
          this.$api.workMaintainConfigInsert(params).then((res) => {
            if (res.code == 200) {
              this.$message.success('添加成功')
              this.$emit('success', false)
              this.dialogBeforeClose()
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 16px;
}
.table-title {
  .title {
    width: 80px;
    padding: 0;
    font-size: 14px;
    color: #606266;
    i {
      display: inline-block;
      width: 8px;
      height: 16px;
      border-radius: 0 8px 8px 0;
      background: #3562db;
      margin-right: 8px;
      position: relative;
      top: 2px;
    }
  }
}
.el-form {
  .el-form-item {
    .el-form-item__content {
      .el-input,
      .el-select,
      .el-date-picker,
      .el-date-editor--timerange,
      .el-date-editor--daterange,
      .el-cascader {
        width: 100%;
      }
    }
  }
}
::v-deep .model-dialog .el-dialog__body {
  display: block !important;
}
.time-picker-box {
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  height: 100%;
  width: 100%;
  padding: 16px 16px 0px;
  > span {
    width: 24px;
  }
}
.time-picker-box:last-child {
  padding-bottom: 16px;
}
</style>
