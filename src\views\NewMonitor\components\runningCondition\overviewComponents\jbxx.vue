<!-- 运行监测 -->
<template>
  <ContentCard :title="item.componentTitleShow" :scrollbarHover="true" :cstyle="{ width: '100%', height: '100%' }"
    class="drag_class" :hasMoreOper="['edit']"
    @more-oper-event="(val) => $emit('all-more-Oper', val, item.componentName, item.chartType)">
    <div slot="content" style="width: 100%; height: 100%; ">
      <p class="txt-img">{{ objData.assetsName }} <img src="../../../../../assets/images/newMonitor/police.gif" alt=""
          v-if="objData.alarm">
      </p>
      <div style="display: flex">
        <div class="content_left">
          <img src="../../../../../assets/images/defaultimg.png" alt=""
            v-if="objData.pictureUrl === null || objData.pictureUrl === ''">
          <img :src="$tools.imgUrlTranslation(objData.pictureUrl)" alt="" v-else>
        </div>
        <div class="content_right">
          <p>sn码：<em>{{ objData.factoryCode }}</em></p>
          <p>设备型号：<em>{{ objData.deviceModel }}</em></p>
          <p>归属部门：<em>{{ objData.relevantDept }}</em></p>
          <p>启用日期：<em>{{ objData.startDate }}</em></p>
        </div>
      </div>
    </div>
  </ContentCard>
</template>
<script>
export default {
  name: 'jbxx',
  props: {
    item: {
      type: Object,
      default: () => { }
    },
    systemCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      objData: {},
    }
  },
  mounted() { },
  methods: {
    jbxxData(id) {
      this.$api.getAssetsDetailByIntegratedMonitor({ id: id || '' }).then((res) => {
        if (res.code === "200") {
          this.objData = res.data
        }
      })
    },

  }
}
</script>
<style lang="scss" scoped>
.content_left {
  margin-right: 30px;

  img {
    width: 140px;
    height: 140px;
    margin: 2px 0;
  }
}

.txt-img {
  font-size: 16px;
  color: #333;

  img {
    transform: scale(0.5);
  }
}

.content_right {
  text-align: left;

  p {
    width: 100%;
    text-align: left;
    color: #96989A;
    font-size: 14px;

    em {
      color: #333;
      font-style: normal;
    }
  }
}
</style>