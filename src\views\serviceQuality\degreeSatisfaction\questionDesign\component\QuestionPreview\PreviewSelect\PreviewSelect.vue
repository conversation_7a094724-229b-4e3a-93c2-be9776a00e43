<template>
  <div>
    <div class="quesName">
      <span v-show="pathName[diff].isQuestionNum">{{ index + 1 }}、</span>
      {{ selectOption.name }}
      <span v-show="pathName[diff].isShowSubjectType" class="chexMinMax">[下拉菜单]</span>
      <span v-if="selectOption.isMust == 1" class="starRed">*</span>
    </div>
    <div class="questionContainer">
      <el-select v-model="selectedValue" :disabled="pathName[diff].isDisable" placeholder="请选择" popper-class="questionSelect" style="width: 200px;" @change="handleSelectChange">
        <el-option v-for="childitem in selectOption.optionsData" :key="childitem.id" :label="childitem.name" :value="childitem.id"></el-option>
      </el-select>
    </div>
  </div>
</template>
<script>
export default {
  name: 'PreviewSelect',
  props: {
    // 当前题目的所有相关信息
    previewOption: {
      type: Object
    },
    index: {
      type: Number
    },
    // 是否显示题目序号，true为显示，false为不显示
    isQuestionNum: {
      type: Boolean
    },
    // 表示用于在哪个页面显示该题目时的相关设置信息
    pathName: {
      type: Object
    },
    diff: {
      type: String
    }
  },
  data() {
    return {
      currentPathName: '',
      selectedValue: '',
      selectOption: {},
      storage: {}
    }
  },
  computed: {
    options: {
      set(value) {},
      get() {
        return this.selectOption.optionsData.map((item) => {
          return {
            value: item.id,
            text: item.name
          }
        })
      }
    }
  },
  created() {
    this.selectOption = this.previewOption
    if (this.pathName[this.diff].isSetDefaultValue) {
      if (this.previewOption.answers.length > 0) {
        this.selectedValue = this.previewOption.answers[0].optionId
      }
    }
  },

  methods: {
    checkValided() {
      if (this.selectOption.isMust === 0) {
        return false
      }
      // 判断当前题目是否必填，必填时才需要判断是否已答
      if (this.selectOption.isMust !== 1) {
        return true
      }
      // 判断当前题目是否已答
      if (!this.isAnswer) {
        return true
      }
      return false
    },
    doValidedFlag() {
      return this.checkValided()
    },
    doValided() {
      this.isValided = this.checkValided()
      return this.isValided
    },
    handleSelectChange(childitem) {
      this.answerVal = [
        {
          pvqId: localStorage.getItem('questId'), // 问卷id
          questionId: this.selectOption.id, // 题目id
          questionType: this.selectOption.type, // 题目类型
          optionId: childitem.id,
          optionName: childitem.name
        }
      ]
      this.$parent.$parent.$parent.addAnswer && this.$parent.$parent.$parent.addAnswer('selectFun', { old: this.storage, news: childitem })
      this.storage = childitem
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
.quesName {
  font-size: 16px;
}

.starRed {
  color: red;
}

.questionContainer {
  padding: 20px 0 0;
}

.chexMinMax {
  margin-left: 3px;
}
</style>
