<template>
  <div class="container">
    <div class="header">
      <span class="title">消息提醒</span>
      <span class="control" @click="readClick">全部已读</span>
    </div>
    <div id="getMsgScrollTop">
      <div v-infinite-scroll="recordLoad" class="list" infinite-scroll-disabled="disabled">
        <div v-for="(item, i) in msgDataList" :key="i" class="item">
          <!-- <i class="ri-mail-fill" /> -->
          <div class="svg-div">
            <div v-if="item.readStatus == 0" class="red-round"></div>
            <svg-icon class="svg" :name="msgIconList[item.msgCatId]?.icon ?? 'announcement'" />
          </div>
          <div class="info" @click="navigationTo(item)">
            <div class="title">{{ item.msgTitle }}</div>
            <div class="date">{{ item.publishTime }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="more" @click="jumpMsgRecord">进入通知列表</div>
    <template v-if="workOrderDetailCenterShow">
      <el-dialog :visible.sync="workOrderDetailCenterShow" custom-class="detailDialog main" :close-on-click-modal="false" :modal="false" :before-close="workOrderDetailCloseDialog">
        <template slot="title">
          <span class="dialog-title">{{ dialogTitle }}</span>
        </template>
        <component :is="iomsComponent" :rowData="detailObj" />
      </el-dialog>
    </template>
    <template v-if="alarmDetailShow">
      <AlarmDetailDialog ref="alarmDetail" :alarmId="alarmDetail.alarmId" :visible.sync="alarmDetailShow" @update:visible="closeAlarmDialog" />
    </template>
    <WorkOrderDetailDialog v-if="workOrderDetailShow" sourceType="alarm" :visible.sync="workOrderDetailShow" :alarmDetail="alarmDetail" :dialogDetail="workOderDialogData" />
  </div>
  <!-- <el-tabs ref="tabs" v-model="activeName" class="notification">
    <el-tab-pane label="通知 (5)" name="notice" class="container">
      <div class="list">
        <div class="item">
          <i class="ri-mail-fill" />
          <div class="info">
            <div class="title">你收到了 8 份日报</div>
            <div class="date">2020-10-10 10:00:00</div>
          </div>
        </div>
        <div class="item">
          <i class="ri-service-fill" />
          <div class="info">
            <div class="title">你收到了 3 位同事的好友申请，请及时处理</div>
            <div class="date">2020-10-10 10:00:00</div>
          </div>
        </div>
        <div class="item">
          <i class="ri-file-edit-fill" />
          <div class="info">
            <div class="title">你有 3 份合同待审批</div>
            <div class="date">2020-10-10 10:00:00</div>
          </div>
        </div>
        <div class="item">
          <i class="ri-mail-fill" />
          <div class="info">
            <div class="title">你收到了 8 份日报</div>
            <div class="date">2020-10-10 10:00:00</div>
          </div>
        </div>
        <div class="item">
          <i class="ri-service-fill" />
          <div class="info">
            <div class="title">你收到了 3 位同事的好友申请，请及时处理</div>
            <div class="date">2020-10-10 10:00:00</div>
          </div>
        </div>
      </div>
      <div class="more">进入通知列表</div>
    </el-tab-pane>
    <el-tab-pane label="消息" name="message" class="container">
      <div class="list">
        <div class="empty">你已读完所有消息</div>
      </div>
    </el-tab-pane>
    <el-tab-pane label="待办 (2)" name="todo" class="container">
      <div class="list">
        <div class="item">
          <i class="ri-bug-fill" />
          <div class="info">
            <div class="title">你有 2 个来自项目「back-stage」的 bug 待处理</div>
            <div class="date">2020-10-10 10:00:00</div>
          </div>
        </div>
        <div class="item">
          <i class="ri-git-merge-fill" />
          <div class="info">
            <div class="title">你有 3 个来自项目「back-stage」的代码合并申请，提交人：Hooray，提交备注：专业版更新</div>
            <div class="date">2020-10-10 10:00:00</div>
          </div>
        </div>
      </div>
    </el-tab-pane>
  </el-tabs> -->
</template>
<script>
import { msgIconList } from '@/util/dict.js'
export default {
  name: 'Notification',
  components: {
    workOrderDetailYS: () => import('@/views/serviceQuality/transport/components/workOrderDetailList.vue'),
    workOrderDetailWX: () => import('@/views/serviceQuality/maintenance/components/workOrderDetailList.vue'),
    workOrderDetailBJ: () => import('@/views/serviceQuality/cleaning/components/workOrderDetailList.vue'),
    workOrderDetailSSP: () => import('@/views/serviceQuality/snapshot/components/workOrderDetailList.vue')
  },
  props: {},
  data() {
    return {
      activeName: 'notice',
      alarmDetailShow: false,
      alarmDetail: {},
      workOrderDetailShow: false, // 工单详情弹窗
      workOderDialogData: [], // 工单详情传递数据
      total: 20,
      page: 1,
      loading: false,
      msgIconList: Object.freeze(msgIconList),
      msgDataList: [],
      workOrderDetailCenterShow: false, // 工单详情弹窗
      dialogTitle: '', // 弹窗标题
      detailObj: {}, // 工单详情传递数据
      iomsComponent: 'workOrderDetailWX', // 工单详情组件(维修)
      pageSize: 20
    }
  },
  computed: {
    noMore() {
      return this.msgDataList.length >= this.total
    },
    disabled() {
      return this.loading || this.noMore
    }
  },
  mounted() {
    this.messageInit()
  },
  methods: {
    messageInit() {
      // 查看sessionStorage是否有存储的分页数据 与 滚动条位置
      var msgDataPosition = JSON.parse(sessionStorage.getItem('msgDataPosition'))
      if (msgDataPosition) {
        this.page = 1
        this.total = 0
        this.msgDataList = []
        this.getMessageByUserId(msgDataPosition)
      } else {
        this.page = 1
        this.total = 0
        this.msgDataList = []
        this.getMessageByUserId()
      }
    },
    // 获取消息列表
    getMessageByUserId(msgDataPosition) {
      // this.loading = true
      const params = {
        userId: this.$store.state.user.userInfo.user.staffId,
        msgType: 0, // 0:消息 1:待办 2:todolist
        msgSysId: 0,
        page: this.page,
        // 有存储的分页数据则先请求之前历史数据
        pageSize: msgDataPosition ? msgDataPosition.page * msgDataPosition.pageSize : this.pageSize
      }
      this.$api
        .getMessageByUserId(params)
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            this.msgDataList = res.data.list.length ? this.msgDataList.concat(res.data.list) : this.msgDataList
            this.total = res.data.total
            if (msgDataPosition) {
              // 历史数据渲染加载完成 将历史的分页页数放入当前的分页页数中  下次请求接着走之前的分页  将滚动条跳至上次位置
              this.$nextTick(() => {
                this.page = msgDataPosition.page
                document.querySelector('#getMsgScrollTop').parentNode.scrollTop = msgDataPosition.scrollTop
              })
              // 清除缓存的位置  之后不在加载历史数据
              sessionStorage.removeItem('msgDataPosition')
            }
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    recordLoad() {
      this.page++
      this.loading = true
      this.getMessageByUserId()
    },
    // 跳转查看详情
    navigationTo(item) {
      // 跳转前记录当前分页页数 与请求每页数量  记录滚动条位置  存在sessionStorage
      var scrollTop = document.querySelector('#getMsgScrollTop').parentNode.scrollTop
      let msgDataPosition = {
        page: this.page,
        pageSize: this.pageSize,
        scrollTop
      }
      sessionStorage.setItem('msgDataPosition', JSON.stringify(msgDataPosition))
      // 未读状态 点击刷新notice数量
      if (item.readStatus === 0) {
        this.$store.commit('tabbar/setNoticeChange', Math.random())
      }
      const selectTypeData = msgIconList[item.msgCatId]
      // 通知公告
      if (selectTypeData.type == 'system') {
        this.$router.push({
          path: '/drag/noticeDetail',
          query: {
            msgId: item.msgId,
            msgSysId: item.msgSysId
          }
        })
        return
      }
      const params = {
        userId: this.$store.state.user.userInfo.user.staffId,
        msgId: item.msgId,
        msgSysId: item.msgSysId
      }
      this.$api.getImserverMsgInfo(params).then((res) => {
        if (res.code == 200) {
          const data = res.data
          let msgBody = {}
          try {
            msgBody = JSON.parse(data.msgBody)
          } catch (error) {
            return this.$message.error('消息体解析失败，请联系管理员')
          }
          if (!Object.keys(msgBody).length) {
            return this.$message.warning('此消息暂无详情数据')
          }
          const selectTypeData = msgIconList[data.msgCatId]
          const elements = selectTypeData?.elements?.split(',') ?? []
          // 遍历msgBody对象保留elements中的属性
          for (const key in msgBody) {
            if (!elements.includes(key)) {
              delete msgBody[key]
            }
          }
          // msgBody的元素数量不等于elements的元素数量，说明有查询属性缺失
          if (Object.keys(msgBody).length != elements.length) {
            return this.$message.warning('消息数据缺失，请联系管理员')
          }
          if (selectTypeData.type == 'ioms') {
            // 确警工单
            if (selectTypeData.typeCode == 16) {
              this.workOderDialogData = [
                {
                  workTypeName: msgBody.type,
                  id: msgBody.workNum,
                  active: true
                }
              ]
              this.workOrderDetailShow = !this.workOrderDetailShow
            } else {
              this.detailObj = {
                id: msgBody.workNum
              }
              this.dialogTitle = `${msgBody.type}（${msgBody.flowType}）`
              if (selectTypeData?.component) {
                this.iomsComponent = selectTypeData?.component
                this.workOrderDetailCenterShow = true
              } else {
                this.$message.warning('该工单类型暂未开放')
              }
            }
          } else if (selectTypeData.type == 'icis') {
            this.$router.push({
              path: '/InspectionManagement/taskManagement/taskDetail',
              query: {
                taskId: msgBody.id,
                type: 'detail',
                systemType: selectTypeData.typeCode
              }
            })
          } else if (selectTypeData.type == 'warn') {
            this.alarmDetail = msgBody
            // this.alarmDetailShow = true
            this.$router.push({
              path: '/allAlarm/alarmDetail',
              query: {
                alarmId: msgBody.alarmId
              }
            })
          } else if (selectTypeData.type == 'life') {
            // 寿命提醒功能 跳转设备详情
            const { id, assetsId, configType } = msgBody
            this.$router.push({
              name: 'addDevice',
              query: {
                alarmId: msgBody.alarmId,
                type: 'details',
                configType,
                id,
                assetsId
              }
            })
          } else if (selectTypeData.type == 'approve') {
            // 审批消息 跳转审批详情
            const { projectCode, processInstanceId, status } = msgBody
            this.$router.push({
              path: '/operation/flowFormDetail',
              query: {
                detailId: projectCode,
                flowId: processInstanceId,
                handleType: 'detail',
                // handleType: status == 'finished' ? 'detail' : 'handle',
                status
              }
            })
          } else {
            this.$message.warning('该消息类型暂未开放')
          }
        }
      })
    },
    // 工单详情弹窗关闭
    workOrderDetailCloseDialog() {
      this.workOrderDetailCenterShow = false
      this.messageInit()
    },
    // 报警详情弹窗关闭
    closeAlarmDialog() {
      this.messageInit()
    },
    // 跳转至消息列表
    jumpMsgRecord() {
      this.$router.push({
        path: '/drag/todoAndmsgRecord?page=msg'
      })
    },
    // 全部已读
    readClick() {
      this.$confirm('是否将所有消息标记为已读?', '提示', {
        cancelButtonClass: 'el-button--primary is-plain',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = {
          userId: this.$store.state.user.userInfo.user.staffId,
          msgType: 0,
          msgSysId: 0
        }
        this.$api.onekeyreadpc(params).then((res) => {
          if (res.code == 200) {
            this.$message.success('消息阅读成功')
            this.$store.commit('tabbar/setNoticeChange', Math.random())
          } else {
            this.$message.error(res.message)
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.notification {
  margin-top: -10px;
  ::v-deep .el-tabs__header {
    margin-bottom: 0;
  }
  ::v-deep .el-tabs__nav-scroll {
    text-align: center;
    .el-tabs__nav {
      display: inline-block;
      margin: 0 auto;
      float: none;
      .el-tabs__item {
        padding: 0 12px;
      }
    }
  }
}
.container {
  .header {
    padding: 5px 10px 15px 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e4e7ed;
    .title {
      font-size: 16px;
      font-family: PingFang SC-Semibold, PingFang SC;
      font-weight: 600;
      color: #333333;
    }
    .control {
      font-size: 14px;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #3562db;
      cursor: pointer;
    }
  }
  .list {
    max-height: 400px;
    overflow-y: auto;
    overscroll-behavior: contain;
    .item {
      display: flex;
      align-items: flex-start;
      padding: 15px 10px;
      transition: 0.3s;
      border-bottom: 1px solid #eee;
      cursor: pointer;
      &:hover {
        background-color: #e6f7ff;
      }
      &:last-child {
        border-bottom: unset;
      }
      .svg-div {
        position: relative;
        width: 40px;
        height: 40px;
        margin: auto 10px auto 0;
        .svg {
          width: 100%;
          height: 100%;
        }
        .red-round {
          position: absolute;
          width: 6px;
          height: 6px;
          background: #fa403c;
          border-radius: 50%;
          -webkit-box-shadow: 0 0 2px 3px white;
          box-shadow: 0 0 1px 2px white;
          right: 5px;
          top: 5px;
        }
      }
      .info {
        flex: 1;
        .title {
          font-size: 14px;
          line-height: 1.5;
          @include text-overflow(2);
        }
        .date {
          margin-top: 5px;
          font-size: 12px;
          color: #999;
        }
      }
    }
    .empty {
      padding: 30px 0;
      text-align: center;
      color: #999;
    }
  }
  .more {
    padding: 15px 0;
    margin-bottom: -10px;
    text-align: center;
    color: #666;
    border-top: 1px solid #eee;
    cursor: pointer;
  }
}
</style>
