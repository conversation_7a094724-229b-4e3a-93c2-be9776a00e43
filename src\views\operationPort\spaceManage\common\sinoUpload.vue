<!--
 * @Author: <PERSON>
 * @Date: 2022-03-02 18:33:29
 * @LastEditors: l<PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-09-19 13:47:48
 * @Description: file content
 * @FilePath: \ihbs-web\src\components\common\sinoUpload.vue
-->
<template>
  <div class="sino_upload">
    <el-upload
      class="avatar-uploader"
      :class="{ disabled: uploadDisabled }"
      action=""
      :limit="limit"
      multiple
      list-type="picture-card"
      accept=".png,.jpg"
      :before-upload="beforeAvatarUpload"
      :http-request="httpRequest"
      :on-preview="handlePictureCardPreview"
      :on-remove="handleRemove"
      :on-exceed="handleExceed"
      :file-list="fileList"
    >
      <i class="el-icon-plus avatar-uploader-icon"></i>
      <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过{{ flieSizeType == 1 ? '2M' : flieSizeType == 2 ? '5M' : '500KB' }},大小为200*200px以内</div>
    </el-upload>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="" />
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    fileList: {
      type: Array,
      default: () => []
    },
    limit: {
      type: Number,
      default: 1
    },
    flieSizeType: {
      type: Number,
      default: 0 // 0:500KB, 1:2M, 2:5M
    },
    downLoadDisabled: {
      type: Boolean,
      default: false
    },
    deleteDisabled: {
      type: Boolean,
      default: false
    },
    modelName: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      uploadDisabled: false,
      dialogImageUrl: '',
      dialogVisible: false
    }
  },
  watch: {},
  mounted() {
    // let fileNum = [];
    // this.fileList.forEach((list) => {
    //   list.url ? fileNum.push(list.url) : ""
    // })
    // fileNum.length< this.limit
    //   ? (this.uploadDisabled = false)
    //   : (this.uploadDisabled = true);
  },
  methods: {
    beforeAvatarUpload(file) {
      // flieSizeType 0: 500KB, 1: 2M, 2: 5M
      const isLt2M = file.size / 1024 / 1024 < 2
      const isLt5M = file.size / 1024 / 1024 < 5
      const isLt500Mb = file.size / 1024 < 500
      if (this.flieSizeType == 1) {
        if (!isLt2M) {
          this.$message.warning('上传头像图片大小不能超过 2MB!')
        }
        return isLt2M
      } else if (this.flieSizeType == 2) {
        if (!isLt5M) {
          this.$message.warning('上传头像图片大小不能超过 5MB!')
        }
        return isLt5M
      } else {
        return new Promise((resolve, reject) => {
          if (!isLt500Mb) {
            this.$message.warning('上传头像图片大小不能超过 500kb!')
          }
          // 调用【限制图片尺寸】Fn
          this.limitFileWH(200, 200, file).then((res) => {
            file.isFlag = res
            if (file.isFlag) {
              resolve()
              return file.isFlag && isLt500Mb
            } else {
              reject()
              return file.isFlag && isLt500Mb
            }
          })

          //  return isLt500Mb && file.isFlag;
        })
      }
    },
    limitFileWHS(E_width, E_height, file) {
      let _this = this
      let imgWidth = ''
      let imgHight = ''
      const isSize = new Promise(function (resolve, reject) {
        let width = E_width
        let height = E_height
        let _URL = window.URL || window.webkitURL
        let img = new Image()
        img.onload = function () {
          imgWidth = img.width
          imgHight = img.height
          let valid = img.width == width && img.height == height
          valid ? resolve() : reject()
        }
        img.src = _URL.createObjectURL(file)
      }).then(
        () => {
          return true
        },
        () => {
          _this.$message.warning({
            message: '上传图片大小为' + E_width + '*' + E_height + 'px，请重新上传！'
          })
          return false
        }
      )
      return isSize
    },
    // 限制图片尺寸
    limitFileWH(E_width, E_height, file) {
      let _this = this
      let imgWidth = ''
      let imgHight = ''
      const isSize = new Promise(function (resolve, reject) {
        let width = E_width
        let height = E_height
        let _URL = window.URL || window.webkitURL
        let img = new Image()
        img.onload = function () {
          imgWidth = img.width
          imgHight = img.height
          let valid = img.width <= width && img.height <= height
          valid ? resolve() : reject()
        }
        img.src = _URL.createObjectURL(file)
      }).then(
        () => {
          return true
        },
        () => {
          _this.$message.warning({
            message: '上传图片大小为' + E_width + '*' + E_height + 'px以内，请重新上传！'
          })
          return false
        }
      )
      return isSize
    },

    httpRequest(item) {
      let data = {
        file: item.file,
        modelName: 'spacePic',
        storageType: 'minio',
        persistenceType: 'forever',
        specifyFileName: new Date().getTime(),
        folderPath: 'hospital',
        serviceName: 'ihbs'
      }
      this.$api.getUploadOne(data).then((res) => {
        if (res.code == 200) {
          this.dialogImageUrl = res.data.fileHost + res.data.fileUrl
          this.$emit('input', res.data)
          this.$message({
            message: res.msg,
            type: 'success'
          })
        }
      })
    },
    handleRemove(file, fileList) {
      this.$emit('input', file, 1)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleDownload(file) {
      console.log(file)
    },
    handleExceed(file) {
      this.$message.warning('最多上传' + this.limit + '个文件')
    }
  }
}
</script>

<style lang="scss" scoped>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 300px;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 150px;
  height: 150px;
  line-height: 150px;
  text-align: center;
}

.avatar {
  width: 150px;
  height: 150px;
  display: block;
  margin: 0 auto;
}
</style>
