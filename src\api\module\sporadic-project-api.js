import { getRequest, postRequest, downFile } from '../http.js'
// 服务前缀
const PrefixService = __PATH.VUE_SPORADIC_API
/**
 * 零星工程
 */
const apiConfig = {
  // ---------------------------------供应商管理-------------------------------------------------
  /** 供应商列表分页查询 */
  pageProjectSupplier: postRequest('supplier/pageProjectSupplier', PrefixService),
  /** 根据ID查询供应商 */
  selectProjectSupplierOne: getRequest('supplier/selectProjectSupplierOne', PrefixService, 'json'),
  /** 保存供应商信息 */
  saveProjectSupplier: postRequest('supplier/saveProjectSupplier', PrefixService),
  /** 更新供应商信息 */
  updateProjectSupplier: postRequest('supplier/updateProjectSupplier', PrefixService),
  /** 修改供应商状态 */
  disOrUseProjectSupplier: getRequest('supplier/disOrUseProjectSupplier', PrefixService),
  /** 删除供应商 */
  deleteProjectSupplier: getRequest('supplier/deleteProjectSupplier', PrefixService),
  /** 供应商下拉 */
  getProjectSupplierAll: postRequest('supplier/getProjectSupplierAll', PrefixService),
  // ---------------------------------标签配置--------------------------------------------------
  /** 分页查询标签 */
  pageProjectTagInfo: postRequest('function/pageProjectTagInfo', PrefixService),
  /** 根据ID查询标签 */
  selectProjectTagInfoOne: getRequest('function/selectProjectTagInfoOne', PrefixService, 'json'),
  /** 保存标签 */
  saveProjectTagInfo: postRequest('function/saveProjectTagInfo', PrefixService),
  /** 修改标签 */
  updateProjectTagInfo: postRequest('function/updateProjectTagInfo', PrefixService),
  /** 启用|禁用标签 */
  disOrUseProjectTagInfo: getRequest('function/disOrUseProjectTagInfo', PrefixService),
  /** 删除标签(单一) */
  deleteProjectTagInfo: getRequest('function/deleteProjectTagInfo', PrefixService),
  // ---------------------------------业务表单配置--------------------------------------------------
  /** 业务表单配置分页列表 */
  queryProjectBusinessFormByPage: postRequest('projectBusinessForm/queryProjectBusinessFormByPage', PrefixService),
  /** 业务表单配置全部数据列表 */
  queryProjectBusinessFormAll: postRequest('projectBusinessForm/queryProjectBusinessFormAll', PrefixService),
  /** 表单配置id查询配置详情 */
  queryProjectBusinessFormDetail: postRequest('projectBusinessForm/queryProjectBusinessFormDetail', PrefixService),
  /** 获取表单配置默认配置项列表 */
  getFormConfigDefaultList: postRequest('projectBusinessForm/getFormConfigDefaultList', PrefixService),
  /** 删除业务表单配置 */
  deleteProjectBusinessFormById: postRequest('projectBusinessForm/deleteProjectBusinessFormById', PrefixService),
  /** 查询流程列表 */
  getworkFlowDesignModeList: postRequest('projectBusinessForm/getworkFlowDesignModeList', PrefixService),
  /** 新增流程表单基础配置 */
  createProjectBusinessForm: postRequest('projectBusinessForm/createProjectBusinessForm', PrefixService),
  /** 根据表单key 获取流程表单字段下拉列表 */
  getFormNodeListByFormKey: postRequest('projectBusinessForm/getFormNodeListByFormKey', PrefixService),
  /** 业务表单配置 编辑配置 */
  updateProjectBusinessForm: postRequest('projectBusinessForm/updateProjectBusinessForm', PrefixService),
  /** 业务表单同步工作流 */
  synchronousWorkFlow: postRequest('projectBusinessForm/synchronousWorkFlow', PrefixService),
  // ---------------------------------业务状态配置--------------------------------------------------
  /** 状态配置分页列表 */
  queryProjectStateByPage: postRequest('projectState/queryProjectStateByPage', PrefixService),
  /** 根据ID获取状态配置详情 */
  queryProjectStateDetail: postRequest('projectState/queryProjectStateDetail', PrefixService),
  /** 新增状态配置 */
  createProjectState: postRequest('projectState/createProjectState', PrefixService),
  /** 更新状态配置 */
  updateProjectState: postRequest('projectState/updateProjectState', PrefixService),
  /** 根据ID删除状态配置 */
  deleteProjectStateById: postRequest('projectState/deleteProjectStateById', PrefixService),
  /** 流程key获取默认流程节点 */
  getWorkNodeListByFlowKey: postRequest('projectBusinessForm/getWorkNodeListByFlowKey', PrefixService),
  // ---------------------------------业务列表列配置--------------------------------------------------
  /** 新增列表列配置 */
  createProjectColumn: postRequest('projectColumn/createProjectColumn', PrefixService),
  /** 根据ID删除列表列配置 */
  deleteProjectColumnById: postRequest('projectColumn/deleteProjectColumnById', PrefixService),
  /** 分页获取列表列配置 */
  queryProjectColumnByPage: postRequest('projectColumn/queryProjectColumnByPage', PrefixService),
  /** 根据ID获取列表列配置详情 */
  queryProjectColumnDetail: postRequest('projectColumn/queryProjectColumnDetail', PrefixService),
  /** 编辑列表列配置 */
  updateProjectColumn: postRequest('projectColumn/updateProjectColumn', PrefixService),
  // ---------------------------------单据管理配置--------------------------------------------------
  /** 单据管理分页列表 */
  queryProjectDocumentConfigByPage: postRequest('projectDocumentConfig/queryProjectDocumentConfigByPage', PrefixService),
  /** 单据管理新增配置 */
  createProjectDocumentConfig: postRequest('projectDocumentConfig/createProjectDocumentConfig', PrefixService),
  /** 单据管理删除配置 */
  deleteProjectDocumentConfigById: postRequest('projectDocumentConfig/deleteProjectDocumentConfigById', PrefixService),
  /** 单据管理编辑配置 */
  updateProjectDocumentConfig: postRequest('projectDocumentConfig/updateProjectDocumentConfig', PrefixService),
  // ---------------------------------统计分析--------------------------------------------------
  /** 统计分析数据查询 */
  queryProjectStatisticAnalysis: postRequest('statisticAnalysis/queryProjectStatisticAnalysis', PrefixService),
  // ---------------------------------支付单--------------------------------------------------
  /** 支付单列表 */
  queryprojectPaymentOrderByPage: postRequest('projectPaymentOrder/queryProjectPaymentOrderByPage', PrefixService),
  /** 已生成支付单列表 */
  queryprojectPaymentGenerateByPage: postRequest('projectPaymentOrder/queryProjectPaymentGenerateByPage', PrefixService),
  /** 手动生成 */
  manuallyGeneratedReport: getRequest('projectPaymentOrder/manuallyGeneratedReport', PrefixService),
  /** 作废 */
  cancelReportByIds: getRequest('projectPaymentOrder/cancelReportByIds', PrefixService),
  /** 详情 */
  getPaymentGenerateAndFileById: getRequest('projectPaymentOrder/getPaymentGenerateAndFileById', PrefixService),
  // ---------------------------------超时提醒--------------------------------------------------
  /** 列表 */
  pageReminderConfig: postRequest('function/pageReminderConfig', PrefixService),
  /** 保存 */
  saveReminderConfig: postRequest('function/saveReminderConfig', PrefixService),
  /** 修改 */
  updateReminderConfig: postRequest('function/updateReminderConfig', PrefixService),
  /** 删除 */
  deleteReminderConfig: getRequest('function/deleteReminderConfig', PrefixService),
  /** 详情 */
  selectReminderConfigOne: getRequest('function/selectReminderConfigOne', PrefixService),
  /** 项目下拉 */
  selectUnUseBusinessForm: getRequest('function/selectUnUseBusinessForm', PrefixService),
  // ---------------------------------字典配置--------------------------------------------------
  /** 分页获取字典 */
  getDictConfigByPage: postRequest('projectInfo/dictConfig/queryDictConfigByPage', PrefixService),
  /** 删除字典 */
  deleteDictData: getRequest('projectInfo/dictConfig/delete', PrefixService),
  /** 新增字典 */
  insertDictConfigData: postRequest('projectInfo/dictConfig/insert', PrefixService),
  /** 编辑字典 */
  updateDictConfigData: postRequest('projectInfo/dictConfig/update', PrefixService),
  /** 字典详情 */
  getDictDataById: getRequest('projectInfo/dictConfig/view', PrefixService),
  /** 查询全部字典 */
  getDictConfigData: postRequest('projectInfo/dictConfig/queryDictConfigAll', PrefixService),
  // 获取全部项目表头
  getAllProColumnsData: postRequest('projectInfo/getProjectDynamicTableHeader', PrefixService),
  // 获取全部项目list
  getAllProList: postRequest('projectInfo/queryProjectInfoByPage', PrefixService),
  // 获取标签
  proTagList: postRequest('function/queryProjectTagInfoAll', PrefixService),
  // 质保金列表
  retentionMoneyList: postRequest('projectAssurance/queryProjectAssuranceByPage', PrefixService),
  projectStatisticExport: downFile('statisticAnalysis/projectStatisticExport', PrefixService)
}
export default apiConfig
