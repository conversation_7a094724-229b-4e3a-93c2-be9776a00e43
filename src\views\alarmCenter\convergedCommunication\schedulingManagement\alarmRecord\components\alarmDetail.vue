<template>
  <el-dialog title="报警记录" width="45%" :visible.sync="alarmRecordDialogShow" custom-class="model-dialog" :before-close="closeDialog">
    <div class="content">
      <el-form ref="form" :model="itemInfo" label-width="120px">
        <div class="row_item">
          <el-form-item label="报警类型：" prop="channelNumber">
            <div class="input_width">
              <span>{{ itemInfo.alarmTypeVal }}</span>
            </div>
          </el-form-item>
          <el-form-item label="报警级别：" prop="groupName">
            <div class="input_width">
              <span>{{ alarmLevelItem[itemInfo.alarmLevel].text || '-' }}</span>
            </div>
          </el-form-item>
        </div>
        <div class="row_item">
          <el-form-item label="报警位置：" prop="remarks">
            <div class="input_width">
              <span>{{ itemInfo.devicePosition }}</span>
            </div>
          </el-form-item>
          <el-form-item label="报警对象：">
            <div class="input_width">
              <span>{{ itemInfo.deviceName }}</span>
            </div>
          </el-form-item>
        </div>
        <div class="row_item">
          <el-form-item label="报警时间：">
            <div class="call_class">
              <span>{{ itemInfo.alarmTime }}</span>
            </div>
          </el-form-item>
        </div>
        <h3 class="h3Title">消息接收终端</h3>
        <div>
          <div v-for="(item, index) in itemInfo.callRecordList" :key="index" class="terminal-row-item">
            <el-form-item label="终端号码：">
              <div class="call_class">
                <span>{{ item.destinationNumber || '' }}</span>
              </div>
            </el-form-item>
            <el-form-item label="是否确认：">
              <div class="call_class">
                <span>{{ getConfirmState(item) }}</span>
              </div>
            </el-form-item>
            <el-form-item label="确认时间：">
              <div class="call_class">
                <span>{{ item.startTime || '' }}</span>
              </div>
            </el-form-item>
            <el-form-item v-if="item.filePath" label="视频：" prop="office">
              <video
                v-for="(videoItem, videoIndex) in item.filePath"
                :key="videoIndex"
                ref="videoElement"
                autoplay
                :src="videoItem"
                controls
                muted
                width="100%"
                height="100%"
              ></video>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: 'alarmDetail',
  components: {},
  props: {
    alarmRecordDialogShow: {
      type: Boolean,
      default: false
    },
    itemInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      alarmLevelItem: {
        0: { text: '通知', color: '#3562DB' },
        1: { text: '一般', color: '#3562DB' },
        2: { text: '紧急', color: '#FF9435' },
        3: { text: '重要', color: '#FA403C' }
      }
    }
  },
  mounted() {},
  methods: {
    closeDialog() {
      this.$emit('closeAlarmRecordDialog')
    },
    getConfirmState(item) {
      if (item.destinationNumberMap) {
        try {
          const arr = JSON.parse(item.destinationNumberMap)
          return arr.some((i) => i.state == 1) ? '确认' : '未确认'
        } catch (e) {
          return ''
        }
      }
      return ''
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  .content {
    margin: 0 auto;
    background: #fff;
    border-radius: 4px;
    width: 100%;
    height: 380px;
    overflow: auto;
    padding-right: 20px;
  }
  .input_width {
    width: 300px;
    .el-select,
    .el-date-editor.el-input,
    .el-cascader {
      width: 100%;
    }
  }
  .call_class {
    > span {
      margin-right: 16px;
    }
  }
  .h3Title {
    padding: 10px 20px;
  }
  .el-form-item {
    margin-bottom: 10px;
  }
}
.terminal-row-item {
  background: #f6f8fa;
  border: 1px solid #e0e3e8;
  border-radius: 8px;
  margin-bottom: 16px;
  margin-left: 20px;
  width: 90%;
  box-sizing: border-box;
}
</style>
