<template>
  <div v-if="isCloseChild" class="drawer" @click="close">
    <div class="main_content" :class="{ close: !showDrawer }" @click="(e) => e.stopPropagation()">
      <header>
        <span>报警记录</span>
        <!-- <span style="margin-left: 15px; color: #353535; font-weight: bold;">{{ surveyCount }}</span> -->
        <div class="tools">
          <i class="el-icon-close" style="font-size: 18px; vertical-align: middle; cursor: pointer" @click="close"></i>
        </div>
      </header>
      <div class="content">
        <div class="statistics">
          <div class="survey_box">
            <div class="survey-count">
              <div class="statistics_value">设备总数</div>
              <div class="statistics_num">{{ surveyCount }}</div>
            </div>
            <svg-icon name="survey-count-icon" />
          </div>
          <div class="statistics_box">
            <div class="statistics_item">
              <div class="statistics_value">本年报警数量</div>
              <div class="statistics_num">{{ yearCount }}</div>
            </div>
            <div class="statistics_item">
              <div class="statistics_value">今日报警数量</div>
              <div class="statistics_num">{{ dayCount }}</div>
            </div>
            <div class="statistics_item">
              <div class="statistics_value">未确认报警数量</div>
              <div class="statistics_num" style="color: #fb4460">{{ unConfirmCount }}</div>
            </div>
            <div class="statistics_item">
              <div class="statistics_value">已确认警数量</div>
              <div class="statistics_num" style="color: #1eac9e">{{ confirmedCount }}</div>
            </div>
          </div>
        </div>
        <div
          v-infinite-scroll="
            () => {
              scrollEvent()
            }
          "
          class="statistics_list"
        >
          <div v-for="item of policeList" :key="item.id" class="list_content" @click="disposition(item)">
            <svg-icon name="warn-list-icon" />
            <div class="list_box">
              <div class="list_title">
                <span>{{ item.alarmDeviceName }}</span
                >&nbsp;
                <span>{{ item.alarmDetails }}</span>
              </div>
              <div class="list_bottom">
                <div class="list_time"><i class="el-icon-time" />{{ item.alarmStartTime }}</div>
                <div class="list_map" :title="item.gridName"><i class="el-icon-location-outline" />{{ item.alarmSpaceName }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { monitorTypeList } from '@/util/dict.js'
export default {
  props: {
    closeState: Boolean,
    requestHttp: {
      type: String,
      default: ''
    },
    projectCode: {
      type: String,
      default: ''
    },
    isAlarmMode: {
      type: String,
      default: ''
    },
    treeCode: {
      type: String,
      default: ''
    },
    surveyEntityCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isCloseChild: false,
      showDrawer: false,
      policeList: [],
      yearCount: '', // 本年报警数量
      dayCount: '', // 今日报警数量
      unConfirmCount: '', // 未确认报警数量
      confirmedCount: '', // 已确认报警数量
      surveyCount: '',
      pageData: {
        pageSize: 10,
        page: 1
      },
      alarmTotal: 0,
      monitorData: monitorTypeList.find((item) => item.projectCode == this.projectCode)
    }
  },
  watch: {
    closeState: {
      handler(val) {
        if (val) {
          this.pageData.page = 1
          this.isCloseChild = val
          this.$emit('isCloseState', this.isCloseChild)
          this.$nextTick(() => {
            this.showDrawer = val
          })
          let paramData = {
            surveyCode: this.surveyEntityCode || '',
            startTime: '',
            endTime: '',
            regionCode: ''
          }
          this.$api.getMonitorStatisticData({ projectCode: this.projectCode, ...paramData, page: 1, pageSize: 10 }, this.requestHttp).then((res) => {
            this.policeList = res.data.policeList.records
            this.yearCount = res.data.yearCount
            this.dayCount = res.data.dayCount
            this.unConfirmCount = res.data.unConfirmCount
            this.alarmTotal = res.data.policeList.total
            this.confirmedCount = res.data.confirmedCount
            this.surveyCount = res.data.surveyCount
          })
        }
      },
      deep: true
    }
  },
  mounted() {},
  methods: {
    close() {
      this.showDrawer = false
      setTimeout(() => {
        this.isCloseChild = false
        this.$emit('isCloseState', false)
      }, 200)
    },
    scrollEvent() {
      if (this.policeList.length < this.alarmTotal) {
        this.pageData.page += 1
        let paramData = {
          surveyCode: this.surveyEntityCode || '',
          startTime: '',
          endTime: '',
          regionCode: ''
        }
        this.$api.getMonitorStatisticData({ projectCode: this.projectCode, ...paramData, ...this.pageData }, this.requestHttp).then((res) => {
          this.policeList = this.policeList.concat(res.data.policeList.records)
        })
      }
    },
    // 跳转
    disposition(row) {
      if (this.isAlarmMode == '1') {
        this.$router.push({
          path: '/allAlarm/allAlarmIndex',
          query: {
            projectCode: this.projectCode
          }
        })
      } else {
        this.$router.push({
          path: '/' + this.monitorData.policeDetailsPath,
          query: {
            type: 'add',
            alarmId: row.alarmId,
            projectCode: this.projectCode
          }
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.list_title,
.list_map {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.content {
  box-sizing: border-box;
  // padding: 20px 14px;
  padding-bottom: 16px;
  overflow: auto;
  height: calc(100% - 56px);
  .statistics {
    padding: 16px;
    display: flex;
    justify-content: space-between;
  }
  .survey_box {
    width: 210px;
    height: 170px;
    background: rgb(53 98 219 / 10%);
    border-radius: 4px;
    display: flex;
    align-items: center;
    .survey-count {
      width: 50%;
      height: 60%;
      text-align: center;
      display: flex;
      justify-content: space-evenly;
      flex-direction: column;
      .statistics_value {
        font-size: 15px;
        font-family: 'PingFang SC-Medium', 'PingFang SC';
        font-weight: 500;
        color: #121f3e;
      }
      .statistics_num {
        font-size: 30px;
        font-family: Arial-Bold, Arial;
        font-weight: bold;
        color: #3562db;
      }
    }
    svg {
      flex: 1;
      font-size: 50px;
    }
  }
  .statistics_box {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    width: 288px;
    height: 170px;
    .statistics_item {
      width: 139px;
      height: 80px;
      text-align: center;
      display: flex;
      justify-content: space-evenly;
      flex-direction: column;
      .statistics_num {
        font-size: 22px;
        font-weight: bold;
        color: #353535;
      }
      .statistics_value {
        color: #939cb4;
        font-size: 14px;
      }
    }
  }
  .statistics_list {
    height: calc(100% - 202px);
    padding-top: 10px;
    padding-left: 16px;
    box-sizing: border-box;
    overflow-y: auto;
    overflow-x: hidden;
  }
  .list_content {
    cursor: pointer;
    border-radius: 4px;
    width: calc(100% - 16px);
    height: 70px;
    margin-bottom: 10px;
    background: #fff;
    box-shadow: 0 5px 15px 0 rgb(56 62 68 / 15%);
    display: flex;
    align-items: center;
    > svg {
      margin: 0 16px;
      font-size: 44px;
    }
    .list_box {
      flex: 1;
      overflow: hidden;
      display: flex;
      height: 100%;
      flex-direction: column;
      justify-content: space-evenly;
      .list_title {
        font-size: 16px;
        font-family: 'PingFang SC-Medium', 'PingFang SC';
        color: #121f3e;
      }
      .list_bottom {
        font-size: 14px;
        font-family: 'PingFang SC-Regular', 'PingFang SC';
        color: #414653;
        display: flex;
        > div {
          width: 50%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          i {
            font-size: 16px;
            vertical-align: middle;
            margin-right: 5px;
          }
        }
      }
    }
  }
}
.content input {
  width: 200px;
  height: 40px;
}
.content .sino_sdcp_input {
  margin: 20px 10px 0 0;
  /* line-height: 40px; */
}
.drawer {
  width: 100%;
  height: 100%;
  background: rgb(127, 127, 127, 0.5);
  position: fixed;
  top: 0;
  right: 0;
  z-index: 999;
}
.main_content {
  position: fixed;
  top: 64px;
  right: 12px;
  background: #fff;
  transition: all 0.5s;
  z-index: 999;
  text-align: left;
  width: 540px;
  height: 92%;
  box-shadow: 0 6px 18px 0 rgb(18 31 62 / 20%);
  border-radius: 4px;
  float: right;
  header {
    height: 56px;
    line-height: 56px;
    background: #fff;
    border-radius: 4px 4px 0 0;
    span {
      font-size: 18px;
      font-family: 'PingFang SC-Medium', 'PingFang SC';
      color: #121f3e;
    }
  }
}
.close {
  margin-right: -680px;
}
.primary {
  margin-top: 5px;
  background-color: #2cc7c5;
  color: #fff;
  width: 94px;
  vertical-align: top;
}
footer {
  width: 100%;
  height: 60px;
  line-height: 60px;
  box-sizing: border-box;
  background-color: #fff;
  text-align: right;
  box-shadow: 0 -4px 4px 0 rgb(243 244 248 / 100%);
  padding-right: 18px;
  position: absolute;
  bottom: 0;
}
header {
  height: 62px;
  border-radius: 0 10px 0 0;
  background: #fff;
  line-height: 62px;
  padding: 0 20px;
  font-size: 20px;
  font-family: NotoSansHans-Medium;
  font-weight: 500;
  color: rgb(96 98 102 / 100%);
  box-sizing: border-box;
  border-bottom: 1px solid #d8dee7;
}
.tools {
  float: right;
  box-sizing: border-box;
}
.header-close {
  cursor: pointer;
  color: #606266;
  border: 1px solid rgb(220 223 229 / 100%);
  vertical-align: top;
  margin-top: 5px;
}
.header-close:hover {
  color: rgb(44 199 197 / 100%);
}
.header-close:focus {
  color: rgb(44 199 197 / 100%);
  background-color: #eaf9f9;
}
@media screen and (max-width: 1600px) {
  .content .sino_sdcp_input {
    margin: 14px 10px 0 0;
    /* line-height: 40px; */
  }
  .main_content {
    width: 510px;
  }
  header {
    height: 40px;
    line-height: 40px;
  }
  .content {
    padding: 10px 0 0 10px;
    height: 100%;
  }
  footer {
    height: 40px;
    line-height: 40px;
  }
}
.info_list_icon {
  display: inline-block;
  min-width: 28px;
  text-align: center;
  color: #5188fc;
  font-size: 20px;
}
</style>
