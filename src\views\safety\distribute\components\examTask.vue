<template>
  <div class="heard">
    <div class="course_title">
      <div>考试任务</div>
      <div class="radioKs">
        <el-radio-group v-model="activeName" @change="changeRadio">
          <el-radio label="0">关联考试计划</el-radio>
          <el-radio label="1">根据课程组卷</el-radio>
        </el-radio-group>
      </div>
    </div>
    <div v-if="activeName == '0'" style="height: 100%;">
      <div class="exam_content">
        <div class="course_nav" v-for="(item, index) in examinatioList" :key="index">
          <div class="courer_name">
            <img src="../../../../assets/images/icon_exam.png" alt="">
            <div>{{ item.name }}</div>
          </div>
          <span>考试时间：{{ moment(item.startTime).format("YYYY-MM-DD") }}至{{ moment(item.endTime).format("YYYY-MM-DD")
            }}</span>
          <span>考试时长：<span>{{ item.duration }}</span> 分钟</span>
          <span>考试题数：{{ item.count }}</span>
          <span>通过分数：{{ item.passScore }}</span>
          <div class="operation">
            <i class="el-icon-delete Iconx" @click="deleteExam(item, index)"></i>
          </div>
        </div>
      </div>
      <el-button type="primary" @click="addExaminatio">添加考试计划</el-button>
    </div>
    <div v-if="activeName == '1'" style="height: 100%;">
      <div class="exam_content autoClass">
        <el-form ref="formInfoRef" :model="formInfo" :rules="formInfoRules" label-width="160px">
          <el-form-item label="试卷名称" prop="name">
            <el-input v-model="formInfo.name" maxlength="30" show-word-limit placeholder="最多30个字符"
              style="width: 440px;"></el-input>
          </el-form-item>
          <el-form-item label="答题时长" prop="duration">
            <el-input placeholder="请输入答题时长" v-model="formInfo.duration" style="width: 440px;">
              <template slot="append">分钟</template>
            </el-input>
          </el-form-item>
          <!-- <el-form-item label="考试通过自动发放证书">
            <el-select v-model="formInfo.credentialId" placeholder="请选择通过获取证书" style="width: 440px;">
              <el-option v-for="item in certificateList" :key="item.id" :label="item.certificateName" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="考试不通过">
            <el-radio-group v-model="formInfo.rejectDeal" style="width: 440px;">
              <el-radio label="1">不补考</el-radio>
              <el-radio label="2">自动组卷补考</el-radio>
              <el-radio label="3">手动派发补考试卷</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="通过分数" prop="passScore">
            <el-input placeholder="请输入" v-model="formInfo.passScore" style="width: 440px;">
              <template slot="append">分</template>
            </el-input>
          </el-form-item>
          <el-form-item label="试卷描述">
            <el-input v-model="formInfo.disc" type="textarea" show-word-limit maxlength="200"
              placeholder="请输入描述,最多输入200个字符" style="width: 600px;"></el-input>
          </el-form-item>
        </el-form>
        <automatic ref="automatic" :autoInfo="autoInfo" :scoreInfo="scoreInfo" @submitAuto="submitAuto"></automatic>
      </div>
    </div>
    <el-drawer :visible.sync="drawer" :with-header="true" size="65%" :show-close="true">
      <div slot="title" class="coursrDrawer">选择考试</div>
      <div class="deawer_conter">
        <div class="deawer_heard">
          <el-input v-model="formInline.name" placeholder="请输入试卷名称" style="width: 200px; margin: 0px 10px;"></el-input>
          <el-cascader v-model="formInline.subjectId" clearable class="sino_sdcp_input mr15"
            style="width: 200px; margin: 0px 10px;" :options="subjectList" :props="props"
            placeholder="请选择类型"></el-cascader>
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="search">查询</el-button>
        </div>
        <div class="table_conter">
          <el-table ref="multipleTable" :data="tableData" tooltip-effect="dark" style="width: 100%"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" fixed="left" align="center"></el-table-column>
            <el-table-column prop="name" label="试卷名称" width="150" align="center"></el-table-column>
            <el-table-column prop="deptName" label="所属单位" show-overflow-tooltip align="center"></el-table-column>
            <el-table-column prop="subjectName" label="所属科目" show-overflow-tooltip align="center"></el-table-column>
            <el-table-column label="考试期限" show-overflow-tooltip align="center">
              <template slot-scope="scope">
                <span>{{ moment(scope.row.startTime).format("YYYY-MM-DD") }}至{{
                  moment(scope.row.endTime).format("YYYY-MM-DD") }}</span>
              </template>
            </el-table-column>
            <el-table-column label="考试学员" show-overflow-tooltip align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.studentNum || 0 }}人</span>
              </template>
            </el-table-column>
            <el-table-column label="考题数量" show-overflow-tooltip align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.count || 0 }}题</span>
              </template>
            </el-table-column>
            <el-table-column label="总分数" show-overflow-tooltip align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.score || 0 }}分</span>
              </template>
            </el-table-column>
            <el-table-column label="通过分数" show-overflow-tooltip align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.passScore || 0 }}分</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div class="derwer_footer">
        <div class="derwer_footerSum"> 已选择 <span class="sumNamber">{{ tableSum }}</span> 个课程</div>
        <div>
          <el-button type="primary" plain @click="cancel">取消</el-button>
          <el-button type="primary" @click="submit">添加</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import moment from "moment";
import automatic from "../../examPlan/components/automatic.vue"
export default {
  components: { automatic },
  props: {
    examType: {
      type: String,
      default: '0'
    },
    type: {
      type: String,
      default: '0'
    },
    examList: {
      type: Array,
      default: []
    },
    subjectId: {
      type: Number,
      default: null
    },
    examInfo: {
      type: Object,
      default: {}
    },
    taskScoreInfo: {
      type: Object,
      default: {}
    },
    examAutoInfo: {
      type: Object,
      default: {}
    },
    startTime: {
      type: String,
      default: '',
    },
    endTime: {
      type: String,
      default: '',
    }
  },
  data() {
    return {
      moment,
      formInline: {
        name: '',
        subjectId: null
      },
      formInfo: {
        name: '',
        duration: '',
        credentialId: '',
        rejectDeal: '1',
        disc: '',
        passScore: '',
      },
      autoInfo: {
        questionsList: [
          {
            questionTypes: [
              {
                type: "1",
                num: '',
                score: '',
              },
              {
                type: "2",
                num: '',
                score: '',
              },
              {
                type: "3",
                num: '',
                score: '',
              },
            ],
            courseId: '',
            questionsNum: []
          },
        ],
      },
      scoreInfo: {
        nums: 0,
        sumScore: 0
      },
      timeLine: [],
      hiddenLevelList: [],
      drawer: false,
      tableData: [],
      multipleSelection: [],
      tableSum: '0',
      activeName: "0",
      form: {
        name: '',
        radio: 1,
      },
      topiconFigList: [{
        name: '',
      }],
      activeIndex: '',
      examinatioList: [],
      certificateList: [],
      subjectList: [],
      props: {
        children: "children",
        label: "name",
        value: "id",
        checkStrictly: true,
        emitPath: false,
      },
      formInfoRules: {
        name: [{ required: true, message: "请输入试卷名称", trigger: "blur" }],
        duration: [{ required: true, message: "请输入答题时长", trigger: "blur" }],
        passScore: [{ required: true, message: "请输入通过分数", trigger: "blur" }]
      }
    };
  },
  created() {
    this.init()
    this.getDataList()
    this.examinatioList = this.examList
    this.activeName = this.examType
    this.formInfo = this.examInfo
    this.autoInfo = this.examAutoInfo
    this.scoreInfo = this.taskScoreInfo
    if (this.activeName == 1) {
      this.$nextTick(() => {
        this.$refs.automatic.getCourseList(this.subjectId)
      })
    }
  },
  methods: {
    init() {
      // 获取科目
      let data = {
        pageNo: 1,
        pageSize: 999,
      };
      this.$api.subjectListAll(data).then((res) => {
        if (res.code == 200) {
          this.subjectList = this.$tools.transData(
            res.data,
            "id",
            "parentId",
            "children"
          )
        } else {
          this.$message.error(res.message);
        }
      });
      // 获取证书列表
      this.$api.certificateList({ pageNo: 1, pageSize: 9999 }).then((res) => {
        if (res.code == 200) {
          this.certificateList = res.data.records;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 查询列表
    getDataList(tages = false) {
      this.tableLoading = true
      let params = {
        ...this.formInline,
        current: 1,
        size: 9999,
        startTime: this.startTime,
        endTime: this.endTime
      }
      this.$api.getExamPlanNewList(params).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.list
          console.log(this.tableData, 'this.tableData');
          if (tages) {
            setTimeout(() => {
              this.tableData.forEach((row) => {
                const matchedIndex = this.examinatioList.findIndex(item => item.id === row.id)
                this.$refs.multipleTable.toggleRowSelection(row, matchedIndex != -1)
              })
            })
          }
        } else {
          this.$message.error(res.msg);
        }
        this.tableLoading = false
      });
    },
    // 添加考试计划
    addExaminatio() {
      this.drawer = true
      this.$nextTick(() => {
        this.getDataList(true)
      })
    },
    handleSelectionChange(val) {
      this.tableSum = val.length
      console.log(this.type, 'type');
      if (this.type == 'exam') {
        if (val.length > 1) {
          //移除上一次选中行数据
          val.shift();
          //修改选中图标为未选中状态
          this.$refs.multipleTable.clearSelection();
          //将当前选中行改为选中状态
          this.$refs.multipleTable.toggleRowSelection(val[0]);
        } else {
          this.multipleSelection = val;
        }
      } else {
        this.multipleSelection = val;
      }


    },
    // 取消关闭弹窗
    cancel() {
      this.$refs.multipleTable.clearSelection()
      this.drawer = false
    },
    // 提交
    submit() {
      this.examinatioList = this.multipleSelection
      this.$refs.multipleTable.clearSelection()
      this.drawer = false
    },
    changeRadio(val) {
      this.activeIndex = val
      if (val == 1) {
        this.$nextTick(() => {
          console.log(this.$refs.automatic, ' this.$refs.automatic');
          this.$refs.automatic.getCourseList(this.subjectId)
        })
      }
    },
    // 提交校验
    getValidate() {
      if (this.activeName == '1') {
        this.$refs.formInfoRef.validate((valid) => {
          this.$set(this.formInfo, 'type', 0)
          if (valid) {
            this.$refs.automatic.submit()
            return true
          } else {
            return false
          }
        })
      }
    },
    submitAuto(questionsInfo, questionsScore) {
      this.autoInfo = questionsInfo
      this.scoreInfo = questionsScore
    },
    // 查看
    detail() { },
    // 删除
    deleteExam(item, index) {
      this.examinatioList.splice(index, 1)
    },
    // 查询
    search() {
      this.getDataList()
    },
    // 重置
    resetForm() {
      this.formInline = {
        name: '',
        subjectId: null
      }
      this.getDataList()
    },
  },
};
</script>
<style lang="scss" scoped>
.heard {
  height: 100%;
}

.exam_content {
  height: calc(100% - 70px);
  overflow: auto;
  margin-top: 16px;
}

.autoClass {
  height: calc(100% - 10px);
}

.course_nav {
  height: 62px;
  background: #FAF9FC;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  line-height: 62px;
  padding: 0px 15px;
  font-size: 14px;
  margin: 15px 0px;
}

.course_title {
  margin-top: 8px;
  font-weight: 600;
  color: #333333;
  font-size: 16px;
  display: flex;
}

.courer_name {
  display: flex;
  align-items: center;
  width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  img {
    margin-right: 16px;
  }
}

.operation {
  display: flex;
  align-items: center;
  color: #3562DB;
}

.radioKs {
  margin-left: 20px;
  color: #666666;
}

.Iconx {
  margin-left: 20px;
}

.configuration {
  height: 32px;
  line-height: 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20px 0px 10px 0px;
}

.configTitle {
  font-weight: 600;
  color: #333333;
  font-size: 16px;
  margin-right: 20px;
}

.topic {
  height: 200px;
  background: #FAF9FC;
  padding: 10px;
}

.topicList {
  width: 100%;
  height: 130px;
  background: #FFFFFF;
  border-radius: 8px 8px 8px 8px;
  opacity: 1;
  padding: 0px 0px 20px 0px;
  margin-bottom: 20px;
}

.topicTitle {
  display: flex;
  justify-content: space-between;
}

.deletex {
  margin: 20px 20px 0px 20px;
  font-weight: 400;
  color: #FF6461;
  font-size: 14px;
  cursor: pointer;
}

.remark {
  margin: 20px 0px 0px 20px;
  font-weight: 400;
  color: #CCCED3;
  font-size: 14px;
}

.btn {
  margin-top: 25 px;
  height: 20px !important;
}

.coursrDrawer {
  color: #333333;
  font-weight: 500;
  font-size: 18px;
}

::v-deep .el-drawer__body {
  overflow: hidden;
}

::v-deep .el-drawer__header {
  height: 56px;
  line-height: 56px !important;
  padding-bottom: 20px;
  border-bottom: 1px solid #DCDFE6;
  box-shadow: -4px 0 4px 0 rgba(203, 205, 220, 0.24);
}

.deawer_conter {
  padding: 0px 20px;

}

.deawer_heard {
  width: 100%;
  margin-bottom: 16px;
  // display: flex;
}

.derwer_footer {
  width: 100%;
  height: 56px;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0px 0px 12px 3px rgba(0, 0, 0, 0.1);
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  position: absolute;
  bottom: 0px;
  left: 0px;
  display: flex;
  justify-content: space-between;
  line-height: 56px;
  padding: 0px 20px;
}

.derwer_footerSum {
  font-size: 14px;
  font-weight: 400;
  color: #7F848C;

  .sumNamber {
    font-size: 16px;
    font-weight: 500;
    color: #333333;
  }
}

.table_conter {
  height: 600px;
  overflow: auto;
}
</style>