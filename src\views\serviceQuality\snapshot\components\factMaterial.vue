<template>
  <div class="main">
    <el-dialog
      v-dialogDrag
      custom-class="mainDialog"
      append-to-body
      :visible.sync="changefactMaterialShow"
      :close-on-click-modal="false"
      :before-close="closeDialog"
      class="personDialog"
    >
      <template slot="title">
        <span class="dialog-title">耗材选择</span>
      </template>
      <div class="dialog-content" style="width: 100%; display: flex;">
        <div class="content-left">
          <el-tree class="tree" node-key="id" default-expand-all :data="treeData" :props="defaultProps" @node-click="handleNodeClick"></el-tree>
        </div>
        <div class="content-right">
          <div style="margin-bottom: 20px;">
            <el-input
              v-model="depProductName"
              style="width: 317px; margin-right: 20px;"
              placeholder="请输入耗材名称"
              maxlength="25"
              onkeyup="if(value.length>25)value=value.slice(0,25)"
              @keyup.enter.native="_searchByCondition"
            ></el-input>
            <el-button class="sino-button-sure" style="margin-left: 30px;" @click="_searchByCondition">查询</el-button>
            <el-button class="sino-button-sure" @click="_resetCondition">重置</el-button>
          </div>
          <el-table
            ref="multipleTable"
            v-loading="tableLoading"
            :data="tableData"
            height="300"
            :cell-style="$tools.setCell(3)"
            :header-cell-style="$tools.setHeaderCell(3)"
            style="width: 100%;"
            element-loading-background="rgba(0, 0, 0, 0.2)"
            @selection-change="selectionChange"
          >
            <el-table-column fixed type="selection" width="70" align="center"></el-table-column>
            <el-table-column fixed prop="depProductName" show-overflow-tooltip label="耗材名称"></el-table-column>
            <el-table-column fixed prop="remark" show-overflow-tooltip label="耗材说明"></el-table-column>
          </el-table>
          <el-pagination
            :current-page="pageNo"
            :page-sizes="[15, 20, 30, 40]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            class="pagination"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="sino-button-sure" @click="closeDialog">取 消</el-button>
        <el-button class="sino-button-sure" type="primary" @click="savePeople">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: 'factMaterial',
  props: {
    changefactMaterialShow: {
      type: Boolean,
      default: false
    },
    factMaterialData: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data() {
    return {
      tableData: [],
      tableLoading: false,
      treeData: [],
      selectNodeId: '',
      depProductName: '',
      defaultProps: {
        children: 'children',
        label: 'text'
      },
      pageNo: 1,
      pageSize: 15,
      total: 0
    }
  },
  watch: {},
  mounted() {
    this.getTreeData()
    this.getConsumablesList()
  },
  methods: {
    getTreeData() {
      this.$api.getfactMaterialTreeData({}).then((res) => {
        this.treeData = this.$tools.listToTree(res, 'id', 'parent')
      })
    },
    getConsumablesList() {
      this.tableLoading = true
      const params = {
        workTypeCode: '1',
        catalogIdId: this.selectNodeId,
        depProductName: this.depProductName,
        pageNo: this.pageNo,
        pageSize: this.pageSize
      }
      this.$api.getConsumablesList(params).then((res) => {
        this.tableData = res.rows
        this.total = res.total
        this.tableLoading = false
      })
    },
    selectionChange(selection) {
      if (selection.length > 1) {
        this.selectionParams = selection.shift()
        this.$refs.multipleTable.toggleRowSelection(this.selectionParams, false)
      } else {
        this.selectionParams = selection
      }
    },
    handleNodeClick(data) {
      this.selectNodeId = data.id
      this.getConsumablesList()
    },
    // 取消按钮
    closeDialog() {
      this.$emit('closeDialog')
    },
    // 确认按钮
    savePeople() {
      this.$emit('factMaterialSure', this.selectionParams[0])
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getConsumablesList()
    },
    handleCurrentChange(val) {
      this.pageNo = val
      this.getConsumablesList()
    },
    _searchByCondition() {
      this.pageNo = 1
      this.getConsumablesList()
    },
    _resetCondition() {
      this.pageSize = 15
      this.selectNodeId = ''
      this.depProductName = ''
      this._searchByCondition()
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
::v-deep .mainDialog {
  .el-dialog__body {
    padding: 10px 20px;
  }

  .el-dialog__footer {
    padding-right: 30px;
  }

  .dialog-title {
    color: #3562db;
    font-family: PingFangSC-Medium, "PingFang SC";
  }

  .dialog-title::before {
    content: "";
    display: inline-block;
    width: 2px;
    border-radius: 1px;
    height: 13px;
    background: #3562db;
    margin-right: 10px;
  }

  .content-left {
    width: 30%;
    margin-right: 3%;
    height: 400px;
    overflow: auto;
  }

  .content-right {
    width: 70%;

    .sino-button-sure {
      min-width: 4rem;
      height: 2.4rem;
    }
  }
}

.main {
  ::v-deep .el-dialog {
    width: 55%;

    .el-dialog__body {
      padding: 10px 20px;
    }

    .el-dialog__footer {
      padding-right: 30px;
    }
  }

  ::v-deep .el-table-column--selection .cell {
    padding-left: 0.875rem;
    padding-right: 0.875rem;
    text-overflow: initial;
  }
}

.el-button {
  height: 38px;
}
</style>
