import Layout from '@/layout'
import EmptyLayout from '@/layout/empty'
export default [
  {
  path: '/education-manage',
  component: Layout,
  redirect: '/education-manage/curriculumReform',
  meta: {
    title: '教育培训'
    // menuAuth: '/education-manage/curriculumReform'
  },
  children: [
    {
      path: 'curriculumReform',
      component: () => import('@/views/safety/courseIndex/curriculumReform.vue'),
      name: 'curriculumReform',
      meta: {
        title: '学习资料'
      }
    },
    {
      path: 'courseDetils',
      name: 'courseDetils',
      component: () => import('@/views/safety/courseOnline/components/courseDetils.vue'),
      meta: {
        title: '课程详情',
        sidebar: false,
        activeMenu: '/courseOnline/components/courseDetils'
      }
    },
    {
      path: 'answerIng',
      name: 'answerIng',
      component: () => import('@/views/safety/courseOnline/components/answerIng.vue'),
      meta: {
        title: '课后习题',
        sidebar: false,
        activeMenu: '/courseOnline/components/answerIng'
      }
    },
    {
      path: 'seeTrainFile',
      name: 'seeTrainFile',
      component: () => import('@/views/safety/courseIndex/components/seeTrainFile.vue'),
      meta: {
        title: '课程详情',
        sidebar: false,
        activeMenu: '/courseIndex/components/seeTrainFile'
      }
    },
    {
      path: 'taskDetails',
      name: 'taskDetails',
      component: () => import('@/views/safety/courseOnline/components/taskDetails.vue'),
      meta: {
        title: '我的任务详情',
        sidebar: false,
        activeMenu: '/courseOnline/components/taskDetails'
      }
    },
    {
      path: 'onLineCourse',
      component: () => import('@/views/safety/courseOnline/onLineCourse.vue'),
      name: 'onLineCourse',
      meta: {
        title: '在线学习'
      }
    },
    {
      path: 'addCourse',
      name: 'addCourse',
      component: () => import('@/views/safety/courseIndex/components/addCourse.vue'),
      meta: {
        title: '创建课程',
        sidebar: false,
        activeMenu: '/courseIndex/components/addCourse'
      }
    },
    {
      path: 'courseInfo',
      name: 'courseInfo',
      component: () => import('@/views/safety/courseIndex/components/courseInfo.vue'),
      meta: {
        title: '课程详情',
        sidebar: false,
        activeMenu: '/courseIndex/components/courseInfo'
      }
    },
    {
      path: 'videoPlay',
      name: 'videoPlay',
      component: () => import('@/views/safety/courseIndex/components/videoPlay.vue'),
      meta: {
        title: '视频',
        sidebar: false,
        activeMenu: '/courseIndex/components/videoPlay'
      }
    },
    {
      path: 'seeFile',
      name: 'seeFile',
      component: () => import('@/views/safety/courseIndex/components/seeFile.vue'),
      meta: {
        title: '文件',
        sidebar: false,
        activeMenu: '/courseIndex/components/seeFile'
      }
    },
    {
      path: 'learningTasks',
      component: () => import('@/views/safety/courseOnline/learningTasks.vue'),
      name: 'learningTasks',
      meta: {
        title: '学习任务'
      }
    },
    {
      path: 'distributeTask',
      component: () => import('@/views/safety/distribute/distributeTask.vue'),
      name: 'distributeTask',
      meta: {
        title: '任务派发'
      }
    },
    {
      path: 'learnTaskDetails',
      name: 'learnTaskDetails',
      component: () => import('@/views/safety/distribute/components/learnTaskDetails.vue'),
      meta: {
        title: '任务派发',
        sidebar: false,
        activeMenu: '/distribute/components/learnTaskDetails'
      }
    },
    {
      path: 'courseSchedule',
      name: 'courseSchedule',
      component: () => import('@/views/safety/distribute/components/courseSchedule.vue'),
      meta: {
        title: '课程详情',
        sidebar: false,
        activeMenu: '/distribute/components/courseSchedule'
      }
    },
    {
      path: 'addTask',
      name: 'addTask',
      component: () => import('@/views/safety/distribute/components/addTask.vue'),
      meta: {
        title: '创建学习任务',
        sidebar: false,
        activeMenu: '/distribute/components/addTask'
      }
    },
    {
      path: 'classificationIndex',
      component: () => import('@/views/safety/classification/classificationIndex.vue'),
      name: 'classificationIndex',
      meta: {
        title: '课程分类管理'
      }
    },
    {
      path: 'trainingTemplate',
      component: () => import('@/views/safety/trainTemplate/trainingTemplate.vue'),
      name: 'trainingTemplate',
      meta: {
        title: '培训模板'
      }
    },
    {
      path: 'addTemplate',
      component: () => import('@/views/safety/trainTemplate/components/addTemplate.vue'),
      name: 'addTemplate',
      meta: {
        title: '创建培训模板',
        sidebar: false,
        activeMenu: '/trainTemplate/components/addTemplate'
      }
    },
    {
      path: 'templateDetails',
      component: () => import('@/views/safety/trainTemplate/components/templateDetails.vue'),
      name: 'templateDetails',
      meta: {
        title: '培训模板详情',
        sidebar: false,
        activeMenu: '/trainTemplate/components/templateDetails'
      }
    },
    {
      path: 'trainingPlan',
      component: () => import('@/views/safety/planTrain/trainingPlan.vue'),
      name: 'trainingPlan',
      meta: {
        title: '培训计划',
        activeMenu: '/trainingPlan'
      }
    },
    {
      path: 'addTrainPlan',
      name: 'addTrainPlan',
      component: () => import('@/views/safety/planTrain/components/addTrainPlan.vue'),
      meta: {
        title: '创建培训',
        sidebar: false,
        activeMenu: '/planTrain/components/addTrainPlan'
      }
    },
    {
      path: 'addConferencePlan',
      name: 'addConferencePlan',
      component: () => import('@/views/safety/planTrain/components/addConferencePlan.vue'),
      meta: {
        title: '创建培训',
        sidebar: false,
        activeMenu: '/planTrain/components/addConferencePlan'
      }
    },
    {
      path: 'trainPlanDetail',
      name: 'trainPlanDetail',
      component: () => import('@/views/safety/planTrain/trainingPlan/trainPlanDetail.vue'),
      meta: {
        title: '培训计划详情',
        sidebar: false,
        activeMenu: '/planTrain/trainingPlan/trainPlanDetail'
      }
    },
    {
      path: 'conferencePlanDetail',
      name: 'conferencePlanDetail',
      component: () => import('@/views/safety/planTrain/trainingPlan/conferencePlanDetail.vue'),
      meta: {
        title: '会议计划详情',
        sidebar: false,
        activeMenu: '/planTrain/trainingPlan/conferencePlanDetail'
      }
    },
    {
      path: 'trainingTasks',
      component: () => import('@/views/safety/taskTrain/trainingTasks.vue'),
      name: 'trainingTasks',
      meta: {
        title: '培训任务'
      }
    },
    {
      path: 'addTrainlate',
      name: 'addTrainlate',
      component: () => import('@/views/safety/taskTrain/addTrainlate.vue'),
      meta: {
        title: '创建培训记录',
        sidebar: false,
        activeMenu: 'taskTrain/addTrainlate.vue'
      }
    },
    {
      path: 'trainDetails',
      name: 'trainDetails',
      component: () => import('@/views/safety/taskTrain/trainDetails.vue'),
      meta: {
        title: '培训详情',
        sidebar: false,
        activeMenu: 'taskTrain/trainDetails.vue'
      }
    },
    {
      path: 'addConference',
      name: 'addConference',
      component: () => import('@/views/safety/taskTrain/addConference.vue'),
      meta: {
        title: '创建会议记录',
        sidebar: false,
        activeMenu: 'taskTrain/addConference.vue'
      }
    },
    {
      path: 'questionBankManagement',
      component: () => import('@/views/safety/question/questionBankManagement.vue'),
      name: 'questionBankManagement',
      meta: {
        title: '考题管理'
      }
    },
    {
      path: 'addQuestion',
      name: 'addQuestion',
      component: () => import('@/views/safety/question/addQuestion.vue'),
      meta: {
        title: '逐道录题',
        sidebar: false,
        activeMenu: 'taskTrain/addQuestion.vue'
      }
    },
    {
      path: 'examPlanIndex',
      component: () => import('@/views/safety/examPlan/examPlanIndex.vue'),
      name: 'examPlanIndex',
      meta: {
        title: '考试计划'
      }
    },
    {
      path: 'addExam',
      name: 'addExam',
      component: () => import('@/views/safety/examPlan/addExam.vue'),
      meta: {
        title: '组卷',
        sidebar: false,
        activeMenu: 'taskTrain/addExam.vue'
      }
    },
    {
      path: 'examDetails',
      name: 'examDetails',
      component: () => import('@/views/safety/examPlan/examDetails.vue'),
      meta: {
        title: '试题详情',
        sidebar: false,
        activeMenu: 'taskTrain/examDetails.vue'
      }
    },
    {
      path: 'examRecords',
      component: () => import('@/views/safety/examRecord/examRecords.vue'),
      name: 'examRecords',
      meta: {
        title: '考试任务'
      }
    },
    {
      path: 'recordsDetails',
      name: 'recordsDetails',
      component: () => import('@/views/safety/examRecord/recordsDetails.vue'),
      meta: {
        title: '详情',
        sidebar: false,
        activeMenu: 'taskTrain/recordsDetails.vue'
      }
    },
    {
      path: 'answerInfo',
      name: 'answerInfo',
      component: () => import('@/views/safety/examRecord/answerInfo.vue'),
      meta: {
        title: '详情',
        sidebar: false,
        activeMenu: 'taskTrain/answerInfo.vue'
      }
    }
  ]
},
]
