<!-- 报警统计弹窗 -->
<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="报警统计"
    width="60%"
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="content">
      <div class="content-heade">
        <el-date-picker
          v-model="searchFrom.dataRange"
          type="daterange"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptions"
          :clearable="false"
        />
        <div style="display: inline-block;">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="searchForm">查询</el-button>
        </div>
      </div>
      <div v-loading="loading" class="content-main">
        <ContentCard class="content-left" title="报警统计">
          <div slot="content" style="width: 100%; height: 100%; display: flex;">
            <div class="cardContent-left">
              <div v-for="item in alarmStatisticsList" :key="item.title" class="left-item">
                <p class="item-title">{{ item.title }}</p>
                <p class="item-value" :style="{ color: item.color }">{{ item.value }}<span>个</span></p>
                <img class="item-icon" :src="item.icon" :alt="item.title" />
              </div>
            </div>
            <echarts ref="deviceType" domId="deviceType" width="65%" height="100%" />
          </div>
        </ContentCard>
        <ContentCard class="content-right" title="单设备">
          <div slot="content" class="card-content">
            <el-select v-model="searchFrom.menuCode" placeholder="请选择设备" filterable @change="searchForm">
              <el-option label="全部" :value="projectCode == distributingCode ? entityMenuCode : ''"> </el-option>
              <el-option v-for="item in entityList" :key="item.code" :label="item.name" :value="item.code"></el-option>
            </el-select>
            <div class="card-content-table table-content">
              <el-table ref="table" :resizable="false" border :data="tableData" :height="tableHeight" style="width: 100%;">
                <el-table-column label="序号" type="index" width="50" />
                <el-table-column prop="alarmStartTime" label="报警时间" width="170"></el-table-column>
                <el-table-column prop="alarmObjectName" label="报警对象名称" width="120" show-overflow-tooltip></el-table-column>
                <el-table-column prop="iphPoliceLevel" label="严重等级" width="110">
                  <span slot-scope="scope" class="alarmLevel" :style="{ background: alarmLevelItem[scope.row.alarmLevel].color }">
                    {{ alarmLevelItem[scope.row.alarmLevel].text }}
                  </span>
                </el-table-column>
                <el-table-column prop="incidentName" label="事件类型" show-overflow-tooltip></el-table-column>
                <el-table-column prop="menuName" label="类型" show-overflow-tooltip></el-table-column>
                <el-table-column prop="alarmSpaceName" label="位置" show-overflow-tooltip></el-table-column>
                <el-table-column prop="alarmStatus" label="处理状态" width="120">
                  <div slot-scope="scope" class="alarmStatus" :style="{ color: scope.row.alarmStatus == 0 ? '#FA403C' : scope.row.alarmStatus == 1 ? '#fd9434' : '#999' }">
                    <span class="alarmStatusIcon" :style="{ background: scope.row.alarmStatus == 0 ? '#FA403C' : scope.row.alarmStatus == 1 ? '#ff9435' : '#999' }"></span>
                    {{ scope.row.alarmStatus == 0 ? '未处理' : scope.row.alarmStatus == 1 ? '处理中' : '已关闭' }}
                  </div>
                </el-table-column>
              </el-table>
            </div>
            <div class="card-content-footer">
              <el-pagination
                :current-page="pagination.current"
                :page-sizes="pagination.pageSizeOptions"
                :page-size="pagination.size"
                :layout="pagination.layoutOptions"
                :total="pagination.total"
                @size-change="paginationSizeChange"
                @current-change="paginationCurrentChange"
              >
              </el-pagination>
            </div>
          </div>
        </ContentCard>
      </div>
    </div>
    <span slot="footer">
      <el-button plain type="primary" @click="closeDialog">关 闭</el-button>
    </span>
  </el-dialog>
</template>
<script>
import alarmStatistics from '@/assets/images/monitor/alarm-statistics.png'
import alarmAlarm from '@/assets/images/monitor/alarm-pending.png'
import alarmDoing from '@/assets/images/monitor/alarm-doing.png'
import chartMixin from '../../airMenu/mixins/chartMixin'
import tableListMixin from '@/mixins/tableListMixin.js'
import moment from 'moment'
moment.locale('zh-cn')
import { monitorTypeList } from '@/util/dict.js'
export default {
  name: 'alarmStatisticsDialog',
  mixins: [chartMixin, tableListMixin],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    projectCode: {
      type: String,
      default: ''
    },
    entityMenuCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      entityList: [],
      loading: false,
      tableData: [],
      distributingCode: monitorTypeList.find((item) => item.projectName == '配电监测').projectCode, // 配电监测code
      searchFrom: {
        menuCode: '', // 设备
        dataRange: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')] // 时间范围
      },
      pickerOptions: {
        shortcuts: [
          {
            text: '今日',
            onClick(picker) {
              picker.$emit('pick', [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')])
            }
          },
          {
            text: '本月',
            onClick(picker) {
              picker.$emit('pick', [moment().startOf('month').format('YYYY-MM-DD'), moment().endOf('month').format('YYYY-MM-DD')])
            }
          },
          {
            text: '本年',
            onClick(picker) {
              picker.$emit('pick', [moment().startOf('year').format('YYYY-MM-DD'), moment().endOf('year').format('YYYY-MM-DD')])
            }
          }
        ]
      },
      alarmStatisticsList: [
        {
          title: '报警统计',
          color: '#3562DB',
          icon: alarmStatistics,
          value: 0
        },
        {
          title: '未处理',
          color: '#FA403C',
          icon: alarmAlarm,
          value: 0
        },
        {
          title: '处理中',
          color: '#FF9435',
          icon: alarmDoing,
          value: 0
        }
      ],
      alarmLevelItem: {
        0: { text: '通知', color: '#3562DB' },
        1: { text: '一般', color: '#3562DB' },
        2: { text: '紧急', color: '#FF9435' },
        3: { text: '重要', color: '#FA403C' }
      }
    }
  },
  computed: {},
  created() {
    this.searchFrom.menuCode = this.projectCode == this.distributingCode ? this.entityMenuCode : ''
    this.getAirPoliceMenuList()
    this.$nextTick(() => {
      this.getDataList()
    })
  },
  mounted() {},
  methods: {
    // 获取报警设备菜单
    getAirPoliceMenuList() {
      let params = {projectCode: this.projectCode}
      if (this.projectCode == this.distributingCode) params.menuCode = this.entityMenuCode
      this.$api.GetAirPoliceMenuList(params).then((res) => {
        if (res.code == 200) {
          this.entityList = res.data
        }
      })
    },
    // 查看详情
    getDataList() {
      let params = {
        menuCode: this.searchFrom.menuCode,
        startTime: this.searchFrom.dataRange[0],
        endTime: this.searchFrom.dataRange[1],
        projectCode: this.projectCode,
        page: this.pagination.current,
        pageSize: this.pagination.size
      }
      let newArr = []
      this.loading = true
      this.$api
        .GetAirRunPoliceDetail(params)
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            ;(res.data?.airRunPolice?.policeList ?? []).forEach((item) => {
              newArr.push({
                name: item.menuName,
                value: item.policeCount
              })
            })
            this.tableData = res.data?.policeList?.list ?? []
            this.pagination.total = res.data?.policeList?.count ?? 0
            this.alarmStatisticsList[0].value = res.data?.airRunPolice?.total ?? 0
            this.alarmStatisticsList[1].value = res.data?.airRunPolice?.noDealCount ?? 0
            this.alarmStatisticsList[2].value = res.data?.airRunPolice?.isDealCount ?? 0
            this.$refs.deviceType.init(this.setPieChart('个', newArr, ['监测项类型', '报警占比']))
          } else {
            this.$refs.deviceType.init(this.setPieChart())
          }
        })
        .catch(() => {
          this.loading = false
          this.$refs.deviceType.init(this.setPieChart())
        })
    },
    // 重置查询
    resetForm() {
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.searchFrom.menuCode = this.projectCode == this.distributingCode ? this.entityMenuCode : ''
      this.searchForm()
    },
    // 查询
    searchForm() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 0;
  max-height: calc(88vh - 110px);
}

.content {
  width: 100%;
  height: 100%;

  .content-heade {
    padding: 10px;
    background: #fff;

    & > div {
      margin-right: 10px;
    }
  }

  .content-main {
    width: 100%;
    padding: 15px 10px;
  }

  .content-left {
    height: 415px;
    width: 100%;

    .cardContent-left {
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: 35%;
      height: 100%;
      padding-right: 16px;

      p {
        margin: 0;
      }

      .left-item {
        padding: 14px 22px;
        background: #faf9fc;
        border-radius: 4px;
        margin-bottom: 7px;
        position: relative;

        .item-title {
          font-size: 15px;
          font-weight: 500;
          color: #121f3e;
        }

        .item-value {
          margin-top: 4px;
          font-size: 30px;

          & > span {
            font-size: 15px;
            font-weight: 500;
            color: #ccced3;
          }
        }

        .item-icon {
          position: absolute;
          right: 22px;
          bottom: 14px;
          width: 40px;
          height: 40px;
        }
      }

      & :last-child {
        margin-bottom: 0;
      }
    }
  }

  .content-right {
    height: 500px;
    width: 100%;
    margin-top: 16px;

    .card-content {
      height: 100%;
      padding: 0 0 0 4px;
      display: flex;
      flex-direction: column;

      .card-content-table {
        flex: 1;
        overflow: auto;
        margin-top: 16px;

        ::v-deep .el-progress {
          .el-progress-bar__outer {
            border-radius: 0;
            height: 8px !important;
          }

          .el-progress-bar__inner {
            border-radius: 0;
          }
        }

        .alarmLevel {
          padding: 3px 6px;
          border-radius: 4px;
          color: #fff;
          line-height: 14px;
        }

        .alarmStatus {
          position: relative;
          display: inline-block;
          padding-left: 12px;

          .alarmStatusIcon {
            display: inline-block;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            border-radius: 100%;
          }
        }
      }

      .card-content-footer {
        padding: 10px 0 0;
      }
    }
  }
}

::v-deep .model-dialog {
  padding: 0 !important;
  margin-top: 8vh !important;
}
</style>
