<template>
  <div class="form-detail">
    <div class="reserve-scorll">
      <div class="width: 100%">
        <!-- 创建工单 -->
        <div class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('CZ', 0)">
              <span class="linear-g-span1">创建工单（已完工）</span>
              <span>{{ workOrderDetail.createDate }}</span>
              <i ref="CZright" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" ref="CZdown" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <!-- <div class="show-content"> -->
            <TransitionHeight ref="CZ" heigtRef="CZBox">
              <ul class="item-row">
                <li class="width45">
                  <span class="li-first-span">反馈人</span><span class="li-last-span">{{ workOrderDetail.callerName }}</span>
                </li>
                <li class="width45">
                  <span class="li-first-span">反馈电话</span><span class="li-last-span">{{ workOrderDetail.sourcesPhone }}</span>
                </li>
              </ul>
              <ul class="item-row">
                <li class="width45">
                  <span class="li-first-span">反馈日期</span><span class="li-last-span">{{ workOrderDetail.createDate }}</span>
                </li>
                <li class="width45">
                  <span class="li-first-span">任务点</span><span class="li-last-span">{{ workOrderDetail.taskPointName }}</span>
                </li>
              </ul>
              <ul class="item-row">
                <li class="width45">
                  <span class="li-first-span">问题科室</span><span class="li-last-span">{{ workOrderDetail.sourcesDeptName }}</span>
                </li>
                <li class="width45">
                  <span class="li-first-span">问题地点</span><span class="li-last-span">{{ workOrderDetail.locationName }}</span>
                </li>
              </ul>
              <ul class="item-row">
                <li class="width45">
                  <span class="li-first-span">地点电话</span><span class="li-last-span">{{ workOrderDetail.locationPhone }}</span>
                </li>
              </ul>
              <!-- 0 1 3  呼叫中心 web app -->
              <ul class="item-row">
                <li style="display: flex">
                  <span class="li-first-span">录音</span>
                  <div v-if="workOrderDetail.callerTapeUrl" id="audio-box">
                    <audio controls>
                      <source :src="$tools.imgUrlTranslation(workOrderDetail.callerTapeUrl)" />
                      <!-- src="https://sinomis.oss-cn-beijing.aliyuncs.com/ioms/ZKYXYY/question/2021/06/24/mp3/20210624104642208466.mp3?Expires=1649988786&OSSAccessKeyId=LTAIeUfTgrfT5j7Y&Signature=QI12blGWwn1KzARBWOu7TdeD0rI%3D" -->
                    </audio>
                    <!-- <a href="javascript:;" onclick="downLoad(olgTaskManagement.audioPath)" title="下载">下载</a> -->
                  </div>
                  <span></span>
                </li>
              </ul>
              <!-- 1 3 web app -->
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">附件</span>
                  <!-- listAttUrl -->
                  <p v-if="workOrderDetail.attachmentUrl">
                    <span v-for="(img, index) in workOrderDetail.attachmentUrl.split(',')" :key="index">
                      <el-image style="width: 100px; height: 100px; margin-right: 15px" :src="$tools.imgUrlTranslation(img)" :preview-src-list="[$tools.imgUrlTranslation(img)]">
                      </el-image>
                    </span>
                  </p>
                  <span></span>
                </li>
              </ul>
              <ul class="item-row">
                <li class="width45">
                  <span class="li-first-span">问题内容</span><span class="li-last-span">{{ workOrderDetail.questionDescription }}</span>
                </li>
              </ul>
              <ul class="item-row">
                <li class="width120">
                  <span class="li-first-span" style="width: 150px">耗材列表</span>
                  <table v-if="workOrderDetail.consumableList?.length" class="maint-table" style="table-layout: fixed">
                    <tbody>
                      <tr>
                        <td style="width: 50px">序号</td>
                        <td style="width: 100px">耗材名称</td>
                        <td style="width: 100px">耗材规格</td>
                        <td style="width: 100px">耗材单位</td>
                        <td style="width: 50px">数量</td>
                        <td style="width: 50px">单价</td>
                        <td style="width: 50px">总价</td>
                      </tr>
                      <tr v-for="(item, index) in workOrderDetail.consumableList" :key="index">
                        <td>
                          <div title="序号" class="one-line">{{ index + 1 }}</div>
                        </td>
                        <td>
                          <div title="耗材名称" class="one-line">{{ item.depName }}</div>
                        </td>
                        <td>
                          <div title="耗材规格" class="one-line">{{ item.specification }}</div>
                        </td>
                        <td>
                          <div title="耗材单位" class="one-line">{{ item.unit }}</div>
                        </td>
                        <td>
                          <div title="数量" class="one-line">{{ item.accOutNum }}</div>
                        </td>
                        <td>
                          <div title="单价" class="one-line">{{ item.price }}</div>
                        </td>
                        <td>
                          <div title="总价" class="one-line">{{ item.totalPrice }}</div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </li>
              </ul>
            </TransitionHeight>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TransitionHeight from './transitionHeight.vue'
import moment from 'moment'

let that
export default {
  name: 'workOrderDetailList',
  components: {
    TransitionHeight
  },
  filters: {
    UDfilter(val) {
      if (val === '0') {
        return '紧急事故'
      } else if (val === '1') {
        return '紧急催促'
      } else {
        return '一般'
      }
    }
  },
  props: {
    rowData: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data() {
    return {
      moment,
      cancelFilter: '',
      rateTexts: ['非常差', '差', '一般', '满意', '非常满意'],
      cancelReasonOptions: [],
      workOrderDetail: {
        olgTaskManagement: {
          repairWork: ''
        }
      }
    }
  },
  beforeCreate() {
    that = this
  },
  created() {
    const params = this.rowData
    this.getWorkOrderDetail(params)
  },
  mounted() {
    setTimeout(() => {
      this.collectEvent('CZ')
    }, 100)
  },
  methods: {
    jumpOrderDetail() {},
    getWorkOrderDetail(params) {
      this.$api.getSelfOrderDetails({ id: params.id }).then((res) => {
        // console.log('@', res)
        if (res.code == '200') {
          this.workOrderDetail = res.data
        }
      })
    },
    // 获取字典
    getIomsDictList(type) {
      const params = {
        type: type
      }
      this.$api.getIomsDictList(params).then((res) => {
        this.cancelReasonOptions = res
      })
    },
    transform(val) {
      let arr = this.cancelReasonOptions.find((i) => {
        return i.value == val
      })
      if (arr) {
        return arr.label
      } else {
        return ''
      }
    },
    // 展开关闭事件
    collectEvent(box) {
      console.log(box)
      // console.log(this.$refs[box + 'right' + i][0].style.display)
      if (this.$refs[box + 'right'].style.display === 'inline-block') {
        this.$refs[box + 'right'].style.display = 'none'
        this.$refs[box + 'down'].style.display = 'inline-block'
        this.$refs[box].show = true
      } else {
        this.$refs[box + 'right'].style.display = 'inline-block'
        this.$refs[box + 'down'].style.display = 'none'
        this.$refs[box].show = false
      }
    },
    /*
     *  综合维修工单科室及地点的历史申报记录
     * type 对应查询类型：1：地点 2：科室
     * code 对应的科室地点code或者地点code
     * name 对应的科室地点name或者地点name
     */
    openTab(type, code, name) {
      let historyDept = '' // eslint-disable-line no-unused-vars
      let historyDeptName = '' // eslint-disable-line no-unused-vars
      let historyLocaltion = '' // eslint-disable-line no-unused-vars
      let historyLocaltionName = '' // eslint-disable-line no-unused-vars
      if (type === 1) {
        historyLocaltion = code
        if (code !== undefined && code !== '' && code !== null) {
          historyLocaltion = code.replace(/\_/g, ',') // eslint-disable-line no-useless-escape
        }
        historyLocaltionName = name
      } else if (type === 2) {
        historyDept = code
        historyDeptName = name
      }
      console.log(historyDept, historyDeptName, historyLocaltion, historyLocaltionName)
      this.$router.push({
        name: 'workOrderList',
        params: {
          isHistory: '1',
          historyDept: historyDept,
          historyLocaltion: historyLocaltion,
          historyDeptName: historyDeptName,
          historyLocaltionName: historyLocaltionName,
          workTypeCode: this.workOrderDetail.olgTaskManagement.workTypeCode
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.form-detail {
  height: 100%;
  width: 100%;
  padding: 10px 25px;
  overflow-y: scroll;
  box-sizing: border-box;

  .reserve-scorll {
    width: 100%;
    height: calc(100% - 50px);
    overflow-y: auto;
    box-sizing: border-box;
    padding-right: 15px;
  }

  .reserve-plan {
    width: 100%;
    position: relative;
    // height: inherit;
    .plan-title {
      display: flex;
      width: 100%;
      height: 30px;

      .color-box {
        height: 1.25rem;
        width: 1.25rem;
        color: #3562db;
        line-height: 1.25rem;
        text-align: center;
        border-radius: 3px;
        font-size: 16px;
        margin: auto;
      }

      .linear-g {
        margin-left: 2px;
        width: calc(100% - 22px);
        height: 100%;
        background: #f6f5fa;
        font-size: 13px;
        line-height: 30px;
        font-family: PingFangSC-Medium;
        color: #3562db;
        border-radius: 6px;
        cursor: pointer;

        .linear-g-span1 {
          margin: 0 15px;
          font-weight: 600;
        }

        .title-icon {
          float: right;
          line-height: 30px;
          margin-right: 5px;
        }
      }
    }

    .plan-content {
      width: calc(100% - 33px);
      margin-left: 11px;
      color: #676a6c;
      font-size: 13px;

      .item-row {
        width: 100%;
        display: flex;
        padding: 20px 0 20px 30px;
        box-sizing: border-box;

        .width30 {
          width: 30%;
        }

        .width45 {
          width: 45%;
        }

        .width90 {
          width: 90%;
          display: flex;
          align-items: center;
        }
        .width120 {
          width: 120%;
          display: flex;
          align-items: center;
        }

        ::v-deep .el-image__error,
        ::v-deep .el-image__placeholder {
          background: center;
        }

        .li-first-span {
          display: inline-block;
          width: 80px;
          white-space: nowrap;
          font-size: 14px;
          font-family: PingFang-SC-Medium, PingFang-SC;
          font-weight: 700;
          color: #676a6c;
        }

        .li-last-span {
          font-size: 14px;
          font-family: PingFang-SC-Medium, PingFang-SC;
          font-weight: 500;
          color: #676a6c;
        }

        .recording-ALabel {
          color: #0379f1;
          font-size: 14px;
          text-decoration: none;

          i {
            margin: 0 3px 0 10px;
          }
        }

        #audio-box {
          display: flex;
        }

        #audio-box > audio {
          width: 260px;
          height: 30px;
        }

        #audio-box > a {
          width: 40px;
          text-align: center;
          background-color: #2cc7c5;
          height: 35px;
          line-height: 35px;
          color: #fff;
          border-radius: 5px;
          margin-left: 10px;
        }
      }

      .show-content {
        width: 100%;
      }
    }

    .plan-content-line {
      border-left: 1px solid #676a6c;
    }

    .plan-content-noline {
      width: calc(100% - 33px);
      margin-left: 11px;
      padding: 20px 0 20px 20px;
      color: #b5bacb;
      font-size: 13px;
    }

    .maint-table {
      width: 60%;

      td {
        padding: 5px 0 5px 10px;
        border: 1px solid #eee;
        height: 25px;
        line-height: 25px;
        display: table-cell;
        vertical-align: middle;
        color: #676a6c;
      }

      tr:first-child {
        background-color: #f5f6fc;
      }

      td:first-child {
        width: 35%;
      }

      .one-line {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .repair-work {
      float: right;
      margin: 15px 10px;
      position: absolute;
      top: 40px;
      right: 10px;

      ::v-deep .el-checkbox__label {
        color: #121f3e;
      }
    }
  }
}
</style>
