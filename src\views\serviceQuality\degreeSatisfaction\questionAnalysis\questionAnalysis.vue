<template>
  <div class="main-container">
    <div class="whole">
      <div class="page-title">单题分析</div>
      <el-row style="border-bottom: 1px solid #d8dee7">
        <el-col :md="16" :lg="16" :xl="14" style="height: auto">
          <BreadcrumbNavBar />
        </el-col>
        <el-col :md="8" :lg="8" :xl="10">
          <HeaderButton />
        </el-col>
      </el-row>
      <div v-if="data.total.answerCount != 0" class="main-container">
        <!-- count盒子 -->
        <div class="countBox">
          <span>总样本数:{{ data.total.answerCount }}</span>
        </div>
        <!-- 问卷只有填空题时不做分析，向用户展示提示信息 -->
        <div v-if="data.analysis.length === 0" class="show-info">
          <span>本问卷没有可分析的题型！（只支持单选、多选、下拉菜单）</span>
        </div>
        <!-- 题盒子 -->
        <div v-for="(item, index) in data.analysis" :key="index" class="topicBox">
          <!-- 表格 -->
          <table class="tableCss" style="width: 100%" cellspacing="0" cellpadding="0" border="1">
            <thead>
              <tr>
                <th :colspan="getHeaderLength(item.type)">
                  {{ index + 1 }}.{{ item.name }}
                  <span class="titleColor">{{ getObjKey(transformation, item.type).name }}</span>
                </th>
              </tr>
            </thead>
            <tbody>
              <!-- 表格表头 -->
              <tr>
                <td v-for="(cks, indexTable) in getHeader(item.type)" :key="indexTable">
                  {{ cks.title }}
                </td>
              </tr>
              <!-- 表格表头结束 -->
              <!-- 表格信息 -->
              <!-- 非二级下拉 -->
              <template v-if="!getSee(item.type)">
                <tr v-for="(obj, indexDropdown) in item.value" :key="indexDropdown">
                  <td v-for="(cks, indexTable) in getHeader(item.type)" :key="indexTable">
                    <!-- 进度条 -->
                    <span v-if="cks.progressBar">
                      <el-progress color="#5188FC" class="progressBar" :text-inside="true" :stroke-width="24" :percentage="obj[cks.key]"></el-progress>
                    </span>
                    <!-- 进度条结束 -->
                    <!-- 百分号 和文字展示 -->
                    <div v-else>{{ transformationData(obj, cks) }}</div>
                    <!-- 百分号 和文字展示结束 -->
                  </td>
                </tr>
              </template>
              <!-- 非二级下拉结束 -->
              <!-- 二级下拉 -->
              <template v-else>
                <tr v-for="(objs, objsIndex) in transformationSelect(item.value)" :key="objsIndex">
                  <td v-for="(cks, indexConter) in getHeaderSelect(item.type, objs.count)" :key="indexConter" :rowspan="mer(cks, objs.count)">
                    <!-- 进度条 -->
                    <span v-if="cks.progressBar">
                      <el-progress color="#3562db" class="progressBar" :text-inside="true" :stroke-width="24" :percentage="objs[cks.key]" status="success"></el-progress>
                    </span>
                    <!-- 进度条结束 -->
                    <!-- 百分号 和文字展示 -->
                    <div v-else>
                      {{ cks.splicing ? `${objs[cks.key]}%` : objs[cks.key] }}
                    </div>
                    <!-- 百分号 和文字展示结束 -->
                  </td>
                </tr>
              </template>
              <!-- 二级下拉 结束-->

              <!-- 表格信息结束 -->
            </tbody>
          </table>
          <!-- 表格结束 -->
          <div class="buttonBox">
            <el-radio-group v-model="buttonArray[index].radio" fill="#3562db" size="mini">
              <span v-for="(item, indexRadio) in button" :key="indexRadio" class="buttonStyle" @click="seeSwitch(index, item)">
                <el-radio-button :label="item.name"></el-radio-button>
              </span>
            </el-radio-group>
          </div>
          <!-- 图型 -->
          <div v-if="buttonArray[index].flag" class="graphicalChart">
            <ve-chart
              :legend-visible="false"
              :toolbox="toolbox"
              :data="buttonArray[index].chartData"
              :settings="buttonArray[index].chartSettings"
              :colors="buttonArray[index].color"
              height="350px"
            ></ve-chart>
          </div>
        </div>
      </div>
      <div v-else class="main-container">
        <div class="countBox">
          <span>请先收集答卷</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import VePie from 'v-charts/lib/pie.common'
import VeChart from 'v-charts/lib/chart.common'
// import VeLine from 'v-charts/lib/line.common'
// import VeHistogram from 'v-charts/lib/histogram.common'
import BreadcrumbNavBar from '../component/BreadcrumbNavBar/BreadcrumbNavBar.vue'
import HeaderButton from '../questionDesign/component/HeaderButton/HeaderButton.vue'
export default {
  components: {
    BreadcrumbNavBar,
    HeaderButton,
    // [VePie.name]:VePie,
    // [VeHistogram.name]:VeHistogram,
    // [VeLine.name]:VeLine,
    // [VeChart.name]:VeChart
    // VePie,
    // VeHistogram,
    // VeLine,
    VeChart
  },
  data() {
    this.toolbox = {
      // 设置工具箱
      feature: {
        // 自 定义工具栏
        saveAsImage: {
          name: ''
        } // 设置导出
      }
    }
    return {
      columns: {
        ordinary: [
          {
            key: 'name',
            value: 'optionName'
          },
          {
            key: '数量',
            value: 'answerCnt'
          }
        ],
        special: [
          {
            key: 'name',
            value: 'optionName'
          },
          {
            key: '数量',
            value: 'avgSort'
          }
        ]
      },
      buttonArray: [], // 按钮绑定列表
      button: [
        // 按钮列表
        {
          name: '表格', // 按钮名称
          color: [],
          key: false, // 是否展示标识
          flag: '' // 展示那种 图形标识
        },
        {
          color: ['#3562db', '#fc8251', '#5470c6', '#9A60B4', '#ef6567', '#f9c956', '#3BA272', '#45C2E0', '#C1EBDD', '#FFC851', '#5A5476', '#1869A0', '#FF9393'],

          name: '饼状图',
          // color: null,
          flag: 'pie',
          key: true
        },
        {
          color: ['#3562db'],
          name: '柱状图',
          flag: 'histogram',
          key: true
        },
        {
          color: ['#3562db'],
          name: '折线图',
          flag: 'line',
          key: true
        }
      ],
      transformationSelectFonfig: [
        // 转换配置数据
        {
          old: 'optionName', // 转换optionName 为outerLayerOptionName
          new: 'outerLayerOptionName'
        },
        {
          old: 'prop',
          new: 'outerLayerProp'
        },
        {
          old: 'answerCnt',
          new: 'outerLayerAnswerCnt'
        },
        {
          old: 'prop',
          new: 'outerLayerProp'
        }
      ],
      transformation: {
        // 通过题的类型来获取表格是否是普通还是特殊
        checkbox: { key: 'ordinary', chart: 'ordinary', name: '[多选]' }, // 多选
        radio: { key: 'ordinary', chart: 'ordinary', name: '[单选]' }, // 单选
        select: { key: 'ordinary', chart: 'ordinary', name: '[下拉菜单]' }, // 下拉菜单
        array: { key: 'ordinary', chart: 'ordinary', name: '[矩阵]' }, // 矩阵
        sort: { key: 'sortOrdinary', chart: 'special', name: '[排序]' }, // 排序
        nd_select: {
          key: 'ndSelectOrdinary',
          flag: true,
          chart: 'ordinary',
          name: '[二级下拉菜单]'
        } // 二级下拉
      },
      configObj: {
        ndSelectOrdinary: [
          // title 标题  key字段值  //flag 代表需要合并的字段   splicing需要拼接%  progressBar条形图
          { title: '一级菜单', key: 'outerLayerOptionName', flag: true },
          { title: '答卷数量', key: 'outerLayerAnswerCnt', flag: true },
          { title: '占比', key: 'outerLayerProp', flag: true, splicing: true },
          {
            title: '条形图',
            key: 'outerLayerProp',
            flag: true,
            progressBar: true
          },
          { title: '二级菜单', key: 'optionName' },
          { title: '答卷数量', key: 'answerCnt' },
          { title: '占二级菜单比例', key: 'ndProp', splicing: true },
          { title: '占总比例', key: 'prop', splicing: true }
        ],
        sortOrdinary: [
          { title: '选项', key: 'optionName' },
          { title: '排序(值越小排序越靠前)', key: 'avgSort' },
          { title: '占比', key: 'prop', splicing: true },
          { title: '条形图', key: 'prop', progressBar: true }
        ],
        // 配置表格展示 表头内容
        ordinary: [
          { title: '选项', key: 'optionName' },
          { title: '答题数量', key: 'answerCnt' },
          { title: '占比', key: 'prop', splicing: true },
          { title: '条形图', key: 'prop', progressBar: true }
        ]
      },
      data: {
        total: {
          answerCount: 0
        },
        analysis: []
      },
      questionId: localStorage.getItem('questId') // 获取问卷id
    }
  },
  created() {
    // 获取问卷id
    var date = {
      questionId: this.questionId,
      hospitalCode: 'BJSJTYY',
      unitCode: 'BJSYGJ',
      userId: '5662b1b798af0c716f2673aac6d39e44',
      userName: '董文杰6'
    }
    this.$api.getSingle(date).then((res) => {
      if (res.status == 200) {
        this.data = res.data
        this.tableDataConversion()
      } else {
        this.$message.error(res.message)
      }
    })
  },
  methods: {
    // 转换数据格式
    transformationData(obj, cks) {
      if (cks.splicing) {
        return `${obj[cks.key]}%`
      } else {
        return obj[cks.key]
      }
    },
    // 表格数据转换   将数据转换成v-chart要的数据 和有多少个按钮组合
    tableDataConversion() {
      var count = this.data.analysis.length
      var value = this.data.analysis
      for (var a = 0; a < count; a++) {
        var obj = {}
        obj['radio'] = '表格'
        obj['chartData'] = this.transformationVcharts(value, a)
        this.buttonArray.push(obj)
      }
    },
    // 将表格数据转换
    transformationVcharts(value, a) {
      var chartData = {
        columns: [],
        rows: []
      }
      var flagObj = this.getObjKey(this.transformation, value[a].type).chart
      var flagArr = this.getObjKey(this.columns, flagObj)
      for (var d = 0; d < flagArr.length; d++) {
        chartData.columns.push(flagArr[d].key)
      }
      for (var b = 0; b < value[a].value.length; b++) {
        var rowsObj = {}
        for (var c = 0; c < flagArr.length; c++) {
          rowsObj[flagArr[c].key] = value[a].value[b][flagArr[c].value]
        }
        chartData.rows.push(rowsObj)
      }
      return chartData
    },
    // 图形切换点击事件
    seeSwitch(index, flag) {
      // 修改导出文件名称
      this.toolbox.feature.saveAsImage.name = flag.name
      // 那个题展示图标
      this.buttonArray[index].flag = flag.key
      // 图标展示的标识（具体展示那种类型的图表）
      this.buttonArray[index].chartSettings = { type: flag.flag }
      // 表格颜色切换
      this.buttonArray[index].color = flag.color
    },
    // 二级下拉 需要 合并返回
    mer(cks, count) {
      if (cks.flag) {
        return count
      } else {
        return 1
      }
    },
    // 将后端数据转换成需要的前端数据
    transformationSelect(arr) {
      var arrCopy = []
      for (var a = 0; a < arr.length; a++) {
        var count = arr[a]['singleAnalyses'].length == 0 ? 1 : arr[a]['singleAnalyses'].length
        for (var b = 0; b < arr[a]['singleAnalyses'].length; b++) {
          var obj = arr[a]['singleAnalyses'][b]
          for (var c = 0; c < this.transformationSelectFonfig.length; c++) {
            obj[this.transformationSelectFonfig[c]['new']] = arr[a][this.transformationSelectFonfig[c]['old']]
          }
          obj['count'] = count
          arrCopy.push(obj)
          count = 0
        }
      }
      return arrCopy
    },
    // 二级下拉标识
    getSee(str) {
      return this.getObjKey(this.transformation, str)['flag']
    },
    // 返回表头
    getHeaderLength(str) {
      var arr = this.getObjKey(this.configObj, this.getObjKey(this.transformation, str)['key'])
      return arr ? arr.length : 0
    },
    // 获取指定对象 和指定key的内容
    getObjKey(obj, key) {
      return obj[key]
    },
    // 删除合并后 表格数据的数据
    getHeaderSelect(str, count) {
      var arr = this.getObjKey(this.configObj, this.getObjKey(this.transformation, str)['key'])
      var arrCopy = JSON.parse(JSON.stringify(arr))
      if (count == 0) {
        for (var a = 0; a < arrCopy.length; a++) {
          if (arrCopy[a]['flag']) {
            arrCopy.splice(a, 1)
            a--
          }
        }
      }
      return arrCopy
    },
    // 获取表头
    getHeader(str) {
      return this.getObjKey(this.configObj, this.getObjKey(this.transformation, str)['key'])
    }
  }
}
</script>

<style lang="scss" scoped>
.main-container {
  height: calc(100vh - 200px);
  margin: 0 !important;
  overflow-y: scroll;
  padding: 20px;
  margin-bottom: 50px;

  .whole {
    width: 100%;
    height: 100%;
    border-radius: 5px;
    background-color: #fff !important;

    .page-title {
      height: 50px;
      line-height: 50px;
      color: #606266;
      font-size: 14px;
      padding-left: 25px;
      border-bottom: 1px solid #d8dee7;
    }

    .submit-container {
      display: flex;
      justify-content: center;
      padding-bottom: 20px;
    }

    .el-button--text {
      color: #fff;
      padding: 12px 10px;
    }

    .el-button--text:hover,
    .el-button:hover {
      color: #fff;
      background-color: #3562db;
      border-radius: 0;
      border-color: transparent;
    }
  }
}

@media screen and (max-width: 1366px) {
  .main-container .el-button--text {
    color: #fff;
    padding: 0 15px;
  }
}

.quesM-title {
  height: 50px;
  line-height: 50px;
  color: #606266;
  font-size: 14px;
  padding-left: 25px;
  border-bottom: 1px solid #d8dee7;
}

.tableCss {
  margin-top: 20px;
}

table,
td {
  border: 1px solid #ccc;
  border-collapse: collapse;
  font-weight: normal;
  vertical-align: middle;
  height: 35px;
  text-align: left;
  text-indent: 35px;
  background: white;
}

table,
th {
  border: 1px solid #ccc;
  border-collapse: collapse;
  font-weight: 600;
  height: 40px;
  font-size: 15px;
  vertical-align: middle;
  background: #ddd;
}

.countBox {
  font-size: 20px;
  font-weight: 300;
}

.show-info {
  font-size: 16px;
  font-weight: 300;
  color: red;
}

.progressBar {
  display: flex;
}

.buttonBox {
  display: flex;
  justify-content: flex-end;
  margin: 5px;
}

.buttonStyle {
  margin-left: 5px;
}

.titleColor {
  color: #3562db;
  font-weight: 600;
}
</style>
