<!-- eslint-disable vue/no-mutating-props -->
<template>
  <div>
    <div v-if="!isEdit" class="details">
      <el-row :gutter="24">
        <el-col :span="9">
          <FormTextItem title="位置" :value="formData.folderName" />
        </el-col>
        <el-col :span="15">
          <p style="margin-bottom: 24px; height: 21px"></p>
        </el-col>
        <el-col :span="9">
          <FormTextItem title="所有者部门" :value="formData.archiveOwnerDeptName" />
        </el-col>
        <el-col :span="9">
          <FormTextItem title="所有者" :value="formData.archiveOwnerName" />
        </el-col>
        <el-col :span="9">
          <FormTextItem title="创建时间" :value="moment(formData.createTime).format('YYYY-MM-DD HH:mm:ss')" />
        </el-col>
        <el-col :span="9">
          <FormTextItem title="创建人" :value="formData.createName" />
        </el-col>
        <el-col :span="9">
          <FormTextItem title="最近修改" :value="moment(formData.updateTime).format('YYYY-MM-DD HH:mm:ss')" />
        </el-col>
        <el-col :span="9">
          <FormTextItem title="修改人" :value="formData.updateName" />
        </el-col>
      </el-row>
    </div>
    <div v-else class="form-box">
      <el-form ref="form" :model="formData" label-width="100px" :rules="rules">
        <el-row :gutter="24">
          <el-col :span="9">
            <el-form-item label="位置" prop="folderId">
              <el-cascader v-model="formData.folderId" :options="options"
                :props="{ emitPath: false, checkStrictly: true, children: 'children', label: 'label', value: 'id' }"
                clearable></el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="15">
            <p style="margin-bottom: 24px; height: 38px"></p>
          </el-col>
          <el-col :span="9">
            <el-form-item label="所有者部门" prop="archiveOwnerDeptId">
              <el-cascader v-model="formData.archiveOwnerDeptId" placeholder="请选择所有者部门" :options="deptList"
                collapse-tags :props="{
                  value: 'id',
                  label: 'deptName',
                  checkStrictly: true,
                  emitPath: false
                }" :show-all-levels="false" clearable filterable size="small" style="width: 100%"
                @change="handleSelectDept">
              </el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="所有者" prop="archiveOwnerId">
              <el-select v-model="formData.archiveOwnerId" @change="handleOwnerChange">
                <el-option v-for="item in ownerList" :key="item.id" :value="item.id" placeholder="请选择所有者"
                  :label="item.staffName" clearable></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="创建时间" prop="archiveName">
              <el-date-picker v-model="formData.updateTime" type="datetime" disabled> </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="创建人" prop="archiveName">
              <el-input v-model="formData.createName" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="最近修改" prop="archiveName">
              <el-date-picker v-model="formData.updateTime" type="datetime" disabled> </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="修改人" prop="archiveName">
              <el-input v-model="formData.updateName" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>
<script>
import FormTextItem from '@/views/operationPort/dossierManager/components/FormTextItem.vue'
import moment from 'moment'
import { transData } from '@/util'
export default {
  components: { FormTextItem },
  props: {
    formData: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      moment,
      options: [],
      rules: {
        folderId: [{ required: true, message: '请选择位置', trigger: ['blur', 'change'] }],
        archiveOwnerDeptId: [{ required: true, message: '请选择所有者部门', trigger: ['blur', 'change'] }]
      },
      ownerList: [],
      deptList: []
    }
  },
  watch: {
    'formData.archiveOwnerDeptId': {
      handler(val) {
        if (val)
          this.getLersonnelList(val)
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.handleGetTreeData()
    this.getDeptList()
  },
  methods: {
    handleSelectDept(e) {
      const values = [e]
      // eslint-disable-next-line vue/no-mutating-props
      this.formData.archiveOwnerDeptName = values.length ? this.findNamesByIds(this.deptList, values) : ''
      // eslint-disable-next-line vue/no-mutating-props
      this.formData.archiveOwnerId = ''
      // eslint-disable-next-line vue/no-mutating-props
      this.formData.archiveOwnerName = ''
    },
    /**
     * @description 递归查找树结构中多个 id 匹配项，并返回这些匹配项的 name 属性拼接成的字符串
     * @param {Array} tree 树结构数组
     * @param {Array} targetIds 目标 id 数组
     * @param {String} childrenKey 子节点属性名，默认为 'children'
     * @returns {String} 匹配项的 name 属性拼接成的字符串
     */
    findNamesByIds(tree, targetIds, childrenKey = 'children') {
      const names = []
      function findNames(tree, targetIds) {
        for (let node of tree) {
          if (targetIds.includes(node.id)) {
            names.push(node.deptName)
          }
          if (node[childrenKey] && node[childrenKey].length > 0) {
            findNames(node[childrenKey], targetIds)
          }
        }
      }
      findNames(tree, targetIds)
      return names.join(',')
    },
    handleOwnerChange(e) {
      const item = this.ownerList.find((item) => item.id === e)
      // eslint-disable-next-line vue/no-mutating-props
      this.formData.archiveOwnerName = item ? item.staffName : this.formData.archiveOwnerId
    },
    // 获取部门列表
    getDeptList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == '200') {
          this.deptList = transData(res.data, 'id', 'pid', 'children')
          this.getLersonnelList()
        }
      })
    },
    // 获取人员列表
    getLersonnelList(officeId) {
      let params = {
        current: 1,
        size: 99999,
        officeId
      }
      this.$api.staffList(params).then((res) => {
        if (res.code == 200) {
          this.ownerList = res.data.records
        }
      })
    },
    /**
     * @description 递归删除值为空数组的child属性，避免级联选择器显示后一级空列表
     * @param {Array} optionList 数据源
     * @param {String} childName 需要删除的child属性名 如 chhildren  childList等等
     */
    handleRemoveEmptyChild(optionList, childName) {
      for (let i = 0; i < optionList.length; i++) {
        if (optionList[i][childName].length !== 0) {
          this.handleRemoveEmptyChild(optionList[i][childName], childName)
        } else {
          delete optionList[i][childName]
        }
      }
    },
    handleGetTreeData() {
      this.$api.fileManagement.selectFolderTree({ folderType: '4', isMine: false }).then((res) => {
        this.handleRemoveEmptyChild(res.data, 'children')
        this.options = res.data
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.form-box {
  .el-select,
  .el-date-editor.el-input,
  .el-cascader {
    width: 100%;
  }
}
</style>
