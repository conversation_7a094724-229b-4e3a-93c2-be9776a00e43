<template>
  <div class="main-container">
    <div class="whole">
      <div class="page-title">问卷传播</div>
      <el-row style="border-bottom: 1px solid #d8dee7;">
        <el-col :md="16" :lg="16" :xl="14" style="height: auto;">
          <BreadcrumbNavBar />
        </el-col>
        <el-col :md="8" :lg="8" :xl="10">
          <HeaderButton />
        </el-col>
      </el-row>
      <div class="bottom">
        <div class="bgcTitle">链接二维码</div>
        <div class="addBorder">
          <!-- <div class="everyrow">
            <span class="span">电脑端答题链接:</span>
            <el-input v-model="computerLINK"></el-input>
            <el-button type="primary">复制链接</el-button>
            &nbsp;
            <a href="" style="color: #5482ee">打开链接</a>
          </div> -->
          <div class="everyrow">
            <span class="span">手机端答题链接:</span>
            <el-input id="success_form_input" v-model="phoneLink" readonly></el-input>
            <el-button type="primary" @click="copyLink">复制链接</el-button>
            &nbsp;
            <span class="willJump">请在手机微信端进行访问！</span>
          </div>
          <div class="QRcode">
            <span class="span" style="margin-top: 103px; float: left;">二维码:</span>
            <div id="qrcode" ref="qrCode" style="width: 120px; height: 120px; float: left; margin: 0 10px;" />
            <span class="prePhone">(请使用手机微信扫一扫进行答题)</span>
            <div class="clear"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
// import QRCode from 'qrcodejs2'
import QRCode from 'qrcodejs2-fix';
import BreadcrumbNavBar from '../component/BreadcrumbNavBar/BreadcrumbNavBar.vue'
import HeaderButton from '../questionDesign/component/HeaderButton/HeaderButton.vue'
export default {
  components: {
    BreadcrumbNavBar,
    HeaderButton
  },
  data() {
    return {
      computerLINK: '',
      phoneLink: ''
    }
  },
  created() {
    this.getShareUrl()
  },
  methods: {
    crateQrcode(url) {
      this.$refs.qrCode.innerHTML = ''
      this.$nextTick(() => {
        this.code = new QRCode(this.$refs.qrCode, {
          width: 120,
          height: 120, // 高度
          text: url, // 二维码内容
          background: '#f0f',
          foreground: '#ff0',
          correctLevel: QRCode.CorrectLevel.L
        })
      })
    },
    copyLink() {
      // 获取 要 copy 的input框 js写法
      const input = document.querySelector('#success_form_input')
      // 保存要 copy的值
      input.select()
      // 调用windows 底层copy函数
      if (document.execCommand('copy')) {
        document.execCommand('copy')
      }
    },
    getShareUrl() {
      this.$api.getShareUrl({ questionId: localStorage.getItem('questId') }).then((res) => {
        if (res.status == 200) {
          this.phoneLink = res.data
          this.crateQrcode(res.data)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.main-container {
  height: 100%;
  padding: 15px;
  margin: 0 !important;

  .whole {
    width: 100%;
    height: 100%;
    border-radius: 5px;
    background-color: #fff !important;

    .page-title {
      height: 50px;
      line-height: 50px;
      color: #606266;
      font-size: 14px;
      padding-left: 25px;
      border-bottom: 1px solid #d8dee7;
    }

    .submit-container {
      display: flex;
      justify-content: center;
      padding-bottom: 20px;
    }

    .el-button--text {
      color: #fff;
      padding: 12px 10px;
    }

    .el-button--text:hover,
    .el-button:hover {
      color: #fff;
      background-color: #5188fc;
      border-radius: 0;
      border-color: transparent;
    }
  }
}

@media screen and (max-width: 1366px) {
  .main-container .el-button--text {
    color: #fff;
    padding: 0 15px;
  }
}

.bottom {
  width: 98%;
  height: 70vh;
  overflow: auto;

  /* background-color: pink; */
  margin: 0 auto 20px;
}

.bgcTitle {
  padding: 10px;
  font-size: 14px;
  font-weight: 600;
  background-color: #3562db;
  color: white;
}

.el-input {
  width: 400px;
  margin-left: 10px;
  height: 40px;
}

.span {
  font-size: 14px;
  font-weight: 600;
}

.everyrow {
  padding: 10px;

  .willJump {
    font-size: 16px;
    color: #5188fc;
    font-weight: 600;
  }
  // .willJump:hover {
  //   cursor: pointer;
  //   text-decoration: underline;
  // }
}

.el-checkbox {
  margin-left: 10px;
}

.addBorder {
  border: 1px solid #5188fc;
}

.el-button {
  margin: 0 0 10px 10px;
}

a {
  text-decoration: none;
}

.QRcode {
  padding-left: 65px;
  margin-top: 30px;
}

.clear::after {
  content: ".";
  display: block;
  clear: both;
  height: 0;
  visibility: hidden;
}

.prePhone {
  font-size: 14px;
  margin-top: 103px;
  display: inline-block;
}
</style>
