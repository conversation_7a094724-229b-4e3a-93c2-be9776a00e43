<template>
    <el-dialog v-if="dialogShow" title="选择设备" width="60%" :visible.sync="dialogShow" custom-class="model-dialog"
        :before-close="closeDialog">
        <div class="camera_content" style="padding: 10px 20px 10px 10px;">
            <div class="header_operation">
                <div class="search_box">
                    <div class="search_select">
                        <el-input v-model="assetsData.assetsName" placeholder="设备名称"
                            style="width: 200px;margin-right:10px;"></el-input>
                        <el-select v-model="assetsData.sysOf1" :placeholder="'请选择设备类型'" clearable style="width: 200px">
                            <el-option v-for="(item, index) in deviceTypeList" :key="index"
                                :label="item.dictionaryDetailsName" :value="item.dictionaryDetailsId"></el-option>
                        </el-select>
                    </div>
                </div>
                <div class="header_btn">
                    <el-button type="primary" plain @click="assetsResetting">重置</el-button>
                    <el-button type="primary" @click="searchForm">查询</el-button>
                </div>
            </div>
            <div class="table_div">
                <TablePage ref="multipleAssetsTable" v-loading="tableLoading" row-key="id" tooltip-effect="dark"
                    :tableColumn="tableColumn" :data="tableData" height="300px" :pageData="pageinationData"
                    @pagination="paginationChange" @selection-change="handleSelectionChange">
                </TablePage>
            </div>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button type="primary" plain @click="closeDialog">取 消</el-button>
            <el-button type="primary" @click="groupSubmit">确 定</el-button>
        </span>
    </el-dialog>
</template>
<script lang="jsx">
export default {
    name: '',
    props: {
        dialogShow: {
            type: Boolean,
            default: false
        },
        dialogData: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            assetsData: {
                assetsName: '',//设备名称编码
                sysOf1: '',//设备类型
            },
            deviceTypeList: [],//设备类型
            multipleSelection: [],//选中的数据
            tableColumn: [
                {
                    type: 'selection',
                    align: 'center',
                    width: 80,
                    reserveSelection: true
                },
                {
                    prop: 'assetsName',
                    label: '设备名称'
                },
                {
                    prop: 'assetsCode',
                    label: '设备编码'
                },
                {
                    prop: 'assetsTypeName',
                    label: '设备类型'
                },
            ],
            tableData: [],
            tableLoading: false,
            pageinationData: {
                page: 1,
                pageSize: 15,
                total: 0
            }
        }
    },

    mounted() {
        this.getAssetsListData()
        this.getDeviceType()
    },
    methods: {
        // 设备类型
        getDeviceType() {
            this.deviceTypeList = []
            let data = {
                dictionaryCode: this.dialogData.dictionaryDetailsCode
            }
            this.$api.getQueryRootSubCategoryDetails(data).then((res) => {
                if (res.code == '200') {
                    this.deviceTypeList = res.data
                }
            })
        },
        // 多选
        handleSelectionChange(val) {
            this.multipleSelection = val
        },
        // 查询
        searchForm() {
            this.pageinationData.page = 1
            this.getAssetsListData()
        },
        // 重置
        assetsResetting() {
            Object.assign(this.assetsData, {
                assetsName: '',
                sysOf1: ''
            })
            this.pageinationData = {
                page: 1,
                pageSize: 15,
                total: 0
            }
            this.getAssetsListData()
        },
        // 关联设备（查询）
        getAssetsListData() {
            let data = {
                ...this.assetsData,
                dictionaryDetailsCode: this.dialogData.dictionaryDetailsCode,
                page: this.pageinationData.page,
                pageSize: this.pageinationData.pageSize,
                assetsGroupId: this.dialogData.id, // 选中的id
                equipAttr: this.dialogData.equipAttr
            }
            this.tableData = []
            this.tableLoading = true
            this.$api.getCustomGroupingListGroup(data).then((res) => {
                this.tableLoading = false
                if (res.code == '200') {
                    this.tableData = res.data.records
                    this.pageinationData.total = res.data.total
                    // 选中数据回显
                    const selectedData = this.tableData.filter(item => item.isSelect === 1);
                    selectedData.forEach(row => {
                        this.$refs.multipleAssetsTable.toggleRowSelection(row);
                    });
                }
            })
        },
        paginationChange(pagination) {
            Object.assign(this.pageinationData, pagination)
            this.getAssetsListData()
        },
        closeDialog() {
            this.$emit('closeDialog')
        },
        groupSubmit() {
            if (this.multipleSelection.length) {
                const dataList = {
                    assetsGroupId: this.dialogData.id,
                    groupEquRelatedList: this.multipleSelection.map((item) => ({
                        assetsGroupId: this.dialogData.id,
                        assetsInfoId: item.id
                    }))
                };
                this.$emit('submitDialog', dataList)
            } else {
                this.$message({
                    message: '请选择关联设备！',
                    type: 'warning'
                })
            }
        }
    }
}
</script>
<style lang="scss" scope>
.model-dialog {

    // width: 60%;
    .camera_content {
        width: 100%;
        margin: 0 auto;
        background: #fff;
        border-radius: 4px;
        // display: flex;
        width: 100%;
        height: 100%;

        .header_operation {
            // padding: 24px;
            margin-bottom: 10px;
            display: flex;

            .search_box {
                display: flex;

                >div {
                    margin-right: 16px;
                }

                .search_input {
                    width: 200px;
                }
            }
        }

        .table_div {
            height: calc(100% - 100px);
        }

        .paging_box {
            display: flex;
            justify-content: flex-end;
            padding: 6px;
        }
    }
}
</style>