<!--
 * @Author: hedd
 * @Date: 2023-04-25 14:27:37
 * @LastEditTime: 2024-07-19 10:18:30
 * @FilePath: \ihcrs_pc\src\views\monitor\monitoringConfig\managementConfig\alarmConfiguration.vue
 * @Description:
-->
<template>
  <AlarmConfiguration></AlarmConfiguration>
</template>
<script>
// import { monitorTypeList } from '@/util/dict.js'
export default {
  name: 'alarmConfiguration',
  components: {
    AlarmConfiguration: () => import('./components/alarmConfiguration')
  },
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    next((vm) => {
      if (from.query.activeName) {
        vm.activeName = from.query.activeName
      }
      vm.$store.commit('keepAlive/add', names)
    })
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['alarmConfigForm'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {}
  },
  mounted() {
    // 123
  },
  methods: {
    // 123
  }
}
</script>
<style lang="scss" scoped>
// 123
</style>
