const mockRoutes = [
  {
    path: '/newSystemSettings',
    name: 'newSystemSettings',
    component: 'Layout',
    redirect: '/newSystemSettings/categoryManagement',
    meta: {
      title: '系统设置',
      menuAuth: '/newSystemSettings',
    },
    children: [
      {
        path: 'categoryManagement',
        name: 'categoryManagement',
        component: 'EmptyLayout',
        redirect: { name: 'categoryManagement' },
        meta: {
          title: '品类管理',
          menuAuth: '/newSystemSettings/categoryManagement',
        },
        children: [
          {
            path: '',
            name: 'categoryManagement',
            component: 'categoryManagement',
            meta: {
              title: '品类管理',
              sidebar: false,
              breadcrumb: false,
            },
          },
        ],
      },
      // {
      //   path: 'environmentalInterval',
      //   name: 'environmentalInterval',
      //   component: 'EmptyLayout',
      //   redirect: { name: 'environmentalInterval' },
      //   meta: {
      //     title: '环境区间',
      //     menuAuth: '/newSystemSettings/environmentalInterval',
      //   },
      //   children: [
      //     {
      //       path: '',
      //       name: 'environmentalInterval',
      //       component: 'environmentalInterval',
      //       meta: {
      //         title: '环境区间',
      //         sidebar: false,
      //         breadcrumb: false,
      //       },
      //     },
      //   ],
      // },
      {
        path: 'graphicPlotting',
        name: 'graphicPlotting',
        component: 'EmptyLayout',
        redirect: { name: 'graphicPlotting' },
        meta: {
          title: '新图形绘制',
          menuAuth: '/newSystemSettings/graphicPlotting',
        },
        children: [
          {
            path: '',
            name: 'graphicPlotting',
            component: 'meta2d',
            meta: {
              title: '新图形绘制',
              sidebar: false,
              breadcrumb: false,
            },
          },
          {
            path: 'preview',
            name: 'preview',
            component: 'preview',
            meta: {
              title: '预览',
              sidebar: false,
              activeMenu: '/newSystemSettings/graphicPlotting',
            },
          },
        ],
      },
      {
        path: 'systemIntervalState',
        name: 'systemIntervalState',
        component: 'EmptyLayout',
        redirect: { name: 'systemIntervalState' },
        meta: {
          title: '区间状态',
          menuAuth: '/newSystemSettings/systemIntervalState',
        },
        children: [
          {
            path: '',
            name: 'systemIntervalState',
            component: 'systemIntervalState',
            meta: {
              title: '区间状态',
              sidebar: false,
              breadcrumb: false,
            },
          },
          {
            path: 'addIntervalForm',
            name: 'addIntervalForm',
            component: 'addIntervalForm',
            meta: {
              title: '区间状态配置',
              sidebar: false,
              activeMenu: '/newSystemSettings/addIntervalForm',
            },
          },
        ],
      },
      {
        path: 'systemManagement',
        name: 'systemManagement',
        component: 'EmptyLayout',
        redirect: { name: 'systemManagement' },
        meta: {
          title: '系统管理',
          menuAuth: '/newSystemSettings/systemManagement',
        },
        children: [
          {
            path: '',
            name: 'systemManagement',
            component: 'systemManagement',
            meta: {
              title: '系统管理',
              sidebar: false,
              breadcrumb: false,
            },
          },
        ],
      },
      // 添加其他子路由
    ],
  },
  // 添加其他主路由
];

import Layout from '@/layout';
import EmptyLayout from '@/layout/empty';
import newMonitorApi from '@/api/newMonitorApi';
import _ from 'lodash';
const componentMap = {
  Layout,
  EmptyLayout,
  // 系统设置组件（懒加载）
  categoryManagement: () => import('@/views/NewMonitor/newSystemSettings/categoryManagement.vue'),
  environmentalInterval: () => import('@/views/NewMonitor/newSystemSettings/environmentalInterval.vue'),
  graphicPlotting: () => import('@/views/NewMonitor/newSystemSettings/graphicPlotting.vue'),
  meta2d: () => import('@/views/NewMonitor/newSystemSettings/meta2d.vue'),
  preview: () => import('@/views/NewMonitor/newSystemSettings/preview.vue'),
  systemIntervalState: () => import('@/views/NewMonitor/newSystemSettings/systemIntervalState.vue'),
  addIntervalForm: () => import('@/views/NewMonitor/newSystemSettings/components/addIntervalForm.vue'),
  systemManagement: () => import('@/views/NewMonitor/newSystemSettings/systemManagement.vue'),


  operationalOverview: () => import('@/views/NewMonitor/runningMenu/operationalOverview.vue'),// 运行总览
  operationalMonitoring: () => import('@/views/NewMonitor/runningMenu/operationalMonitoring.vue'),// 运行监测
  deviceDetails: () => import('@/views/NewMonitor/airConditioningTerminal/deviceDetails.vue'),//  运行监测详情
  clusterSetting: () => import('@/views/NewMonitor/airConditioningTerminal/clusterSetting.vue'),// 运行监测配置
  addNewForm: () => import('@/views/NewMonitor/airConditioningTerminal/addNewForm.vue'),// 运行监测新建
  airScenarioStrategy: () => import('@/views/NewMonitor/runningMenu/airScenarioStrategy.vue'),// 场景策略
  airScenarioStrategyDetails: () => import('@/views/NewMonitor/airConditioningTerminal/airScenarioStrategyDetails.vue'),// 场景策略详情
  airRunCalendar: () => import('@/views/NewMonitor/runningMenu/airRunCalendar.vue'),// 运行日历
  calendarDetails: () => import('@/views/NewMonitor/airConditioningTerminal/calendarDetails.vue'),// 运行日历详情
  calendarDetailsEquipment: () => import('@/views/NewMonitor/airConditioningTerminal/calendarDetailsEquipment.vue'),// 运行日历设备详情
  historicalDataSystem: () => import('@/views/NewMonitor/runningMenu/historicalDataSystem.vue'),// 历史数据
  powerStatisticalAnalysis: () => import('@/views/NewMonitor/distributionSystem/powerStatisticalAnalysis.vue'),// 统计分析
  agvStatisticAnalysis: () => import('@/views/NewMonitor/customizedPage/agvStatisticAnalysis.vue'),// 物流监测统计分析
  agvStatisticAnalysisDetails: () => import('@/views/NewMonitor/customizedPage/agvStatisticAnalysisDetails.vue'),// 物流监测统计分析详情
  integratedSupervision: () => import('@/views/NewMonitor/elevatorSystem/integratedSupervision.vue'),// 综合监控
};
/**
 * 递归函数，将 mockRoutes 转换为完整的路由配置
 * @param {Array} routes - mockRoutes 数组
 * @param {string} parentName - 父路由名称，用于生成唯一的子路由名称
 * @returns {Array} - 完整的路由配置
 */
const generateRoutes = (routes, parentName = '') => {
  return routes.map((route) => {
    const {
      path,
      name,
      component,
      redirect,
      meta,
      children,
    } = route;
    // 生成新的路由名称，确保唯一
    const uniqueName = parentName ? `${parentName}_${name}` : name;
    const newRoute = {
      path,
      name: uniqueName,
      redirect,
      meta,
    };
    if (componentMap[component]) {
      newRoute.component = componentMap[component];
    } else {
      console.warn(`Component not found: ${component}`);
    }
    // 递归处理子路由
    if (children && children.length > 0) {
      newRoute.children = generateRoutes(children, uniqueName); // 传递当前路由的唯一名称作为父名称
    }
    return newRoute;
  });
};
export async function fetchRoutes() {
  try {
    const res = await newMonitorApi.getSystemManagementAlllist();
    const newRoutes = [];
    res.data.forEach(item => {
      if (item.routerJson) {
        if (typeof item.routerJson === 'string') {
          item.routerJson = JSON.parse(item.routerJson);
        }
        newRoutes.push(item.routerJson);
      }

    });
    const combinedRoutes = [...newRoutes, ...mockRoutes];
    const routes = generateRoutes(combinedRoutes);
    return routes;
  } catch (error) {
    console.error('获取路由数据失败:', error);
    return generateRoutes(mockRoutes); // 如果失败，返回默认的 mockRoutes
  }
}
export default fetchRoutes;