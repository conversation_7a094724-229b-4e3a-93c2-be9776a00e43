<template>
  <PageContainer :footer="true">
    <div slot="content" class="inner" style="height: 100%">
      <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
        <span class="green_line"></span>
        基本信息
      </div>
      <el-form ref="form" :model="form" :rules="rules" label-width="90px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="单位名称" prop="unitsName">
              <el-input v-if="type != 'view'" v-model="form.unitsName" placeholder="请输入单位名称"></el-input>
              <span v-else>{{ form.unitsName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="编码" prop="unitsCode">
              <el-input v-if="type != 'view'" v-model="form.unitsCode" placeholder="请输入编码"></el-input>
              <span v-else>{{ form.unitsCode }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="开户行">
              <el-input v-if="type != 'view'" v-model="form.depositBank" placeholder="请输入开户行"></el-input>
              <span v-else>{{ form.depositBank }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开户行账号">
              <el-input v-if="type != 'view'" v-model="form.accountNum" placeholder="请输入开户行账号"></el-input>
              <span v-else>{{ form.accountNum }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="所在地区">
              <el-input v-if="type != 'view'" v-model="form.address" placeholder="请输入所在地区"></el-input>
              <span v-else>{{ form.address }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="详细地址">
              <el-input v-if="type != 'view'" v-model="form.detailAddress" placeholder="请输入详细地址"></el-input>
              <span v-else>{{ form.detailAddress }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="联系人">
              <el-input v-if="type != 'view'" v-model="form.contacts" placeholder="请输入联系人"></el-input>
              <span v-else>{{ form.contacts }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系方式">
              <el-input v-if="type != 'view'" v-model="form.contactsPhone" placeholder="请输入联系方式"></el-input>
              <span v-else>{{ form.contactsPhone }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="单位类型" prop="unitsTypeCode">
              <el-select v-if="type != 'view'" v-model="form.unitsTypeCode" placeholder="请选择单位类型" style="width: 100%">
                <el-option v-for="item in typeData" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
              <span v-else>{{ form.unitsTypeName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="状态" required>
              <el-select v-if="type != 'view'" v-model="form.status" placeholder="请选择上级分类" style="width: 100%">
                <el-option label="启用" value="0"></el-option>
                <el-option label="禁用" value="1"></el-option>
              </el-select>
              <span v-else>{{ form.status == '0' ? '启用' : '禁用' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="说明">
              <el-input v-if="type != 'view'" v-model="form.remarks" type="textarea" :rows="2" placeholder="请输入内容"></el-input>
              <span v-else>{{ form.remarks }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">取消</el-button>
      <el-button type="primary" @click="submit">保存</el-button>
    </div>
  </PageContainer>
</template>
<script>
export default {
  components: {},
  data() {
    return {
      form: {
        unitsCode: '',
        unitsName: '',
        unitsTypeCode: '',
        depositBank: '',
        accountNum: '',
        address: '',
        detailAddress: '',
        contacts: '',
        contactsPhone: '',
        remarks: '',
        status: '0'
      },
      typeData: [
        {
          id: '3',
          name: '供应商'
        },
        {
          id: '5',
          name: '生产厂家'
        }
      ],
      rules: {
        unitsName: [{ required: true, message: '请输入单位名称', trigger: 'blur' }],
        unitsCode: [{ required: true, message: '请输入编码', trigger: 'blur' }],
        unitsTypeCode: [{ required: true, message: '请选择上级分类', trigger: 'change' }]
      },
      type: 'add'
    }
  },
  created() {
    this.form.unitsTypeCode = this.$route.query.parentId || ''
    this.type = this.$route.query.type || 'add'
    if (this.type != 'add') {
      this.dataDetail(this.$route.query.id)
    }
  },
  methods: {
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const params = {
            ...this.form,
            unitsTypeName: this.typeData.find((i) => i.id == this.form.unitsTypeCode).name,
            category: '2',
            legalPersonName: '',
            complaintCall: '',
            businessLicenseUrl: '',
            brand: '',
            sort: '',
            unitClasses: '2',
            userName: this.$store.state.user.userInfo.user.staffName,
            userId: this.$store.state.user.userInfo.user.staffId
          }
          if (this.type == 'edit') {
            params.id = this.$route.query.id
            params.nature = '1'
          }
          this.$api.hscAddUnit(params).then((res) => {
            if (res.code == '200') {
              this.$message.success('保存成功！')
              this.$router.go(-1)
            } else {
              this.$message.error(res.message || '保存失败！')
            }
          })
        }
      })
    },
    dataDetail(id) {
      const params = {
        id,
        userName: this.$store.state.user.userInfo.user.staffName,
        userId: this.$store.state.user.userInfo.user.staffId
      }
      this.$api.hscUnitDetail(params).then((res) => {
        if (res.code == '200') {
          delete res.data.brandList
          this.form = res.data
        } else {
          this.$message.error(res.message || '获取失败！')
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.inner {
  background-color: #fff;
  padding: 15px 60px;
}
</style>
