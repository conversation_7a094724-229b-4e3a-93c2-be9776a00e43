<template>
  <div class="card-item">
    <div class="card-empty">
      <header class="header drag_class" :class="{ mover: item.draggable }">
        <div class="legend-title">工作台</div>
      </header>
      <div class="card-content"></div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    item: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {}
  },
  methods: {}
}
</script>
<style lang="scss" scoped>
.card-item {
  height: 100%;
  width: 100%;
  background: #fff;
  border-radius: 10px;
  box-sizing: border-box;
  padding: 16px 10px;

  .card-empty {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  header {
    align-items: center;
    height: 40px;
    line-height: 40px;
  }

  .mover {
    cursor: move;
  }

  .card-content {
    display: flex;
    flex-wrap: wrap;
    padding: 10px 15px;
    height: calc(100% - 40px);
    width: 100%;
    overflow: auto;
  }
}
</style>
