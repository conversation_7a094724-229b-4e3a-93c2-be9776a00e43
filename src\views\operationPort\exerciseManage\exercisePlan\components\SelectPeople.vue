<template>
  <el-dialog title="请选择人员" width="48%" :visible.sync="personDialogShow" custom-class="model-dialog"
    :before-close="closeDialog">
    <div class="content">
      <div class="leftContent">
        <div class="topSearch">
          <el-input v-model="filterText" placeholder="请输入关键字" clearable suffix-icon="el-icon-search"> </el-input>
        </div>
        <div class="treeBox">
          <el-tree ref="tree" v-loading="treeLoading" :check-strictly="true" :data="treeData" :props="defaultProps"
            style="margin-top: 10px" :filter-node-method="filterNode" :highlight-current="true"
            :default-expanded-keys="expanded" @node-click="handleNodeClick"></el-tree>
        </div>
      </div>
      <div class="rightcontent">
        <div class="search-box">
          <el-input v-model="searchForm.staffName" clearable filterable placeholder="请输入姓名"></el-input>
          <el-input v-model="searchForm.mobile" clearable filterable placeholder="请输入手机号"></el-input>
          <div class="ml-16">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button type="primary" plain @click="reset">重置</el-button>
          </div>
        </div>
        <div class="sino_table">
          <el-table ref="sinoTable" v-loading="tableLoading" :data="tableData" :row-key="getRowKeys" height="300px"
            stripe border @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center" :reserve-selection="true"></el-table-column>
            <el-table-column v-for="(column, index) in tableColumn" :key="index" :prop="column.prop"
              :label="column.label" :min-width="column.minWidth" show-overflow-tooltip>
              <template slot-scope="scope">
                <span>{{ scope.row[column.prop] }}</span>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination :current-page="pagination.current" :page-sizes="[15, 30, 50, 100]" :page-size="pagination.size"
            :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
            @current-change="handleCurrentChange"></el-pagination>
        </div>
      </div>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="submitDialog('exportForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'SelectPeople',
  components: {},
  props: {
    personDialogShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      filterText: '',
      treeData: [],
      treeLoading: true,
      expanded: [],
      tableLoading: false,
      unitTypeList: [],
      tableHeight: '',
      tableData: [],
      multipleSelection: [],
      radioObj: {},
      // -----------------------------Pagination
      pagination: {
        current: 1,
        size: 15
      }, // 分页数据
      total: 0, // 数据总条数
      selectList: [],
      allSpaceTreeData: [],
      defaultProps: {
        label: 'unitComName', children: 'children'
      },
      tableColumn: [
        {
          prop: 'staffName',
          label: '姓名'
        },
        {
          prop: 'officeName',
          label: '所属科室/部门'
        },
        {
          prop: 'mobile',
          label: '手机号'
        }
      ],
      searchForm: {
        staffName: '',
        mobile: ''
      }
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    search() {
      this.getPersonListFn()
    },
    reset() {
      this.searchForm = {
        staffName: '',
        mobile: ''
      }
      this.getPersonListFn()
    },
    getRowKeys(row) {
      return row.id
    },
    getTableData() {
      this.getUnitTreeFn()
    },

    // 单位列表tree 树
    getUnitTreeFn() {
      this.treeLoading = true
      this.$api.getUnitList({}).then((res) => {
        this.treeLoading = false
        if (res.code == 200) {
          this.treeData = res.data ? res.data : []
          this.getPersonListFn()
        }
      })
    },
    // 人员列表
    getPersonListFn() {
      this.tableLoading = true
      this.$api
        .staffListByPage({
          ...this.pagination,
          ...this.searchForm,
          pmId: this.checkedData ? this.checkedData.umId : ''
        })
        .then((res) => {
          if (res.code == 200) {
            this.tableLoading = false
            this.tableData = res.data.records
            this.total = res.data.total
          }
        })
    },

    // 过滤
    filterNode(value, data) {
      if (!value) return true
      return data.unitComName.indexOf(value) !== -1
    },
    // 树状图点击
    handleNodeClick(data) {
      this.pagination.current = 1
      this.checkedData = data
      this.getPersonListFn()
    },
    //  ----------------------------------------------Tree_Fn

    // ---------------------------------------------------------- TabelFn
    handleSizeChange(val) {
      this.pagination.current = 1
      this.pagination.size = val
      this.getPersonListFn()
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.getPersonListFn()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
      this.selectList = JSON.parse(JSON.stringify(this.multipleSelection))
    },
    closeDialog() {
      this.$emit('closePersonDialog')
    },
    submitDialog() {
      this.$emit('submitPersonDialog', this.selectList)
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  .content {
    margin: 0 auto;
    background: #fff;
    border-radius: 4px;
    display: flex;
    width: 100%;
    height: 100%;

    .leftContent {
      width: 260px;
      .topSearch {
        padding: 0px 10px;
      }
      .treeBox {
        height: 300px;
        width: 100%;
        overflow-x: hidden;
        overflow-y: scroll;
      }
    }
    .rightcontent {
      margin-left: 20px;
    }
  }
  .search-box {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .el-input {
      width: 180px;
      margin-right: 16px;
    }
  }
  .el-pagination {
    margin-top: 10px;
    width: 100%;
  }
}
</style>

