/*
 * @Author: hedd
 * @Date: 2023-02-24 13:48:57
 * @LastEditTime: 2024-04-30 11:05:25
 * @FilePath: \ihcrs_pc\vitePlugins\index.js
 * @Description:
 */
// import requireTransform from 'vite-plugin-require-transform'
import vueJsx from '@vitejs/plugin-vue2-jsx'
import vue from '@vitejs/plugin-vue2'
// import legacy from '@vitejs/plugin-legacy'
import OptimizationPersist from 'vite-plugin-optimize-persist'
import PkgConfig from 'vite-plugin-package-config'
import viteCompression from 'vite-plugin-compression'
// import topLevelAwait from 'vite-plugin-top-level-await'
import createHtml from './html'
import createSvgIcon from './svg-icon'
import fileList from './fileList'
import buildTimestampPlugin from './recordTime'

export default function createVitePlugins(viteEnv, isBuild = false) {
  const vitePlugins = [
    vueJsx(),
    vue(),
    PkgConfig(),
    viteCompression({
      verbose: true,
      disable: false,
      threshold: 10240,
      algorithm: 'gzip',
      ext: '.gz'
    }),
    OptimizationPersist(),
    fileList(),
    buildTimestampPlugin()
    // legacy({
    //   targets: ['defaults', 'not IE 11']
    // })
    // topLevelAwait({
    //   promiseExportName: '__tla',
    //   promiseImportName: (i) => `__tla_${i}`
    // })
    // requireTransform({
    //   fileRegex: /.js$|.jsx$ | .vue$/
    // })
  ]
  vitePlugins.push(createHtml(viteEnv, isBuild))
  vitePlugins.push(createSvgIcon(isBuild))
  return vitePlugins
}
