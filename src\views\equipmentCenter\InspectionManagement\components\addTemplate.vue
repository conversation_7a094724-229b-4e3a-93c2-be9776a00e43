<template>
  <PageContainer v-loading="blockLoading" :footer="true">
    <div slot="content" class="table-content">
      <div class="content_box">
        <el-form ref="formInline" :model="formInline" :inline="true" class="form-inline" label-width="100px" :rules="rules">
          <el-form-item label="模板名称" prop="taskBookName">
            <el-input
              v-model.trim="formInline.taskBookName"
              type="textarea"
              :readonly="readonly"
              placeholder="请填写模板名称"
              show-word-limit
              :maxlength="50"
              style="width: 500px"
              class="width_lengthen"
            ></el-input>
          </el-form-item>
          <el-form-item v-show="activeName == 'moban'" label="模板分类" prop="taskBookSort">
            <el-select ref="changeSelect" v-model.trim="formInline.taskBookSort" filterable placeholder="模板类型" @change="handleNodeClick($event, 'changeSelect')">
              <el-option v-for="item in taskBookSortArr" :key="item.id" :label="item.dictName" :value="item.id"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-show="activeName == 'shebei'" label="模板分类" prop="taskBookSort">
            <el-cascader v-model="formInline.taskBookSort" :options="taskBookSortArr" :props="cascaderProps" clearable @change="hangdleChange"> </el-cascader>
          </el-form-item>
          <br />
          <el-form-item label="模板类型" prop="taskBookType" @change="toggleBookType">
            <el-radio-group v-model.trim="formInline.taskBookType">
              <el-radio v-for="item in taskBookTypeArr" :key="item.id" :label="item.id">{{ item.dictName }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <br />
          <el-form-item label="模板说明">
            <el-input
              v-model.trim="formInline.projectExplain"
              :readonly="readonly"
              style="width: 500px"
              type="textarea"
              class="project-textarea"
              placeholder="请输入模板说明，最多200字"
              show-word-limit
              :autosize="{ minRows: 4, maxRows: 8 }"
              maxlength="200"
            ></el-input>
          </el-form-item>
        </el-form>
        <template>
          <div style="margin-bottom: 20px">
            <div class="content-table">
              <div class="table-title">
                <span class="title"> <i></i>模板内容</span>
                <span class="line"></span>
              </div>
              <div v-if="formInline.taskBookType == '1'" class="inspection-content">
                <div v-for="(item, index) in maintainProjectdetails" :key="index" class="content-block">
                  <div class="content-line"></div>
                  <div class="porject-name">
                    <div class="porject porject-index">
                      <span class="index-icon">
                        <img :src="icon" alt />
                      </span>
                      <span class="index-text">{{ index + 1 }}</span>
                    </div>
                    <div class="porject porject-input">
                      {{ systemType == '2' ? '保养' : systemType == '5' ? '年检' : '巡检' }}项目：
                      <el-input
                        v-model.trim="item.detailName"
                        maxlength="200"
                        :autosize="{ minRows: 1, maxRows: 8 }"
                        type="textarea"
                        :readonly="readonly"
                        :placeholder="`请填写${systemType == '2' ? '保养' : systemType == '5' ? '年检' : '巡检'}项目`"
                        show-word-limit
                        class="word-limit-input"
                        auto-complete="off"
                      ></el-input>
                    </div>
                    <div v-if="!readonly" class="porject porject-button">
                      <el-button class="sino-button-sure" @click="addProject">添加项目</el-button>
                      <el-button v-if="index != 0" class="sino-button-sure" @click="deteleRow(maintainProjectdetails, index)">删除</el-button>
                    </div>
                  </div>
                  <div class="content-block">
                    <div v-for="(e, i) in item.maintainProjectdetailsTermList" :key="i" class="termContent">
                      <div class="termContent-input">
                        {{ systemType == '2' ? '保养' : systemType == '5' ? '年检' : '巡检' }}选项：
                        <el-select v-model="e.isNum" :disabled="readonly" placeholder="请选择">
                          <el-option v-for="item in termTypeOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
                        </el-select>
                      </div>
                      <div class="termContent-input">
                        <span>{{ systemType == '2' ? '保养' : systemType == '5' ? '年检' : '巡检' }}要点：</span>
                        <el-input
                          v-model.trim="e.content"
                          maxlength="200"
                          :autosize="{ minRows: 1, maxRows: 8 }"
                          type="textarea"
                          :readonly="readonly"
                          :placeholder="`请填写${systemType == '2' ? '保养' : systemType == '5' ? '年检' : '巡检'}要点说明`"
                          show-word-limit
                          style="width: 400px"
                          class="word-limit-input"
                        ></el-input>
                      </div>
                      <div v-if="!readonly" class="termContent-input">
                        <span class="termContent-button button-add" @click="addrow(item.maintainProjectdetailsTermList)">
                          <i class="el-icon-plus"></i>
                          <span>添加行</span>
                        </span>
                        <span v-if="i != 0" class="termContent-button button-detele" @click="deteleRow(item.maintainProjectdetailsTermList, i)">
                          <i class="el-icon-delete"></i>
                          <span>删除</span>
                        </span>
                      </div>
                      <template>
                        <!-- 数值填空 -->
                        <div v-if="e.isNum == '0'" class="termContent-tools tools-number">
                          <div class="termContent-number">
                            正常范围：
                            <el-input
                              v-model.trim="e.rangeStart"
                              style="width: 180px"
                              maxlength="15"
                              :readonly="readonly"
                              onkeyup="value=value.replace(/[^\w\.\/]/ig,'')"
                            ></el-input>
                            <span style="padding: 0 10px">—</span>
                            <el-input
                              v-model.trim="e.rangeEnd"
                              maxlength="15"
                              onkeyup="value=value.replace(/[^\w\.\/]/ig,'')"
                              :readonly="readonly"
                              style="margin: 0; width: 180px"
                            ></el-input>
                          </div>
                          <div class="termContent-number">
                            单位：
                            <el-select v-model="e.einheitCode" :disabled="readonly" placeholder="请选择" @change="selectUnit(e, e.einheitCode)">
                              <el-option v-for="item in unitOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
                            </el-select>
                          </div>
                        </div>
                        <!-- 巡检选项选项为单选  isNum:3 -->
                        <div v-if="e.isNum == '3'" class="termContent-tools tools-radio">
                          <el-table stripe :data="e.termJson" :cell-style="{ padding: '8px' }" style="width: 718px">
                            <el-table-column prop="contText" label="选项文字" width="450">
                              <template slot-scope="scope">
                                <div class="radio-text">
                                  <el-input
                                    v-model.trim="scope.row.contText"
                                    show-word-limit
                                    :autosize="{ minRows: 1, maxRows: 8 }"
                                    type="textarea"
                                    :maxlength="50"
                                    :readonly="readonly"
                                    style="width: 300px; display: inline-block; margin-right: 15px"
                                  ></el-input>
                                  <span v-if="!readonly" style="margin-right: 15px" :class="['sion-icon', 'el-icon-plus']" @click="addTabelRow(e)"></span>
                                  <span
                                    v-if="!readonly && scope.$index != 0"
                                    style="color: #fc2c61"
                                    :class="['sion-icon', 'el-icon-delete', scope.$index == 0 ? 'icon-disabled' : '']"
                                    @click="radioDeteleRow(e.termJson, scope.$index)"
                                  ></span>
                                </div>
                              </template>
                            </el-table-column>
                            <el-table-column label="默认" align="center">
                              <template slot="header">
                                <span class="iconfont icon-xuanzhong">默认</span>
                                <el-link type="primary" @click="clearDefault(e, item.id)">（清除）</el-link>
                              </template>
                              <template slot-scope="scope">
                                <el-radio v-model="scope.row.isDefault" label="0" :disabled="readonly" @change.native="selectRadio(scope.row, scope.$index, e)">&nbsp;</el-radio>
                              </template>
                            </el-table-column>
                            <el-table-column v-if="!readonly" label="上移 下移" width="100">
                              <template slot-scope="scope">
                                <span :class="['sion-icon', 'el-icon-top', scope.$index == 0 ? 'icon-disabled' : '']" @click="upRow(scope.row, scope.$index, e.termJson)"></span>
                                <span
                                  :class="['sion-icon', 'el-icon-bottom', scope.$index == e.termJson.length - 1 ? 'icon-disabled' : '']"
                                  @click="downRow(scope.row, scope.$index, e.termJson)"
                                ></span>
                              </template>
                            </el-table-column>
                          </el-table>
                        </div>
                      </template>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else class="inspection-content">
                <div class="content-block">
                  <el-button class="sino-button-sure" @click="addNewRow">新增一项</el-button>
                  <el-table ref="materialTable" :data="tableData" border style="width: 90%; overflow: auto; margin-top: 15px" :cell-style="{ padding: '6px' }">
                    <el-table-column label="序号" width="60" align="center">
                      <template slot-scope="scope">
                        <span>{{ scope.$index + 1 }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column :label="systemType == '2' ? '保养内容' : systemType == '5' ? '年检内容' : '巡检内容'" align="center">
                      <template slot-scope="scope">
                        <el-input
                          v-model.trim="scope.row.content"
                          show-word-limit
                          :autosize="{ minRows: 1, maxRows: 6 }"
                          type="textarea"
                          :placeholder="`请填写${systemType == '2' ? '保养' : systemType == '5' ? '年检' : '巡检'}内容`"
                          :maxlength="50"
                          :readonly="readonly"
                        >
                        </el-input>
                      </template>
                    </el-table-column>
                    <el-table-column label="标准要求" align="center">
                      <template slot-scope="scope">
                        <el-input
                          v-model.trim="scope.row.standardRequirements"
                          show-word-limit
                          :autosize="{ minRows: 1, maxRows: 8 }"
                          type="textarea"
                          placeholder="请填写标准要求"
                          :maxlength="50"
                          :readonly="readonly"
                        >
                        </el-input>
                      </template>
                    </el-table-column>
                    <el-table-column :label="systemType == '2' ? '保养依据' : systemType == '5' ? '年检依据' : '巡检依据'" align="center">
                      <template slot-scope="scope">
                        <el-input
                          v-model.trim="scope.row.inspectionBasis"
                          show-word-limit
                          :autosize="{ minRows: 1, maxRows: 8 }"
                          type="textarea"
                          :placeholder="`请填写${systemType == '2' ? '保养' : systemType == '5' ? '年检' : '巡检'}依据`"
                          :maxlength="50"
                          :readonly="readonly"
                        >
                        </el-input>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="100" align="center">
                      <template v-if="scope.$index !== 0" slot-scope="scope">
                        <el-button type="text" size="small" @click="deleteNewRow(scope.$index, scope.row)"> 删除 </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">关闭</el-button>
      <el-button type="primary" @click="submitForm()">保存</el-button>
    </div>
  </PageContainer>
</template>
<script>
import { treeToList } from '@/util'
import icon from '@/assets/images/inspectionCenter/ic-document.png'
export default {
  name: 'maintenanceAddTemplate',
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      const typeList = {
        add: '新增模板',
        edit: '编辑模板',
        copy: '复制模板'
      }
      to.meta.title = typeList[to.query.type] ?? '新增模板'
    }
    next()
  },
  async beforeRouteLeave(to, from, next) {
    await this.$store.commit('keepAlive/clean')
    next()
  },
  data() {
    return {
      icon,
      systemType: '', // 系统标识,
      activeName: '',
      title: '',
      taskBookSortArr: [],
      baseName: '', // 选择设备
      defaultProps: {
        label: 'baseName',
        children: 'children'
      },
      taskBookTypeArr: [
        {
          id: '0',
          dictName: '日常巡检'
        },
        {
          id: '1',
          dictName: '专业巡检'
        }
      ],
      cascaderProps: {
        label: 'baseName',
        value: 'id',
        children: 'children',
        checkStrictly: true
      },
      tableData: [
        {
          index: 0,
          content: '',
          standardRequirements: '',
          inspectionBasis: ''
        }
      ],
      pageOrigin: '', // 保存页面来源，是日常保养，还是日常巡检
      readonly: false,
      formInline: {
        taskBookName: '',
        taskBookSort: '', // 模板分类/设备类别
        taskBookType: '0', // 默认日常巡检
        projectExplain: '',
        id: '' // 模板id，修改必传
      },
      typeList: [],
      workTypeCodeList: [],
      maintainProjectdetails: [
        {
          detailName: '', // 项目名称
          id: 0,
          maintainProjectdetailsTermList: [
            {
              isNum: '1', // 巡检选项
              content: '', // 巡检要点
              id: 0,
              rangeStart: '', // 数值范围
              rangeEnd: '',
              einheitCode: '', // 单位code
              einheitName: '', // 单位名称
              termJson: [{ contText: '', isDefault: '0' }] // 单选选项文字
            }
          ]
        }
      ],
      unitOptions: [],
      termTypeOptions: [],
      query: {},
      id: '',
      blockLoading: false,
      typeArr: [],
      typeTreeData: [],
      propsType: {
        children: 'children',
        label: 'typeName',
        value: 'typeId',
        checkStrictly: true
      },
      rules: {
        taskBookName: [{ required: true, message: '请输入模板名称', trigger: 'change' }],
        taskBookType: [{ required: true, message: '请选择模板类型', trigger: 'change' }],
        taskBookSort: [{ required: true, message: '请输入模板分类', trigger: 'change' }]
      },
      total: 0,
      parentName: '',
      equipmentTypeName: '',
      taskBookSortAll: [] // 所有设备类别
    }
  },
  created() {
    const routerNameList = ['templateManagement', 'maitemplateManagement', 'comInstemplateManagement']
    if (!this.$store.state.keepAlive.list.some((e) => routerNameList.includes(e))) {
      this.initEvent()
    }
  },
  activated() {
    this.initEvent()
  },
  mounted() {},
  methods: {
    initEvent() {
      // 重置form表单
      const data = this.$options.data()
      delete data.rules
      this.$nextTick(() => {
        Object.assign(this.$data, data)
        this.$refs.formInline.resetFields()
        this.systemType = this.$route.meta.type
        this.query = this.$route.query
        this.activeName = this.$route.query.activeName
        if (this.systemType == '2') {
          this.taskBookTypeArr = [
            {
              id: '0',
              dictName: '日常保养'
            },
            {
              id: '1',
              dictName: '专业保养'
            }
          ]
        } else if (this.systemType == '5') {
          // 年检
          this.taskBookTypeArr = [
            {
              id: '0',
              dictName: '日常年检'
            },
            {
              id: '1',
              dictName: '专业年检'
            }
          ]
        } else {
          this.taskBookTypeArr = [
            {
              id: '0',
              dictName: '日常巡检'
            },
            {
              id: '1',
              dictName: '专业巡检'
            }
          ]
        }
        if (this.activeName == 'moban') {
          this.getTemTree()
        } else {
          this.getEquTree()
        }
        this.title = this.query.type == 'add' ? '新增模板管理' : this.query.type == 'copy' ? '复制模板管理' : '修改模板管理'
        if (this.query.type == 'edit') {
          this._getTaskBookDetails() // 查询详情
          this.formInline.id = this.query.id
        }
        // 任务书类型、巡检选项、单位
        this._getDictValue('inspection_options') // 巡检选项
        this._getDictValue('engineering_unit') // 单位
        if (this.query.id) {
          // 修改，详情，获取模板详情
          this._getTaskBookDetails()
        }
      })
    },
    findTreeId(data) {
      const prentIds = []
      prentIds.unshift(data.id)
      if (data.levelType == '3') {
        this.taskBookSortAll.find((i) => {
          if (i.id == data.parentId) {
            prentIds.unshift(i.id)
            this.taskBookSortAll.find((j) => {
              if (j.id == i.parentId) {
                prentIds.unshift(j.id)
              }
            })
          }
        })
      } else if (data.levelType == '2') {
        this.taskBookSortArr.find((i) => {
          if (i.id == data.parentId) {
            prentIds.unshift(i.id)
          }
        })
      }
      return prentIds
    },
    findTreeName(data) {
      const prentNames = []
      prentNames.unshift(data.baseName)
      if (data.levelType == '3') {
        this.taskBookSortAll.find((i) => {
          if (i.id == data.parentId) {
            prentNames.unshift(i.baseName)
            this.taskBookSortAll.find((j) => {
              if (j.id == i.parentId) {
                prentNames.unshift(j.baseName)
              }
            })
          }
        })
      } else if (data.levelType == '2') {
        this.taskBookSortArr.find((i) => {
          if (i.id == data.parentId) {
            prentNames.unshift(i.baseName)
          }
        })
      }
      return prentNames
    },
    hangdleChange(type) {
      const names = []
      type.forEach((i) => {
        this.taskBookSortAll.find((j) => {
          if (i == j.id) {
            names.push(j.baseName)
          }
        })
      })
      this.baseName = names.join(',')
    },
    // 获取模板树形结构
    getTemTree() {
      // 危化品
      if (this.systemType == '4') {
        this.taskBookSortArr = [
          {
            id: '4',
            dictName: '日常巡检'
          },
          {
            id: '5',
            dictName: '管理部门安全巡查'
          }
        ]
      } else {
        // moduleDifferentiation 1.设施设备巡检 2.设施保养 3.综合巡检 4.危化品安全巡查
        this.$api.getTemTree({ moduleDifferentiation: this.systemType }).then((res) => {
          if (res.code == 200) {
            this.taskBookSortArr = res.data
            if (this.query.type == 'add') {
              this.formInline.taskBookSort = this.query.checkedData.id
              this.baseName = this.query.checkedData.dictName
            }
          } else {
            this.$message.error(res.message)
          }
        })
      }
    },
    // 获取设备树形结构
    getEquTree() {
      this.$api.getEquTree({ systemIdentificationClassification: this.systemType }).then((res) => {
        if (res.code == 200) {
          this.taskBookSortArr = res.data
          this.taskBookSortAll = treeToList(res.data)
          if (this.query.type == 'add') {
            this.formInline.taskBookSort = this.findTreeId(this.query.checkedData)
            this.baseName = this.findTreeName(this.query.checkedData).join(',')
          }
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 选择下拉树 数据
    handleNodeClick(data, changeData) {
      if (changeData == 'changeSelect') {
        var objVal = {}
        this.taskBookSortArr.forEach((val) => {
          if (val.id == data) {
            objVal = val
          }
        })
        this.formInline.taskBookSort = objVal.id
        this.baseName = objVal.dictName
      } else {
        this.formInline.taskBookSort = data.id
        this.baseName = data.baseName
      }
      this.$refs[changeData].blur()
    },
    // 切换任务数类型
    toggleBookType() {
      if (this.formInline.taskBookType == 1 && this.equipmentTypeName == '日常巡检') {
        this.maintainProjectdetails.forEach((i, index) => {
          const item = {
            isNum: '2',
            content: '',
            id: '',
            rangeStart: '',
            rangeEnd: '',
            einheitCode: '',
            termJson: [{ contText: '', isDefault: '0' }]
          }
          this.maintainProjectdetails[index].maintainProjectdetailsTermList = [item]
        })
      }
    },
    // 获取巡检选项/单位
    _getDictValue(types) {
      this.$api.getDictValueList({ dictType: types }).then((res) => {
        if (types == 'inspection_options') {
          this.termTypeOptions = res.data
          if (this.systemType === '5') {
            this.termTypeOptions.push({ dictLabel: '上传文件', dictValue: '4' })
          }
        } else {
          this.unitOptions = res.data
        }
      })
    },
    // 点击单选按钮
    selectRadio(row, index, val) {
      for (var i = 0; i < val.termJson.length; i++) {
        if (index == i) {
          val.termJson[i].isDefault = '0'
        } else {
          val.termJson[i].isDefault = '1'
        }
      }
    },
    // 选择单位
    selectUnit(i, j) {
      i.einheitName = this.unitOptions.find((item) => {
        return j == item.dictValue
      }).dictLabel
    },
    // 点击确定
    submitForm() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          this.tableData.forEach((i, index) => {
            const item = {
              isNum: '2',
              content: i.content,
              id: '',
              rangeStart: '',
              rangeEnd: '',
              einheitCode: '',
              termJson: [{ contText: '', isDefault: '0' }]
            }
            this.tableData[index].maintainProjectdetailsTermList = [item]
          })
          let isEmpty = this.checkEmpty(this.maintainProjectdetails)
          this.blockLoading = true
          let userInfo = this.$store.state.user.userInfo.user
          let data = {
            staffId: userInfo.staffId,
            userName: userInfo.staffName,
            deptCode: userInfo.deptId,
            deptName: userInfo.deptName,
            // companyCode: obj.companyCode,
            // userType: obj.type,
            systemIdentificationClassification: this.systemType, // 1（设施设备巡检）2（设备保养）3（综合巡检）4.危化品安全巡查
            dictTypeId: this.formInline.taskBookSort instanceof Array ? this.formInline.taskBookSort.join(',') : this.formInline.taskBookSort,
            dictTypeName: this.baseName,
            projectName: this.formInline.taskBookName,
            equipmentTypeId: this.formInline.taskBookType,
            equipmentTypeName: this.taskBookTypeArr.find((item) => {
              return item.id == this.formInline.taskBookType
            })?.dictName,
            projectExplain: this.formInline.projectExplain,
            maintainProjectdetails: this.formInline.taskBookType == '1' ? JSON.stringify(this.maintainProjectdetails) : JSON.stringify(this.tableData),
            // maintainProjectdetails: JSON.stringify(this.maintainProjectdetails),
            id: this.formInline.id
          }
          let header = {}
          if (this.formInline.id) {
            header = {
              'operation-type': 2,
              'operation-id': this.formInline.id,
              'operation-name': this.formInline.taskBookName
            }
          } else {
            header = {
              'operation-type': 1
            }
          }
          this.$api.addTaskBookProject(data, header).then((res) => {
            if (res.code == 200) {
              this.$message.success(res.message)
              this.$router.go(-1)
            } else {
              this.$message.error(res.message)
            }
            this.blockLoading = false
          })
        }
      })
    },
    // 查询详情
    _getTaskBookDetails() {
      let userInfo = this.$store.state.user.userInfo.user
      let data = {
        staffId: userInfo.staffId,
        userName: userInfo.staffName,
        id: this.query.id
      }
      this.$api.getTaskBookDetails(data).then((res) => {
        const { code, data, message } = res
        if (code == 200) {
          this.tableData = data.maintainProjectdetails.map((item, index) => {
            const children = {}
            children.index = index + 1
            children.content = item.detailName
            children.standardRequirements = item.standardRequirements
            children.inspectionBasis = item.inspectionBasis
            return children
          })
          this.maintainProjectdetails = data.maintainProjectdetails
          this.formInline.taskBookName = this.query.type == 'copy' ? data.projectName + '副本' : data.projectName
          this.formInline.taskBookType = data.equipmentTypeId
          this.formInline.taskBookSort = data.dictTypeId
          this.baseName = data.dictTypeName
          this.formInline.projectExplain = data.projectExplain
          this.equipmentTypeName = data.equipmentTypeName
          if (this.activeName == 'moban') {
            this.formInline.taskBookSort = data.dictTypeId
            this.baseName = data.dictTypeName
          } else {
            this.formInline.taskBookSort = data.dictTypeId.split(',')
            this.baseName = data.dictTypeName
          }
        }
      })
    },
    // 点击添加行
    addrow(arr) {
      arr.push({
        id: arr.length,
        isNum: '1',
        content: '',
        rangeStart: '',
        rangeEnd: '',
        einheitCode: '',
        termJson: [{ contText: '', isDefault: '0' }]
      })
    },
    // 专业巡检点击添加项目
    addProject() {
      this.maintainProjectdetails.push({
        detailName: '',
        id: this.maintainProjectdetails.length,
        maintainProjectdetailsTermList: [
          {
            isNum: '1',
            content: '',
            id: '',
            rangeStart: '',
            rangeEnd: '',
            einheitCode: '',
            termJson: [{ contText: '', isDefault: '0' }]
          }
        ]
      })
    },
    // 日常巡检新增一行
    addNewRow() {
      this.tableData.push({
        index: this.tableData.length,
        content: '',
        standardRequirements: '',
        inspectionBasis: ''
      })
    },
    // 日常巡检删除行
    deleteNewRow(index, row) {
      if (index == 0) return
      this.tableData.splice(index, 1)
    },
    // 删除按钮行
    radioDeteleRow(arr, index) {
      // 第一个不可删除
      if (index == 0) return
      // 如果删除的选项为默认选中项 0 把第一个改为默认选中
      if (arr[index].isDefault == '0') {
        arr[0].isDefault = '0'
        arr.splice(index, 1)
      } else {
        arr.splice(index, 1)
      }
    },
    // 删除
    deteleRow(arr, index) {
      if (index == 0) return
      arr.splice(index, 1)
    },
    // 添加单选按钮行
    addTabelRow(option) {
      option.termJson.push({ contText: '', isDefault: '1' })
    },
    // 清除单选默认
    clearDefault(option, parentId) {
      this.maintainProjectdetails.forEach((e) => {
        if (e.id == parentId) {
          let index = e.maintainProjectdetailsTermList.findIndex((item) => item.id == option.id)
          if (index != -1) {
            let arr = JSON.parse(JSON.stringify(e.maintainProjectdetailsTermList[index].termJson))
            arr.forEach((el) => {
              el.isDefault = '1'
            })
            this.$set(e.maintainProjectdetailsTermList[index], 'termJson', arr)
          }
        }
      })
    },
    upRow(row, index, arr) {
      if (index == 0) return
      arr.splice(index - 1, 1, ...arr.splice(index, 1, arr[index - 1]))
    },
    downRow(row, index, arr) {
      if (index == arr.length - 1) return
      arr.splice(index + 1, 1, ...arr.splice(index, 1, arr[index + 1]))
    },
    checkEmpty(arr) {}
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-textarea .el-input__count {
  line-height: normal;
}
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  padding: 10px;
  overflow-y: auto;
}
::v-deep .el-textarea__inner {
  height: 35px;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  appearance: none !important;
  margin: 0;
}
input[type='number'] {
  appearance: textfield;
}
.content_box {
  height: calc(100% - 30px);
  margin-top: 10px;
  padding: 20px 25px 25px;
  background: #fff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}
.project-textarea {
  ::v-deep .el-textarea__inner {
    height: 96px !important;
    max-height: 96px !important;
    overflow-y: auto !important;
  }
}
.width_lengthen {
  width: 766px;
  /* padding-right: 45px; */
}
.el-textarea ::v-deep .el-input__count {
  // bottom: -6px !important;
}
.project-textarea textarea {
  height: 120px;
}
.sion-icon {
  cursor: pointer;
  padding-left: 3px;
  color: #5188fc;
}
.icon-disabled {
  cursor: not-allowed;
}
.form-inline {
  .el-input,
  .el-select,
  .el-cascader {
    width: 300px;
  }
}
.inspection-content {
  width: 100%;
  padding-left: 42px;
  font-size: 14px;
  font-weight: 400;
  color: rgb(96 98 102 / 100%);
  .content-block {
    margin: 20px 0;
    .porject-name {
      .porject {
        display: inline-block;
      }
      .porject-index {
        margin-right: 10px;
        .index-icon {
          position: relative;
          right: 8px;
          top: 1px;
        }
      }
      .porject-input {
        width: 795px;
        .el-input {
          display: inline-block;
          width: 90%;
        }
      }
      .porject-button {
        width: 200px;
        padding-left: 30px;
      }
    }
    .termContent {
      padding-left: 52px;
      margin-top: 20px;
      .termContent-input {
        display: inline-block;
        width: auto;
        margin-right: 23px;
        .el-textarea {
          vertical-align: middle;
        }
        .el-input {
          display: inline-block;
          width: 400px;
        }
        .termContent-button {
          height: 42px;
          line-height: 42px;
          color: #5188fc;
          margin-right: 20px;
          cursor: pointer;
        }
        .button-detele {
          color: rgb(252 44 97 / 100%);
        }
      }
      .termContent-tools {
        padding-left: 75px;
        margin-top: 20px;
        .termContent-number {
          display: inline-block;
          margin-right: 20px;
          .el-input,
          .el-select {
            display: inline-block;
            width: 100px;
            margin-left: 10px;
          }
        }
      }
      .termContent-radio {
        .radio-text {
          .el-input {
            display: inline-block;
            width: 300px;
          }
        }
      }
    }
  }
}
.content-table .table-title {
  .title {
    width: 80px;
    padding: 0;
    font-size: 14px;
    color: #606266;
    i {
      display: inline-block;
      width: 8px;
      height: 16px;
      border-radius: 0 8px 8px 0;
      background: #3562db;
      margin-right: 8px;
      position: relative;
      top: 2px;
    }
  }
  .line {
    display: inline-block;
    width: calc(100% - 85px);
    height: 1px;
    border-top: 1px dashed #dcdfe6;
    position: relative;
    top: -2px;
    left: 2px;
  }
}
.sino-button-sure {
  height: 32px;
  color: #fff;
  background-color: #5188fc;
  border: 1px solid #5188fc;
  font-size: 16px;
  padding: 2px 10px;
}
</style>
