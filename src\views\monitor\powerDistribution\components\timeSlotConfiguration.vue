<template>
  <el-dialog
    v-if="dialogShow"
    :title="title"
    width="45%"
    :visible.sync="dialogShow"
    custom-class="model-dialog"
  >
    <div class="time_slot">
      <div v-for="(item, index) in timeRanges" :key="index" class="time_item">
        <span class="time_name">{{ item.name }}</span>
        <div class="batch_time">
          <div v-for="(v, i) in item.ranges" :key="i" class="slot_time_picker">
            <el-time-picker
              v-model="v.time"
              style="width: 210px;"
              format="HH:mm"
              value-format="HH:mm"
              is-range
              :picker-options="pickerOptions"
              range-separator="-"
              start-placeholder="开始时间"
              end-placeholder="结束时间">
            </el-time-picker>
          </div>
          <span class="add_del" @click="addTimeRange(item)">+</span>
          <span v-if="item.ranges.length > 1" class="add_del" @click="removeTimeRange(item)">-</span>
        </div>

      </div>
    </div>

    <div slot="footer">
      <el-button type="primary" plain @click="resetTimeArr">重置</el-button>
      <el-button type="primary" @click="submit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import moment from 'moment'
export default {
  data() {
    return {
      dialogShow: false,
      title: '分时段配置',
      pickerOptions: {
        format: 'HH:mm'
      },
      timeRanges: [
        {
          name: '尖峰',
          status: 0,
          ranges: [

          ]
        },
        {
          name: '高峰',
          status: 1,
          ranges: [

          ]
        },
        {
          name: '平时',
          status: 2,
          ranges: [

          ]
        },
        {
          name: '谷峰',
          status: 3,
          ranges: [

          ]
        }
      ]
    }
  },
  mounted() {
    // this.resetTimeArr()
  },
  methods: {
    getData() {
      this.dialogShow = true
      this.getList()
    },
    getList() {
      // 调用 selectTime 接口获取时间数据
      this.$api.selectTime().then(res => {
        // 如果返回的数据为空，则重置时间数组
        if (!res.data.length) {
          this.resetTimeArr()
        } else {
          // 遍历时间范围数组
          this.timeRanges.forEach(ele => {
            // 清空范围数组
            ele.ranges = []
            // 遍历接口返回的数据
            res.data.forEach(item => {
              if (ele.status == item.status) {
                // 提取开始时间和结束时间的小时和分钟部分
                ele.ranges.push({
                  time: [
                    item.startTime.substring(0, 5),
                    item.endTime.substring(0, 5)
                  ]
                })

              }
            })
          })
        }
      })
    },
    resetTimeArr() {
      // 尖峰时段   10:30－11:30，19:00－21:00
      // 高峰时段   8:30－10:30，18:00－19:00，21:00-23:00
      // 平时时间段   12:00－17:00
      // 谷峰时段   23:00－23:59 00:00-07:00
      this.timeRanges = [
        {
          name: '尖峰',
          status: 0,
          ranges: [
            { time: ['10:31', '11:30']},
            { time: ['19:01', '21:00']}
          ]
        },
        {
          name: '高峰',
          status: 1,
          ranges: [
            { time: ['08:31', '10:30']},
            { time: ['18:01', '19:00']},
            { time: ['21:01', '23:00']}
          ]
        },
        {
          name: '平时',
          status: 2,
          ranges: [
            { time: ['12:01', '17:00']}
          ]
        },
        {
          name: '谷峰',
          status: 3,
          ranges: [
            { time: ['23:01', '23:59']},
            { time: ['00:00', '07:00']}
          ]
        }
      ]
    },
    addTimeRange(item) {
      item.ranges.push({ time: ['00:00', '23:59']})
    },
    removeTimeRange(item) {
      item.ranges.splice(item.ranges.length - 1, 1)
    },
    checkTimeRange() {
      return new Promise((resolve, reject) => {
        // 将所有时间范围的时间段合并成一个数组
        const arr = this.timeRanges.reduce((arr, item) => {
          return [...arr, ...item.ranges]
        }, [])
        const len = arr.length
        for (let i = 0; i < len - 1; i++) {
          const item = arr[i]
          for (let j = i + 1; j < len; j++) {
            const nextItem = arr[j]
            // 定义第一个时间范围
            const range1Start = moment(item.time[0], 'HH:mm')
            const range1End = moment(item.time[1], 'HH:mm')
            // 定义第二个时间范围
            const range2Start = moment(nextItem.time[0], 'HH:mm')
            const range2End = moment(nextItem.time[1], 'HH:mm')
            // 判断两个时间范围是否互相重叠 && 开始时间不能等于结束时间
            const isOverlap = (range1Start.isBefore(range2End) && range1End.isAfter(range2Start)) || item.time[0] == nextItem.time[1]
            if (isOverlap) {
              reject()
            }
          }
        }
        resolve()
      })
    },
    submit() {
      this.checkTimeRange().then(() => {
        const arr = this.timeRanges.reduce((arr, item) => {
          return [
            ...arr,
            ...item.ranges.map(ele => {
              return {
                name: item.name,
                status: item.status,
                startTime: ele.time[0] + ':00',
                endTime: ele.time[1] + ':59'
              }
            })
          ]
        }, [])
        this.$api.saveTime({list: arr}).then(res => {
          if (res.code == '200') {
            this.dialogShow = false
          }
        })
      }, () => {
        this.$message.error('时间范围相重叠，请重新选择！')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-date-editor .el-range__icon {
  line-height: 24px;
}

.time_slot {
  display: flex;
  flex-direction: column;

  .time_item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .batch_time {
      flex: 1;
      display: flex;
      flex-wrap: wrap;
    }

    .slot_time_picker {
      margin-right: 10px;
      margin-bottom: 10px;
    }

    .time_name {
      display: inline-block;
      font-size: 14px;
      margin-right: 20px;
    }

    .add_del {
      font-weight: 500;
      cursor: pointer;
      color: #3562db;
      margin-left: 20px;
      font-size: 28px;
    }
  }
}
</style>
