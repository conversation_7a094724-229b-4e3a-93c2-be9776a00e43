<template>
  <PageContainer v-if="routeInfo.isFalg == 0">
    <div slot="header">
      <div class="search-header">
        <div class="search-from">
          <el-input v-model="filter.name" placeholder="请输入培训模板名称" style="width: 200px"></el-input>
          <el-date-picker v-model="filter.timeLine" type="daterange" value-format="yyyy-MM-dd" range-separator="至"
            start-placeholder="开始日期" end-placeholder="结束日期" @change="dataPink">
          </el-date-picker>
          <el-button type="primary" plain @click="reset">重置</el-button>
          <el-button type="primary" @click="search">查询</el-button>
        </div>
        <div>
          <el-button type="primary" @click="addTemplate('add')">创建模板</el-button>
          <el-button type="primary" :disabled="!multipleSelection.length >= 1" @click="batchDetel">批量删除</el-button>
        </div>
      </div>
    </div>
    <div slot="content" style="height: 100%; margin-top: 16px; background-color: #fff">
      <div class="table-content">
        <el-table v-loading="tableLoading" :data="tableData" tyle="width: 100%;" height="100%" border stripe
          title="双击查看详情" @selection-change="handleSelectionChange" @row-dblclick="openDetails">
          <el-table-column type="selection" width="55" fixed="left" align="center"></el-table-column>
          <el-table-column label="序号" width="55" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{
                (paginationData.pageNo - 1) * paginationData.pageSize +
                scope.$index +
                1
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="培训模板名称" align="center" show-overflow-tooltip>
          </el-table-column>
          <el-table-column prop="disc" label="培训内容" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="coursewareNum" label="培训课件数" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="createTime" label="创建时间" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="taskTeamName" label="操作" align="center" show-overflow-tooltip>
            <template slot-scope="scope">

              <div class="operateBths">
                <el-link type="info" @click="openDetails(scope.row, 'detail')">编辑
                </el-link>
                <el-link type="primary" @click="copyList(scope.row)">复制</el-link>
                <el-link type="primary" @click="openDetails(scope.row, 'examine')">查看</el-link>
                <el-link type="primary" @click="deletExam(scope.row)">删除</el-link>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="contentTable-footer">
          <el-pagination style="margin-top: 3px" :current-page="paginationData.pageNo"
            layout="total, sizes, prev, pager, next, jumper" :total="paginationData.total"
            :page-size="paginationData.pageSize" :page-sizes="[15, 30, 50, 100]" @size-change="handleSizeChange"
            @current-change="handleCurrentChange"></el-pagination>
        </div>
      </div>
    </div>
  </PageContainer>
   <div v-else>
  <permissionPrompt></permissionPrompt>
  </div>
</template>
  
<script>
import axios from "axios";
import moment from 'moment'
import permissionPrompt from "@/views/safety/courseIndex/components/permissionPrompt.vue";
// import { servicesLoading } from "@/assets/js/temp.js";
export default {
  components: { permissionPrompt },
  data() {
    return {
      filter: {
        name: '',
        timeLine: [],
        startTime: '',
        endTime: '',
      },
      formInline: {
        templateName: "",
        startTime: "",
        endTime: "",
      },
      indexTy: '',
      timeLine: [],
      params: [], // 删除id集合
      paramsIds: [],
      multipleSelection: [], // 列表勾选中的值
      hiddenLevelList: [], // 获取筛选类型数据
      tableData: [],
      Ids: [], // 选择列表id集合
      tableLoading: false,
      routeInfo:{},
      paginationData: {
        pageNo: 1,
        pageSize: 15,
        total: 0,
      }
    };
  },
  computed: {},
  created() {
    // if (this.$route.query) {
    //   sessionStorage.setItem("routeInfo", JSON.stringify(this.$route.query));
    //   this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
    // }
     this.routeInfo = JSON.parse(sessionStorage.getItem('routeInfo'))
    if (this.routeInfo.isFalg == 1) {
      return
    }
    this.getDataList(); // 列表
  },
  mounted() {
    let self = this;
    document.onkeydown = function (event) {
      let _key = event.keyCode;
      if (_key == 13) {
        event.preventDefault();
        self.search();
      }
    };
  },
  methods: {
    // 查询列表
    getDataList() {
      let params = {
        current: this.paginationData.pageNo,
        size: this.paginationData.pageSize,
        ...this.filter
      }
      this.$api.tarainTemplateList(params).then(res => {
        if (res.code == 200) {
          this.tableData = res.data.list
          this.paginationData.total = res.data.total
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 删除
    deletExam(row) {
      this.params = []
      this.params.push(row.id)
      this.$confirm("删除后将无法恢复，是否确定删除？", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$api.tarainTemplateDetel({ ids: this.params }).then(res => {
          if (res.code == 200) {
            this.$message.success(res.msg)
            this.getDataList()
          } else {
            this.$message.error(res.msg)
          }
        })
      });
    },
    //  ------------------------------------------------操作表格中数据------------------------------------------------------------
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 批量姗迟
    batchDetel() {
      this.paramsIds = []
      this.multipleSelection.map((item) => {
        this.Ids.push(item.id)
        this.paramsIds = this.Ids
      })
      this.$confirm('确定要删除当前科目吗，删除后将无法恢复，是否确定删除？', "提醒", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        cancelButtonClass: "btn-cancel",
        type: "warning"
      }).then(res => {
        this.$api.tarainTemplateDetel({ ids: this.paramsIds }).then(res => {
          if (res.code == 200) {
            this.$message.success(res.msg)
            this.getDataList()
          } else {
            this.$message.error(res.msg)
          }
        })
      })
    },
    // 双击列表查看详情
    openDetails(row, indexOf) {
      console.log(row,'eow');
      this.indexTy = indexOf
      if (this.indexTy == 'detail') {
        this.$router.push({
          path: "addTemplate",
          query: {
            id: row.id,
            indexOf: this.indexTy,
          },
        });
      } else if (this.indexTy == 'examine') {
        this.$router.push({
          path: "templateDetails",
          query: {
            id: row.id,
          },
        });
      }
    },
    // 复制列表
    copyList(row) {
      let params = {
        id: row.id
      }
      this.$api.tarainTemplateCopy(params).then( res => {
        if(res.code == 200) {
          this.$message.success(res.msg)
          this.getDataList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 创建模板
    addTemplate(indexOf) {
      this.indexTy = indexOf
      this.$router.push({
        path: "addTemplate",
        query: {
          indexOf: this.indexTy,
        }
      });
    },
    // 选择筛选时间
    dataPink(data) {
      this.filter.startTime =  data[0]
      this.filter.endTime = data[1]
    },
    // 查询
    search() {
      this.paginationData.pageNo = 1;
      this.getDataList();
    },
    // 重置
    reset() {
      this.filter.name = "";
      this.filter.startTime = "";
      this.filter.endTime = "";
      this.filter.timeLine = [];
      this.paginationData.pageNo = 1;
      this.getDataList();
    },
    // 分页
    handleSizeChange(val) {
      this.paginationData.pageSize = val;
      this.getDataList();
    },
    handleCurrentChange(val) {
      this.paginationData.pageNo = val;
      this.getDataList();
    }
  },
};
</script>
  
<style lang="scss" scoped>
.search-header {
  padding: 10px 16px;

  .search-from {
    padding-bottom: 12px;

    &>div {
      margin-top: 12px;
      margin-right: 10px;
    }
  }
}

.table-content {
  padding: 16px;
  height: calc(100% - 80px);

  .statusBtn {
    font-size: 14px;
    display: flex;
    justify-content: center;

    .auditIng {
      width: 58px;
      height: 24px;
      background-color: #fff7e8;
      border-radius: 4px;
      color: #d25f00;
    }

    .auditNo {
      width: 78px;
      height: 24px;
      background-color: #ffece8;
      border-radius: 4px;
      color: #cb2634;

      img {
        vertical-align: middle;
      }
    }

    .relwase {
      width: 58px;
      height: 24px;
      background-color: #e8ffea;
      border-radius: 4px;
      color: #009a29;
    }

    .inProgress {
      width: 86px;
      height: 24px;
      background-color: #E6EFFC;
      border-radius: 4px;
      color: #2749BF;
    }

    .relwaseNo {
      width: 58px;
      height: 24px;
      background-color: #F2F4F9;
      border-radius: 4px;
      color: #86909C;
    }
  }

  .operateBths {
    color: #3562DB;

    .el-link {
      margin-right: 8px;
    }
  }
}

.contentTable-footer {
  padding: 10px 0 0 0;
}
</style>
  