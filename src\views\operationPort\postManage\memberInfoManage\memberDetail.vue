<template>
  <PageContainer :footer="activeName !== '2'">
    <div slot="content" class="content">
      <div class="content-top">
        <div class="back" @click="goBack"><i class="el-icon-arrow-left"></i><span class="title">成员详情</span></div>
        <div class="personInfo">
          <div class="image">
            <img v-if="personInfo.avatar" :src="personInfo.avatar" width="100px" style="height: 100px" />
            <img v-else src="@/assets/images/operationPort/personAvatar.png" />
          </div>
          <div class="info">
            <div class="nameInfo">
              <span class="name">{{ personInfo.staffName || '' }}</span>
              <span v-if="personInfo.accountStatus === 0" class="state state-normal">
                <img src="@/assets/images/operationPort/normal.png" alt="" />
                <span>正常</span>
              </span>
              <span v-if="personInfo.accountStatus === 1" class="state state-forzen">
                <img src="@/assets/images/operationPort/frozen.png" alt="" />
                <span>已冻结</span>
              </span>
            </div>
            <div class="itemBox">
              <div v-for="(item, index) in statisticsData" :key="index" class="item">
                <span class="label">{{ item.name }}</span>
                <span class="value">{{ personInfo[item.key] || '--' }}</span>
              </div>
            </div>
          </div>
          <div class="QrCode">
            <div ref="qrCodeUrl" class="qRcode"></div>
          </div>
        </div>
      </div>
      <div class="content-bottom">
        <el-tabs v-model="activeName" :before-leave="handleBeforeLeave" @tab-click="handleClick">
          <el-tab-pane label="基础信息" name="1">
            <div v-if="activeName === '1'" class="tabContent" style="padding: 24px 0">
              <baseFormMember v-if="queryParams.type === 'edit'" ref="baseForm"></baseFormMember>
              <baseInfoMember v-else></baseInfoMember>
            </div>
          </el-tab-pane>
          <el-tab-pane label="证书信息" name="2">
            <div v-if="activeName === '2'" class="tabContent">
              <el-button v-auth="'memberInfoManage:edit'" type="primary" @click="onCreate()">上传</el-button>
              <el-table v-loading="loadingStatus" height="480" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
                <el-table-column type="index" width="55" label="序号" />
                <el-table-column label="证书名称" prop="certDictName" show-overflow-tooltip></el-table-column>
                <el-table-column label="证书编号" prop="certNo" show-overflow-tooltip></el-table-column>
                <el-table-column label="颁发机构" prop="issuingAuthority" show-overflow-tooltip></el-table-column>
                <el-table-column label="发证日期" prop="certTime" show-overflow-tooltip></el-table-column>
                <el-table-column label="有效截止日期" prop="validityPeriod" show-overflow-tooltip>
                  <template #default="{ row }">
                    <span>{{ row.type == 2 ? '长期有效' : row.validityPeriod }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="证书状态" prop="status" show-overflow-tooltip>
                  <template #default="{ row }">
                    <span class="certificate-list__tag" :class="[row.status === '有效期内' ? 'certificate-list__tag--1' : 'certificate-list__tag--2']">{{ row.status }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="添加人" prop="createName" show-overflow-tooltip></el-table-column>
                <el-table-column label="添加时间" prop="createTime" show-overflow-tooltip></el-table-column>
                <el-table-column label="附件" prop="fileUrl" width="180px" show-overflow-tooltip>
                  <template #default="{ row }">
                    <div v-if="row.list && row.list.length">
                      <div v-for="(f1, index) in row.list" :key="index" class="fileClass">
                        <span class="profile" @click="openPic(f1)"> {{ f1.urlName }}</span>
                      </div>
                    </div>
                    <span v-else>--</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="140px">
                  <template #default="{ row }">
                    <el-button v-auth="'memberInfoManage:edit'" type="text" @click="onOperation('edit', row.id)">编辑</el-button>
                    <el-button v-auth="'memberInfoManage:edit'" class="text-red" type="text" @click="onOperation('delete', row.id)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <!-- 证书上传编辑 -->
      <template v-if="isCertificateDialog">
        <certificateDialog
          v-if="isCertificateDialog"
          :certificatevisible="isCertificateDialog"
          :certificateDiaTitle="diaTitle"
          :certificateId="rowId"
          :certificateType="operationType"
          @submitCertificateDialog="submitCertificateDialog"
          @closeDialog="closeCertificateDialog"
        />
      </template>
      <!-- 图片查看 -->
      <template v-if="isImagesDialog">
        <imagesDialog v-if="isImagesDialog" :imagesShow="isImagesDialog" :picList="imagesList" @closeImagesDialog="() => (isImagesDialog = false)" />
      </template>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="cancel">取消</el-button>
      <el-button v-if="queryParams.type === 'view'" v-auth="'memberInfoManage:edit'" type="primary" @click="edit"> 编辑 </el-button>
      <el-button v-if="queryParams.type === 'edit'" v-auth="'memberInfoManage:edit'" type="primary" :loading="formLoading" @click="submit">保存</el-button>
    </div>
  </PageContainer>
</template>
<script>
import QRCode from 'qrcodejs2-fix'
import certificateDialog from '../components/certificateDialog.vue'
import imagesDialog from './components/imagesDialog.vue'
import baseInfoMember from '../components/baseInfoMember.vue'
import baseFormMember from '../components/baseFormMember.vue'
export default {
  name: 'memberDetail',
  components: {
    certificateDialog,
    baseInfoMember,
    baseFormMember,
    imagesDialog
  },
  data() {
    return {
      statusOptions: [
        { value: '0', label: '有效期内' },
        { value: '1', label: '过期' },
        { value: '2', label: '已续证' }
      ],
      activeName: '1',
      state: '3',
      personInfo: {},
      postId: '',
      statisticsData: [
        {
          name: '职工工号：',
          key: 'staffNum'
        },
        {
          name: '所属组织：',
          key: 'deptName'
        },
        {
          name: '手机号码：',
          key: 'phone'
        }
      ],
      tableData: [],
      loadingStatus: false,
      isCertificateDialog: false, // 证书弹框
      diaTitle: '',
      rowId: 0,
      formLoading: false, // 表单提交
      operationType: '', // 操作类型 上传/编辑
      queryParams: {}, // 路由参数
      isImagesDialog: false, // 图片显示
      imagesList: [] // 图片list
    }
  },
  mounted() {
    this.queryParams = this.$route.query
    this.activeName = this.queryParams.currentType === '2' ? '2' : '1'
    this.getStaffByIdFn()
    this.getCertificateDataList()
    // this.$nextTick(() => {
    //   this.creatQrCode()
    // })
  },
  methods: {
    // 获取证书
    getCertificateDataList() {
      this.loadingStatus = true
      let data = {
        page: 1,
        pageSize: 999,
        staffId: this.$route.query.staffId
      }
      this.$api.supplierAssess
        .certificateListByPage(data)
        .then((res) => {
          this.loadingStatus = false
          if (res.code == 200) {
            this.tableData = res.data ? res.data.records : []
          } else if (res.message) {
            this.tableData = []
            this.$message.error(res.message)
          }
        })
        .catch((err) => {
          this.loadingStatus = false
        })
    },
    // 操作
    onCreate() {
      // 证书
      this.rowId = null
      this.operationType = 'add'
      this.diaTitle = '上传证书'
      this.isCertificateDialog = true
    },
    //  根据人员ID获取人员详情
    getStaffByIdFn() {
      this.$api.supplierAssess
        .getUserInfoById({
          staffId: this.$route.query.staffId
        })
        .then((res) => {
          if (res.code == 200) {
            this.personInfo = res.data
            this.personInfo.avatar = this.$tools.imgUrlTranslation(res.data.avatar)
          }
        })
    },
    // 返回
    goBack() {
      this.$router.go(-1)
    },
    // 取消编辑
    cancel() {
      if (this.queryParams.type === 'edit') {
        this.queryParams.type = 'view'
      } else {
        this.goBack()
      }
    },
    // 页面离开
    handleBeforeLeave() {
      if (this.queryParams.type === 'edit') return false
    },
    handleClick(val) {
      this.activeName = val.name
      if (this.activeName === '1') {
        if (this.queryParams.type === 'edit') {
          this.$confirm('确定保存修改信息', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info'
          })
            .then(() => {
              this.submit()
            })
            .catch(() => {})
        }
      } else {
        this.getCertificateDataList()
      }
    },
    // 删除
    onOperation(type, id) {
      // 删除
      if (type === 'delete') {
        this.$confirm('是否确认删除所选证书？', '确认删除', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api.supplierAssess.deleteUserCertificateInfoById({ id: id }).then((res) => {
            if (res.code === '200') {
              this.$message.success(res.msg)
              this.getCertificateDataList()
            } else {
              this.$message.error(res.msg)
            }
          })
        })
      } else if (type === 'edit') {
        this.operationType = 'edit'
        this.rowId = id
        this.diaTitle = '编辑证书'
        this.isCertificateDialog = true
      }
    },
    // 下载文件
    downLoad(val) {
      let url = this.$tools.imgUrlTranslation(val)
      window.open(url)
    },
    // 弹框确认
    submitCertificateDialog(formData) {
      formData.staffId = this.$route.query.staffId
      formData.type = formData.longTerm ? 2 : 1
      delete formData.longTerm
      if (formData.id) {
        this.$api.supplierAssess
          .updateUserCertificateInfo(formData)
          .then((res) => {
            if (res.code == 200) {
              this.$message({
                type: 'success',
                message: '保存成功'
              })
              this.getCertificateDataList()
            } else {
              this.$message({
                type: 'error',
                message: res.message || '保存失败'
              })
            }
          })
          .catch(() => {})
      } else {
        this.$api.supplierAssess
          .saveUserCertificateInfo(formData)
          .then((res) => {
            if (res.code == 200) {
              this.$message({
                type: 'success',
                message: '保存成功'
              })
              this.getCertificateDataList()
            } else {
              this.$message({
                type: 'error',
                message: res.message || '保存失败'
              })
            }
          })
          .catch(() => {})
      }
      this.isCertificateDialog = false
    },
    // 证书关闭
    closeCertificateDialog() {
      this.isCertificateDialog = false
    },
    // 打开图片
    openPic(val) {
      let url = this.$tools.imgUrlTranslation(val.url)
      window.open(url)
    },
    submit() {
      if (this.$refs['baseForm']) {
        this.$refs['baseForm'].validate((valid) => {
          if (valid) {
            let data = {
              ...this.$refs.baseForm.formInline,
              id: this.personInfo.id
            }
            this.$api
              .UpdateUserInfo(data, { 'operation-type': 2, 'operation-id': data.id, 'operation-name': data.staffName })
              .then((res) => {
                if (res.code == 200) {
                  this.$message({
                    type: 'success',
                    message: '保存成功'
                  })
                  this.queryParams.type = 'view'
                  this.getStaffByIdFn()
                } else {
                  this.$message({
                    type: 'error',
                    message: res.message || '保存失败'
                  })
                }
              })
              .catch(() => {})
          }
        })
      }
    },
    edit() {
      this.queryParams.type = 'edit'
    },
    /** 生成签名二维码 */
    creatQrCode() {
      this.$refs.qrCodeUrl.innerHTML = '' // 词句代码将之前的二维码清空了
      var qrcode = new QRCode(this.$refs.qrCodeUrl, {
        text: '33', // 需要转换为二维码的内容
        width: 100,
        height: 100,
        colorDark: '#000000', // 二维码颜色
        colorLight: '#ffffff', // 二维码背景色
        correctLevel: QRCode.CorrectLevel.L // 容错率，L/M/H
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  height: 100%;
  .content-top {
    background-color: #fff;
    .back {
      font-weight: 500;
      font-size: 14px;
      color: #333333;
      padding: 17px 24px;
      cursor: pointer;
      border-bottom: 1px solid #dcdfe6;
    }
    .title {
      margin-left: 10px;
    }
    .personInfo {
      width: 100%;
      padding: 24px;
      display: flex;
      .info {
        margin-left: 16px;
        width: 100%;
        .nameInfo {
          .name {
            font-weight: 500;
            font-size: 20px;
            color: #333333;
            margin-bottom: 16px;
          }
          .state {
            padding: 0px 8px;
            border-radius: 4px;
            margin-left: 16px;
            img {
              vertical-align: middle;
            }
            span {
              font-size: 14px;
              margin-left: 5px;
            }
          }
          .state-normal {
            padding: 4px 8px;
            background: #e6effc;
            span {
              color: #2749bf;
            }
          }
          .state-forzen {
            padding: 4px 8px;
            background: #ffece8;
            span {
              color: #cb2634;
            }
          }
        }
        .itemBox {
          display: flex;
          flex-wrap: wrap;
          margin-top: 16px;
          .item {
            width: calc(100% / 3);
            font-size: 14px;
            margin-top: 16px;
            .label {
              display: inline-block;
              color: #7f848c;
              width: 80px;
              text-align: right;
            }
            .value {
              color: #333333;
            }
          }
        }
      }
    }
  }
  .content-bottom {
    margin-top: 16px;
    height: calc(100% - 221px);
    background-color: #fff;
    .tabContent {
      padding: 24px 48px;
      height: 100%;
      .el-table {
        margin-top: 16px;
        flex: 1;
        .text-red {
          color: #ea0000 !important;
        }
      }
    }
  }
}
.el-tabs {
  height: 100%;
}
::v-deep .el-tabs__content {
  height: calc(100% - 40px) !important;
}
::v-deep .el-tab-pane {
  height: 100%;
}
::v-deep .el-tabs__nav {
  transform: translateX(20px) !important;
}
::v-deep .profile {
  color: #008af4;
  cursor: pointer;
}
.fileClass {
  width: 90%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.certificate-list {
  &__tag {
    // 过期
    &--2 {
      --color: #f53f3f;
    }
    // 有效期
    &--1 {
      --color: #08cb83;
    }
    // 续证
    &:before {
      content: '';
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
      height: 6px;
      width: 6px;
      border-radius: 4px;
      background-color: var(--color);
    }
  }
}
</style>
