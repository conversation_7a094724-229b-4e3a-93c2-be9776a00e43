<template>
  <PageContainer :footer="true">
    <div slot="content" v-loading="pageLoading" class="addApp-content">
      <el-form ref="formInline" :model="formInline" :rules="rules" :disabled="$route.query.type == 'detail'">
        <ContentCard title="新增租赁">
          <div slot="content" style="overflow: hidden">
            <el-row :gutter="24">
              <el-col :md="20">
                <el-form-item label="租赁空间" prop="params" label-width="120px">
                  <el-tag
                    v-for="(item, index) in formInline.params"
                    :key="item.spaceId"
                    style="margin-right: 10px; color: #409eff"
                    :closable="$route.query.type != 'detail'"
                    :type="item.spaceId"
                    @close="handleClose(index)"
                  >
                    {{ item.spaceLocalName }}
                  </el-tag>
                  <el-button type="text" @click="showSpaceAllocation = true">添加</el-button>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :md="7">
                <el-form-item label="租户名称" prop="tenantName" label-width="120px">
                  <el-select v-model="formInline.tenantName" placeholder="请选择租户">
                    <el-option v-for="item in tenantAll" :key="item.id" :label="item.tenantName" :value="item.id"> </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :md="7">
                <el-form-item label="租赁开始日期" prop="tenantStartTime" label-width="120px">
                  <el-date-picker v-model="formInline.tenantStartTime" type="date" value-format="yyyy-MM-dd" placeholder="选择租赁开始日期"> </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :md="5">
                <el-form-item label="租赁结束日期" prop="tenantEndTime" label-width="120px">
                  <el-date-picker v-model="formInline.tenantEndTime" type="date" value-format="yyyy-MM-dd" placeholder="选择租赁结束日期"> </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :md="7">
                <el-form-item label="租赁用途" prop="usage" label-width="120px">
                  <el-select v-model="formInline.usage" placeholder="请选择租赁用途">
                    <el-option v-for="item in purposeArr" :key="item.dictCode" :label="item.dictValue" :value="item.dictCode"> </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :md="5">
                <el-form-item label="租赁费用" prop="tenantCosts" label-width="120px">
                  <el-input v-model="formInline.tenantCosts" placeholder="请输入租赁费用">
                    <template slot="append">
                      <span>元</span>
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :md="14">
                <el-form-item label="营业执照" prop="files" label-width="120px">
                  <el-upload
                    action=""
                    :file-list="iconFileList"
                    :accept="uploadAcceptDict['picture'].type"
                    :before-upload="beforeAvatarUpload"
                    :http-request="(file) => httpRequset(file)"
                    :on-remove="(file, fileList) => handleRemove(file, fileList)"
                    :on-change="(file, fileList) => fileChange(file, fileList)"
                    :disabled="$route.query.type == 'detail'"
                  >
                    <el-button size="small" type="primary">点击上传</el-button>
                    <div slot="tip" class="el-upload__tip">支持{{ uploadAcceptDict['picture'].type }},且大小不超过{{ uploadAcceptDict['picture'].fileSize }}M</div>
                  </el-upload>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :md="10">
                <el-form-item label="备注" prop="remark" label-width="120px">
                  <el-input v-model="formInline.remark" placeholder="请输入备注" type="textarea" maxlength="200" :autosize="{ minRows: 4 }" show-word-limit></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </ContentCard>
      </el-form>
      <spaceAllocationDialog v-if="showSpaceAllocation" :visible.sync="showSpaceAllocation" @confirm="confirmSpaceAllocation" />
    </div>
    <div slot="footer">
      <el-button
        type="primary"
        plain
        @click="
          () => {
            $router.go(-1)
          }
        "
      >关闭</el-button
      >
      <el-button type="primary" :disabled="$route.query.type == 'detail'" @click="submitForm('formInline')">保存</el-button>
    </div>
  </PageContainer>
</template>
<script>
import { uploadAcceptDict } from '@/util/dict.js'
import moment from 'moment'
export default {
  name: 'addApp',
  components: {
    spaceAllocationDialog: () => import('./../../spaceUsage/spaceApplication/spaceAllocationDialog.vue')
  },
  // 路由前置守卫动态修改meta中的title
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      const typeList = {
        add: '新增租户',
        edit: '编辑租户',
        detail: '租户详情'
      }
      to.meta.title = typeList[to.query.type] ?? '租户详情'
    }
    next()
  },
  async beforeRouteLeave(to, from, next) {
    if (!['leaseManagement'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      uploadAcceptDict,
      purposeArr: [], // 租赁用途
      tenantAll: [], // 所有租户
      showSpaceAllocation: false,
      pageLoading: false,
      iconFileList: [],
      formInline: {
        params: [], // 租赁空间
        tenantName: '', // 租户名称
        tenantStartTime: '', // 租赁开始日期
        tenantEndTime: '', // 租赁结束日期
        usage: '', // 租赁用途
        tenantCosts: '', // 租赁费用
        files: [], // 营业执照
        remark: '' // 备注
      },
      rules: {
        params: [{ type: 'array', required: true, message: '请选择租赁空间', trigger: ['blur', 'change'] }],
        tenantName: [{ required: true, message: '请选择租户', trigger: ['blur', 'change'] }],
        tenantStartTime: [{ required: true, message: '选择租赁开始日期', trigger: ['blur', 'change'] }],
        tenantEndTime: [{ required: true, message: '选择租赁结束日期', trigger: ['blur', 'change'] }]
      }
    }
  },
  activated() {
    this.init()
  },
  created() {
    if (!this.$store.state.keepAlive.list.includes('leaseManagement')) {
      this.init()
    }
  },
  methods: {
    init() {
      Object.assign(this.$data, this.$options.data())
      this.$nextTick(() => {
        this.$refs.formInline.resetFields()
      })
      this.getQueryTenantAllList()
      this.getDictionaryList()
      if (this.$route.query.type != 'add') {
        this.getAppInfo()
      }
    },
    handleClose(index) {
      this.formInline.params.splice(index, 1)
    },
    getDictionaryList() {
      let data = {
        dictType: '1',
        parentId: '1'
      }
      this.$api.getSpaceDictionaryList(data).then((res) => {
        this.purposeArr = res.data
      })
    },
    getQueryTenantAllList() {
      this.$api.queryTenantAllList().then((res) => {
        this.tenantAll = res.data
      })
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.formInline.id) {
            let data = {
              ...this.formInline
            }
            this.$api.updateLease(data, { 'operation-type': 2, 'operation-name': this.formInline.tenantName, 'operation-id': this.formInline.id }).then((res) => {
              if (res.code == 200) {
                this.$message({ message: '修改成功', type: 'success' })
                this.$router.go(-1)
              } else {
                this.$message({ message: res.msg, type: 'error' })
              }
            })
          } else {
            let data = {
              id: '',
              ...this.formInline
            }
            this.$api.addNewObj(data, { 'operation-type': 1 }).then((res) => {
              if (res.code == 200) {
                this.$message({ message: '提交成功', type: 'success' })
                this.$router.go(-1)
              } else {
                this.$message({ message: res.msg, type: 'error' })
              }
            })
          }
        }
      })
    },
    confirmSpaceAllocation(list) {
      console.log(list)
      let arr = [
        ...this.formInline.params,
        ...list.map((item) => {
          return {
            functionType: item.functionDictName,
            localtion: item.simName,
            spaceLocalName: item.localSpaceName,
            spaceLocalNo: item.localSpaceCode,
            // spaceLocalName: item.ssmName,
            // spaceLocalNo: item.ssmId,
            spaceId: item.id
          }
        })
      ]
      // 去除选择重复的项
      this.formInline.params = arr.filter((item, index, self) => {
        return index === self.findIndex((obj) => obj.spaceId === item.spaceId)
      })
    },
    // 获取应用详情
    getAppInfo() {
      this.pageLoading = true
      this.$api
        .queryLeaseById({ id: this.$route.query.id })
        .then((res) => {
          this.pageLoading = false
          if (res.code == 200) {
            let { id, tenantName, tenantStartTime, tenantEndTime, usage, tenantCosts, remark, functionType, localtion, spaceLocalName, spaceLocalNo, spaceId, files } = res.data
            this.formInline = {
              id,
              tenantName: Number(tenantName),
              tenantStartTime: moment(tenantStartTime).format('YYYY-MM-DD'),
              tenantEndTime: moment(tenantEndTime).format('YYYY-MM-DD'),
              usage,
              tenantCosts,
              remark,
              files: files || [],
              params: [
                {
                  spaceId,
                  functionType,
                  localtion,
                  spaceLocalName,
                  spaceLocalNo
                }
              ]
            }
            this.iconFileList = files.map((ele) => {
              return {
                url: this.$tools.imgUrlTranslation(ele.fileUrl),
                name: ele.fileName,
                status: 'success'
              }
            })
            console.log('this.fileArr=======', this.iconFileList)
          }
        })
        .catch(() => {
          this.pageLoading = false
        })
      console.log('应用信息', this.formInline)
    },
    fileChange(file, fileList) {
      // this.fileList = fileList
      // this.iconFileList = fileList
    },
    handleRemove(file, fileList) {
      console.log(file)
      this.formInline.files = this.formInline.files.filter((item) => {
        return item.fileName != file.name
      })
    },
    httpRequset(file) {
      const params = new FormData()
      params.append('file', file.file)
      this.$api.spaceaUpload(params).then((res) => {
        if (res.code == 200) {
          console.log(file)
          this.formInline.files.push({
            fileUrl: res.data,
            fileName: file.file.name
          })
          // this.formInline.businessLicenseUrl = res.data
          this.$message({
            message: '上传成功',
            type: 'success'
          })
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    beforeAvatarUpload(file) {
      const fileSize = this.uploadAcceptDict['picture'].fileSize
      const isLt2M = file.size / 1024 / 1024 < fileSize
      if (!isLt2M) {
        this.$message.error(`上传文件大小不能超过 ${fileSize}MB!`)
        return false
      }
      const extension = file.name.substring(file.name.lastIndexOf('.'))
      const isFormat = this.uploadAcceptDict['picture'].type.includes(extension)
      if (!isFormat) {
        this.$message.error(`上传图片只能是 ${this.uploadAcceptDict['picture'].type}格式!`)
        return false
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.addApp-content {
  background: #fff;
  border-radius: 4px;
  height: 100%;
  padding: 10px;
  overflow-y: auto;
  // ::v-deep .el-upload {
  //   width: 130px;
  //   height: 130px;
  // }
  // ::v-deep .el-upload-list {
  //   .el-upload-list__item {
  //     width: 130px;
  //     height: 130px;
  //   }
  // }
  .hide {
    ::v-deep .el-upload--picture-card {
      display: none;
    }
  }
}
</style>
