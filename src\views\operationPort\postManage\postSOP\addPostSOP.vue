<template>
  <PageContainer class="positionManage-list">
    <template #content>
      <div class="add-post-sop-container">
        <div slot="header" class="clearfix">
          <span>{{ type === 'edit' ? '编辑岗位SOP' : '新增岗位SOP' }}</span>
        </div>
        <el-form ref="form" :model="form" :rules="rules" label-width="100px" class="add-post-sop-form">
          <el-row class="first-row">
            <el-col :span="11">
              <el-form-item label="SOP名称" prop="name" required>
                <el-input v-model="form.name" placeholder="请输入SOP名称" older="请输入SOP名称" show-word-limit maxlength="50" />
              </el-form-item>
            </el-col>
            <el-col :span="11" :offset="2">
              <el-form-item label="标题" prop="title">
                <el-input v-model="form.title" placeholder="请输入标题" show-word-limit maxlength="50" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="应用岗位" prop="selectedPositions">
                <el-select
                  v-model="selectedPositions"
                  multiple
                  placeholder="请选择应用岗位"
                  style="width: 100%"
                  :popper-append-to-body="false"
                  :popper-class="'hidden-popper'"
                  value-key="id"
                  @focus="openPositionDialog"
                  @change="clearPosition"
                >
                  <el-option v-for="item in selectedPositions" :key="item.id" :label="item.name" :value="item" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="应用值班岗" prop="selectedDutyPositions">
                <el-select
                  v-model="selectedDutyPositions"
                  multiple
                  placeholder="请选择应用值班岗"
                  style="width: 100%"
                  :popper-append-to-body="false"
                  :popper-class="'hidden-popper'"
                  value-key="id"
                  @focus="openDutyPositionDialog"
                  @change="clearDutyPosition"
                >
                  <el-option v-for="item in selectedDutyPositions" :key="item.id" :label="item.name" :value="item" />
                </el-select>
                <div class="tip-text">※值班岗的SOP会优先于岗位的SOP显示</div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" :rows="3" maxlength="500" show-word-limit />
          </el-form-item>
          <el-form-item label="内容" prop="content">
            <editor ref="myTextEditor" v-model="form.content" class="my-editor"></editor>
          </el-form-item>
        </el-form>
        <div class="form-buttons">
          <el-button type="primary" @click="submitForm">保存</el-button>
          <el-button @click="cancel">取消</el-button>
        </div>
      </div>
      <!-- 岗位选择弹窗 -->
      <position-select-dialog
        :visible.sync="positionDialogVisible"
        title="选择岗位"
        leftTitle="选择岗位"
        searchPlaceholder="搜索岗位名称"
        itemName="岗位"
        emptyTip=""
        dialogType="position"
        :initialSelected="selectedPositions"
        @confirm="handlePositionSelected"
      />
      <!-- 值班岗选择弹窗 -->
      <position-select-dialog
        :visible.sync="dutyPositionDialogVisible"
        title="选择值班岗"
        leftTitle="选择值班岗"
        searchPlaceholder="搜索值班岗名称"
        itemName="值班岗"
        emptyTip=""
        dialogType="duty"
        :initialSelected="selectedDutyPositions"
        @confirm="handleDutyPositionSelected"
      />
    </template>
  </PageContainer>
</template>
<script>
import editor from './components/quillEditor.vue'
import PositionSelectDialog from './components/positionSelectDialog.vue'
export default {
  name: 'AddPostSOP',
  components: {
    editor,
    PositionSelectDialog
  },
  data() {
    return {
      // 编辑/新增标识
      type: 'add',
      // 表单参数
      form: {
        id: undefined,
        name: '',
        title: '',
        positionIds: [], // 存储已选岗位的ID数组
        positionNames: '', // 存储已选岗位的名称（逗号分隔）
        dutyPositionIds: [], // 存储已选值班岗的ID数组
        dutyPositionNames: '', // 存储已选值班岗的名称（逗号分隔）
        content: '',
        remark: '',
        status: '1'
      },
      // 岗位选项
      positionOptions: [],
      // 已选岗位
      selectedPositions: [],
      // 值班岗选项
      dutyPositionOptions: [],
      // 已选值班岗
      selectedDutyPositions: [],
      // 弹窗可见性
      positionDialogVisible: false,
      dutyPositionDialogVisible: false,
      // 表单校验规则
      rules: {
        name: [
          { required: true, message: '请输入SOP名称', trigger: 'blur' },
          { max: 50, message: 'SOP名称长度不能超过50个字符', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    // 判断是否为编辑
    this.type = this.$route.query.type === 'edit' ? 'edit' : 'add'
    if (this.type === 'edit') {
      const id = this.$route.query.id
      this.getDetail(id)
    }
    this.getPositionList()
    this.getDutyPositionList()
  },
  methods: {
    // 获取SOP详情
    getDetail(id) {
      // 调用API获取数据
      this.$api.supplierAssess
        .getSupPostSopDetail({ id: id })
        .then((res) => {
          if (res.code === '200' && res.data) {
            const detail = res.data
            // 处理值班岗ID和名称
            let dutyPostIds = []
            if (detail.dutyPostId) {
              dutyPostIds = detail.dutyPostId.split(',').map((id) => id.trim())
            }
            // 设置表单数据
            this.form = {
              id: detail.id,
              name: detail.sopName || '',
              title: detail.sopTitle || '',
              content: detail.content || '',
              remark: detail.remark || '',
              status: detail.status || '1',
              // 处理岗位信息
              positionIds: detail.postId ? detail.postId.split(',').map((id) => id.trim()) : [],
              positionNames: detail.postName || '',
              // 处理值班岗信息
              dutyPositionIds: dutyPostIds,
              dutyPositionNames: detail.dutyPostName || ''
            }
            // 设置富文本内容
            if (this.$refs.myTextEditor) {
              this.$refs.myTextEditor.content = this.form.content
            }
            // 先清空已选项
            this.selectedPositions = []
            this.selectedDutyPositions = []
            // 先获取岗位和值班岗列表，再设置已选项
            Promise.all([this.getPositionList(), this.getDutyPositionList()]).then(() => {
              // 设置已选岗位和值班岗
              this.setSelectedPositions()
              this.setSelectedDutyPositions()
            })
          } else {
            this.$message.error(res.msg || '获取SOP详情失败')
          }
        })
        .catch((err) => {
          console.error('获取SOP详情失败', err)
          this.$message.error('获取SOP详情失败')
        })
    },
    // 获取岗位列表
    getPositionList() {
      // 调用API获取岗位列表
      return this.$api.supplierAssess
        .getPostChildData()
        .then((res) => {
          if (res.code == '200' && res.data) {
            // 将树形结构数据转换为扁平结构
            const flattenData = this.flattenTreeData(res.data, 'code', 'name', 'child')
            this.positionOptions = flattenData
            // 如果是编辑模式，设置已选岗位
            if (this.type === 'edit') {
              this.setSelectedPositions()
            }
          } else {
            this.$message.error('获取岗位数据失败')
            this.positionOptions = []
          }
        })
        .catch(() => {
          this.$message.error('获取岗位数据失败')
          this.positionOptions = []
        })
    },
    // 获取值班岗列表
    getDutyPositionList() {
      // 调用API获取值班岗列表
      return this.$api.supplierAssess
        .getDutyPostData()
        .then((res) => {
          if (res.code == '200' && res.data) {
            // 将树形结构数据转换为扁平结构
            const flattenData = this.flattenTreeData(res.data, 'id', 'dutyPostName', 'child')
            this.dutyPositionOptions = flattenData
            // 如果是编辑模式，设置已选值班岗
            if (this.type === 'edit') {
              this.setSelectedDutyPositions()
            }
          } else {
            this.$message.error('获取值班岗数据失败')
            this.dutyPositionOptions = []
          }
        })
        .catch(() => {
          this.$message.error('获取值班岗数据失败')
          this.dutyPositionOptions = []
        })
    },
    // 将树形结构数据转换为扁平结构
    flattenTreeData(data, idField, nameField, childrenField) {
      const result = []
      const flatten = (items) => {
        if (!items) return
        items.forEach((item) => {
          // 创建一个标准化的项目对象
          const flatItem = {
            id: item[idField] || item.id,
            name: item[nameField] || item.name || '',
            originalData: item
          }
          result.push(flatItem)
          // 处理子节点
          if (item[childrenField] && item[childrenField].length > 0) {
            flatten(item[childrenField])
          } else if (item.children && item.children.length > 0) {
            flatten(item.children)
          }
        })
      }
      flatten(data)
      return result
    },
    // 设置已选岗位
    setSelectedPositions() {
      if (this.form.positionIds && this.form.positionIds.length > 0 && this.positionOptions.length > 0) {
        // 根据ID匹配完整的岗位对象
        this.selectedPositions = this.positionOptions.filter((item) => this.form.positionIds.includes(item.id) || this.form.positionIds.includes(item.id.toString()))
        // 如果没有匹配到任何岗位，但有岗位名称，则创建临时对象
        if (this.selectedPositions.length === 0 && this.form.positionNames) {
          const names = this.form.positionNames.split(',')
          this.selectedPositions = this.form.positionIds.map((id, index) => ({
            id: id,
            name: names[index] || id
          }))
        }
      }
    },
    // 设置已选值班岗
    setSelectedDutyPositions() {
      if (this.form.dutyPositionIds && this.form.dutyPositionIds.length > 0 && this.dutyPositionOptions.length > 0) {
        // 根据ID匹配完整的值班岗对象
        this.selectedDutyPositions = this.dutyPositionOptions.filter((item) => {
          const itemId = item.id
          const itemIdStr = item.id.toString()
          // 检查每个ID是否匹配
          for (const id of this.form.dutyPositionIds) {
            const idStr = id.toString()
            if (itemId === id || itemIdStr === idStr) {
              return true
            }
          }
          return false
        })
        // 如果没有匹配到任何值班岗，但有值班岗名称，则创建临时对象
        if (this.selectedDutyPositions.length === 0 && this.form.dutyPositionNames) {
          const names = this.form.dutyPositionNames.split(',')
          this.selectedDutyPositions = this.form.dutyPositionIds.map((id, index) => ({
            id: id,
            name: names[index] || id
          }))
        }
      }
    },
    // 打开岗位选择弹窗
    openPositionDialog() {
      this.positionDialogVisible = true
    },
    // 打开值班岗选择弹窗
    openDutyPositionDialog() {
      this.dutyPositionDialogVisible = true
    },
    // 处理岗位选择结果
    handlePositionSelected(positions) {
      this.selectedPositions = positions
      this.form.positionIds = positions.map((item) => item.id || item.code)
      this.form.positionNames = positions.map((item) => item.name || item.postName || '').join(',')
    },
    // 处理值班岗选择结果
    handleDutyPositionSelected(positions) {
      // 确保每个选中的值班岗都有id和name属性
      this.selectedDutyPositions = positions.map((item) => {
        return {
          id: item.id || item.code,
          name: item.name || item.dutyPostName || '',
          originalData: item.originalData || item
        }
      })
      // 更新表单数据
      this.form.dutyPositionIds = this.selectedDutyPositions.map((item) => item.id)
      this.form.dutyPositionNames = this.selectedDutyPositions.map((item) => item.name).join(',')
    },
    // 表单提交
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 获取最新的富文本内容
          if (this.$refs.myTextEditor) {
            this.form.content = this.$refs.myTextEditor.content
          }
          // 构建API参数
          const params = {
            sopName: this.form.name,
            sopTitle: this.form.title,
            remark: this.form.remark,
            content: this.form.content, // 富文本内容
            postId: this.form.positionIds.join(','), // 多个岗位ID用逗号分隔
            postName: this.form.positionNames, // 已经是逗号分隔的字符串
            dutyPostId: this.form.dutyPositionIds.join(','), // 多个值班岗ID用逗号分隔
            dutyPostName: this.form.dutyPositionNames // 已经是逗号分隔的字符串
          }
          // 如果是编辑模式，添加id参数
          if (this.type === 'edit' && this.form.id) {
            params.id = this.form.id
          }
          // 调用saveOrUpdate接口
          this.$api.supplierAssess
            .saveOrUpdate(params)
            .then((res) => {
              if (res.code == 200) {
                this.$message.success(this.type === 'edit' ? '修改成功' : '新增成功')
                this.goBack()
              } else {
                this.$message.error(res.msg || (this.type === 'edit' ? '修改失败' : '新增失败'))
              }
            })
            .catch((err) => {
              console.error(err)
              this.$message.error(this.type === 'edit' ? '修改失败' : '新增失败')
            })
        }
      })
    },
    // 取消按钮
    cancel() {
      this.goBack()
    },
    // 返回列表页
    goBack() {
      this.$router.push('/postManage/postSOP')
    },
    clearPosition(val) {
      this.selectedPositions = val
      this.form.positionIds = val.map((item) => item.id || item.code)
      this.form.positionNames = val.map((item) => item.name || item.postName || '').join(',')
    },
    clearDutyPosition(val) {
      this.selectedDutyPositions = val
      this.form.dutyPositionIds = val.map((item) => item.id || item.code)
      this.form.dutyPositionNames = val.map((item) => item.name || item.dutyPostName || '').join(',')
    }
  }
}
</script>
<style lang="scss" scoped>
.positionManage-list {
  background-color: #fff;
  padding: 15px;
  .add-post-sop-form {
    margin-top: 10px;
    height: calc(100% - 95px);
  }
  .form-buttons {
    text-align: right;
  }
  .first-row {
    .el-form-item {
      margin-bottom: 22px;
    }
    .el-input {
      height: 40px;
    }
    .el-form-item__content {
      line-height: 40px;
    }
  }
  .add-post-sop-container {
    height: 100%;
    .clearfix {
      margin: 0 0 10px 0;
      font-size: 16px;
    }
    .tip-text {
      color: #909399;
      font-size: 12px;
      margin-top: 5px;
    }
    .my-editor {
      width: 100%;
      height: 300px;
    }
    .select-container {
      width: 100%;
      .selected-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 40px;
        padding: 0 15px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        cursor: pointer;
        .placeholder {
          color: #c0c4cc;
        }
      }
      .selected-tags {
        margin-top: 5px;
        .el-tag {
          margin-right: 5px;
          margin-bottom: 5px;
        }
      }
    }
  }
}
</style>
<style>
/* 全局样式，不使用scoped */
.hidden-popper {
  display: none !important;
}
</style>
