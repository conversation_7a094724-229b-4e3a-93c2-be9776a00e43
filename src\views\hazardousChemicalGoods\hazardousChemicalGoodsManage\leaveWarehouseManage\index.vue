<template>
  <PageContainer class="leaveWarehouseManage">
    <template #content>
      <el-tabs class="leaveWarehouseManage__nav" :value="currentTab" @tab-click="onTabClick">
        <el-tab-pane name="manual" label="手工出库"></el-tab-pane>
        <!-- <el-tab-pane name="scanCode" label="扫码出库"></el-tab-pane> -->
        <el-tab-pane name="apply" label="领用申请单出库"></el-tab-pane>
        <el-tab-pane name="list" label="出库单查询"></el-tab-pane>
        <el-tab-pane name="particulars" label="出库明细查询"></el-tab-pane>
      </el-tabs>
      <component :is="currentComponent" class="leaveWarehouseManage__content" @openDetailComponent="openDetailComponent" @goBack="goBack" />
    </template>
  </PageContainer>
</template>
<script>
import leaveWarehouseList from './leaveWarehouseList.vue'
import applyLeaveWarehouse from './applyLeaveWarehouse.vue'
import particulars from './particulars.vue'
import scanCodeLeaveWarehouse from './scanCodeLeaveWarehouse.vue'
import manualLeaveWarehouse from './manualLeaveWarehouse.vue'
export default {
  name: 'leaveWarehouseManage',
  components: {
    leaveWarehouseList,
    applyLeaveWarehouse,
    particulars,
    scanCodeLeaveWarehouse,
    manualLeaveWarehouse
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.query && from.query.currentType) {
        vm.currentTab = from.query.currentType
      }
    })
  },
  data() {
    return {
      currentTab: 'manual',
      componentConfig: {
        manual: 'manualLeaveWarehouse',
        list: 'leaveWarehouseList',
        apply: 'applyLeaveWarehouse',
        particulars: 'particulars',
        scanCode: 'scanCodeLeaveWarehouse'
      }
    }
  },
  computed: {
    currentComponent() {
      return this.componentConfig[this.currentTab]
    }
  },
  methods: {
    onTabClick(e) {
      const name = e.name
      if (name !== this.currentTab) {
        this.$router.push({ query: { name } })
        this.currentTab = name
      }
    },
    // 组件详情
    openDetailComponent(row) {
      if (row.type === 'outWarehouseWarrant') {
        this.$router.push({ query: row })
        this.currentTab = 'manual'
      }
    },
    // 返回上一页
    goBack() {
      this.currentTab = 'list'
      this.$router.go(-1)
    }
  }
}
</script>
<style lang="scss" scoped>
.page-container.leaveWarehouseManage {
  ::v-deep(.container-content) {
    background: #fff;
    .leaveWarehouseManage__nav {
      .el-tabs__nav-scroll {
        padding: 0 32px;
      }
    }
    .leaveWarehouseManage__content {
      height: calc(100% - 40px);
      overflow: hidden;
    }
  }
}
</style>
