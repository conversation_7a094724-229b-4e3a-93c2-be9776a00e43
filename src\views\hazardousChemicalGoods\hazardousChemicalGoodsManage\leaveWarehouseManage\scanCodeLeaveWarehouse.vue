<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="content_box">
        <el-form ref="formRef" :model="formModel" :rules="rules" label-width="auto" class="formRef"
          :disabled="isDisabled">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="出库单号" prop="recordNumber">
                <el-input v-model="formModel.recordNumber" placeholder="系统自动生成" disabled>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="出库类型" prop="outwarehouseType">
                <el-select v-model="formModel.outwarehouseType" filterable placeholder="请选择">
                  <el-option v-for="item in outWarehouseTypeOptions" :key="item.id" :label="item.name"
                    :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="出库仓库" prop="warehouseId">
                <el-select v-model="formModel.warehouseId" filterable placeholder="请选择入库仓库" @change="warehouseChange">
                  <el-option v-for="item of warehouseOptions" :key="item.id" :label="item.warehouseName"
                    :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="出库时间" prop="createTime">
                <el-date-picker v-model="formModel.createTime" type="datetime" format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss" placeholder="系统自动生成"
                  :picker-options="pickerOptions"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="出库人" prop="person">
                <el-input v-model="formModel.person" placeholder="默认库房管理员" filterable disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="验收人" prop="acceptorId">
                <el-select v-model="formModel.acceptorId" placeholder="验收人" clearable filterable
                  @change="acceptorChange">
                  <el-option v-for="item of acceptorOptions" :key="item.id" :label="item.staffName"
                    :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="领用科室" prop="officesId">
                <el-select v-model="formModel.officesId" placeholder="请选择领用科室" @change="officesChange" filterable>
                  <el-option v-for="item in officesOptions" :key="item.umId" :label="item.unitComName"
                    :value="item.umId"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="备注" prop="remarks">
                <el-input v-model="formModel.remarks" placeholder="请输入备注"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="inventoryInfo">
            <div class="inventoryInfo_title">
              <span class="green_line"></span>
              危化品明细
            </div>
            <div class="inventoryInfo_input">
              <span>69条码</span>
              <el-input v-model.trim="code69" placeholder="请输出69条码" maxlength="13"
                onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')"></el-input>
            </div>
          </div>
          <div class="scanCodeLeaveWarehouse_table">
            <el-table v-loading="tableLoadingStatus" height="100%" style="width: 100%;" :data="tableData" border stripe
              class="tableAuto" row-key="id">
              <el-table-column type="index" label="序号" width="50"> </el-table-column>
              <el-table-column prop="materialTypeName" label="危化品分类" show-overflow-tooltip>
              </el-table-column>
              <!-- <el-table-column prop="materialCode" label="危化品编码" show-overflow-tooltip></el-table-column> -->
              <el-table-column prop="materialName" label="危化品名称" show-overflow-tooltip></el-table-column>
              <el-table-column prop="model" label="规格型号" show-overflow-tooltip></el-table-column>
              <el-table-column prop="basicUnitName" label="基础单位" show-overflow-tooltip></el-table-column>
              <el-table-column prop="inventory" label="库存数量" show-overflow-tooltip></el-table-column>
              <el-table-column prop="operateCount" label="出库数量" width="180">
                <template #default="{ row }">
                  <el-input-number v-model="row.operateCount" size="small" :min="0" :precision="0"
                    :max="parseInt(row.inventory)">
                  </el-input-number>
                </template>
              </el-table-column>
              <el-table-column prop="serviceLife" label="有效期" width="180">
                <template #default="{ row }">
                  <el-date-picker v-model="row.serviceLife" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                    placeholder="选择日期">
                  </el-date-picker>
                </template>
              </el-table-column>
              <!-- <el-table-column prop="unitPrice" label="进货单价" width="180">
                <template #default="{ row }">
                  <el-input v-model="row.unitPrice" placeholder="进货单价" oninput="if(isNaN(value)) { value =
               null } if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}">
                  </el-input>
                </template>
              </el-table-column> -->
              <!-- <el-table-column prop="trademark" label="品牌" show-overflow-tooltip></el-table-column> -->
              <!-- <el-table-column prop="supplierName" label="供应商" show-overflow-tooltip></el-table-column> -->
              <el-table-column prop="manufacturerName" label="生产厂家" show-overflow-tooltip></el-table-column>
              <el-table-column label="操作">
                <template #default="{ row }">
                  <el-button type="text" class="text-red" @click="deleteData(row, row.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form>
      </div>
      <!--选择危化品 -->
      <template v-if="sectionDialogShow">
        <selectHazardousChemicalDialog :sectionDialogShow="sectionDialogShow" @submitSectionDialog="submitSectionDialog"
          :warehouseType="'2'" @closeSectionDialog="closeSectionDialog" />
      </template>
    </div>
    <div slot="footer">
      <el-button type="primary" @click="submitForm(1)" :loading="formLoading" :disabled="!isOperation">暂存</el-button>
      <el-button type="primary" @click="submitForm(2)" :loading="formLoading" :disabled="!isOperation">出库单确认</el-button>
      <el-button type="primary" plain @click="operating('print')" :disabled="isOperation">打印出库单</el-button>
      <el-button type="primary" @click="operating('again')" :disabled="isOperation">再次出库</el-button>
    </div>
  </PageContainer>

</template>
<script>
import tableListMixin from '@/mixins/tableListMixin'
import moment from 'moment'
export default {
  name: 'scanCodeLeaveWarehouse',
  mxins: [tableListMixin],
  components: {
    selectHazardousChemicalDialog: () => import('../requisitionRegistrationManage/components/selectHazardousChemical.vue')
  },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      sectionDialogShow: false,
      // 正常表单
      formModel: {
        recordNumber: '',//出库单号
        outwarehouseType: '',//出库类型
        warehouseId: '',  // 出库仓库id
        warehouseName: "",//出库仓库name
        createTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),//出库时间
        person: '',//出库人
        officesId: '',//领用单位id
        officesName: '',//领用单位name
        acceptorId: '',//验收人id
        acceptorName: '',//验收人name
        remarks: '',//备注
      },
      rules: {
        outwarehouseType: [{ required: true, message: '请选择出库类型', trigger: 'change' }],
        warehouseId: [{ required: true, message: '请选择出库仓库', trigger: 'change' }],
        acceptorId: [{ required: true, message: '请选择验收人', trigger: 'change' }],
        officesId: [{ required: true, message: '请选择领用单位', trigger: 'change' }],
        createTime: [{ required: true, message: '请选择出库时间', trigger: 'change' }],
      },
      outWarehouseTypeOptions: [],//出库类型下拉
      officesOptions: [],//领用科室
      warehouseOptions: [],//出库仓库下拉
      acceptorOptions: [],//验收人下拉
      tableLoadingStatus: false,
      isDisabled: false,
      isOperation: true,//操作
      dataBackFileArr: [],
      tableData: [],
      code69: '',
      formLoading: false,//表单loading
    }
  },
  watch: {
    code69(val, o) {
      if (val.length === 13) {
        this.getHcsDetailData(val)
      }
    },
  },
  mounted() {
    this.init()
    this.formModel.person = this.$store.state.user.userInfo.user.staffName
  },
  methods: {
    //根据code查询危化品数据
    getHcsDetailData(val) {
      let data = {
        goodsCode: val
      }
      this.$api.getHcsById(data).then((res) => {
        this.code69 = ""
        if (res.code == '200' && Object.keys(res.data).length > 0) {
          let arr = []
          arr.push(res.data)
          this.tableData = this.removeSame(this.tableData.concat(arr))
        }
      })
    },
    //数组对象去重
    removeSame(array) {
      let newArray = []
      for (let item of array) {
        // 使用JSON.stringfy()方法将数组和数组元素转换为JSON字符串之后再使用includes()方法进行判断
        if (JSON.stringify(newArray).includes(JSON.stringify(item))) {
          continue
        } else {
          // 不存在的添加到数组中
          newArray.push(item)
        }
      }
      return newArray
    },
    //初始化
    init() {
      //this.getPersonListFn()
      this.getDeptList()
      this.getWarehouseTypeDataFn()
      this.getWarehouseListFn()
    },
    // 获取库房下拉
    getWarehouseListFn() {
      let params = {
        warehouseType: 2,
        status: '0'
      }
      this.$api.getWarehouseList(params).then((res) => {
        if (res.code == '200') {
          this.warehouseOptions = res.data.list
        }
      })
    },
    //  获取部门列表
    getDeptList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == 200) {
          this.officesOptions = res.data
        }
      })
    },
    //获取领用申请name
    officesChange(val) {
      if (val) {
        this.formModel.officesName = this.officesOptions.find((item) => item.id == val).deptName
      }
    },
    //获取出库类型
    getWarehouseTypeDataFn() {
      let params = {
        userId: this.$store.state.user.userInfo.user.staffId,
        userName: this.$store.state.user.userInfo.user.staffName,
        typeCode: "CKLX",
        status: '0'
      }
      this.$api.getWarehouseType(params).then((res) => {
        this.outWarehouseTypeOptions = res.data;
        this.formModel.outwarehouseType = this.outWarehouseTypeOptions[0].id
      });
    },
    //仓库选择取仓库信息
    warehouseChange(val) {
      this.tableData = []
      if (val) {
        this.formModel.warehouseName = this.warehouseOptions.find((item) => item.id == val).warehouseName
        this.getWarehouseDetailData(val)
      }
    },
    //获取库房详情
    getWarehouseDetailData(e) {
      const userInfo = this.$store.state.user.userInfo.user
      let params = {
        userId: userInfo.staffId,
        userName: userInfo.staffName,
        id: e
      }
      this.$api.getWarehouseById(params).then((res) => {
        if (res.code == '200') {
          let staffNameArr = res.data.manageName.split(',')
          this.acceptorOptions = staffNameArr.map((name, index) => ({ staffName: name, id: index + 1 }));
          this.formModel.acceptorId = this.acceptorOptions[0].id
          this.formModel.acceptorName = this.acceptorOptions[0].staffName
        }
      })
    },
    // // 获取人员列表
    // getPersonListFn() {
    //   let params = {
    //     current: 1,
    //     size: 9999,
    //   }
    //   this.$api.staffList(params).then((res) => {
    //     if (res.code == 200) {
    //       this.acceptorOptions = res.data.records
    //     }
    //   })
    // },
    //验收人获取name
    acceptorChange(val) {
      if (val) {
        this.formModel.acceptorName = this.acceptorOptions.find((item) => item.id == val).staffName
      }
    },
    deleteData(val, index) {
      this.tableData.splice(index, 1);
    },
    // 添加危化品弹窗
    closeSectionDialog() {
      this.sectionDialogShow = false
    },
    submitSectionDialog(list) {
      this.tableData = list
      this.sectionDialogShow = false
    },
    // 点击确定
    submitForm(type) {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.formLoading = true
          this.tableData.forEach(el => {
            el.amount = 1
          });
          let params = {
            ...this.formModel,
            userId: this.$store.state.user.userInfo.user.staffId,
            userName: this.$store.state.user.userInfo.user.staffName,
            materialRecordArrayStr: JSON.stringify(this.tableData),
            status: type || 1,
            amount: 1,
          }
          this.$api.saveOutWarehouseRecordData(params).then((res) => {
            this.formLoading = false
            if (res.code == '200') {
              this.isOperation = false
              this.isDisabled = true
              this.formModel.recordNumber = res.data.recordNumber
              this.$message.success(res.message)
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },
    // 打印和再次上传
    operating(type) {
      if (type === 'print') {
        this.$router.push({
          name: "outboundDeliveryOrder",
          query: {
            currentType: 'manual',
            inWarehouseId: this.formModel.recordNumber
          }
        })
      } else if (type === 'again') {
        this.isOperation = true
        this.isDisabled = false
        this.$refs.formRef.resetFields();
      }
    },
  }
}
</script>
<style scoped lang="scss">
.container-content > div {
  background-color: #fff;
  height: 100%;
  position: relative;
  .content_box {
    padding: 0 24px;
    background: #fff;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    height: 100%;
    .formRef {
      height: 100%;
      width: 100%;
    }
    .inventoryInfo {
      display: flex;
      height: 40px;
      line-height: 40px;
      margin-bottom: 8px;
      align-items: center !important;
      .inventoryInfo_title {
        font-size: 16px;
        .el-button {
          margin: 0 20px;
        }
        .green_line {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 2px;
          background: #3562db;
          margin-right: 10px;
          vertical-align: middle;
        }
      }
      .inventoryInfo_input {
        display: flex;
        width: 25%;
        margin-left: 24px;
        span {
          margin-right: 8px;
        }
      }
    }
    .scanCodeLeaveWarehouse_table {
      margin-top: 10px;
      height: calc(100% - 280px) !important;
    }
    .text-red {
      color: #ff1919;
    }
  }
  .el-input,
  .el-select,
  .el-cascader {
    width: 80%;
  }
}
::v-deep .scanCodeLeaveWarehouse .el-upload-list {
  display: none !important;
}

::v-deep .scanCodeLeaveWarehouse {
  display: inline-block;
}
</style>

