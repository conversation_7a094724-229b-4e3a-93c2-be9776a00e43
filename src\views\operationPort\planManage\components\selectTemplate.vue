<template>
  <el-dialog v-dialogDrag custom-class="model-dialog" append-to-body :visible="visible" :close-on-click-modal="false" :before-close="closeDialog" width="50%" title="选择预案模板">
    <div class="dialog-content">
      <div style="position: relative; padding: 10px; margin-bottom: 10px; background: #fff; border-radius: 4px">
        <el-input v-model="searchFrom.planName" placeholder="预案名称" clearable style="width: 200px"></el-input>
        <el-select v-model="searchFrom.planType" placeholder="预案类型" clearable style="margin-left: 10px">
          <el-option v-for="item in planTypeList" :key="item.thirdSystemCode" :label="item.thirdSystemName" :value="item.thirdSystemCode"></el-option>
        </el-select>
        <el-button plain type="primary" class="ml-16" @click="resetForm">重置</el-button>
        <el-button type="primary" @click="searchForm">查询</el-button>
        <el-button style="position: absolute; right: 10px; top: calc(50%); transform: translateY(-50%)" type="primary" icon="el-icon-plus" @click="saveTemplate('add')">
          创建新预案
        </el-button>
      </div>
      <div style="height: 400px; background: #fff; padding: 10px; border-radius: 4px">
        <TablePage
          ref="table"
          v-loading="tableLoading"
          :showPage="true"
          :tableColumn="tableColumn"
          :data="tableData"
          height="calc(100% - 40px)"
          :pageData="pageData"
          :pageProps="pageProps"
          @pagination="paginationChange"
        >
        </TablePage>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="saveTemplate">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script lang="jsx">
export default {
  name: 'selectTemplate',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectTemplate: '',
      searchFrom: {
        planName: '',
        planType: ''
      },
      planTypeList: [], // 预案类型列表
      tableLoading: false,
      tableColumn: [
        {
          prop: 'select',
          label: '',
          align: 'center',
          width: 60,
          render: (h, row) => {
            return (
              <el-radio v-model={this.selectTemplate} label={row.row.id}>
                <span></span>
              </el-radio>
            )
          }
        },
        {
          prop: 'icon',
          label: '',
          width: 100,
          render: (h, row) => {
            return <img style="width:48px; height:48px;" src={this.$tools.imgUrlTranslation(JSON.parse(row.row.regulationsFlow)[0]?.url ?? '')}></img>
          }
        },
        {
          width: 180,
          prop: 'planName',
          label: '预案名称',
          render: (h, row) => {
            return (
              <div class="planName">
                <p>{row.row.planName}</p>
                <p>{row.row.regulationsDesc}</p>
              </div>
            )
          }
        },
        {
          width: 120,
          prop: 'planType',
          label: '预案类型',
          formatter: (scope) => {
            return scope.row.planType ? this.planTypeList.find((v) => v.thirdSystemCode == scope.row.planType)?.thirdSystemName : ''
          }
        },
        {
          prop: 'path',
          label: '法规文案',
          showOverflowTooltip: false,
          render: (h, row) => {
            return <div class="twoLinesEllipsis" domPropsInnerHTML={row.row.regulationsText}></div>
          }
        },
        {
          prop: 'regulationsDoc',
          label: '法规文档',
          render: (h, row) => {
            return (
              <div style="cursor: pointer; color: #3562DB;" onClick={() => window.open(this.$tools.imgUrlTranslation(JSON.parse(row.row.regulationsDoc)[0]?.url ?? ''), '_blank')}>
                {JSON.parse(row.row.regulationsDoc)[0]?.name ?? ''}
              </div>
            )
          }
        }
      ],
      tableData: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      pageProps: {
        page: 'current',
        pageSize: 'size',
        total: 'total'
      }
    }
  },
  computed: {},
  created() {
    this.getAlarmSystem()
    this.getPlanTemplate()
  },
  methods: {
    saveTemplate(type) {
      if (!this.selectTemplate && !type) this.$message.warning('请选择预案魔板')
      this.$emit('saveTemplate', type == 'add' ? '' : this.selectTemplate)
      this.closeDialog()
    },
    // 获取预案模板列表
    getPlanTemplate() {
      let param = {
        ...this.searchFrom,
        pageSize: this.pageData.size,
        page: this.pageData.current
      }
      this.tableLoading = true
      this.$api
        .GetPlanTemplate(param)
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.records
            this.pageData.total = res.data.total
          }
          this.tableLoading = false
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    // 获取预案类型
    getAlarmSystem() {
      this.$api.getAlarmThirdSystemData().then((res) => {
        if (res.code == 200) {
          this.planTypeList = res.data
        }
      })
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    },
    // 查询
    searchForm() {
      this.getPlanTemplate()
    },
    // 重置查询
    resetForm() {
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.searchForm()
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getPlanTemplate()
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  overflow: hidden;
  .el-input {
    width: 200px;
  }
  .el-pagination {
    margin-top: 10px;
  }
  .ml-16 {
    margin-left: 16px;
  }
  ::v-deep(.planName) {
    p {
      margin: 0px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
