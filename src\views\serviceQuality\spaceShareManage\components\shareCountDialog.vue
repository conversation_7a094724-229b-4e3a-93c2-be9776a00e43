<template>
  <div>
    <el-dialog
      v-if="dialogVisible"
      v-dialogDrag
      :modal="false"
      :close-on-click-modal="false"
      title="分摊运算"
      width="30%"
      :visible.sync="dialogVisible"
      custom-class="model-dialog"
      :before-close="closeDialog"
    >
      <div v-loading="loading" class="dialog-content" element-loading-text="正在处理中，请勿操作">
        <el-form ref="formInline" :model="queryForm" inline class="form-inline" :rules="rules" label-width="120px">
          <el-form-item label="空间面积：" prop="shareType">
            <el-radio-group v-model="queryForm.shareType">
              <el-radio :label="0">
                逐级分摊
                <el-tooltip placement="top">
                  <i class="el-icon-warning" style="color: #606266"></i>
                  <span slot="content">按照医院>院区>楼栋>楼层>空间逐级分摊</span>
                </el-tooltip>
              </el-radio>
              <el-radio :label="1">
                全局分摊
                <el-tooltip placement="top">
                  <i class="el-icon-warning" style="color: #606266"></i>
                  <span slot="content">按照医院总建筑面积扣除所有空间建筑总面积，然后按比例分配</span>
                </el-tooltip>
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="submitFormData">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: 'shareCountDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      dialogVisible: this.visible,
      queryForm: {
        shareType: ''
      },
      rules: {
        shareType: { required: true, message: '请选择分摊类型', trigger: 'change' }
      }
    }
  },
  computed: {},
  mounted() {},
  methods: {
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', false)
      this.$refs['formInline'].resetFields()
    },
    submitFormData() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          this.loading = true
          this.$api
            .SpaceShareApi(this.queryForm)
            .then((res) => {
              if (res.code == 200) {
                this.$message.success('空间分摊成功')
                this.closeDialog()
              } else {
                this.$message.error(res.data)
              }
            })
            .finally(() => {
              this.loading = false
            })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  background: #fff;
  padding: 10px;
  max-height: 500px;
  overflow-y: auto;
  ::v-deep(.form-inline) {
    margin-top: 24px;
  }
}
</style>
