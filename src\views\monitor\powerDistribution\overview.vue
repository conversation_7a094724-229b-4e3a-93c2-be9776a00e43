<!-- 配电总览 -->
<template>
  <PageContainer>
    <div slot="content" class="staging-content" @mouseover="viewMouseover" @mouseleave="viewMouseleave">
      <el-tabs v-model="tabsActive" class="content-head" @tab-click="tabClick">
        <el-tab-pane v-for="(item, index) in firstLevelMenuList" :key="index" :label="item.name" :name="item.id ? item.code : 'zdsb'"></el-tab-pane>
      </el-tabs>
      <div v-show="tabsActive != 'zdsb'" class="content-main">
        <dashboard v-if="dashExampleShow" id="dashExample" :style="{ width: editExampleShow ? 'calc(100% - 220px)' : '100%' }">
          <dash-layout v-bind="dlayout" :debug="false">
            <template v-for="(item, index) in dlayout.items">
              <dash-item v-show="item.status == 1" v-bind.sync="dlayout.items[index]" :key="item.componentName" @moveEnd="moveEnd" @resizeEnd="resizeEnd">
                <component
                  :is="item.componentName"
                  :ref="item.componentName"
                  :requestInfo="{ projectCode, entityMenuCode: tabsActive }"
                  :item="item"
                  @all-more-Oper="allMoreOper"
                ></component>
              </dash-item>
            </template>
          </dash-layout>
        </dashboard>
        <div id="editExample" :style="{ width: editExampleShow ? '215px' : '0' }">
          <div class="round-box">
            <div class="editExample-title">
              <div>添加模块</div>
              <p>请点击卡片修改显示模块</p>
            </div>
            <div class="editExample-content">
              <div
                v-for="(item, index) in dlayout.items"
                :key="index"
                :class="{ 'editExample-content-item': true, 'active-editExample-item': activeExample.includes(item.componentName) }"
                @click="addStaging(item)"
              >
                <span>{{ item.componentTitle }}</span>
              </div>
            </div>
            <div class="editExample-footer">
              <el-button type="primary" plain :disabled="hasStatusFalg" @click="cancelStaging">取消</el-button>
              <el-button type="primary" @click="saveStaging">保存</el-button>
            </div>
          </div>
        </div>
      </div>
      <div v-show="tabsActive == 'zdsb'" class="content-main">
        <keyDeviceList :requestInfo="{ projectCode }" />
      </div>
      <alarmStatisticsDialog v-if="alarmStatisticsDialog" :projectCode="projectCode" :entityMenuCode="tabsActive" :visible.sync="alarmStatisticsDialog" />
      <transformerMonitorDialog v-if="transformerMonitorDialog" :requestInfo="{ projectCode, entityMenuCode: tabsActive }" :visible.sync="transformerMonitorDialog" />
      <envirMonitorDialog v-if="envirMonitorDialog" :requestInfo="{ projectCode, menuCode: tabsActive }" :visible.sync="envirMonitorDialog" />
    </div>
  </PageContainer>
</template>
<script>
import { debounce } from 'lodash/function'
import { Dashboard, DashLayout, DashItem } from 'vue-responsive-dash'
import { monitorTypeList } from '@/util/dict.js'
export default {
  name: 'powerOverview',
  components: {
    Dashboard,
    DashLayout,
    DashItem,
    envirMonitor: () => import('./overviewComponents/envirMonitor'),
    systemMonitor: () => import('./overviewComponents/systemMonitor'),
    operationMonitor: () => import('./overviewComponents/operationMonitor'),
    transformerMonitor: () => import('./overviewComponents/transformerMonitor'),
    patrolMonitor: () => import('./overviewComponents/patrolMonitor'),
    alarmAnalysis: () => import('./overviewComponents/alarmAnalysis'),
    electricityAnalysis: () => import('./overviewComponents/electricityAnalysis'),
    alarmStatisticsDialog: () => import('../components/alarmStatisticsDialog/index.vue'),
    transformerMonitorDialog: () => import('./components/transformerMonitorDialog.vue'),
    envirMonitorDialog: () => import('./components/envirMonitorDialog.vue'),
    keyDeviceList: () => import('./overviewComponents/keyDeviceList.vue')
  },
  data() {
    return {
      interval: null,
      tabsActive: '',
      // x=> 4 8 12 小中大
      dlayout: {
        breakpoint: 'xs',
        numberOfCols: 48,
        items: [
          // {id: 101, componentName: 'envirMonitor', componentTitle: '环境监测', x: 0, y: 0, width: 36, height: 5, dragAllowFrom: '.drag_class', resizable: false, draggable: false, status: 1 },
          // {id: 102, componentName: 'systemMonitor', componentTitle: '系统监测', x: 36, y: 0, width: 12, height: 5, dragAllowFrom: '.drag_class', resizable: false, draggable: false, status: 1 },
          // {id: 103, componentName: 'operationMonitor', componentTitle: '运行监测', x: 0, y: 5, width: 15, height: 11, dragAllowFrom: '.drag_class', resizable: false, draggable: false, status: 1 },
          // {id: 104, componentName: 'transformerMonitor', componentTitle: '变压器监测', x: 15, y: 5, width: 18, height: 11, dragAllowFrom: '.drag_class', resizable: false, draggable: false, status: 1 },
          // {id: 105, componentName: 'patrolMonitor', componentTitle: '巡检监测', x: 33, y: 5, width: 15, height: 11, dragAllowFrom: '.drag_class', resizable: false, draggable: false, status: 1 },
          // {id: 106, componentName: 'alarmAnalysis', componentTitle: '报警分析', x: 0, y: 16, width: 24, height: 11, dragAllowFrom: '.drag_class', resizable: false, draggable: false, status: 1 },
          // {id: 107, componentName: 'electricityAnalysis', componentTitle: '用电分析', x: 24, y: 16, width: 24, height: 11, dragAllowFrom: '.drag_class', resizable: false, draggable: false, status: 1 }
        ]
      },
      initItems: [
        { id: 'envirMonitor', componentName: 'envirMonitor', componentTitle: '环境监测', status: 1, x: 0, y: 0, width: 36, height: 5 },
        { id: 'systemMonitor', componentName: 'systemMonitor', componentTitle: '系统监测', status: 1, x: 36, y: 0, width: 12, height: 5 },
        { id: 'operationMonitor', componentName: 'operationMonitor', componentTitle: '运行监测', status: 1, x: 0, y: 5, width: 15, height: 12 },
        { id: 'transformerMonitor', componentName: 'transformerMonitor', componentTitle: '变压器监测', status: 1, x: 15, y: 5, width: 18, height: 12 },
        { id: 'patrolMonitor', componentName: 'patrolMonitor', componentTitle: '巡检监测', status: 1, x: 33, y: 5, width: 15, height: 12 },
        { id: 'alarmAnalysis', componentName: 'alarmAnalysis', componentTitle: '报警分析', status: 1, x: 0, y: 17, width: 24, height: 11 },
        { id: 'electricityAnalysis', componentName: 'electricityAnalysis', componentTitle: '用电分析', status: 1, x: 24, y: 17, width: 24, height: 11 }
      ],
      // stagingConfig: Object.freeze(stagingConfig),
      reservedItems: [],
      dashExampleShow: true, // 工作台显示隐藏
      editExampleShow: false, // 编辑工作台显示隐藏
      activeExample: [], // 当前激活的工作台
      firstLevelMenuList: [],
      projectCode: monitorTypeList.find((item) => item.projectName == '配电监测').projectCode,
      alarmStatisticsDialog: false,
      transformerMonitorDialog: false,
      envirMonitorDialog: false
    }
  },
  computed: {
    hasStatusFalg() {
      return this.dlayout.items.filter((e) => e.status === 0).length === this.dlayout.items.length
    }
  },
  created() {
    this.getParentMenuList()
  },
  methods: {
    // 鼠标移入
    viewMouseover: debounce(function () {
      console.log('鼠标移入')
      clearTimeout(this.interval)
    }, 1000),
    // 鼠标移出
    viewMouseleave: debounce(function () {
      console.log('鼠标移出')
      if (this.editExampleShow || this.alarmStatisticsDialog || this.transformerMonitorDialog || this.envirMonitorDialog) return
      let second = 5
      let index = -1
      clearTimeout(this.interval)
      this.interval = setInterval(() => {
        second--
        if (second < 1) {
          index += 1
          this.tabsActive = this.firstLevelMenuList[index].code ? this.firstLevelMenuList[index].code : 'zdsb'
          index = index == this.firstLevelMenuList.length - 1 ? -1 : index
          second = 5
        }
      }, 1000)
    }, 1000),
    // 获取一级菜单
    getParentMenuList() {
      this.$api.GetParentMenuList({ projectId: this.projectCode }).then((res) => {
        if (res.code == 200) {
          this.firstLevelMenuList = res.data
          this.tabsActive = res.data[0].code
          this.getWorktopManageList()
          this.viewMouseleave()
        }
      })
    },
    // tab切换
    tabClick(tab) {
      console.log(this.tabsActive)
    },
    // 获取工作台管理列表
    getWorktopManageList() {
      this.editExampleShow = false
      this.$store.commit('settings/dragSidebarCollapse', false)
      this.$api.getWorktopManageList({ userId: this.$store.state.user.userInfo.user.staffId, menuType: 7 }).then((res) => {
        if (res.code == 200) {
          const data = res.data && res.data.length ? res.data : this.initItems
          const items = []
          data.forEach((item) => {
            items.push({
              id: item.id,
              componentName: item.componentName,
              componentTitle: item.componentTitle,
              x: item.x,
              y: item.y,
              width: item.width,
              height: item.height,
              dragAllowFrom: '.drag_class',
              resizable: false,
              draggable: false,
              path: item.path,
              status: item.status
            })
          })
          this.dashExampleShow = false
          this.$nextTick(() => {
            this.dashExampleShow = true
            this.dlayout.items = items
            this.reservedItems = JSON.parse(JSON.stringify(items))
            const hasStatusLength = items.filter((e) => e.status === 0).length
            hasStatusLength == items.length ? this.allMoreOper('edit') : ''
          })
        }
      })
    },
    // 拖拽结束事件
    moveEnd(item) {
      console.log('moveEnd', this.dlayout)
    },
    // 缩放结束事件
    resizeEnd(item) {
      const resizeList = ['transformerMonitor']
      if (resizeList.includes(item.id)) {
        this.$refs[item.id][0].echartsResize()
      }
      // console.log('resizeEnd', this.dlayout)
    },
    // 添加/减少模块
    addStaging(item) {
      if (this.activeExample.includes(item.componentName)) {
        console.log('减少模块==========', item)
        this.activeExample.splice(this.activeExample.indexOf(item.componentName), 1)
        this.dlayout.items.map((e) => {
          if (e.componentName === item.componentName) {
            e.status = 0
          }
        })
      } else {
        console.log('添加模块==========', item)
        this.activeExample.push(item.componentName)
        this.dlayout.items.map((e) => {
          if (e.componentName === item.componentName) {
            e.status = 1
          }
        })
      }
    },
    // 取消编辑工作台模块
    cancelStaging() {
      this.editExampleShow = false
      this.dashExampleShow = false
      this.$nextTick(() => {
        this.dashExampleShow = true
        this.dlayout.items = JSON.parse(JSON.stringify(this.reservedItems))
        this.$store.commit('settings/dragSidebarCollapse', false)
      })
    },
    // 保存工作台模块
    saveStaging() {
      const items = this.dlayout.items
      const params = []
      items.forEach((item) => {
        params.push({
          id: item.id,
          componentName: item.componentName,
          componentTitle: item.componentTitle,
          path: item.path,
          status: item.status,
          x: item.x,
          y: item.y,
          width: item.width,
          height: item.height
        })
      })
      console.log('save提交', params)
      this.$api.saveWorktopManage({ jsonList: JSON.stringify(params), userId: this.$store.state.user.userInfo.user.staffId, menuType: 7 }).then((res) => {
        if (res.code == 200) {
          this.$message.success('保存成功')
          this.getWorktopManageList()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 更多操作事件
    allMoreOper(type, component) {
      if (type === 'more') {
        if (component == 'envirMonitor') {
          // 环境监测
          this.envirMonitorDialog = true
        } else if (component == 'transformerMonitor') {
          // 变压器监测
          this.transformerMonitorDialog = true
        } else if (component == 'alarmAnalysis') {
          // 报警分析
          this.alarmStatisticsDialog = true
        }
        console.log(type, component)
      } else if (type === 'edit') {
        this.dlayout.items.map((e) => {
          e.resizable = true
          e.draggable = true
        })
        this.activeExample = this.dlayout.items.filter((e) => e.status === 1).map((e) => e.componentName)
        this.editExampleShow = true
        this.$store.commit('settings/toggleSidebarCollapse', true)
        this.$store.commit('settings/dragSidebarCollapse', true)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.page-container {
  margin: 0;
  height: 100%;
  width: 100%;
}
.staging-content {
  height: 100%;
  width: 100%;
  :deep(.content-head) {
    margin: 10px 10px 0;
    background: #fff;
    border-radius: 4px;
    .el-tabs__header .el-tabs__nav-wrap::after {
      display: none;
    }
    .el-tabs__active-bar {
      bottom: 1px;
    }
    .el-tabs__item {
      padding: 0 20px !important;
    }
    .el-tabs__content {
      display: none;
    }
  }
  .content-main {
    width: 100%;
    height: calc(100% - 50px);
    display: flex;
    justify-content: space-between;
  }
  #dashExample {
    height: 100%;
    // background: #f5f7f9;
    overflow-y: auto;
    overflow-x: hidden;
    :deep(.placeholder) {
      background: #e2e6eb !important;
      border-radius: 10px;
      opacity: 1;
    }
  }
  #editExample {
    height: 100%;
    // background-color: #fff;
    box-shadow: 10px 0 10px -10px #c7c7c7;
    box-shadow: 10px 0 10px -10px #c7c7c7;
    padding: 16px 0;
    display: flex;
    flex-direction: column;
    .round-box {
      width: 100%;
      height: 100%;
      background-color: #08305d0a;
      border-radius: 10px;
      padding: 10px 0;
      display: flex;
      flex-direction: column;
    }
    .editExample-title {
      > div {
        font-size: 16px;
        font-family: PingFangSC-Regular-Blod;
        color: #121f3e;
        margin: 0 0 12px 15px;
        height: 22px;
        line-height: 22px;
      }
      > p {
        height: 18px;
        font-size: 12px;
        line-height: 18px;
        margin: 0 0 12px 15px;
        color: #999;
      }
    }
    .editExample-content {
      flex: 1;
      // height: calc(100% - 50px);
      overflow-y: auto;
      padding: 10px 20px;
      .editExample-content-item {
        cursor: pointer;
        width: 100%;
        box-sizing: border-box;
        height: 40px;
        line-height: 40px;
        margin-bottom: 16px;
        border-radius: 4px;
        padding: 0 10px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        background-color: #fff;
        text-align: center;
        color: #121f3e;
        font-size: 13px;
        font-family: 'PingFang SC-Regular', 'PingFang SC';
        &:hover {
          // color: #3562db;
          // background: rgba(53, 98, 219, 0.2);
          box-shadow: 0 4px 12px 0 rgb(17 19 23 / 10%);
        }
      }
      .active-editExample-item {
        background-color: #3562db !important;
        color: #fff !important;
        border-color: #3562db !important;
      }
    }
    .editExample-footer {
      text-align: right;
      padding: 5px 15px;
    }
  }
}
::-webkit-scrollbar-thumb:hover,
::-webkit-scrollbar-track:hover {
  background: transparent;
}
::-webkit-scrollbar-thumb {
  background-color: transparent;
}
</style>
