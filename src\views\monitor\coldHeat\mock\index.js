let teamTreeData = [
  // 监测数据
  {
    id: 96,
    projectId: 'b5587fbbe453422ebc937cc0b7c69wsx',
    state: 0,
    name: '给水系统',
    parentId: '#',
    parentIds: '#',
    createTime: 1653293144000,
    hospitalCode: '',
    unitCode: '',
    level: 1,
    code: '50f853ea8d5746bc99d82eb2532ce146',
    isdId: 3,
    isiId: 15,
    jsonStr: null,
    notTeamNodeRole: null,
    list: [
      {
        name: '水泵',
        status: 0, // 0关闭 1打开
        arr: [
          {
            dataName: '电流',
            num: '18',
            company: 'A'
          },
          {
            dataName: '电压',
            num: '18',
            company: 'V'
          },
          {
            dataName: '手自动状态',
            num: '自动',
            company: ''
          },
          {
            dataName: '频率',
            num: '50',
            company: 'hz'
          }
        ]
      },
      {
        name: '冷水机组',
        status: 1, // 0关闭 1打开
        arr: [
          {
            dataName: '电流',
            num: '18',
            company: 'A'
          },
          {
            dataName: '电压',
            num: '18',
            company: 'V'
          },
          {
            dataName: '离线状态',
            num: '在线',
            company: ''
          },
          {
            dataName: '冷却水供水温度',
            num: '18',
            company: '℃'
          },
          {
            dataName: '冷却水回水温度',
            num: '50',
            company: '℃'
          }
        ]
      },
      {
        name: '水泵',
        status: 1, // 0关闭 1打开
        arr: [
          {
            dataName: '电流',
            num: '18',
            company: 'A'
          },
          {
            dataName: '电压',
            num: '18',
            company: 'V'
          },
          {
            dataName: '手自动状态',
            num: '自动',
            company: ''
          },
          {
            dataName: '频率',
            num: '50',
            company: 'hz'
          }
        ]
      },
      {
        name: '冷水机组',
        status: 0, // 0关闭 1打开
        arr: [
          {
            dataName: '电流',
            num: '18',
            company: 'A'
          },
          {
            dataName: '电压',
            num: '18',
            company: 'V'
          },
          {
            dataName: '离线状态',
            num: '在线',
            company: ''
          },
          {
            dataName: '冷却水供水温度',
            num: '18',
            company: '℃'
          },
          {
            dataName: '冷却水回水温度',
            num: '50',
            company: '℃'
          }
        ]
      }
    ]
  },
  {
    id: 217,
    projectId: 'b5587fbbe453422ebc937cc0b7c69wsx',
    state: 0,
    name: '雨水系统',
    parentId: '#',
    parentIds: '#',
    createTime: 1679990131000,
    hospitalCode: null,
    unitCode: null,
    level: 1,
    code: '88dc2a835b8f4f7c9401885164c682b6',
    isdId: 3,
    isiId: 18,
    jsonStr: null,
    notTeamNodeRole: null,
    list: [
      {
        name: '水泵',
        status: 0, // 0关闭 1打开
        arr: [
          {
            dataName: '电流',
            num: '18',
            company: 'A'
          },
          {
            dataName: '电压',
            num: '18',
            company: 'V'
          },
          {
            dataName: '手自动状态',
            num: '自动',
            company: ''
          },
          {
            dataName: '频率',
            num: '50',
            company: 'hz'
          }
        ]
      },
      {
        name: '冷水机组',
        status: 1, // 0关闭 1打开
        arr: [
          {
            dataName: '电流',
            num: '18',
            company: 'A'
          },
          {
            dataName: '电压',
            num: '18',
            company: 'V'
          },
          {
            dataName: '离线状态',
            num: '在线',
            company: ''
          },
          {
            dataName: '冷却水供水温度',
            num: '18',
            company: '℃'
          },
          {
            dataName: '冷却水回水温度',
            num: '50',
            company: '℃'
          }
        ]
      },
      {
        name: '水泵',
        status: 1, // 0关闭 1打开
        arr: [
          {
            dataName: '电流',
            num: '18',
            company: 'A'
          },
          {
            dataName: '电压',
            num: '18',
            company: 'V'
          },
          {
            dataName: '手自动状态',
            num: '自动',
            company: ''
          },
          {
            dataName: '频率',
            num: '50',
            company: 'hz'
          }
        ]
      }
    ]
  }
]

let waterRunTimeData = {
  // 运行时长
  avgRate: '18%',
  hour: 35,
  minute: 0
}

let entityList = [
  // 运行时长图
  {
    time: '2023-04-25 09:59:39',
    value: '1'
  },
  {
    time: '2023-04-25 10:14:39',
    value: '2'
  },
  {
    time: '2023-04-25 10:29:39',
    value: '3'
  },
  {
    time: '2023-04-25 10:44:39',
    value: '1'
  },
  {
    time: '2023-04-25 10:44:39',
    value: '1'
  },
  {
    time: '2023-04-25 10:59:39',
    value: '1'
  },
  {
    time: '2023-04-25 11:14:39',
    value: '1'
  },
  {
    time: '2023-04-25 11:29:39',
    value: '1'
  },
  {
    time: '2023-04-25 11:44:39',
    value: '1'
  },
  {
    time: '2023-04-25 11:59:39',
    value: '54'
  },
  {
    time: '2023-04-25 12:14:39',
    value: '1'
  },
  {
    time: '2023-04-25 12:29:39',
    value: '1'
  },
  {
    time: '2023-04-25 12:44:39',
    value: '1'
  },
  {
    time: '2023-04-25 12:59:39',
    value: '1'
  },
  {
    time: '2023-04-25 13:14:39',
    value: '1'
  },
  {
    time: '2023-04-25 13:29:39',
    value: '1'
  },
  {
    time: '2023-04-25 13:44:39',
    value: '1'
  },
  {
    time: '2023-04-25 13:59:39',
    value: '43'
  },
  {
    time: '2023-04-25 14:14:39',
    value: '1'
  },
  {
    time: '2023-04-25 14:29:39',
    value: '1'
  },
  {
    time: '2023-04-25 14:44:39',
    value: '1'
  },
  {
    time: '2023-04-25 14:59:39',
    value: '1'
  },
  {
    time: '2023-04-25 15:14:39',
    value: '53'
  },
  {
    time: '2023-04-25 15:29:39',
    value: '1'
  },
  {
    time: '2023-04-25 15:44:39',
    value: '1'
  },
  {
    time: '2023-04-25 15:59:39',
    value: '1'
  },
  {
    time: '2023-04-25 16:14:39',
    value: '1'
  },
  {
    time: '2023-04-25 16:29:39',
    value: '1'
  },
  {
    time: '2023-04-25 16:44:39',
    value: '1'
  },
  {
    time: '2023-04-25 16:59:39',
    value: '1'
  },
  {
    time: '2023-04-25 17:14:39',
    value: '1'
  },
  {
    time: '2023-04-25 17:29:39',
    value: '1'
  },
  {
    time: '2023-04-25 17:44:39',
    value: '1'
  },
  {
    time: '2023-04-25 17:59:39',
    value: '1'
  },
  {
    time: '2023-04-25 18:14:39',
    value: '1'
  },
  {
    time: '2023-04-25 18:29:39',
    value: '1'
  },
  {
    time: '2023-04-25 18:44:39',
    value: '1'
  },
  {
    time: '2023-04-25 18:44:39',
    value: '1'
  }
]

let alarmData = {  // 报警统计
  total: 16,
  isDealCount: 8,
  policeList: [
    {
      menuName: '雨水系统',
      policeRate: '100%',
      policeCount: 16
    }
  ],
  type: 3,
  noDealCount: 8
}
export { teamTreeData, waterRunTimeData, entityList, alarmData }
