<!-- 数据报表 -->
<template>
    <PageContainer>
        <div slot="content" class="pow_content">
            <div class="content_left">
                <div class="title_box"><svg-icon name="right-arrow" /> 选择设备</div>
                <el-select v-model="filters.sysOf1" placeholder="所属品类" style="width: 230px; padding-top: 10px;"
                    filterable clearable @change="selectCategory">
                    <el-option v-for="(item, index) in deviceTypeList" :key="index" :label="item.dictionaryDetailsName"
                        :value="item.dictionaryDetailsId"></el-option>
                </el-select>
                <el-cascader ref="refHandle" :options="customGroupingList" style="width: 230px; padding-top: 10px;"
                    :props="{ checkStrictly: true, expandTrigger: 'hover' }" clearable placeholder="所属分组"
                    :show-all-levels="false" v-model="handlerValue" @change="handleChange"></el-cascader>
                <el-input v-model="filters.assetsNameCode" style="width: 230px; padding-top: 10px;"
                    placeholder="设备名称/编码" @blur="blurInput"></el-input>
                <div class="left-div">
                    <p>
                        <label>
                            <input type="checkbox" v-model="allSelected" @change="toggleAllSelection"> 全选
                        </label>
                    </p>
                    <p v-for="item in listData" :key="item.id" :class="{ selected: selectedId === item.id }"
                        :title="item.assetsName" class="left-divp">
                        <label>
                            <input type="checkbox" :checked="isSelected(item.id)"
                                @change="handleCheckboxChange(item.id)">
                            {{ item.assetsName }}
                        </label>
                    </p>
                </div>
            </div>
            <div class="content_right">
                <div class="typeTabs">
                    <el-tabs v-model="choiceTypeTab">
                        <el-tab-pane v-for="item in typeTabs" :key="item.type" style="height: calc(100%);"
                            :label="item.name" :name="item.type">
                            <div class="query_row">
                                <div class="query_left">
                                    <div v-if="item.dateTypeArr.length > 1">
                                        <div v-for="(v, i) in item.dateTypeArr" :key="i" :class="{
                                            'search-aside-item': true,
                                            'search-aside-item-active': selectedTimeType.dateType === v.dateType
                                        }" @click="onDateType(v)">
                                            {{ v.name }}
                                        </div>
                                    </div>
                                    <!-- 日选择 -->
                                    <div v-if="selectedTimeType.dateType == 'day'">
                                        <el-date-picker key="day" v-model="dateTime.day" type="date" placeholder="请选择日期"
                                            :picker-options="pickerOptions" :clearable="false"
                                            value-format="yyyy-MM-dd">
                                        </el-date-picker>
                                    </div>
                                    <!-- 月选择 -->
                                    <div v-else-if="selectedTimeType.dateType == 'month'">
                                        <el-date-picker key="month" v-model="dateTime.month" type="month"
                                            placeholder="请选择日期" :picker-options="pickerOptions" :clearable="false"
                                            value-format="yyyy-MM-dd">
                                        </el-date-picker>
                                    </div>
                                    <!-- 年选择 -->
                                    <div v-else-if="selectedTimeType.dateType == 'year'">
                                        <el-date-picker key="year" v-model="dateTime.year" type="year"
                                            placeholder="请选择日期" :picker-options="pickerOptions" :clearable="false"
                                            value-format="yyyy-MM-dd">
                                        </el-date-picker>
                                    </div>
                                    <!-- 自定义时间范围选择 -->
                                    <div v-else>
                                        <el-date-picker key="daterange" v-model="dateTime.custom" type="daterange"
                                            start-placeholder="开始日期" end-placeholder="结束日期"
                                            :picker-options="pickerOptions" :clearable="false"
                                            value-format="yyyy-MM-dd">
                                        </el-date-picker>
                                    </div>

                                    <el-button type="primary" plain class="re_btn" @click="reset">重置</el-button>
                                    <el-button type="primary" @click="submit">查询</el-button>
                                    <el-button type="primary" icon="el-icon-upload2"
                                        @click="exportExcel(choiceTypeTab)">导出</el-button>
                                    <el-button v-if="choiceTypeTab == 1" type="primary" icon="el-icon-setting"
                                        @click="openTimeSlot">分时段配置</el-button>
                                </div>
                            </div>
                            <div class="query_row">
                                <div class="query_left" style="margin:0 auto;font-size: 20px;">
                                    <div v-if="selectedTimeType.dateType != 'custom'" class="up_down">
                                        <span class="jump_date" @click="jumpDate('subtract')">&lt;</span>
                                        &nbsp;&nbsp; {{ displayedDate }}
                                        <span class="jump_date" :class="{ 'no_clicl': isNext }"
                                            @click="jumpDate('add')">&gt;</span>
                                    </div>
                                </div>
                            </div>
                            <el-select v-model="filters.unit" placeholder="请选择" style="width:150px;" filterable
                                @change="selectUnit">
                                <el-option v-for="(item, index) in unitList" :key="index" :label="item.name"
                                    :value="item.value"></el-option>
                            </el-select>
                            <div :id="`echartsEnergy${item.type}`" style="width: 100%;height: 200px">
                            </div>
                            <div class="my_tabel">
                                <TablePage id="my-table" ref="table" v-loading="tabelLoading" :showPage="false"
                                    :tableColumn="tableColumn" :data="tableData" height="100%" fixed />
                            </div>
                        </el-tab-pane>
                    </el-tabs>
                </div>
            </div>
            <TimeSlotConfigurationVue ref="timeSlotConfiguration" />
        </div>
    </PageContainer>
</template>

<script lang="jsx">
import moment from 'moment'
moment.locale('zh-cn')
import FileSaver from 'file-saver'
import TimeSlotConfigurationVue from './components/timeSlotConfiguration.vue'
import { transData } from '@/util'
import * as echarts from 'echarts';
import * as XLSX from 'xlsx';
import { newMonitorTypeList } from '@/util/newDict.js'
export default {
    name: 'intervalState',
    components: {
        TimeSlotConfigurationVue
    },
    data() {
        return {
            systemCode: this.$route.meta.systemType,
            deviceTypeList: [],//设备类型
            customGroupingList: [],//自定义分组
            unitList: [{
                name: 'kwh',
                value: 1
            }, {
                name: '万kwh',
                value: 2
            }],//图表单位
            customDefaultProps: {
                label: 'groupName',
                isLeaf: 'leaf',
                children: 'children'
            },
            handlerValue: [],
            listData: [],
            filters: {
                unit: 1,
                sysOf1: "",
                groupId: "",
                assetsNameCode: "",
                dataRange: [moment().format('YYYY-MM-DD 00:00:00'), moment().format('YYYY-MM-DD 23:59:59')],
            },
            selectedId: [], // 维护选中的 ID
            allSelected: false, // 全选状态
            typeTabs: [
                {
                    name: '用能数据',
                    type: '0',
                    api: 'getAssetsEnergyList',
                    dateTypeArr: [
                        {
                            dateType: 'day',
                            name: '日',
                            status: 0
                        },
                        {
                            dateType: 'month',
                            name: '月',
                            status: 1
                        },
                        {
                            dateType: 'year',
                            name: '年',
                            status: 2
                        }
                    ],
                    typeArr: []
                },
                {
                    name: '分时段用能',
                    type: '1',
                    api: 'getAssetsEnergySegment',
                    dateTypeArr: [
                        {
                            dateType: 'day',
                            name: '日',
                            status: 0
                        },
                        {
                            dateType: 'month',
                            name: '月',
                            status: 1
                        },
                        {
                            dateType: 'year',
                            name: '年',
                            status: 2
                        }
                    ],
                    typeArr: []
                }
            ],
            tableColumn: [],
            tableData: [],
            tabelLoading: false,
            selectedTimeType: {},   // 选择的筛选时间对象
            queryParams: {
                dateType: null,       //  时间类型
                endTime: null,
                startTime: null,
            },
            dateTime: {
                day: null,
                month: null,
                year: null,
                custom: []
            },  // 查询的日期时间
            choiceTypeTab: null,    // 类型  用能数据、分时段用能
            tabs: [],
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now()
                }
            },
            isShowTooltip: false,
            timeType: 1,
            chartX: [],
            chartY: [],
            isExporting: false, // 添加标志位
        }
    },
    computed: {
        displayedDate() {
            if (this.selectedTimeType.dateType === 'day') {
                return this.dateTime.day;
            } else if (this.selectedTimeType.dateType === 'month') {
                return moment(this.dateTime.month).format('YYYY-MM');
            } else if (this.selectedTimeType.dateType === 'year') {
                return moment(this.dateTime.year).format('YYYY');
            }
            return '';
        },
        isNext() {
            if (this.selectedTimeType.dateType == 'day' && this.dateTime.day) {
                return moment(this.dateTime.day).add(1, 'days').valueOf() > Date.now()
            } else if (this.selectedTimeType.dateType == 'month' && this.dateTime.month) {
                return moment(this.dateTime.month).add(1, 'month').valueOf() > Date.now()
            } else if (this.selectedTimeType.dateType == 'year' && this.dateTime.year) {
                return moment(this.dateTime.year).add(1, 'year').valueOf() > Date.now()
            } else {
                return false
            }
        }
    },
    watch: {
        'choiceTypeTab': function (val) {
            let obj = this.typeTabs.find(ele => ele.type == val)
            this.onDateType(obj.dateTypeArr[0])
            this.getDataList()
            this.filters.unit = 1
        },
    },
    mounted() {
        this.getDeviceType()//品类
        this.getAssetsGroup()//分组
        this.choiceTypeTab = this.typeTabs[0].type
    },
    methods: {
        appendEchartsData(chartX, chartY) {
            let chartElement = document.getElementById(`echartsEnergy${this.choiceTypeTab}`);
            chartElement.removeAttribute('_echarts_instance_');
            if (!chartElement) {
                return;
            }
            let myChart = echarts.init(chartElement);
            const colors = ['#df263d', '#f6a944', '#73a0fa', '#84c028']
            const baseData = {
                grid: {
                    left: '0%',
                    right: '1%',
                    bottom: '5%',
                    top: '5%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: chartX,
                    axisLabel: {
                        formatter: function (value) {
                            return value.length > 5 ? value.substring(0, 5) + '...' : value; // 截断文字
                        }
                    },
                },
                yAxis: {
                    type: 'value',
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                series: [
                    {
                        name: '用能',
                        type: 'bar',
                        data: chartY,
                        barWidth: this.choiceTypeTab === '0' ? '20px' : "50px",
                        itemStyle: {
                            color: function (params) {
                                if (this.choiceTypeTab === '1') {
                                    return colors[params.dataIndex % colors.length];
                                }
                                return '#3562db';
                            }.bind(this)
                        }
                    }
                ]
            }
            myChart.setOption(baseData)
            this.$forceUpdate()
            // 随着屏幕大小调节图表
            window.addEventListener('resize', () => {
                if (myChart) {
                    myChart.resize();
                }
            })
        },
        // 单位选择
        selectUnit(val) {
            if (val === 2) {
                this.chartY = this.chartY.map(value => value / 10000);
            } else {
                this.chartY = this.chartY.map(value => value * 10000);
            }
            this.appendEchartsData(this.chartX, this.chartY)
        },
        // 获取左侧监测项列表
        getDataList() {
            let params = {
                assetsNameCode: this.filters.assetsNameCode,
                dictionaryDetailsCode: this.systemCode,
                equipAttr: '2',
                groupId: this.filters.groupId,
                page: 1,
                pageSize: 1000,
                sysOf1: this.filters.sysOf1,
            }
            this.listData = []
            this.$api
                .getOperationalMonitoringList(params)
                .then((res) => {
                    if (res.code === '200' || res.code === 200) {
                        this.listData = res.data.records || []
                        this.getList()
                    }
                })
                .catch(() => {
                })
        },
        // 所属品类
        getDeviceType() {
            this.deviceTypeList = []
            let data = {
                dictionaryCode: this.systemCode,
                equipAttr: '2',
            }
            this.$api.querySubCategoryDetailsRelatedAssets(data).then((res) => {
                if (res.code == '200') {
                    this.deviceTypeList = res.data
                }
            })
        },
        selectCategory(val) {
            this.filters.sysOf1 = val
            this.getDataList()
        },
        // 所属分组
        getAssetsGroup() {
            this.customGroupingList = []
            let data = {
                dictionaryDetailsCode: this.systemCode,
                equipAttr: '2',
                groupName: "",
            }
            this.$api.getCustomGroupingTree(data).then((res) => {
                if (res.code == '200') {
                    this.customGroupingList = transData(res.data, 'id', 'pid', 'children')
                }
            })
        },
        blurInput(event) {
            this.filters.assetsNameCode = event.target.value.replace(/\s+/g, '')
            this.getDataList()
        },
        // 分组选择
        handleChange(value) {
            this.filters.groupId = value.length > 0 ? value[value.length - 1] : '';
            this.getDataList()
        },
        handleCheckboxChange(id) {
            const index = this.selectedId.indexOf(id);
            if (index > -1) {
                // 如果已经存在，移除选中
                this.selectedId.splice(index, 1);
            } else {
                // 如果不存在，添加选中
                this.selectedId.push(id);
            }
            this.updateAllSelected(); // 更新全选状态
        },
        isSelected(id) {
            return this.selectedId.includes(id);
        },
        toggleAllSelection() {
            if (this.allSelected) {
                // 如果全选被选中，添加所有 ID
                this.selectedId = this.listData.map(item => item.id);
            } else {
                // 如果全选被取消，清空选中状态
                this.selectedId = [];
            }
            this.updateAllSelected(); // 更新全选状态
        },
        updateAllSelected() {
            this.allSelected = this.selectedId.length === this.listData.length;
            this.getList()
        },
        // 重置
        reset() {
            this.filters.unit = 1
            this.selectedTimeType.dateType = 'day'
            this.queryParams.dateType = 0
            this.timeType = 1
            this.typeTabs = [
                {
                    name: '用能数据',
                    type: '0',
                    api: 'getAssetsEnergyList',
                    dateTypeArr: [
                        {
                            dateType: 'day',
                            name: '日',
                            status: 0
                        },
                        {
                            dateType: 'month',
                            name: '月',
                            status: 1
                        },
                        {
                            dateType: 'year',
                            name: '年',
                            status: 2
                        }
                    ],
                    typeArr: []
                },
                {
                    name: '分时段用能',
                    type: '1',
                    api: 'getAssetsEnergySegment',
                    dateTypeArr: [
                        {
                            dateType: 'day',
                            name: '日',
                            status: 0
                        },
                        {
                            dateType: 'month',
                            name: '月',
                            status: 1
                        },
                        {
                            dateType: 'year',
                            name: '年',
                            status: 2
                        }
                    ],
                    typeArr: []
                }
            ]
            this.resetDateTime()
            this.getList()
        },
        // 查询
        submit() {
            this.filters.unit = 1
            this.getList()
        },
        jumpDate(type) {
            if (this.selectedTimeType.dateType == 'day' && this.dateTime.day) {
                this.dateTime.day = moment(this.dateTime.day)[type](1, 'days').format('YYYY-MM-DD')
            } else if (this.selectedTimeType.dateType == 'month' && this.dateTime.month) {
                this.dateTime.month = moment(this.dateTime.month)[type](1, 'month').format('YYYY-MM-DD')
            } else if (this.selectedTimeType.dateType == 'year' && this.dateTime.year) {
                this.dateTime.year = moment(this.dateTime.year)[type](1, 'year').format('YYYY-MM-DD')
            }
            this.getList()
        },
        onDateType(v) {
            const dateTypeMapping = {
                day: 1,
                month: 3,
                year: 4
            };
            this.timeType = dateTypeMapping[v.dateType] || null;
            this.selectedTimeType = v
            this.queryParams.dateType = v.status
            this.resetDateTime()
        },
        resetDateTime() {
            let current = moment().format('YYYY-MM-DD')
            this.dateTime = {
                day: current,
                month: current,
                year: current,
                custom: [current, current]
            }
        },
        getList() {
            this.tabelLoading = true
            this.handleDateChange()
            const typeTab = this.typeTabs.find(ele => ele.type == this.choiceTypeTab)
            let param = {
                dictionaryDetailsCode: 'PDXT',
                assetsIds: this.selectedId,
                endTime: this.queryParams.endTime,
                startTime: this.queryParams.startTime,
                timeType: this.timeType,
            }
            this.$api[typeTab.api](param).then(res => {
                this.tableColumn = []
                this.tableData = []
                this.chartX = []
                this.chartY = []
                this.$nextTick(() => {
                    setTimeout(() => {
                        if (typeTab.type == '0') { // 用能数据
                            this.handleEnergyUsageSurface(res.data)
                        } else if (typeTab.type == '1') {   // 分时段用能
                            this.handleDayparting(res.data)
                        }
                        this.chartX = res.data.chartX
                        this.chartY = res.data.chartY
                        this.appendEchartsData(this.chartX, this.chartY)
                        this.tabelLoading = false
                    }, 100)
                })
            })
        },
        handleDateChange() {
            // 时间类型(0:日 1:月 2:年 3:自定义)
            let type = this.queryParams.dateType
            if (type == 0) {
                this.queryParams.startTime = this.dateTime.day
                this.queryParams.endTime = this.dateTime.day
            } else if (type == 1) {
                this.queryParams.startTime = moment(this.dateTime.month).startOf('month').format('YYYY-MM-DD')
                this.queryParams.endTime = moment(this.dateTime.month).endOf('month').format('YYYY-MM-DD')
            } else if (type == 2) {
                this.queryParams.startTime = moment(this.dateTime.year).startOf('year').format('YYYY-MM-DD')
                this.queryParams.endTime = moment(this.dateTime.year).endOf('year').format('YYYY-MM-DD')
            } else if (type == 3) {
                this.queryParams.startTime = this.dateTime.custom[0]
                this.queryParams.endTime = this.dateTime.custom[1]
            }
        },
        // 用能数据
        handleEnergyUsageSurface(data) {
            this.tableData = []
            // 表头
            this.tableColumn = data.header.map((headerItem, index) => {
                // 创建表头对象
                return {
                    label: headerItem,
                    prop: headerItem, // 根据实际数据字段设置
                    minWidth: 100,
                    fixed: index < 3 ? true : false // 前两列固定
                };
            });
            // 表数据
            this.tableData = data.body.map(item => {
                const rowData = {};
                // 根据表头动态填充行数据
                data.header.forEach(headerItem => {
                    rowData[headerItem] = item[headerItem] || 0; // 如果没有数据则设置为 0
                });
                return rowData;
            });
        },
        // 分时段用能
        handleDayparting(data) {
            this.tableData = []
            // 表头
            this.tableColumn = data.header.map((headerItem, index) => {
                // 创建表头对象
                return {
                    label: headerItem,
                    prop: headerItem, // 根据实际数据字段设置
                    minWidth: 100,
                    fixed: index < 3 ? true : false // 前两列固定
                };
            });
            // 表数据
            this.tableData = data.body.map(item => {
                const rowData = {};
                // 根据表头动态填充行数据
                data.header.forEach(headerItem => {
                    rowData[headerItem] = item[headerItem] || 0; // 如果没有数据则设置为 0
                });
                return rowData;
            });
        },
        // 分时段配置
        openTimeSlot() {
            this.$refs.timeSlotConfiguration.getData()
        },
        // 导出
        exportExcel() {
            if (!this.tableData.length) {
                this.$message.error('暂无导出数据');
                return;
            }
            // 转换表格数据为数组的数组
            const formattedData = this.tableData.map(item => {
                return this.tableColumn.map(col => item[col.prop]); // 根据表头字段取值
            });
            // 添加表头
            formattedData.unshift(this.tableColumn.map(col => col.label));
            const ws = XLSX.utils.aoa_to_sheet(formattedData);
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
            // 定义列宽
            ws['!cols'] = [
                { wpx: 150 },
                { wpx: 100 },
                // 根据需要继续添加列宽
            ];
            const wbout = XLSX.write(wb, {
                bookType: 'xlsx',
                bookSST: true,
                type: 'array'
            });
            try {
                FileSaver.saveAs(
                    new Blob([wbout], { type: 'application/octet-stream' }),
                    '数据报表.xlsx'
                );
            } catch (e) {
                console.error('导出错误:', e);
            }
        },
    }
}
</script>

<style lang="scss" scoped>
::v-deep .el-tab-pane {
    height: calc(100%) !important;
}

::v-deep .el-tabs {
    height: calc(100%) !important;
}

::v-deep .el-tabs__content {
    height: calc(100%) !important;
}

::v-deep .el-table thead.is-group th.el-table__cell {
    background-color: #ededf5;
}

.search-aside-item {
    display: inline-block;
    font-size: 14px;
    padding: 0 20px;
    height: 32px;
    line-height: 32px;
    font-family: PingFangSC-Regular;
    color: $color-primary;
    border: 1px solid $color-primary;
    background: #fff;
    cursor: pointer;
    margin-right: 5px;

    &:hover,
    &:focus {
        color: #fff;
        font-family: PingFangSC-Regular;
        border-color: $color-primary;
        background-color: $color-primary;
        font-weight: 500;
    }
}

.search-aside-item-active {
    color: #fff;
    font-family: PingFangSC-Regular;
    border-color: $color-primary;
    background-color: $color-primary;
    font-weight: 500;
}

::v-deep .el-tabs__header .el-tabs__nav-wrap::after {
    height: 1px !important;
    background-color: #dcdfe6 !important;
}

.pow_header {
    background: #fff;
    border-radius: 4px;

    .title {
        border-bottom: 1px solid #dcdfe6;

        &_p {
            padding: 19px 16px;
        }
    }

    .tabs {
        padding: 0 16px;
    }
}

.pow_content {
    margin-top: 16px;
    // background: #fff;
    height: 100%;
    display: flex;

    .content_left {
        text-align: center;
        width: 250px;
        height: 100%;
        background-color: #fff;
        border-radius: 5px;
        overflow: hidden;

        .left-div {
            text-align: left;
            margin-top: 10px;
            overflow-y: auto;
            height: calc(100% - 215px);

            .selected {
                color: #007bff;
            }

            p {
                cursor: pointer;
                padding-left: 35px;
                width: 230px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        .title_box {
            box-sizing: border-box;
            padding-left: 24px;
            height: 50px;
            width: 100%;
            line-height: 50px;
            text-align: left;
            border-bottom: 1px solid #d8dee7;
        }
    }

    .content_right {
        border-radius: 5px;
        padding: 10px;
        background: #fff;
        width: 0;
        flex: 1;
        margin-left: 10px;

        .typeTabs {
            padding: 0 16px;
            height: 100%;
        }

        .query_row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 16px 0;

            .left-center {

                p {
                    text-align: center;
                    font-size: 22px;
                    margin-top: 10px;
                }
            }

            .query_left {
                display: flex;
                align-items: center;


                .up_down {
                    display: flex;
                    margin-right: 6px;

                    .jump_date {
                        cursor: pointer;
                        margin-left: 16px;

                        font-size: 20px;
                        color: #3562db;
                    }

                    .no_clicl {
                        cursor: not-allowed;
                        opacity: 0.5;
                        pointer-events: none;
                        color: #414653;
                    }
                }

                .re_btn {
                    margin-left: 10px;
                }
            }
        }

        .btn_row {
            padding: 0 16px;
            background: #fff;
            border-radius: 4px;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 16px;

            .harmonicOrder {
                display: flex;
                align-items: center;

                span {
                    font-size: 14px;
                }
            }
        }

        .my_tabel {
            height: calc(100% - 394px);
        }
    }
}
</style>