<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%;">
      <div class="role-content-left">
        <div class="toptip">
          <span class="green_line"></span>
          参数类别
        </div>
        <div class="left_content">
          <ul>
            <li :class="{ pitchOn: setType == 1 }" @click="setType = 1">消息设置</li>
            <li :class="{ pitchOn: setType == 2 }" @click="setType = 2">风险巡检设置</li>
            <li :class="{ pitchOn: setType == 3 }" @click="overTimeHandle">隐患超时配置</li>
            <li :class="{ pitchOn: setType == 4 }" @click="riskHandle">风险评价配置</li>
          </ul>
        </div>
      </div>
      <div class="role-content-right">
        <!-- 消息设置 -->
        <div v-show="setType == 1" class="message_set">
          <div class="title_h">风险巡检消息提醒</div>
          <div class="parameter_box">
            <el-form ref="form" label-width="120px">
              <div class="parameter_row">
                <el-form-item label="提醒方式">
                  <el-checkbox-group v-model="form.checkList[16].value">
                    <el-checkbox label="1">App</el-checkbox>
                    <el-checkbox label="2" disabled>短信</el-checkbox>
                    <el-checkbox label="3" disabled>微信</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </div>
              <div class="parameter_row">
                <el-form-item label="即时提醒">
                  <el-radio v-model="form.checkList[15].value" label="1">开启</el-radio>
                  <el-radio v-model="form.checkList[15].value" label="2">关闭</el-radio>
                </el-form-item>
              </div>
              <div class="parameter_row">
                <el-form-item label="提前提醒">
                  <el-radio-group v-model="form.checkList[14].value[0]" @change="riskChange">
                    <el-radio label="1">开启</el-radio>
                    <el-radio label="2">关闭</el-radio>
                  </el-radio-group>
                </el-form-item>
                <div class="parameter_time">
                  任务开始前
                  <div class="tiem_input">
                    <el-input v-model="form.checkList[14].value[1]" oninput="value=value.replace(/[^\d]/g,'')" class="input_param" placeholder=""></el-input>
                  </div>
                  分钟开启提醒，每隔
                  <div class="tiem_input">
                    <el-input v-model="form.checkList[14].value[2]" oninput="value=value.replace(/[^\d]/g,'')" class="input_param" placeholder=""></el-input>
                  </div>
                  分钟提醒
                  <div class="tiem_input">
                    <el-input class="input_param" value="1" placeholder="" oninput="value=value.replace(/[^\d]/g,'')" disabled></el-input>
                  </div>
                  次，
                </div>
              </div>
              <div class="parameter_row">
                <el-form-item label="信息接收人">
                  <el-radio v-model="form.checkList[13].value" label="1">部门</el-radio>
                  <el-radio v-model="form.checkList[13].value" label="2">责任人</el-radio>
                </el-form-item>
              </div>
            </el-form>
          </div>
          <div class="title_h">隐患排查消息提醒</div>
          <div class="parameter_box">
            <el-form ref="form" label-width="120px">
              <div class="parameter_row">
                <el-form-item label="提醒方式">
                  <el-checkbox-group v-model="form.checkList[12].value">
                    <el-checkbox label="1">App</el-checkbox>
                    <el-checkbox label="2" disabled>短信</el-checkbox>
                    <el-checkbox label="3" disabled>微信</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </div>
              <div class="parameter_row">
                <el-form-item label="即时提醒">
                  <el-radio v-model="form.checkList[11].value" label="1">开启</el-radio>
                  <el-radio v-model="form.checkList[11].value" label="2">关闭</el-radio>
                </el-form-item>
              </div>
              <div class="parameter_row">
                <el-form-item label="提前提醒">
                  <el-radio-group v-model="form.checkList[10].value[0]" @change="troubleshootChange">
                    <el-radio label="1">开启</el-radio>
                    <el-radio label="2">关闭</el-radio>
                  </el-radio-group>
                </el-form-item>
                <div class="parameter_time">
                  任务开始前
                  <div class="tiem_input">
                    <el-input v-model="form.checkList[10].value[1]" class="input_param" oninput="value=value.replace(/[^\d]/g,'')" placeholder=""></el-input>
                  </div>
                  分钟开启提醒，每隔
                  <div class="tiem_input">
                    <el-input v-model="form.checkList[10].value[2]" class="input_param" oninput="value=value.replace(/[^\d]/g,'')" placeholder=""></el-input>
                  </div>
                  分钟提醒
                  <div class="tiem_input">
                    <el-input class="input_param" value="1" placeholder="" oninput="value=value.replace(/[^\d]/g,'')" disabled></el-input>
                  </div>
                  次，
                </div>
              </div>
              <div class="parameter_row">
                <el-form-item label="超期提醒">
                  <el-radio-group v-model="form.checkList[17].value[0]" @change="troubleshootChange">
                    <el-radio label="1">开启</el-radio>
                    <el-radio label="2">关闭</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-time-select
                  v-model="form.checkList[17].value[1]"
                  class="input_param"
                  style="margin-left: 25px;"
                  :picker-options="{
                    start: '08:00',
                    step: '00:15',
                    end: '18:30'
                  }"
                  placeholder="请选择提醒时间"
                >
                </el-time-select>
              </div>
              <div class="parameter_row">
                <el-form-item label="信息接收人">
                  <el-radio v-model="form.checkList[9].value" label="1">部门</el-radio>
                  <el-radio v-model="form.checkList[9].value" label="2" disabled>责任人</el-radio>
                </el-form-item>
              </div>
            </el-form>
          </div>
          <div class="title_h">隐患管理消息提醒</div>
          <div class="parameter_box">
            <el-form ref="form" label-width="120px">
              <div class="parameter_row">
                <el-form-item label="提醒方式">
                  <el-checkbox-group v-model="form.checkList[8].value">
                    <el-checkbox label="1">App</el-checkbox>
                    <el-checkbox label="2" disabled>短信</el-checkbox>
                    <el-checkbox label="3" disabled>微信</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </div>
              <div class="parameter_row">
                <el-form-item label="即时提醒">
                  <el-radio v-model="form.checkList[7].value" label="1">开启</el-radio>
                  <el-radio v-model="form.checkList[7].value" label="2">关闭</el-radio>
                </el-form-item>
              </div>
              <div class="parameter_row">
                <el-form-item label="全院工单池提醒">
                  <el-radio v-model="form.checkList[6].value" label="1">开启</el-radio>
                  <el-radio v-model="form.checkList[6].value" label="2">关闭</el-radio>
                </el-form-item>
              </div>
              <div class="parameter_row">
                <el-form-item label="部门工单池提醒">
                  <el-radio v-model="form.checkList[5].value" label="1">开启</el-radio>
                  <el-radio v-model="form.checkList[5].value" label="2">关闭</el-radio>
                </el-form-item>
              </div>
              <div class="parameter_row">
                <el-form-item label="转派工单提醒">
                  <el-radio v-model="form.checkList[4].value" label="1">开启</el-radio>
                  <el-radio v-model="form.checkList[4].value" label="2">关闭</el-radio>
                </el-form-item>
                <el-form-item label="信息接收人">
                  <el-radio v-model="form.checkList[3].value" label="1">部门</el-radio>
                  <el-radio v-model="form.checkList[3].value" label="2">责任人</el-radio>
                </el-form-item>
              </div>
              <div class="parameter_row">
                <el-form-item label="指派工单提醒">
                  <el-radio v-model="form.checkList[2].value" label="1">开启</el-radio>
                  <el-radio v-model="form.checkList[2].value" label="2">关闭</el-radio>
                </el-form-item>
                <el-form-item label="信息接收人">
                  <el-radio v-model="form.checkList[1].value" label="1">部门</el-radio>
                  <el-radio v-model="form.checkList[1].value" label="2">责任人</el-radio>
                </el-form-item>
              </div>
            </el-form>
          </div>
          <!-- 处理按钮 -->
          <div>
            <el-button type="primary" @click="reset">重置</el-button>
            <el-button type="primary" @click="submit">保存</el-button>
          </div>
        </div>
        <!-- 风险巡检设置 -->
        <div v-show="setType == 2" class="risk_patrol_setup">
          <div class="title_h">风险巡检设置</div>
          <div class="parameter_box">
            <el-form ref="form" label-width="120px">
              <div class="parameter_row">
                <el-form-item label="风险点巡检方式">
                  <!-- <el-checkbox-group v-model="form.checkList[0].value">
                    <el-checkbox label="1">扫码巡检</el-checkbox>
                    <el-checkbox label="2">直接巡检</el-checkbox>
                  </el-checkbox-group> -->
                  <el-radio-group v-model="form.checkList[0].value[0]">
                    <el-radio label="1">扫码巡检</el-radio>
                    <el-radio label="2">直接巡检</el-radio>
                  </el-radio-group>
                </el-form-item>
              </div>
            </el-form>
          </div>
          <!-- 处理按钮 -->
          <div>
            <el-button type="primary" @click="reset">重置</el-button>
            <el-button type="primary" @click="submit">保存</el-button>
          </div>
        </div>

        <!-- 隐患超时配置 -->
        <div v-show="setType == 3" v-loading="overTimeLoading" class="risk_patrol_setup">
          <div class="title_h">风险巡检消息提醒</div>
          <div class="parameter_box">
            <el-form ref="overTimeForm" label-width="200px" class="over-time">
              <el-form-item label="上级单位隐患整改超时预警">
                <el-input v-model="parentOverTime.timeout" style="width: 200px;"></el-input>
                <span class="over-unit">小时</span>
              </el-form-item>
              <br />
              <el-form-item label="单位自查隐患整改超时预警">
                <el-input v-model="unitOverTime.timeout" style="width: 200px;"></el-input>
                <span class="over-unit">小时</span>
              </el-form-item>
              <br />
              <el-form-item label="认领指派超时预警">
                <el-input v-model="assignedOverTime.timeout" style="width: 200px;"></el-input>
                <span class="over-unit">小时</span>
              </el-form-item>
            </el-form>
          </div>
          <!-- 处理按钮 -->
          <div>
            <el-button type="primary" @click="getOverTimeData">重置</el-button>
            <el-button type="primary" @click="saveOverTime">保存</el-button>
          </div>
        </div>

        <!-- 风险评价配置 -->
        <div v-show="setType == 4" class="risk_patrol_setup">
          <div class="title_h">作业活动评价模板</div>
          <div class="">
            <el-radio v-model="templateForm.activities" label="1"
            >模板一
              <table-template type="1" :isActivities="true"></table-template>
            </el-radio>
            <el-radio v-model="templateForm.activities" label="2"
            >模板二
              <table-template type="2"></table-template>
            </el-radio>
          </div>
          <div class="title_h">设备设施评价模板</div>
          <div class="">
            <el-radio v-model="templateForm.facilities" label="1"
            >模板一
              <table-template type="1"></table-template>
            </el-radio>
            <el-radio v-model="templateForm.facilities" label="2"
            >模板二
              <table-template type="2"></table-template>
            </el-radio>
          </div>
          <div class="title_h">区域场所评价模板</div>
          <div class="">
            <el-radio v-model="templateForm.regional" label="1"
            >模板一
              <table-template type="1"></table-template>
            </el-radio>
            <el-radio v-model="templateForm.regional" label="2"
            >模板二
              <table-template type="2"></table-template>
            </el-radio>
          </div>
          <div>
            <el-button type="primary" style="margin-bottom: 5px;" @click="getRiskEvaluationData">重置</el-button>
            <el-button type="primary" @click="saveRiskEvaluation">保存</el-button>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>

<script>
import tableTemplate from './components/TableTemplate.vue'
export default {
  components: {
    tableTemplate
  },
  data() {
    return {
      setType: 1,
      form: {
        checkList: ''
      },
      hiddenForm: {},
      templateForm: {
        activities: '', // 作业活动
        facilities: '', // 设备设施
        regional: '' // 区域场所
      },
      loading: false,
      data: [],
      tableData: [],
      overTimeLoading: false,
      parentOverTime: '',
      unitOverTime: '',
      assignedOverTime: ''
    }
  },
  activated() {},
  mounted() {
    this.getData()
  },
  created() {},
  methods: {
    riskChange(val) {
      if (val == 2) {
        this.form.checkList[14].value[1] = 0
        this.form.checkList[14].value[2] = 0
      } else {
        this.form.checkList[14].value[1] = 10
        this.form.checkList[14].value[2] = 1
      }
      this.$forceUpdate()
    },
    troubleshootChange(val) {
      if (val == 2) {
        this.form.checkList[10].value[1] = 0
        this.form.checkList[10].value[2] = 0
      } else {
        this.form.checkList[10].value[1] = 10
        this.form.checkList[10].value[2] = 1
      }
      this.$forceUpdate()
    },
    /**
     * 参数配置回显
     */
    getData() {
      this.loading = true
      this.form.checkList = ''
      this.data = []
      this.$api.ipsmGetConfigurationListData({}).then((res) => {
        this.loading = false
        if (res.code == 200) {
          this.form.checkList = res.data
          // 数据回显处理
          // 风险巡检设置----风险巡检方式
          this.form.checkList[0].value = res.data[0].value.split(',')
          // 风险巡检消息提醒----提醒方式
          this.form.checkList[16].value = res.data[16].value
          // 风险巡检消息提醒----提前提醒
          this.form.checkList[14].value = res.data[14].value.split(',')

          // 隐患排查消息提醒----提醒方式
          this.form.checkList[12].value = res.data[12].value.split(',')
          // 隐患排查消息提醒----提前提醒
          this.form.checkList[10].value = res.data[10].value.split(',')
          // 隐患排查消息提醒----超期提醒
          this.form.checkList[17].value = res.data[17].value.split(',')
          // 隐患管理消息提醒----提醒方式
          this.form.checkList[8].value = res.data[8].value.split(',')
        }
      })
    },
    /**
     *  保存
     */
    submit() {
      let baseInfo = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      this.form.checkList.forEach((item) => {
        this.data.push(item)
      })
      // this.data = this.form.checkList;
      // 风险巡检设置----风险巡检方式
      this.data[0].value = this.data[0].value.join(',')
      // 风险巡检消息提醒----提醒方式
      this.data[16].value = this.data[16].value
      // 风险巡检消息提醒----提前提醒
      if (this.data[14].value[0] == 2) {
        this.data[14].value = '2'
      } else {
        this.data[14].value = this.data[14].value.join(',')
      }
      // 隐患排查消息提醒----提醒方式
      this.data[12].value = this.data[12].value.join(',')
      // 隐患排查消息提醒----提前提醒
      // console.log(this.data[10].value);
      if (this.data[10].value[0] == 2) {
        this.data[10].value = '2'
      } else {
        this.data[10].value = this.data[10].value.join(',')
      }
      // 隐患排查消息提醒----超期提醒
      // console.log(this.data[10].value);
      if (this.data[17].value[0] == 2) {
        this.data[17].value = '2'
      } else {
        this.data[17].value = this.data[17].value.join(',')
      }
      // 隐患管理消息提醒----提醒方式
      this.data[8].value = this.data[8].value.join(',')

      this.data.forEach((item, i) => {
        this.data[i].unitCode = baseInfo.unitCode
        this.data[i].hospitalCode = baseInfo.hospitalCode
      })
      this.$api.ipsmSetConfigurationSave(JSON.stringify(this.data)).then((res) => {
        if (res.code == 200) {
          this.$message.success(res.message)
          this.getData()
          // location.reload();
        } else {
          this.$message.error(res.message)
          location.reload()
        }
      })
    },
    /**
     * 修改重置
     */
    reset() {
      this.getData()
    },
    overTimeHandle() {
      this.setType = 3
      this.getOverTimeData()
    },
    getOverTimeData() {
      this.overTimeLoading = true
      this.$api.ipsmGetTimeOutConfig({}).then((res) => {
        if (res.code == 200) {
          this.overTimeLoading = false
          res.data.forEach((item) => {
            if (item.free1 == '1') {
              this.parentOverTime = item
            }
            if (item.free1 == '2') {
              this.unitOverTime = item
            }
            if (item.free1 == '3') {
              this.assignedOverTime = item
            }
          })
        }
      })
    },
    saveOverTime() {
      let loginData = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      let timeOutList = [
        {
          id: this.parentOverTime.id,
          timeout: this.parentOverTime.timeout,
          updateName: loginData.name,
          updateCode: loginData.id
        },
        {
          id: this.unitOverTime.id,
          timeout: this.unitOverTime.timeout,
          updateName: loginData.name,
          updateCode: loginData.id
        },
        {
          id: this.assignedOverTime.id,
          timeout: this.assignedOverTime.timeout,
          updateName: loginData.name,
          updateCode: loginData.id
        }
      ]
      this.$api.ipsmSaveTimeOut({ timeOutList }).then((res) => {
        if (res.code == 200) {
          this.$message.success(res.message)
          this.getOverTimeData()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    riskHandle() {
      this.setType = 4
      this.getRiskEvaluationData()
    },
    getRiskEvaluationData() {
      this.$api.ipsmGetRiskEvaluation({ dictType: 'risk_evaluation' }).then((res) => {
        if (res.code == 200) {
          this.templateForm.activities = res.data.activities
          this.templateForm.facilities = res.data.facilities
          this.templateForm.regional = res.data.regional
        } else {
          this.$message.error(res.message)
        }
      })
    },
    saveRiskEvaluation() {
      let params = {
        dictType: 'risk_evaluation',
        remarks: JSON.stringify(this.templateForm)
      }
      console.log(params)
      this.$api.ipsmSaveRiskEvaluation(params).then((res) => {
        if (res.code == 200) {
          this.$message.success(res.message)
          this.getRiskEvaluationData()
        } else {
          this.$message.error(res.message)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.role-content {
  height: 100%;
  display: flex;

  .role-content-left {
    width: 246px;
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    margin-right: 12px;

    .left_content {
      height: calc(100% - 60px);
      overflow: auto;

      ul {
        padding: 0;

        li {
          height: 38px;
          width: 100%;
          font-size: 15px;
          font-family: "PingFang SC-Regular", "PingFang SC";
          font-weight: 400;
          color: #606266;
          line-height: 38px;
          list-style-type: none;
          box-sizing: border-box;
          cursor: pointer;
          padding-left: 27px;

          &:hover {
            background: rgb(53 98 219 / 20%);
            border-radius: 4px 0 0 4px;
          }
        }
      }

      .pitchOn {
        color: #3562db;
        background: linear-gradient(to right, #d9e1f8, #fff);
        font-weight: 500;
      }
    }
  }

  .role-content-right {
    width: calc(100% - 258px);
    height: 100%;
    padding: 10px 10px 0 30px;
    background: #fff;
    border-radius: 4px;
    overflow: auto;
    // flex: 1;
    .message_set {
      height: calc(100% - 40px);
      width: 100%;
      overflow: auto;

      .parameter_box {
        box-sizing: border-box;
        padding-left: 50px;

        .parameter_row {
          width: 100%;
          display: flex;
          align-items: center;

          .parameter_time {
            margin-left: 25px;
            display: flex;
            align-items: center;
            font-size: 14px;
            vertical-align: baseline;
          }

          .tiem_input {
            width: 50px;
          }
        }
      }

      .btn {
        position: absolute;
        bottom: 16px;
      }
    }

    .title_h {
      font-size: 16px;
      font-weight: 500;
      color: #121f3e;
      display: flex;
      align-items: center;
      margin: 15px 0;
    }
  }
}

.el-form-item {
  margin-bottom: 0 !important;
}
</style>
