<template>
  <el-dialog v-dialogDrag title="选择" :visible.sync="selectDialogVisible" width="70%" top="10vh" :before-close="handleClose" :modal="modal" custom-class="model-dialog">
    <div style="display: flex" class="outermost">
      <div class="left">
        <div>
          <el-collapse v-model="activeName" accordion @change="collChange">
            <el-collapse-item v-for="item in hospitalList" :key="item.umId" :title="item.unitComName" :name="item.umId">
              <template slot="title">
                <el-tooltip class="item" effect="light" :content="item.unitComName" placement="top-start" :disabled="item.unitComName && item.unitComName.length < 13">
                  <span class="collapseTitle">{{ item.unitComName }}</span>
                </el-tooltip>
              </template>
              <el-tree v-loading="collLoading" :data="treeList" :props="defaultProps" @node-click="handleNodeClick"></el-tree>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
      <div class="right">
        <div style="display: flex; margin-bottom: 10px">
          <!-- <el-input v-model.trim="form.keyword" placeholder="关键词搜索" class="ipt"></el-input> -->
          <el-input v-model="form.staffName" placeholder="请输入姓名" clearable style="width: 150px; margin-right: 10px"></el-input>
          <el-input v-model="form.mobile" placeholder="请输入手机号" clearable style="width: 150px; margin-right: 10px"></el-input>
          <el-select v-model.trim="form.job" placeholder="请选择在职状态">
            <el-option v-for="item in job" :key="item.value" :label="item.name" :value="item.value"> </el-option>
          </el-select>
          <el-select v-model.trim="form.sex" placeholder="请选择性别">
            <el-option v-for="item in sex" :key="item.value" :label="item.name" :value="item.value"> </el-option>
          </el-select>
          <el-select v-model.trim="form.quarters" placeholder="请选择岗位">
            <el-option v-for="item in quarters" :key="item.id" :label="item.postName" :value="item.id"> </el-option>
          </el-select>

          <el-button type="primary" plain @click="reset">重置</el-button>
          <el-button type="primary" @click="inquiry">查询</el-button>
        </div>
        <div>
          <el-table
            ref="table"
            v-loading="loading"
            :data="tableData"
            :height="tableHeight"
            :row-key="getRowKeys"
            :header-cell-style="{ 'text-align': 'center' }"
            tooltip-effect="dark"
            style="width: 100%"
            border
            stripe
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" align="center" :reserve-selection="true"> </el-table-column>
            <el-table-column type="index" label="序号" prop="" width="50" align="center">
              <template slot-scope="scope">
                <span>{{ (currentPage - 1) * pageSize + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="staffNumber" label="职工工号" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="staffName" label="人员姓名" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="sex" label="性别" align="center"> </el-table-column>
            <el-table-column prop="mobile" label="手机号码" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop=" phone" label="办公电话" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="entryData" label="入职天数" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="unitName" label="归属单位" width="80" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="officeName" label="所属部门" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="postName" label="岗位" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="jobName" label="职务" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="stationStatus" label="在职状态" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="activationFlagStr" label="激活状态" align="center" show-overflow-tooltip> </el-table-column>
          </el-table>
          <el-pagination
            :current-page="currentPage"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            :page-size="pageSize"
            :page-sizes="[15, 30, 50]"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="handleClose">取消</el-button>
      <el-button type="primary" @click="qd">确定</el-button>
    </span>
  </el-dialog>
</template>
  <script>
import { transData, ListTree } from '@/util'
export default {
  props: {
    selectMode: {
      // 1多选 2单选
      type: String,
      default: '1'
    },
    visible: {
      type: Boolean
    },
    type: {
      type: String
    },
    messageType: {
      type: String
    },
    defaultSelectedUser: {
      // 默认选中数据
      type: Array,
      default: () => []
    },
    modal: {
      type: Boolean,
      default: true
    },
    selectDialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      collLoading: true,
      loading: true,
      defaultProps: {
        children: 'children',
        label: 'deptName'
      },
      activeName: '',
      pmId: '',
      officeId: '',
      form: {
        staffName: '', // 姓名
        mobile: '', // 手机号
        quarters: '', // 岗位
        keyword: '', // 关键词
        job: '', // 职务
        sex: '' // 性别
      },
      job: [
        {
          value: '0',
          name: '在职'
        },
        {
          value: '1',
          name: '离职'
        }
      ],
      sex: [
        {
          value: '2',
          name: '女'
        },
        {
          value: '1',
          name: '男'
        }
      ],
      quarters: [],
      pageSize: 15,
      currentPage: 1,
      total: 0,
      tableData: [],
      multipleSelection: [],
      treeList: [],
      spaces: [],
      hospitalList: []
    }
  },
  computed: {
    tableHeight() {
      return document.body.clientHeight - 470
    }
  },
  created() {},
  mounted() {
    this.getTreeList()
    this.getLersonnelList()
    this.getQuarters()
  },
  methods: {
    // 获取岗位信息
    getQuarters() {
      this.$api.selectByList().then((res) => {
        if (res.code == 200) {
          this.quarters = res.data
        }
      })
    },
    handleNodeClick(val) {
      this.pmId = ''
      this.officeId = val.id
      this.getLersonnelList()
    },
    collChange(val) {
      this.officeId = ''
      this.pmId = val
      this.getLersonnelList()
      if (val != '') {
        this.treeList = []
        this.$api.getSelectedDept({ unitId: val }).then((res) => {
          this.collLoading = false
          if (res.code == 200) {
            this.spaces = res.data
            this.treeList = transData(res.data, 'id', 'pid', 'children')
          }
        })
      } else {
        this.getLersonnelList()
      }
      this.collLoading = true
    },
    getRowKeys(row) {
      return row.id
    },
    getTreeList() {
      this.$api.getSelected({}).then((res) => {
        if (res.code == 200) {
          this.hospitalList = res.data
        }
      })
    },
    getLersonnelList() {
      let params = {
        current: this.currentPage,
        size: this.pageSize,
        pmId: this.pmId,
        sex: this.form.sex,
        stationStatus: this.form.job,

        staffName: this.form.staffName, // 姓名
        mobile: this.form.mobile, // 手机号
        officeId: this.officeId,
        postId: this.form.quarters // 岗位
      }
      this.$api.staffList(params).then((res) => {
        this.loading = false
        if (res.code == 200) {
          res.data.records.forEach((item) => {
            if (item.sex == 2) {
              item.sex = '女'
            } else if (item.sex == 1) {
              item.sex = '男'
            }
            if (item.stationStatus == 0) {
              item.stationStatus = '在职'
            } else {
              item.stationStatus = '离职'
            }
          })
          this.tableData = res.data.records
          this.total = res.data.total
          this.defaultSelectedUser.length &&
            this.defaultSelectedUser.forEach((item) => {
              this.tableData.forEach((v, i) => {
                if (item == v.id) {
                  this.$refs.table.toggleRowSelection(v)
                }
              })
            })
        }
      })
    },
    // 查询
    inquiry() {
      this.getLersonnelList()
    },
    // 重置
    reset() {
      this.pageSize = 15
      this.currentPage = 1
      this.form.quarters = ''
      this.form.staffName = ''
      this.form.job = ''
      this.form.sex = ''
      this.form.mobile = ''
      this.getLersonnelList()
    },

    handleClose(val) {
      this.$refs.table.clearSelection()
      this.$emit('updateVisible', val)
    },
    qd() {
      if (this.multipleSelection.length == 0) {
        this.$message({
          showClose: true,
          message: '至少选择一个人员',
          type: 'warning'
        })
      } else {
        if (this.selectMode == '2' && this.multipleSelection.length > 1) {
          this.$message({
            message: '仅可选择单个人员',
            type: 'warning'
          })
          return
        }
        this.$emit('advancedSearchFn', this.multipleSelection, this.type, this.messageType)
        this.$refs.table.clearSelection()
      }
    },
    // 查询列表数据
    getTableList() {},
    // 监听事件
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.getLersonnelList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getLersonnelList()
    }
  }
}
</script>
  
  <style lang="scss" scoped>
.outermost {
  width: 100%;
  max-height: 600px;
  border: 1px solid #eee;
  padding: 10px;
}

.left {
  padding-right: 10px;
  width: 230px;
  height: 100%;

  > div {
    width: 100%;
    padding: 10px;
    height: 100%;
    background-color: #fff;
    overflow: auto;
  }
}

.right {
  background-color: #fff;
  padding: 10px;
  width: calc(100% - 230px);
  height: 100%;
}

.el-pagination {
  // display: flex;
  // justify-content: flex-end;
  margin-top: 5px;
}

.model-dialog {
  z-index: 9999;
  padding: 10 !important;
}

::v-deep .el-select {
  width: 100px !important;
}

.el-select {
  width: 180px !important;
  margin-right: 10px;
}

::v-deep .model-dialog .el-dialog__body {
  overflow-y: hidden !important;
}
::v-deep .collapseTitle {
  width: 80%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
  