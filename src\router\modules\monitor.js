import Layout from '@/layout'
import EmptyLayout from '@/layout/empty'
export default [
  {
    path: '/overview',
    component: Layout,
    redirect: '/overview/operOverview',
    name: 'overview',
    meta: {
      title: '总览',
      menuAuth: '/overview/operOverview'
      // classType: 'menu-item-center' // 用于控制左侧菜单的样式 居中显示
    },
    children: [
      {
        path: 'operOverview',
        name: 'operOverview',
        component: () => import('@/views/monitor/overview/overview.vue'),
        meta: {
          title: '总览',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/overview'
        }
      }
    ]
  },
  {
    path: '/powSystem',
    component: Layout,
    name: 'powSystem',
    redirect: '/powSystem/intervalState',
    meta: {
      title: '系统监测',
      menuAuth: '/powSystem'
      // arrangement: 'column',  // 用于控制左侧菜单的排列方式 一行三个
      // classType: 'row33' // 用于控制左侧菜单的样式 一行三个
    },
    children: [
      {
        path: 'intervalState',
        component: EmptyLayout,
        redirect: { name: 'intervalState' },
        meta: {
          title: '区间状态配置',
          menuAuth: '/powSystem/intervalState'
        },
        children: [
          {
            path: '',
            name: 'intervalState',
            component: () => import('@/views/monitor/powSystem/intervalState.vue'),
            meta: {
              title: '区间状态配置',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'intervalMonitorForm',
            name: 'intervalMonitorForm',
            component: () => import('@/views/monitor/powSystem/form/intervalMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/powSystem/intervalState'
            }
          }
        ]
      },
      {
        path: 'dictManagement',
        component: EmptyLayout,
        redirect: { name: 'dictManagement' },
        meta: {
          title: '字典管理',
          menuAuth: '/powSystem/dictManagement'
        },
        children: [
          {
            path: '',
            name: 'dictManagement',
            component: () => import('@/views/monitor/powSystem/dictManagement.vue'),
            meta: {
              title: '字典管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'intervalMonitorForm',
            name: 'intervalMonitorForm',
            component: () => import('@/views/monitor/powSystem/form/intervalMonitorForm.vue'),
            meta: {
              title: '字典配置',
              sidebar: false,
              activeMenu: '/powSystem/dictManagement'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/powerDistribution',
    component: Layout,
    name: 'powerDistribution',
    redirect: '/powerDistribution/overview',
    meta: {
      title: '配电运行监测',
      menuAuth: '/powerDistribution'
      // arrangement: 'column',  // 用于控制左侧菜单的排列方式 一行三个
      // classType: 'row33' // 用于控制左侧菜单的样式 一行三个
    },
    children: [
      {
        path: 'overview',
        component: EmptyLayout,
        redirect: { name: 'overview' },
        meta: {
          title: '运行总览',
          menuAuth: '/powerDistribution/overview'
        },
        children: [
          {
            path: '',
            name: 'overview',
            component: () => import('@/views/monitor/powerDistribution/overview.vue'),
            meta: {
              title: '运行总览',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'videoMonitor',
            name: 'VideoMonitor',
            component: () => import('@/views/monitor/powerDistribution/videoMonitor.vue'),
            meta: {
              title: '视频监控',
              sidebar: false,
              activeMenu: '/powerDistribution/overview'
            }
          },
          {
            path: 'systemDiagram',
            name: 'SystemDiagram',
            component: () => import('@/views/monitor/powerDistribution/systemDiagram.vue'),
            meta: {
              title: '配电系统图',
              sidebar: false,
              activeMenu: '/powerDistribution/overview'
            }
          }
        ]
      },
      {
        path: 'power',
        component: EmptyLayout,
        redirect: { name: 'power' },
        meta: {
          title: '电力数据',
          menuAuth: '/powerDistribution/power'
        },
        children: [
          {
            path: '',
            name: 'power',
            component: () => import('@/views/monitor/powerDistribution/power.vue'),
            meta: {
              title: '电力数据',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'reportForms',
        component: EmptyLayout,
        redirect: { name: 'reportForms' },
        meta: {
          title: '数据报表',
          menuAuth: '/powerDistribution/reportForms'
        },
        children: [
          {
            path: '',
            name: 'reportForms',
            component: () => import('@/views/monitor/powerDistribution/reportForms.vue'),
            meta: {
              title: '数据报表',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      }
    ]
  },
  {
    path: '/medicalGas',
    component: Layout,
    name: 'medicalGas',
    redirect: '/medicalGas/realtimeMonitor',
    meta: {
      title: '医用气体',
      menuAuth: '/medicalGas'
      // arrangement: 'column',  // 用于控制左侧菜单的排列方式 一行三个
      // classType: 'row33' // 用于控制左侧菜单的样式 一行三个
    },
    children: [
      {
        path: 'realtimeMonitor',
        component: EmptyLayout,
        redirect: { name: 'realtimeMonitor' },
        meta: {
          title: '实时监测',
          menuAuth: '/medicalGas/realtimeMonitor'
        },
        children: [
          {
            path: '',
            name: 'realtimeMonitor',
            component: () => import('@/views/monitor/medicalGas/realtimeMonitor.vue'),
            meta: {
              title: '实时监测',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'monitorDetails',
            name: 'monitorDetails',
            component: () => import('@/views/monitor/components/monitorDetail/index.vue'),
            meta: {
              title: '监测详情',
              sidebar: false,
              activeMenu: '/medicalGas/realtimeMonitor'
            }
          }
        ]
      },
      {
        path: 'alarmCenter',
        component: EmptyLayout,
        redirect: { name: 'alarmCenter' },
        meta: {
          title: '报警中心',
          menuAuth: '/medicalGas/alarmCenter'
        },
        children: [
          {
            path: '',
            name: 'alarmCenter',
            component: () => import('@/views/monitor/medicalGas/alarmCenter.vue'),
            meta: {
              title: '报警中心',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'policeDetails',
            name: 'policeDetails',
            component: () => import('@/views/monitor/commonPage/elecAlarmCenter/policeDetails.vue'),
            meta: {
              title: '报警详情',
              sidebar: false,
              activeMenu: '/medicalGas/alarmCenter'
            }
          }
        ]
      },
      // {
      //   path: 'alarmCenter',
      //   component: EmptyLayout,
      //   redirect: { name: 'alarmCenter' },
      //   meta: {
      //     title: '报警中心',
      //     menuAuth: '/medicalGas/alarmCenter'
      //   },
      //   children: [
      //     {
      //       path: '',
      //       name: 'alarmCenter',
      //       component: () => import('@/views/monitor/medicalGas/alarmCenter.vue'),
      //       meta: {
      //         title: '报警中心',
      //         sidebar: false,
      //         breadcrumb: false
      //       }
      //     },
      //     {
      //       path: 'policeDetails',
      //       name: 'policeDetails',
      //       component: () => import('@/views/monitor/commonPage/elecAlarmCenter/policeDetails.vue'),
      //       meta: {
      //         title: '报警详情',
      //         sidebar: false,
      //         activeMenu: '/medicalGas/alarmCenter'
      //       }
      //     }
      //   ]
      // },
      {
        path: 'filledRecord',
        component: EmptyLayout,
        redirect: { name: 'filledRecord' },
        meta: {
          title: '充装记录',
          menuAuth: '/medicalGas/filledRecord'
        },
        children: [
          {
            path: '',
            name: 'filledRecord',
            component: () => import('@/views/monitor/medicalGas/filledRecord.vue'),
            meta: {
              title: '充装记录',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'statisticalAnalysis',
        component: EmptyLayout,
        redirect: { name: 'statisticalAnalysis' },
        meta: {
          title: '统计分析',
          menuAuth: '/medicalGas/statisticalAnalysis'
        },
        children: [
          {
            path: '',
            name: 'statisticalAnalysis',
            component: () => import('@/views/monitor/medicalGas/statisticalAnalysis.vue'),
            meta: {
              title: '统计分析',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      }
    ]
  },
  {
    path: '/upsMenu',
    component: Layout,
    name: 'upsMenu',
    redirect: '/upsMenu/upsMonitor',
    meta: {
      title: 'UPS运行监测',
      menuAuth: '/upsMenu'
      // arrangement: 'column',  // 用于控制左侧菜单的排列方式 一行三个
      // classType: 'row33' // 用于控制左侧菜单的样式 一行三个
    },
    children: [
      {
        path: 'upsMonitor',
        component: EmptyLayout,
        redirect: { name: 'upsMonitor' },
        meta: {
          title: '运行监测',
          menuAuth: '/upsMenu/upsMonitor'
        },
        children: [
          {
            path: '',
            name: 'upsMonitor',
            component: () => import('@/views/monitor/upsMenu/upsMonitor.vue'),
            meta: {
              title: '运行监测',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'policeDetails',
            name: 'policeDetails',
            component: () => import('@/views/monitor/commonPage/elecAlarmCenter/policeDetails.vue'),
            meta: {
              title: '报警详情',
              sidebar: false,
              activeMenu: '/upsMenu/upsMonitor'
            }
          },
          {
            path: 'monitorDetails',
            name: 'monitorDetails',
            component: () => import('@/views/monitor/components/monitorDetail/index.vue'),
            meta: {
              title: '监测详情',
              sidebar: false,
              activeMenu: '/upsMenu/upsMonitor'
            }
          },
          {
            path: 'batteryDetail',
            name: 'batteryDetail',
            component: () => import('@/views/monitor/components/monitorDetail/index.vue'),
            meta: {
              title: '电池监测详情',
              sidebar: false,
              activeMenu: '/upsMenu/upsMonitor/monitorDetails'
            }
          }
        ]
      }
      // {
      //   path: 'alarmCenter',
      //   component: EmptyLayout,
      //   redirect: { name: 'alarmCenter' },
      //   meta: {
      //     title: '报警中心',
      //     menuAuth: '/upsMonitor/alarmCenter'
      //   },
      //   children: [
      //     {
      //       path: '',
      //       name: 'alarmCenter',
      //       component: () => import('@/views/monitor/medicalGas/alarmCenter.vue'),
      //       meta: {
      //         title: '报警中心',
      //         sidebar: false,
      //         breadcrumb: false
      //       }
      //     },
      //     {
      //       path: 'policeDetails',
      //       name: 'policeDetails',
      //       component: () => import('@/views/monitor/commonPage/elecAlarmCenter/policeDetails.vue'),
      //       meta: {
      //         title: '报警详情',
      //         sidebar: false,
      //         activeMenu: '/upsMenu/upsMonitor'
      //       }
      //     }
      //   ]
      // }
    ]
  },
  {
    path: '/sewageMenu',
    component: Layout,
    name: 'sewageMenu',
    redirect: '/sewageMenu/sewageMonitor',
    meta: {
      title: '污水运行监测',
      menuAuth: '/sewageMenu'
      // arrangement: 'column',  // 用于控制左侧菜单的排列方式 一行三个
      // classType: 'row33' // 用于控制左侧菜单的样式 一行三个
    },
    children: [
      {
        path: 'sewageMonitor',
        component: EmptyLayout,
        redirect: { name: 'sewageMonitor' },
        meta: {
          title: '运行监测',
          menuAuth: '/sewageMenu/sewageMonitor'
        },
        children: [
          {
            path: '',
            name: 'sewageMonitor',
            component: () => import('@/views/monitor/sewageMenu/sewageMonitor.vue'),
            meta: {
              title: '运行监测',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'policeDetails',
            name: 'policeDetails',
            component: () => import('@/views/monitor/commonPage/elecAlarmCenter/policeDetails.vue'),
            meta: {
              title: '报警详情',
              sidebar: false,
              activeMenu: '/sewageMenu/sewageMonitor'
            }
          },
          {
            path: 'monitorDetails',
            name: 'monitorDetails',
            component: () => import('@/views/monitor/components/monitorDetail/index.vue'),
            meta: {
              title: '监测详情',
              sidebar: false,
              activeMenu: '/sewageMenu/sewageMonitor'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/airMenu',
    component: Layout,
    redirect: '/airMenu/airOverview',
    name: 'airMenu',
    meta: {
      title: '空调',
      menuAuth: '/airMenu'
      // arrangement: 'column',  // 用于控制左侧菜单的排列方式 一行三个
      // classType: 'row33' // 用于控制左侧菜单的样式 一行三个
    },
    children: [
      {
        path: 'airOverview',
        component: EmptyLayout,
        redirect: { name: 'airOverview' },
        meta: {
          title: '运行总览',
          menuAuth: '/airMenu/airOverview'
        },
        children: [
          {
            path: '',
            name: 'airOverview',
            component: () => import('@/views/monitor/airMenu/newAirOverview.vue'),
            meta: {
              title: '运行总览',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'airMonitor',
        component: EmptyLayout,
        redirect: { name: 'airMonitor' },
        meta: {
          title: '运行监测',
          menuAuth: '/airMenu/airMonitor'
        },
        children: [
          {
            path: '',
            name: 'airMonitor',
            component: () => import('@/views/monitor/airMenu/airMonitor.vue'),
            meta: {
              title: '运行监测',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'monitorDetails',
            name: 'monitorDetails',
            component: () => import('@/views/monitor/components/monitorDetail/index.vue'),
            meta: {
              title: '监测详情',
              sidebar: false,
              activeMenu: '/airMenu/airMonitor'
            }
          }
        ]
      },
      {
        path: 'groupConfig',
        component: EmptyLayout,
        redirect: { name: 'groupConfig' },
        meta: {
          title: '分组配置',
          menuAuth: '/airMenu/groupConfig'
        },
        children: [
          {
            path: '',
            name: 'groupConfig',
            component: () => import('@/views/monitor/airMenu/groupConfig.vue'),
            meta: {
              title: '分组配置',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'controlStrategy',
        component: EmptyLayout,
        redirect: { name: 'controlStrategy' },
        meta: {
          title: '控制策略',
          menuAuth: '/airMenu/controlStrategy'
        },
        children: [
          {
            path: '',
            name: 'controlStrategy',
            component: () => import('@/views/monitor/airMenu/controlStrategy.vue'),
            meta: {
              title: '控制策略',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'runCalendar',
        component: EmptyLayout,
        redirect: { name: 'runCalendar' },
        meta: {
          title: '运行日历',
          menuAuth: '/airMenu/runCalendar'
        },
        children: [
          {
            path: '',
            name: 'runCalendar',
            component: () => import('@/views/monitor/airMenu/runCalendar.vue'),
            meta: {
              title: '运行日历',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'logging',
        component: EmptyLayout,
        redirect: { name: 'logging' },
        meta: {
          title: '日志记录',
          menuAuth: '/airMenu/logging'
        },
        children: [
          {
            path: '',
            name: 'logging',
            component: () => import('@/views/monitor/airMenu/airLogging.vue'),
            meta: {
              title: '日志记录',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'airReport',
        component: EmptyLayout,
        redirect: { name: 'airReport' },
        meta: {
          title: '报告报表',
          menuAuth: '/airMenu/airReport'
        },
        children: [
          {
            path: '',
            name: 'airReport',
            component: () => import('@/views/monitor/airMenu/airReport.vue'),
            meta: {
              title: '报告报表',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'airAnalysis',
        component: EmptyLayout,
        redirect: { name: 'airAnalysis' },
        meta: {
          title: '统计分析',
          menuAuth: '/airMenu/airAnalysis'
        },
        children: [
          {
            path: '',
            name: 'airAnalysis',
            component: () => import('@/views/monitor/airMenu/airAnalysis.vue'),
            meta: {
              title: '统计分析',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      }
    ]
  },
  {
    path: '/lightingMonitoring',
    component: Layout,
    name: 'lightingMonitoring',
    redirect: '/lightingMonitoring/operationMonitoring',
    meta: {
      title: '智慧照明',
      menuAuth: '/lightingMonitoring'
      // arrangement: 'column',  // 用于控制左侧菜单的排列方式 一行三个
      // classType: 'row33' // 用于控制左侧菜单的样式 一行三个
    },
    children: [
      {
        path: 'operationMonitoring',
        component: EmptyLayout,
        redirect: { name: 'operationMonitoring' },
        meta: {
          title: '运行监测',
          menuAuth: '/lightingMonitoring/operationMonitoring'
        },
        children: [
          {
            path: '',
            name: 'operationMonitoring',
            component: () => import('@/views/monitor/lightingMonitoring/operationMonitoring/operationMonitoring.vue'),
            meta: {
              title: '运行监测',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'operationView',
        component: EmptyLayout,
        redirect: { name: 'operationView' },
        meta: {
          title: '运行总览',
          menuAuth: '/lightingMonitoring/operationView'
        },
        children: [
          {
            path: '',
            name: 'operationView',
            component: () => import('@/views/monitor/lightingMonitoring/operationView/operationView.vue'),
            meta: {
              title: '运行总览',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'lightingGroupConfig',
        component: EmptyLayout,
        redirect: { name: 'lightingGroupConfig' },
        meta: {
          title: '照明分组配置',
          menuAuth: '/lightingMonitoring/lightingGroupConfig'
        },
        children: [
          {
            path: '',
            name: 'lightingGroupConfig',
            component: () => import('@/views/monitor/lightingMonitoring/lightingGroupConfig/lightingGroupConfig.vue'),
            meta: {
              title: '照明分组配置',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'controlStrategy',
        component: EmptyLayout,
        redirect: { name: 'controlStrategy' },
        meta: {
          title: '控制策略管理',
          menuAuth: '/lightingMonitoring/controlStrategy'
        },
        children: [
          {
            path: '',
            name: 'controlStrategy',
            component: () => import('@/views/monitor/lightingMonitoring/controlStrategy/controlStrategy.vue'),
            meta: {
              title: '控制策略管理',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'todaysOperationMode',
        component: EmptyLayout,
        redirect: { name: 'todaysOperationMode' },
        meta: {
          title: '今日运行模式',
          menuAuth: '/lightingMonitoring/todaysOperationMode'
        },
        children: [
          {
            path: '',
            name: 'todaysOperationMode',
            component: () => import('@/views/monitor/lightingMonitoring/todaysOperationMode/todaysOperationMode.vue'),
            meta: {
              title: '今日运行模式',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'runCalendar',
        component: EmptyLayout,
        redirect: { name: 'runCalendar' },
        meta: {
          title: '运行日历管理',
          menuAuth: '/lightingMonitoring/runCalendar'
        },
        children: [
          {
            path: '',
            name: 'runCalendar',
            component: () => import('@/views/monitor/lightingMonitoring/runCalendar/runCalendar.vue'),
            meta: {
              title: '运行日历管理',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'floorPlanModel',
        component: EmptyLayout,
        redirect: { name: 'floorPlanModel' },
        meta: {
          title: '楼层平面图',
          menuAuth: '/lightingMonitoring/floorPlanModel'
        },
        children: [
          {
            path: '',
            name: 'floorPlanModel',
            component: () => import('@/views/monitor/lightingMonitoring/floorPlanModel/index.vue'),
            meta: {
              title: '楼层平面图',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'airConditioningMonitoringRecord',
        component: EmptyLayout,
        redirect: { name: 'airConditioningMonitoringRecord' },
        meta: {
          title: '监控记录',
          menuAuth: '/lightingMonitoring/airConditioningMonitoringRecord'
        },
        children: [
          {
            path: '',
            name: 'runCalendar',
            component: () => import('@/views/monitor/lightingMonitoring/airConditioningMonitoringRecord/index.vue'),
            meta: {
              title: '监控记录',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'airConditioningOperationRecord',
        component: EmptyLayout,
        redirect: { name: 'airConditioningOperationRecord' },
        meta: {
          title: '操作记录',
          menuAuth: '/lightingMonitoring/airConditioningOperationRecord'
        },
        children: [
          {
            path: '',
            name: 'airConditioningOperationRecord',
            component: () => import('@/views/monitor/lightingMonitoring/airConditioningOperationRecord/index.vue'),
            meta: {
              title: '操作记录',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'lightingLogging',
        component: EmptyLayout,
        redirect: { name: 'lightingLogging' },
        meta: {
          title: '日志记录',
          menuAuth: '/lightingMonitoring/lightingLogging'
        },
        children: [
          {
            path: '',
            name: 'lightingLogging',
            component: () => import('@/views/monitor/lightingMonitoring/lightingLogging/index.vue'),
            meta: {
              title: '日志记录',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'jointControlConfig',
        component: EmptyLayout,
        redirect: { name: 'jointControlConfig' },
        meta: {
          title: '联控配置',
          menuAuth: '/lightingMonitoring/jointControlConfig'
        },
        children: [
          {
            path: '',
            name: 'jointControlConfig',
            component: () => import('@/views/monitor/lightingMonitoring/jointControlConfig/index.vue'),
            meta: {
              title: '联控配置',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      }
    ]
  },
  {
    path: '/envirMenu',
    component: Layout,
    name: 'envirMenu',
    redirect: '/envirMenu/envirOverview',
    meta: {
      title: '环境',
      menuAuth: '/envirMenu'
      // arrangement: 'column',  // 用于控制左侧菜单的排列方式 一行三个
      // classType: 'row33' // 用于控制左侧菜单的样式 一行三个
    },
    children: [
      {
        path: 'envirOverview',
        component: EmptyLayout,
        redirect: { name: 'envirOverview' },
        meta: {
          title: '运行总览',
          menuAuth: '/envirMenu/envirOverview'
        },
        children: [
          {
            path: '',
            name: 'envirOverview',
            component: () => import('@/views/monitor/envirMenu/envirOverview.vue'),
            meta: {
              title: '运行总览',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'envirMonitor',
        component: EmptyLayout,
        redirect: { name: 'envirMonitor' },
        meta: {
          title: '运行监测',
          menuAuth: '/envirMenu/envirMonitor'
        },
        children: [
          {
            path: '',
            name: 'envirMonitor',
            component: () => import('@/views/monitor/envirMenu/envirMonitor.vue'),
            meta: {
              title: '运行监测',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'dyeingConfig',
            name: 'DyeingConfig',
            component: () => import('@/views/monitor/envirMenu/dyeingConfig.vue'),
            meta: {
              title: '染色区间配置',
              sidebar: false,
              activeMenu: '/envirMenu/envirMonitor'
            }
          },
          {
            path: 'monitorDetails',
            name: 'monitorDetails',
            component: () => import('@/views/monitor/components/monitorDetail/index.vue'),
            // name: 'MonitorDetails',
            // component: () => import('@/views/monitor/envirMenu/monitorDetails.vue'),
            meta: {
              title: '监测详情',
              sidebar: false,
              activeMenu: '/envirMenu/envirMonitor'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/sewerageMenu',
    component: Layout,
    name: 'sewerageMenu',
    redirect: '/sewerageMenu/sewerageOverview',
    meta: {
      title: '给排水',
      menuAuth: '/sewerageMenu'
      // arrangement: 'column',  // 用于控制左侧菜单的排列方式 一行三个
      // classType: 'row33' // 用于控制左侧菜单的样式 一行三个
    },
    children: [
      {
        path: 'sewerageOverview',
        component: EmptyLayout,
        redirect: { name: 'sewerageOverview' },
        meta: {
          title: '运行总览',
          menuAuth: '/sewerageMenu/sewerageOverview'
        },
        children: [
          {
            path: '',
            name: 'sewerageOverview',
            component: () => import('@/views/monitor/sewerageMenu/sewerageOverview.vue'),
            meta: {
              title: '运行总览',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'sewerageMonitor',
        component: EmptyLayout,
        redirect: { name: 'sewerageMonitor' },
        meta: {
          title: '运行监测',
          menuAuth: '/sewerageMenu/sewerageMonitor'
        },
        children: [
          {
            path: '',
            name: 'sewerageMonitor',
            component: () => import('@/views/monitor/sewerageMenu/sewerageMonitor.vue'),
            meta: {
              title: '运行监测',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'monitorDetails',
            name: 'monitorDetails',
            component: () => import('@/views/monitor/components/monitorDetail/index.vue'),
            // name: 'MonitorDetails',
            // component: () => import('@/views/monitor/sewerageMenu/monitorDetails.vue'),
            meta: {
              title: '监测详情',
              sidebar: false,
              activeMenu: '/sewerageMenu/sewerageMonitor'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/coldHeat',
    component: Layout,
    redirect: '/coldHeat/coldOverview',
    name: 'coldHeat',
    meta: {
      title: '冷热源运行监测',
      menuAuth: '/coldHeat'
      // arrangement: 'column',  // 用于控制左侧菜单的排列方式 一行三个
      // classType: 'row33' // 用于控制左侧菜单的样式 一行三个
    },
    children: [
      {
        path: 'coldOverview',
        component: EmptyLayout,
        redirect: { name: 'coldOverview' },
        meta: {
          title: '运行总览',
          menuAuth: '/coldHeat/coldOverview'
        },
        children: [
          {
            path: '',
            name: 'coldOverview',
            component: () => import('@/views/monitor/coldHeat/coldOverview.vue'),
            meta: {
              title: '运行总览',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'coldMonitor',
        component: EmptyLayout,
        redirect: { name: 'coldMonitor' },
        meta: {
          title: '运行监测',
          menuAuth: '/coldHeat/coldMonitor'
        },
        children: [
          {
            path: '',
            name: 'coldMonitor',
            component: () => import('@/views/monitor/coldHeat/coldMonitor.vue'),
            meta: {
              title: '运行监测',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'monitorDetails',
            name: 'monitorDetails',
            component: () => import('@/views/monitor/components/monitorDetail/index.vue'),
            // path: 'coldDetails',
            // name: 'ColdDetails',
            // component: () => import('@/views/monitor/coldHeat/monitorDetails.vue'),
            meta: {
              title: '监测详情',
              sidebar: false,
              activeMenu: '/coldHeat/coldMonitor'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/boilerMenu',
    component: Layout,
    redirect: '/boilerMenu/boilerOverview',
    name: 'boilerMenu',
    meta: {
      title: '锅炉运行监测',
      menuAuth: '/boilerMenu'
      // arrangement: 'column',  // 用于控制左侧菜单的排列方式 一行三个
      // classType: 'row33' // 用于控制左侧菜单的样式 一行三个
    },
    children: [
      {
        path: 'boilerOverview',
        component: EmptyLayout,
        redirect: { name: 'boilerOverview' },
        meta: {
          title: '运行总览',
          menuAuth: '/boilerMenu/boilerOverview'
        },
        children: [
          {
            path: '',
            name: 'boilerOverview',
            component: () => import('@/views/monitor/boilerMenu/boilerOverview.vue'),
            meta: {
              title: '运行总览',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'videoSurveillance',
            name: 'VideoSurveillance',
            component: () => import('@/views/monitor/boilerMenu/videoSurveillance.vue'),
            meta: {
              title: '视频监控',
              sidebar: false,
              activeMenu: '/boilerMenu/boilerOverview'
            }
          }
        ]
      },
      {
        path: 'boilerMonitor',
        component: EmptyLayout,
        redirect: { name: 'boilerMonitor' },
        meta: {
          title: '运行监测',
          menuAuth: '/boilerMenu/boilerMonitor'
        },
        children: [
          {
            path: '',
            name: 'boilerMonitor',
            component: () => import('@/views/monitor/boilerMenu/boilerMonitor.vue'),
            meta: {
              title: '运行监测',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'monitoringDetails',
            name: 'MonitoringDetails',
            component: () => import('@/views/monitor/boilerMenu/monitoringDetails.vue'),
            meta: {
              title: '监测详情',
              sidebar: false,
              activeMenu: '/boilerMenu/boilerMonitor'
            }
          }
        ]
      }
    ]
  },
  // {
  //   path: '/coldMenu',
  //   component: Layout,
  //   name: 'coldMenu',
  //   redirect: '/coldMenu/coldMOverview',
  //   meta: {
  //     title: '冷气站',
  //     menuAuth: '/coldMenu'
  //   },
  //   children: [
  //     {
  //       path: 'coldMOverview',
  //       component: EmptyLayout,
  //       redirect: { name: 'coldMOverview' },
  //       meta: {
  //         title: '运行总览',
  //         menuAuth: '/coldMenu/airOverview'
  //       },
  //       children: [
  //         {
  //           path: '',
  //           name: 'airOverview',
  //           component: () => import('@/views/monitor/airMenu/airOverview.vue'),
  //           meta: {
  //             title: '运行总览',
  //             sidebar: false,
  //             breadcrumb: false
  //           }
  //         }
  //       ]
  //     }
  //   ]
  // },
  {
    path: '/securityMenu',
    component: Layout,
    name: 'securityMenu',
    redirect: '/securityMenu/securityOverview',
    meta: {
      title: '安防系统监测',
      menuAuth: '/securityMenu'
      // arrangement: 'column',  // 用于控制左侧菜单的排列方式 一行三个
      // classType: 'row33' // 用于控制左侧菜单的样式 一行三个
    },
    children: [
      {
        path: 'securityOverview',
        component: EmptyLayout,
        redirect: { name: 'securityOverview' },
        meta: {
          title: '运行总览',
          menuAuth: '/securityMenu/securityOverview'
        },
        children: [
          {
            path: '',
            name: 'securityOverview',
            component: () => import('@/views/monitor/securityMenu/securityOverview.vue'),
            meta: {
              title: '运行总览',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'securityMonitor',
        component: EmptyLayout,
        redirect: { name: 'securityMonitor' },
        meta: {
          title: '运行监测',
          menuAuth: '/securityMenu/securityMonitor'
        },
        children: [
          {
            path: '',
            name: 'securityMonitor',
            component: () => import('@/views/monitor/securityMenu/securityMonitor.vue'),
            meta: {
              title: '运行监测',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'monitorDetails',
            name: 'monitorDetails',
            component: () => import('@/views/monitor/components/monitorDetail/index.vue'),
            meta: {
              title: '监测详情',
              sidebar: false,
              activeMenu: '/securityMenu/securityMonitor'
            }
          }
          // {
          //   path: 'screeningDetails',
          //   name: 'screeningDetails',
          //   component: () => import('@/views/monitor/components/screeningDetails/index.vue'),
          //   meta: {
          //     title: '安检入口详情',
          //     sidebar: false,
          //     activeMenu: '/securityMenu/securityMonitor'
          //   },
          // },
          // {
          //   path: 'captureList',
          //   name: 'securityMonitor',
          //   component: () => import('@/views/monitor/components/screeningDetails/captureList.vue'),
          //   meta: {
          //     title: '抓拍',
          //     sidebar: false,
          //     breadcrumb: false
          //   }
          // },
        ]
      },
      {
        path: 'accessRecord',
        component: EmptyLayout,
        redirect: { name: 'accessRecord' },
        meta: {
          title: '门禁记录',
          menuAuth: '/securityMenu/accessRecord'
        },
        children: [
          {
            path: '',
            name: 'accessRecord',
            component: () => import('@/views/monitor/securityMenu/accessRecord.vue'),
            meta: {
              title: '门禁记录',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      }
    ]
  },
  {
    path: '/fireControlMenu',
    component: Layout,
    name: 'fireControlMenu',
    redirect: '/fireControlMenu/fireControlOverview',
    meta: {
      title: '消防系统监测',
      menuAuth: '/fireControlMenu'
      // arrangement: 'column',  // 用于控制左侧菜单的排列方式 一行三个
      // classType: 'row33' // 用于控制左侧菜单的样式 一行三个
    },
    children: [
      {
        path: 'fireControlOverview',
        component: EmptyLayout,
        redirect: { name: 'fireControlOverview' },
        meta: {
          title: '运行总览',
          menuAuth: '/fireControlMenu/fireControlOverview'
        },
        children: [
          {
            path: '',
            name: 'fireControlOverview',
            component: () => import('@/views/monitor/fireControlMenu/fireControlOverview.vue'),
            meta: {
              title: '运行总览',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'fireControlMonitor',
        component: EmptyLayout,
        redirect: { name: 'fireControlMonitor' },
        meta: {
          title: '运行监测',
          menuAuth: '/fireControlMenu/fireControlMonitor'
        },
        children: [
          {
            path: '',
            name: 'fireControlMonitor',
            component: () => import('@/views/monitor/fireControlMenu/fireControlMonitor.vue'),
            meta: {
              title: '运行监测',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'monitorDetails',
            name: 'monitorDetails',
            component: () => import('@/views/monitor/components/monitorDetail/index.vue'),
            meta: {
              title: '监测详情',
              sidebar: false,
              activeMenu: '/fireControlMenu/fireControlMonitor'
            }
          }
        ]
      },
      {
        path: 'fireproof',
        component: EmptyLayout,
        redirect: { name: 'fireproof' },
        meta: {
          title: '防火分区',
          menuAuth: '/fireControlMenu/fireproof'
        },
        children: [
          {
            path: '',
            name: 'fireproof',
            component: () => import('@/views/monitor/fireControlMenu/fireproof.vue'),
            meta: {
              title: '防火分区',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'addFireproof',
            name: 'addFireproof',
            component: () => import('@/views/monitor/fireControlMenu/components/addFireproof.vue'),
            meta: {
              title: '新增防火分区',
              sidebar: false,
              activeMenu: '/fireControlMenu/fireproof'
              // breadcrumb: false
            }
          }
        ]
      }
    ]
  },
  {
    path: '/multiMediaMenu',
    component: Layout,
    name: 'multiMediaMenu',
    redirect: '/multiMediaMenu/multiMediaOverview',
    meta: {
      title: '多媒体系统监测',
      menuAuth: '/multiMediaMenu'
      // arrangement: 'column',  // 用于控制左侧菜单的排列方式 一行三个
      // classType: 'row33' // 用于控制左侧菜单的样式 一行三个
    },
    children: [
      {
        path: 'multiMediaOverview',
        component: EmptyLayout,
        redirect: { name: 'multiMediaOverview' },
        meta: {
          title: '运行总览',
          menuAuth: '/multiMediaMenu/multiMediaOverview'
        },
        children: [
          {
            path: '',
            name: 'multiMediaOverview',
            component: () => import('@/views/monitor/multiMediaMenu/multiMediaOverview.vue'),
            meta: {
              title: '运行总览',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'multiMediaMonitor',
        component: EmptyLayout,
        redirect: { name: 'multiMediaMonitor' },
        meta: {
          title: '运行监测',
          menuAuth: '/multiMediaMenu/multiMediaMonitor'
        },
        children: [
          {
            path: '',
            name: 'multiMediaMonitor',
            component: () => import('@/views/monitor/multiMediaMenu/multiMediaMonitor.vue'),
            meta: {
              title: '运行监测',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'monitorDetails',
            name: 'monitorDetails',
            component: () => import('@/views/monitor/components/monitorDetail/index.vue'),
            meta: {
              title: '监测详情',
              sidebar: false,
              activeMenu: '/multiMediaMenu/multiMediaMonitor'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/airCooled',
    component: Layout,
    name: 'airCooled',
    redirect: '/airCooled/airCooledOverview',
    meta: {
      title: '风冷热泵监测',
      menuAuth: '/airCooled'
    },
    children: [
      {
        path: 'airCooledOverview',
        component: EmptyLayout,
        redirect: { name: 'airCooledOverview' },
        meta: {
          title: '运行总览',
          menuAuth: '/airCooled/airCooledOverview'
        },
        children: [
          {
            path: '',
            name: 'airCooledOverview',
            component: () => import('@/views/monitor/airCooled/airCooledOverview.vue'),
            meta: {
              title: '运行总览',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'airCooledMonitor',
        component: EmptyLayout,
        redirect: { name: 'airCooledMonitor' },
        meta: {
          title: '运行监测',
          menuAuth: '/airCooled/airCooledMonitor'
        },
        children: [
          {
            path: '',
            name: 'airCooledMonitor',
            component: () => import('@/views/monitor/airCooled/airCooledMonitor.vue'),
            meta: {
              title: '运行监测',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'monitorDetails',
            name: 'monitorDetails',
            component: () => import('@/views/monitor/components/monitorDetail/index.vue'),
            // path: 'coldDetails',
            // name: 'ColdDetails',
            // component: () => import('@/views/monitor/coldHeat/monitorDetails.vue'),
            meta: {
              title: '监测详情',
              sidebar: false,
              activeMenu: '/airCooled/airCooledMonitor'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/elevatorMenu',
    component: Layout,
    name: 'elevatorMenu',
    redirect: '/elevatorMenu/elevatorOverview',
    meta: {
      title: '电梯运行监测',
      menuAuth: '/elevatorMenu'
      // arrangement: 'column',  // 用于控制左侧菜单的排列方式 一行三个
      // classType: 'row33' // 用于控制左侧菜单的样式 一行三个
    },
    children: [
      {
        path: 'elevatorOverview',
        component: EmptyLayout,
        redirect: { name: 'elevatorOverview' },
        meta: {
          title: '运行总览',
          menuAuth: '/elevatorMenu/elevatorOverview'
        },
        children: [
          {
            path: '',
            name: 'elevatorOverview',
            component: () => import('@/views/monitor/elevatorMenu/elevatorOverview.vue'),
            meta: {
              title: '运行总览',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'elevatorMonitor',
        component: EmptyLayout,
        redirect: { name: 'elevatorMonitor' },
        meta: {
          title: '运行监测',
          menuAuth: '/elevatorMenu/elevatorMonitor'
        },
        children: [
          {
            path: '',
            name: 'elevatorMonitor',
            component: () => import('@/views/monitor/elevatorMenu/elevatorMonitor.vue'),
            meta: {
              title: '运行监测',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'elevatorDetail',
            name: 'elevatorDetail',
            component: () => import('@/views/monitor/elevatorMenu/components/elevatorDetail.vue'),
            meta: {
              title: '电梯详情',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/elevatorMenu/elevatorMonitor'
            }
          },
          {
            path: 'monitorDetails',
            name: 'monitorDetails',
            component: () => import('@/views/monitor/components/monitorDetail/index.vue'),
            meta: {
              title: '监测详情',
              sidebar: false,
              activeMenu: '/elevatorMenu/elevatorMonitor'
            }
          }
        ]
      },
      {
        path: 'elevatorAlarmCenter',
        component: EmptyLayout,
        redirect: { name: 'elevatorAlarmCenter' },
        meta: {
          title: '报警中心',
          menuAuth: '/elevatorMenu/elevatorAlarmCenter'
        },
        children: [
          {
            path: '',
            name: 'elevatorAlarmCenter',
            component: () => import('@/views/monitor/elevatorMenu/elevatorAlarmCenter.vue'),
            meta: {
              title: '报警中心',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'policeDetails',
            name: 'policeDetails',
            component: () => import('@/views/monitor/commonPage/elecAlarmCenter/policeDetails.vue'),
            meta: {
              title: '报警详情',
              sidebar: false,
              activeMenu: '/elevatorMenu/elevatorAlarmCenter'
            }
          }
        ]
      },
      // {
      //   path: 'elevatorAlarmCenter',
      //   component: EmptyLayout,
      //   redirect: { name: 'elevatorAlarmCenter' },
      //   meta: {
      //     title: '报警中心',
      //     menuAuth: '/elevatorMenu/elevatorAlarmCenter'
      //   },
      //   children: [
      //     {
      //       path: '',
      //       name: 'elevatorAlarmCenter',
      //       component: () => import('@/views/monitor/elevatorMenu/elevatorAlarmCenter.vue'),
      //       meta: {
      //         title: '报警中心',
      //         sidebar: false,
      //         breadcrumb: false
      //       }
      //     },
      //     {
      //       path: 'policeDetails',
      //       name: 'policeDetails',
      //       component: () => import('@/views/monitor/commonPage/elecAlarmCenter/policeDetails.vue'),
      //       meta: {
      //         title: '报警详情',
      //         sidebar: false,
      //         activeMenu: '/elevatorMenu/elevatorAlarmCenter'
      //       }
      //     }
      //   ]
      // },
      {
        path: 'elevatorAnalysis',
        component: EmptyLayout,
        redirect: { name: 'elevatorAnalysis' },
        meta: {
          title: '统计分析',
          menuAuth: '/elevatorMenu/elevatorAnalysis'
        },
        children: [
          {
            path: '',
            name: 'elevatorAnalysis',
            component: () => import('@/views/monitor/elevatorMenu/elevatorAnalysis.vue'),
            meta: {
              title: '统计分析',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      }
    ]
  },
  {
    path: '/monitoringConfig',
    component: Layout,
    name: 'monitoringConfig',
    redirect: '/monitoringConfig/airConfiguration',
    meta: {
      title: '监测项配置',
      menuAuth: '/monitoringConfig'
    },
    children: [
      {
        path: 'airConfiguration',
        component: EmptyLayout,
        redirect: { name: 'airConfiguration' },
        meta: {
          title: '空调',
          menuAuth: '/monitoringConfig/airConfiguration'
        },
        children: [
          {
            path: '',
            name: 'airConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: '空调',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'airMonitorForm',
            name: 'airMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/airMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/airConfiguration'
            }
          }
        ]
      },
      {
        path: 'envirConfiguration',
        component: EmptyLayout,
        redirect: { name: 'envirConfiguration' },
        meta: {
          title: '环境监测',
          menuAuth: '/monitoringConfig/envirConfiguration'
        },
        children: [
          {
            path: '',
            name: 'envirConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: '环境监测',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'envirMonitorForm',
            name: 'envirMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/envirMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/envirConfiguration'
            }
          }
        ]
      },
      {
        path: 'sewerConfiguration',
        component: EmptyLayout,
        redirect: { name: 'sewerConfiguration' },
        meta: {
          title: '给排水监测',
          menuAuth: '/monitoringConfig/sewerConfiguration'
        },
        children: [
          {
            path: '',
            name: 'sewerConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: '给排水监测',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'sewerMonitorForm',
            name: 'sewerMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/sewerMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/sewerConfiguration'
            }
          }
        ]
      },
      {
        path: 'coldHotConfiguration',
        component: EmptyLayout,
        redirect: { name: 'coldHotConfiguration' },
        meta: {
          title: '冷热源配置',
          menuAuth: '/monitoringConfig/coldHotConfiguration'
        },
        children: [
          {
            path: '',
            name: 'coldHotConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: '冷热源配置',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'coldHotMonitorForm',
            name: 'coldHotMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/coldHotMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/coldHotConfiguration'
            }
          }
        ]
      },
      {
        path: 'elevatorConfiguration',
        component: EmptyLayout,
        redirect: { name: 'elevatorConfiguration' },
        meta: {
          title: '电梯监测',
          menuAuth: '/monitoringConfig/elevatorConfiguration'
        },
        children: [
          {
            path: '',
            name: 'elevatorConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: '电梯监测',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'elevatorMonitorForm',
            name: 'elevatorMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/elevatorMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/elevatorConfiguration'
            }
          }
        ]
      },
      {
        path: 'powerDistributionConfiguration',
        component: EmptyLayout,
        redirect: { name: 'powerDistributionConfiguration' },
        meta: {
          title: '配电监测',
          menuAuth: '/monitoringConfig/powerDistributionConfiguration'
        },
        children: [
          {
            path: '',
            name: 'powerDistributionConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: '配电监测',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'powerDistributionMonitorForm',
            name: 'powerDistributionMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/powerDistributionMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/powerDistributionConfiguration'
            }
          }
        ]
      },
      {
        path: 'lightConfiguration',
        component: EmptyLayout,
        redirect: { name: 'lightConfiguration' },
        meta: {
          title: '照明监测',
          menuAuth: '/monitoringConfig/lightConfiguration'
        },
        children: [
          {
            path: '',
            name: 'lightConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/lightConfiguration.vue'),
            meta: {
              title: '照明监测',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'lightMonitorForm',
            name: 'lightMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/lightMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/lightConfiguration'
            }
          }
        ]
      },
      {
        path: 'medicalGasConfiguration',
        component: EmptyLayout,
        redirect: { name: 'medicalGasConfiguration' },
        meta: {
          title: '医用气体配置',
          menuAuth: '/monitoringConfig/medicalGasConfiguration'
        },
        children: [
          {
            path: '',
            name: 'medicalGasConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: '医用气体配置',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'medicalGasMonitorForm',
            name: 'medicalGasMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/medicalGasMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/medicalGasConfiguration'
            }
          }
        ]
      },
      {
        path: 'upsConfiguration',
        component: EmptyLayout,
        redirect: { name: 'upsConfiguration' },
        meta: {
          title: 'UPS运行配置',
          menuAuth: '/monitoringConfig/upsConfiguration'
        },
        children: [
          {
            path: '',
            name: 'upsConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: 'UPS运行配置',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'medicalGasMonitorForm',
            name: 'medicalGasMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/medicalGasMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/upsConfiguration'
            }
          }
        ]
      },
      {
        path: 'sewageConfiguration',
        component: EmptyLayout,
        redirect: { name: 'sewageConfiguration' },
        meta: {
          title: ' 污水配置',
          menuAuth: '/monitoringConfig/sewageConfiguration'
        },
        children: [
          {
            path: '',
            name: 'sewageConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: ' 污水配置',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'medicalGasMonitorForm',
            name: 'medicalGasMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/medicalGasMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/sewageConfiguration'
            }
          }
        ]
      },
      {
        path: 'airCooledConfiguration',
        component: EmptyLayout,
        redirect: { name: 'airCooledConfiguration' },
        meta: {
          title: '风冷热泵监测',
          menuAuth: '/monitoringConfig/airCooledConfiguration'
        },
        children: [
          {
            path: '',
            name: 'airCooledConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: '风冷热泵监测',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'airCooledMonitorForm',
            name: 'airCooledMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/airCooledMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/airCooledConfiguration'
            }
          }
        ]
      },
      {
        path: 'broadcastConfiguration',
        component: EmptyLayout,
        redirect: { name: 'broadcastConfiguration' },
        meta: {
          title: '广播终端',
          menuAuth: '/monitoringConfig/broadcastConfiguration'
        },
        children: [
          {
            path: '',
            name: 'broadcastConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: '广播终端',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'securityMonitorForm',
            name: 'securityMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/broadcastConfiguration'
            }
          },
          {
            path: 'securityBatchMonitorForm',
            name: 'securityBatchMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityBatchMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/broadcastConfiguration'
            }
          }
        ]
      },
      {
        path: 'LEDConfiguration',
        component: EmptyLayout,
        redirect: { name: 'LEDConfiguration' },
        meta: {
          title: 'LED屏幕',
          menuAuth: '/monitoringConfig/LEDConfiguration'
        },
        children: [
          {
            path: '',
            name: 'LEDConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: 'LED屏幕',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'securityMonitorForm',
            name: 'securityMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/LEDConfiguration'
            }
          },
          {
            path: 'securityBatchMonitorForm',
            name: 'securityBatchMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityBatchMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/LEDConfiguration'
            }
          }
        ]
      },
      {
        path: 'mediaConfiguration',
        component: EmptyLayout,
        redirect: { name: 'mediaConfiguration' },
        meta: {
          title: '多媒体屏幕',
          menuAuth: '/monitoringConfig/mediaConfiguration'
        },
        children: [
          {
            path: '',
            name: 'mediaConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: '多媒体屏幕',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'securityMonitorForm',
            name: 'securityMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/mediaConfiguration'
            }
          },
          {
            path: 'securityBatchMonitorForm',
            name: 'securityBatchMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityBatchMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/mediaConfiguration'
            }
          }
        ]
      },
      {
        path: 'cameraConfiguration',
        component: EmptyLayout,
        redirect: { name: 'cameraConfiguration' },
        meta: {
          title: '摄像头设备',
          menuAuth: '/monitoringConfig/cameraConfiguration'
        },
        children: [
          {
            path: '',
            name: 'cameraConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: '摄像头设备',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'securityMonitorForm',
            name: 'securityMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/cameraConfiguration'
            }
          },
          {
            path: 'securityBatchMonitorForm',
            name: 'securityBatchMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityBatchMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/cameraConfiguration'
            }
          }
        ]
      },
      {
        path: 'guardConfiguration',
        component: EmptyLayout,
        redirect: { name: 'guardConfiguration' },
        meta: {
          title: '门禁设备',
          menuAuth: '/monitoringConfig/guardConfiguration'
        },
        children: [
          {
            path: '',
            name: 'guardConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: '门禁设备',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'securityMonitorForm',
            name: 'securityMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/guardConfiguration'
            }
          },
          {
            path: 'securityBatchMonitorForm',
            name: 'securityBatchMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityBatchMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/guardConfiguration'
            }
          }
        ]
      },
      {
        path: 'invadeConfiguration',
        component: EmptyLayout,
        redirect: { name: 'invadeConfiguration' },
        meta: {
          title: '入侵设备',
          menuAuth: '/monitoringConfig/invadeConfiguration'
        },
        children: [
          {
            path: '',
            name: 'invadeConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: '入侵设备',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'securityMonitorForm',
            name: 'securityMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/invadeConfiguration'
            }
          },
          {
            path: 'securityBatchMonitorForm',
            name: 'securityBatchMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityBatchMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/invadeConfiguration'
            }
          }
        ]
      },
      {
        path: 'oneKeyConfiguration',
        component: EmptyLayout,
        redirect: { name: 'oneKeyConfiguration' },
        meta: {
          title: '一键报警',
          menuAuth: '/monitoringConfig/oneKeyConfiguration'
        },
        children: [
          {
            path: '',
            name: 'oneKeyConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: '一键报警',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'securityMonitorForm',
            name: 'securityMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/oneKeyConfiguration'
            }
          },
          {
            path: 'securityBatchMonitorForm',
            name: 'securityBatchMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityBatchMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/oneKeyConfiguration'
            }
          }
        ]
      },
      // 报警定位
      {
        path: 'alarmPositioningConfiguration',
        component: EmptyLayout,
        redirect: { name: 'alarmPositioningConfiguration' },
        meta: {
          title: '报警定位',
          menuAuth: '/monitoringConfig/alarmPositioningConfiguration'
        },
        children: [
          {
            path: '',
            name: 'alarmPositioningConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: '报警定位',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'securityMonitorForm',
            name: 'securityMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/alarmPositioningConfiguration'
            }
          },
          {
            path: 'securityBatchMonitorForm',
            name: 'securityBatchMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityBatchMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/alarmPositioningConfiguration'
            }
          }
        ]
      },
      // 保安定位
      {
        path: 'securityPositioningConfiguration',
        component: EmptyLayout,
        redirect: { name: 'securityPositioningConfiguration' },
        meta: {
          title: '保安定位',
          menuAuth: '/monitoringConfig/securityPositioningConfiguration'
        },
        children: [
          {
            path: '',
            name: 'securityPositioningConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: '保安定位',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'securityMonitorForm',
            name: 'securityMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/securityPositioningConfiguration'
            }
          },
          {
            path: 'securityBatchMonitorForm',
            name: 'securityBatchMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityBatchMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/securityPositioningConfiguration'
            }
          }
        ]
      },
      // 智能安检筛查
      {
        path: 'ecurityScreeningConfiguration',
        component: EmptyLayout,
        redirect: { name: 'ecurityScreeningConfiguration' },
        meta: {
          title: '智能安检筛查',
          menuAuth: '/monitoringConfig/ecurityScreeningConfiguration'
        },
        children: [
          {
            path: '',
            name: 'ecurityScreeningConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: '智能安检筛查',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'securityMonitorForm',
            name: 'securityMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/ecurityScreeningConfiguration'
            }
          },
          {
            path: 'securityBatchMonitorForm',
            name: 'securityBatchMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityBatchMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/ecurityScreeningConfiguration'
            }
          }
        ]
      },
      // 固定报警按钮
      {
        path: 'fixedButtonConfiguration',
        component: EmptyLayout,
        redirect: { name: 'fixedButtonConfiguration' },
        meta: {
          title: '固定报警按钮',
          menuAuth: '/monitoringConfig/fixedButtonConfiguration'
        },
        children: [
          {
            path: '',
            name: 'fixedButtonConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: '固定报警按钮',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'securityMonitorForm',
            name: 'securityMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/fixedButtonConfiguration'
            }
          },
          {
            path: 'securityBatchMonitorForm',
            name: 'securityBatchMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityBatchMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/fixedButtonConfiguration'
            }
          }
        ]
      },
      {
        path: 'sensationConfiguration',
        component: EmptyLayout,
        redirect: { name: 'sensationConfiguration' },
        meta: {
          title: '烟感温感',
          menuAuth: '/monitoringConfig/sensationConfiguration'
        },
        children: [
          {
            path: '',
            name: 'sensationConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: '烟感温感',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'securityMonitorForm',
            name: 'securityMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/sensationConfiguration'
            }
          },
          {
            path: 'securityBatchMonitorForm',
            name: 'securityBatchMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityBatchMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/sensationConfiguration'
            }
          }
        ]
      },
      {
        path: 'electricitySafetyConfiguration',
        component: EmptyLayout,
        redirect: { name: 'electricitySafetyConfiguration' },
        meta: {
          title: '用电安全',
          menuAuth: '/monitoringConfig/electricitySafetyConfiguration'
        },
        children: [
          {
            path: '',
            name: 'electricitySafetyConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: '用电安全',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'securityMonitorForm',
            name: 'securityMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/electricitySafetyConfiguration'
            }
          },
          {
            path: 'securityBatchMonitorForm',
            name: 'securityBatchMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityBatchMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/electricitySafetyConfiguration'
            }
          }
        ]
      },
      {
        path: 'manualConfiguration',
        component: EmptyLayout,
        redirect: { name: 'manualConfiguration' },
        meta: {
          title: '手报',
          menuAuth: '/monitoringConfig/manualConfiguration'
        },
        children: [
          {
            path: '',
            name: 'manualConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: '手报',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'securityMonitorForm',
            name: 'securityMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/manualConfiguration'
            }
          },
          {
            path: 'securityBatchMonitorForm',
            name: 'securityBatchMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityBatchMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/manualConfiguration'
            }
          }
        ]
      },
      {
        path: 'fireTankConfiguration',
        component: EmptyLayout,
        redirect: { name: 'fireTankConfiguration' },
        meta: {
          title: '消防水箱',
          menuAuth: '/monitoringConfig/fireTankConfiguration'
        },
        children: [
          {
            path: '',
            name: 'fireTankConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: '消防水箱',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'securityMonitorForm',
            name: 'securityMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/fireTankConfiguration'
            }
          },
          {
            path: 'securityBatchMonitorForm',
            name: 'securityBatchMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityBatchMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/fireTankConfiguration'
            }
          }
        ]
      },
      {
        path: 'firePumpConfiguration',
        component: EmptyLayout,
        redirect: { name: 'firePumpConfiguration' },
        meta: {
          title: '消防水泵',
          menuAuth: '/monitoringConfig/firePumpConfiguration'
        },
        children: [
          {
            path: '',
            name: 'firePumpConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: '消防水泵',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'securityMonitorForm',
            name: 'securityMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/firePumpConfiguration'
            }
          },
          {
            path: 'securityBatchMonitorForm',
            name: 'securityBatchMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityBatchMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/firePumpConfiguration'
            }
          }
        ]
      },
      {
        path: 'smokeFanConfiguration',
        component: EmptyLayout,
        redirect: { name: 'smokeFanConfiguration' },
        meta: {
          title: '防排烟风机',
          menuAuth: '/monitoringConfig/smokeFanConfiguration'
        },
        children: [
          {
            path: '',
            name: 'smokeFanConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: '防排烟风机',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'securityMonitorForm',
            name: 'securityMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/smokeFanConfiguration'
            }
          },
          {
            path: 'securityBatchMonitorForm',
            name: 'securityBatchMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityBatchMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/smokeFanConfiguration'
            }
          }
        ]
      },
      {
        path: 'fireProofConfiguration',
        component: EmptyLayout,
        redirect: { name: 'fireProofConfiguration' },
        meta: {
          title: '防火门',
          menuAuth: '/monitoringConfig/fireProofConfiguration'
        },
        children: [
          {
            path: '',
            name: 'fireProofConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: '防火门',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'securityMonitorForm',
            name: 'securityMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/fireProofConfiguration'
            }
          },
          {
            path: 'securityBatchMonitorForm',
            name: 'securityBatchMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityBatchMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/fireProofConfiguration'
            }
          }
        ]
      },
      {
        path: 'endWaterConfiguration',
        component: EmptyLayout,
        redirect: { name: 'endWaterConfiguration' },
        meta: {
          title: '末端试水',
          menuAuth: '/monitoringConfig/endWaterConfiguration'
        },
        children: [
          {
            path: '',
            name: 'endWaterConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: '末端试水',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'securityMonitorForm',
            name: 'securityMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/endWaterConfiguration'
            }
          },
          {
            path: 'securityBatchMonitorForm',
            name: 'securityBatchMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityBatchMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/endWaterConfiguration'
            }
          }
        ]
      },
      {
        path: 'boilerConfiguration',
        component: EmptyLayout,
        redirect: { name: 'boilerConfiguration' },
        meta: {
          title: '锅炉监测',
          menuAuth: '/monitoringConfig/boilerConfiguration'
        },
        children: [
          {
            path: '',
            name: 'boilerConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: '锅炉监测',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'boilerMonitorForm',
            name: 'boilerMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/boilerMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/boilerMonitorForm'
            }
          }
        ]
      },
      {
        path: 'energyConfiguration',
        component: EmptyLayout,
        redirect: { name: 'energyConfiguration' },
        meta: {
          title: '能耗计量监测',
          menuAuth: '/monitoringConfig/energyConfiguration'
        },
        children: [
          {
            path: '',
            name: 'energyConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: '能耗计量监测',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'energyMonitorForm',
            name: 'energyMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/commonMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/energyConfiguration'
            }
          }
        ]
      },
      {
        path: 'helpConfiguration',
        component: EmptyLayout,
        redirect: { name: 'helpConfiguration' },
        meta: {
          title: 'ip求助',
          menuAuth: '/monitoringConfig/helpConfiguration'
        },
        children: [
          {
            path: '',
            name: 'helpConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: 'ip求助',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'securityMonitorForm',
            name: 'securityMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/oneKeyConfiguration'
            }
          },
          {
            path: 'securityBatchMonitorForm',
            name: 'securityBatchMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityBatchMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/oneKeyConfiguration'
            }
          }
        ]
      },
      {
        path: 'positioningChestCard',
        component: EmptyLayout,
        redirect: { name: 'positioningChestCard' },
        meta: {
          title: '报警定位胸卡',
          menuAuth: '/monitoringConfig/positioningChestCard'
        },
        children: [
          {
            path: '',
            name: 'positioningChestCard',
            component: () => import('@/views/monitor/monitoringConfig/commonConfiguration.vue'),
            meta: {
              title: '报警定位胸卡',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'securityMonitorForm',
            name: 'securityMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/oneKeyConfiguration'
            }
          },
          {
            path: 'securityBatchMonitorForm',
            name: 'securityBatchMonitorForm',
            component: () => import('@/views/monitor/monitoringConfig/form/securityBatchMonitorForm.vue'),
            meta: {
              title: '监测项配置',
              sidebar: false,
              activeMenu: '/monitoringConfig/oneKeyConfiguration'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/managementConfig',
    component: Layout,
    name: 'managementConfig',
    redirect: '/managementConfig/meta2dDraw',
    meta: {
      title: '配置管理',
      menuAuth: '/managementConfig'
      // arrangement: 'column',  // 用于控制左侧菜单的排列方式 一行三个
      // classType: 'row33' // 用于控制左侧菜单的样式 一行三个
    },
    children: [
      // {
      //   path: 'draw',
      //   component: EmptyLayout,
      //   redirect: { name: 'draw' },
      //   meta: {
      //     title: '图形绘制',
      //     menuAuth: '/managementConfig/draw'
      //   },
      //   children: [
      //     {
      //       path: '',
      //       name: 'draw',
      //       component: () => import('@/views/monitor/monitoringConfig/managementConfig/draw.vue'),
      //       meta: {
      //         title: '图形绘制',
      //         sidebar: false,
      //         breadcrumb: false
      //       }
      //     }
      //   ]
      // },
      {
        path: 'meta2dDraw',
        component: EmptyLayout,
        redirect: { name: 'meta-2d' },
        meta: {
          title: '新图形绘制',
          menuAuth: '/managementConfig/meta2dDraw'
        },
        children: [
          {
            path: '',
            name: 'meta-2d',
            component: () => import('@/views/monitor/monitoringConfig/managementConfig/meta2d.vue'),
            meta: {
              title: '新图形绘制',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: '/preview',
            name: 'preview',
            component: () => import('@/views/monitor/monitoringConfig/managementConfig/preview.vue'),
            meta: {
              title: '预览',
              sidebar: false,
              // breadcrumb: false,
              activeMenu: '/managementConfig/meta2dDraw'
            }
          }
        ]
      },
      {
        path: 'alarmConfiguration',
        component: EmptyLayout,
        redirect: { name: 'alarmConfiguration' },
        meta: {
          title: '报警配置',
          menuAuth: '/managementConfig/alarmConfiguration'
        },
        children: [
          {
            path: '',
            name: 'alarmConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/managementConfig/alarmConfiguration.vue'),
            meta: {
              title: '报警配置',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'alarmConfigForm',
            name: 'alarmConfigForm',
            component: () => import('@/views/monitor/monitoringConfig/managementConfig/alarmConfigForm.vue'),
            meta: {
              title: '报警规则配置',
              sidebar: false,
              activeMenu: '/managementConfig/alarmConfiguration'
            }
          }
        ]
      },
      {
        path: 'alarmTypeConfiguration',
        component: EmptyLayout,
        redirect: { name: 'alarmTypeConfiguration' },
        meta: {
          title: '报警类型配置',
          menuAuth: '/managementConfig/alarmTypeConfiguration'
        },
        children: [
          {
            path: '',
            name: 'alarmTypeConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/managementConfig/alarmTypeConfiguration.vue'),
            meta: {
              title: '报警类型配置',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'elevatorAlarmConfiguration',
        component: EmptyLayout,
        redirect: { name: 'elevatorAlarmConfiguration' },
        meta: {
          title: '电梯报警配置',
          menuAuth: '/managementConfig/elevatorAlarmConfiguration'
        },
        children: [
          {
            path: '',
            name: 'elevatorAlarmConfiguration',
            component: () => import('@/views/monitor/monitoringConfig/managementConfig/elevatorAlarmConfiguration.vue'),
            meta: {
              title: '电梯报警配置',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'alarmConfigForm',
            name: 'alarmConfigForm',
            component: () => import('@/views/monitor/monitoringConfig/managementConfig/alarmConfigForm.vue'),
            meta: {
              title: '电梯报警规则配置',
              sidebar: false,
              activeMenu: '/managementConfig/elevatorAlarmConfiguration'
            }
          }
        ]
      },
      {
        path: 'cameraManagement',
        component: EmptyLayout,
        redirect: { name: 'cameraManagement' },
        meta: {
          title: '摄像机管理',
          menuAuth: '/managementConfig/cameraManagement'
        },
        children: [
          {
            path: '',
            name: 'cameraManagement',
            component: () => import('@/views/monitor/monitoringConfig/managementConfig/cameraManagement.vue'),
            meta: {
              title: '摄像机管理',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      }
    ]
  },
  {
    path: '/meterReadingManage',
    component: Layout,
    name: 'meterReadingManage',
    redirect: '/meterReadingManage/meterManage',
    meta: {
      title: '抄表管理',
      menuAuth: '/meterReadingManage'
    },
    children: [
      {
        path: 'meterManage',
        component: EmptyLayout,
        redirect: { name: 'meta-2d' },
        meta: {
          title: '表计管理',
          menuAuth: '/meterReadingManage/meterManage'
        },
        children: [
          {
            path: '',
            name: 'meterManage',
            component: () => import('@/views/monitor/meterReadingManage/meterManage/index.vue'),
            meta: {
              title: '表计管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'addDevice',
            name: 'addDevice',
            component: () => import('@/views/monitor/meterReadingManage/meterManage/addDevice.vue'),
            meta: {
              title: '新增/编辑设备',
              sidebar: false,
              activeMenu: '/meterReadingManage/meterManage'
            }
          },
          {
            path: 'meterRecord',
            name: 'meterRecord',
            component: () => import('@/views/monitor/meterReadingManage/meterManage/meterRecord.vue'),
            meta: {
              title: '抄表记录',
              sidebar: false,
              activeMenu: '/meterReadingManage/meterManage'
            }
          }
        ]
      },
      {
        path: 'meterReadingNum',
        component: EmptyLayout,
        redirect: { name: 'meterReadingNum' },
        meta: {
          title: '抄表示数',
          menuAuth: '/meterReadingManage/meterReadingNum'
        },
        children: [
          {
            path: '',
            name: 'meterReadingNum',
            component: () => import('@/views/monitor/meterReadingManage/meterReadingNum/index.vue'),
            meta: {
              title: '抄表示数',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'statisticsAnalysis',
        component: EmptyLayout,
        redirect: { name: 'statisticsAnalysis' },
        meta: {
          title: '统计分析',
          menuAuth: '/meterReadingManage/statisticsAnalysis'
        },
        children: [
          {
            path: '',
            name: 'statisticsAnalysis',
            component: () => import('@/views/monitor/meterReadingManage/statisticsAnalysis/index.vue'),
            meta: {
              title: '统计分析',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      }
    ]
  }
]
