<template>
  <el-dialog :title="optType == 'add' ? '新增学科科目' : optType == 'edit' ? '编辑学科科目' : '查看学科科目详情'" :visible.sync="showSubjectForm" width="30%" :before-close="handleClose">
    <el-form ref="subjectForm" :model="form" :rules="rules" v-loading="loading" label-width="110px">
      <el-form-item label="学科科目名称" prop="name">
        <el-input  type="textarea"  v-model="form.name" placeholder="请输入科目名称,最多输入30个字" :disabled="optType == 'subJectDetail'"  maxlength="30" show-word-limit style="width: 300px" ></el-input>
      </el-form-item>
      <el-form-item label="上级科学">
        <el-cascader v-model="form.parentId" :options="subjectList" :props="subjectType" @change="handleChange" :disabled="optType == 'subJectDetail'" style="width: 300px;"></el-cascader>
      </el-form-item>
      <el-form-item label="类型描述">
        <el-input type="textarea" v-model="form.description" placeholder="请输入类型描述" :disabled="optType == 'subJectDetail'" maxlength="200" :autosize="{ minRows: 4, maxRows: 5 }"  show-word-limit style="width: 300px;"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose()">取 消</el-button>
      <el-button type="primary" @click="submit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  props: {
    showSubjectForm: {
      type: Boolean,
      default: false,
    },
    optType: {
      type: String,
      default: '',
    },
    editId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      form: {   // 表单提交数据
        id: '',
        name: '',
        parentId: '',
        description: '',
        parentIds: '',
        level: '',
        createBy: '',
      },
      loading: false,
      studylist: [], // 上级学科科目数据
      LOGINDATA: '', // 当前登录人信息
      subjectList: [],
      subjectType: {
        children: 'children',
        label: 'name',
        value: 'id',
        checkStrictly: true,
        emitPath: true,
        disabled: 'disabledStatus'
      },
      rules: {
        name:[
          { required: true, message: '请输入科目名称', trigger: 'blur' }
        ],
      },
    };
  },
  created() {
    // this.LOGINDATA = JSON.parse(sessionStorage.getItem('LOGINDATA'))
    this.getList()
  },
  mounted() {
   this.$watch('showSubjectForm', () => {
      if (this.editId) {
        if(this.optType == 'edit' || this.optType == 'subJectDetail') {
          this.getSubjectDeatil(this.editId)
        }
      }
   })
  },
  methods: {
    handleClose() {
      this.clearForm();
      this.$refs.subjectForm.resetFields()
      this.$emit('closeAdd');
    },
    // 选中上一级学科科目
    handleChange(val) {
      console.log(val,'VAL');
      this.form.parentIds = val.join(',');
      let obj = this.studylist.find(item => item.id == val[val.length - 1]);
      this.form.level = obj.level;
    },
    // 获取上一级学科科目
    getList() {
      let params = {
        level: 2,
      }
      this.$api.ipsmFicationlUpList(params).then((res) => {
        if(res.code == 200) {
          this.studylist = res.data
          this.subjectList = this.$tools.transData(this.studylist, 'id', 'parentId', 'children')
        }
      })
    },
    // 提交表单
    submit() {
      this.$refs.subjectForm.validate((valid) => {
        if(valid) {
          let params = {
            name: this.form.name,
            parentId: this.form.parentId[this.form.parentId.length - 1],
            description: this.form.description,
            level: this.form.level,
            // createBy: this.LOGINDATA.name,
            parentIds: this.form.parentIds,
            id: this.form.id,
          }
          if(this.optType == 'add') {
            delete params.id
          }
          console.log(params);
          this.$api.ipsmFicationlSave(params).then((res) => {
            console.log(res,'RES123');
            if(res.code == 200) {
              this.$message.success(res.data)
              this.handleClose()
              this.$emit('refreshTable')
            } else {
              this.$message.error(res.data)
            }
          })
        }
      })
    },
    // 获取学科科目分类详情
    getSubjectDeatil(courseId) {
      this.loading = true
      this.$api.ipsmFicationlDetail({ id: courseId}).then((res) => {
        this.loading = false
        if(res.code == 200) {
          this.form.name = res.data.name;
          this.form.parentId = res.data.parentIds.split(',');
          this.form.parentId = this.form.parentId.map(Number);
          console.log(this.form.parentId,'this.form.parentId');
          this.form.description = res.data.description;
          this.form.level = res.data.level;
          this.form.parentIds = res.data.parentIds;
          this.form.id = res.data.id
        }
      })
    },
    // 清除表单
    clearForm() {
      this.form.name = '';
      this.form.parentId = '';
      this.form.description = '';
      this.form.parentIds = '';
      this.form.level = '';
      this.form.createBy = ''
    },
  },
};
</script>

<style lang='scss' scoped>
::v-deep .el-textarea .el-input__count {
  color: #909399;
  position: absolute;
  background: none;
  font-size: 12px;
  bottom: -7px;
  right: 10px;
}
</style>
