import Layout from '@/layout'
import EmptyLayout from '@/layout/empty'
export default [
  {
    path: '/hazardousChemicalGoodsOverview',
    component: Layout,
    name: 'hazardousChemicalGoodsOverview',
    redirect: '/hazardousChemicalGoodsOverview/hazardousChemicalGoods_ledger',
    meta: {
      title: '危化品库房总览',
      menuAuth: '/hazardousChemicalGoodsOverview'
    },
    children: [
      {
        path: 'hazardousChemicalGoods_ledger',
        component: EmptyLayout,
        redirect: { name: 'hazardousChemicalGoods_ledger' },
        meta: {
          title: '危化品台账',
          menuAuth: '/hazardousChemicalGoodsOverview/hazardousChemicalGoods_ledger'
        },
        children: [
          {
            path: '',
            name: 'hazardousChemicalGoods_ledger',
            component: () => import('@/views/hazardousChemicalGoods/hazardousChemicalGoods_ledger/index.vue'),
            meta: {
              title: '危化品台账',
              sidebar: false,
              breadcrumb: false,
              menuAuth: '/hazardousChemicalGoodsOverview/hazardousChemicalGoods_ledger',
              auth: [
                'hazardousChemicalsLedger:check',
                'hazardousChemicalsLedger:export'
              ]
            }
          }
        ]
      },
      {
        path: 'hazardousChemicalGoods_warehouse',
        redirect: { name: 'hazardousChemicalGoods_warehouse' },
        component: EmptyLayout,
        meta: {
          title: '库房管理',
          menuAuth: '/hazardousChemicalGoodsOverview/hazardousChemicalGoods_warehouse'
        },
        children: [
          {
            path: '',
            name: 'hazardousChemicalGoods_warehouse',
            component: () => import('@/views/hazardousChemicalGoods/hazardousChemicalGoods_warehouse/index.vue'),
            meta: {
              title: '库房管理',
              sidebar: false,
              breadcrumb: false,
              menuAuth: '/hazardousChemicalGoodsOverview/hazardousChemicalGoods_warehouse',
              auth: [
                'warehouseManagement:add',
                'warehouseManagement:check',
                'warehouseManagement:edit',
                'warehouseManagement:del'
              ]
            }
          },
          {
            path: 'warehouseAdd',
            name: 'warehouseAdd',
            component: () => import('@/views/hazardousChemicalGoods/hazardousChemicalGoods_warehouse/addWarehouse.vue'),
            meta: {
              title: '创建仓库',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/hazardousChemicalGoodsOverview/hazardousChemicalGoods_warehouse'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/hazardousChemicalGoodsManage',
    component: Layout,
    name: 'hazardousChemicalGoodsManage',
    redirect: '/hazardousChemicalGoodsOverview/storageManage',
    meta: {
      title: '危化品管理',
      menuAuth: '/hazardousChemicalGoodsManage'
    },
    children: [
      {
        path: 'storageManage',
        component: EmptyLayout,
        redirect: { name: 'storageManage' },
        meta: {
          title: '入库管理',
          menuAuth: '/hazardousChemicalGoodsManage/storageManage'
        },
        children: [
          {
            path: '',
            name: 'storageManage',
            component: () => import('@/views/hazardousChemicalGoods/hazardousChemicalGoodsManage/storageManage/index.vue'),
            meta: {
              title: '入库管理',
              sidebar: false,
              breadcrumb: false,
              menuAuth: '/hazardousChemicalGoodsManage/storageManage',
              auth: [
                'rkManagement:submit',
                'rkManagement:del',
                'rkManagement:edit',
                'rkManagement:check',
                'rkManagement:export',
              ]
            }
          },
          {
            path: 'hazardousChemicalAdd',
            name: 'hazardousChemicalAdd',
            component: () => import('@/views/hazardousChemicalGoods/hazardousChemicalGoodsManage/storageManage/addHazardousChemical.vue'),
            meta: {
              title: '新增危化品',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/hazardousChemicalGoodsManage/storageManage'
            }
          },
          {
            path: 'warehousingEntry',
            name: 'warehousingEntry',
            component: () => import('@/views/hazardousChemicalGoods/hazardousChemicalGoodsManage/components/warehousingEntry.vue'),
            meta: {
              title: '入库单',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/hazardousChemicalGoodsManage/storageManage'
            }
          }
        ]
      },
      {
        path: 'leaveWarehouseManage',
        redirect: { name: 'leaveWarehouseManage' },
        component: EmptyLayout,
        meta: {
          title: '出库管理',
          menuAuth: '/hazardousChemicalGoodsManage/leaveWarehouseManage'
        },
        children: [
          {
            path: '',
            name: 'leaveWarehouseManage',
            component: () => import('@/views/hazardousChemicalGoods/hazardousChemicalGoodsManage/leaveWarehouseManage/index.vue'),
            meta: {
              title: '出库管理',
              sidebar: false,
              breadcrumb: false,
              menuAuth: '/hazardousChemicalGoodsManage/leaveWarehouseManage',
              auth: [
                'ckManagement:submit',
                'ckManagement:del',
                'ckManagement:edit',
                'ckManagement:check',
                'ckManagement:export',
              ]
            }
          },
          {
            path: 'outboundDeliveryOrder',
            name: 'outboundDeliveryOrder',
            component: () => import('@/views/hazardousChemicalGoods/hazardousChemicalGoodsManage/components/outboundDeliveryOrder.vue'),
            meta: {
              title: '出库单',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/hazardousChemicalGoodsManage/leaveWarehouseManage'
            }
          }
        ]
      },
      {
        path: 'inventoryManage',
        redirect: { name: 'inventoryManage' },
        component: EmptyLayout,
        meta: {
          title: '盘点管理',
          menuAuth: '/hazardousChemicalGoodsManage/inventoryManage'
        },
        children: [
          {
            path: '',
            name: 'inventoryManage',
            component: () => import('@/views/hazardousChemicalGoods/hazardousChemicalGoodsManage/inventoryManage/index.vue'),
            meta: {
              title: '盘点管理',
              sidebar: false,
              breadcrumb: false,
              menuAuth: '/hazardousChemicalGoodsManage/inventoryManage',
              auth: [
                'inventoryManagement:add',
                'inventoryManagement:del',
                'inventoryManagement:check',
              ]
            }
          },
          {
            path: 'inventoryAdd',
            name: 'inventoryAdd',
            component: () => import('@/views/hazardousChemicalGoods/hazardousChemicalGoodsManage/inventoryManage/addInventory.vue'),
            meta: {
              title: '新建盘点',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/hazardousChemicalGoodsManage/inventoryManage'
            }
          }
        ]
      },
      {
        path: 'requisitionRegistrationManage',
        redirect: { name: 'requisitionRegistrationManage' },
        component: EmptyLayout,
        meta: {
          title: '领用登记管理',
          menuAuth: '/hazardousChemicalGoodsManage/requisitionRegistrationManage'
        },
        children: [
          {
            path: '',
            name: 'requisitionRegistrationManage',
            component: () => import('@/views/hazardousChemicalGoods/hazardousChemicalGoodsManage/requisitionRegistrationManage/index.vue'),
            meta: {
              title: '领用登记管理',
              sidebar: false,
              breadcrumb: false,
              menuAuth: '/hazardousChemicalGoodsManage/requisitionRegistrationManage'
            }
          },
          {
            path: 'requisitionRegistrationAdd',
            name: 'requisitionRegistrationAdd',
            component: () => import('@/views/hazardousChemicalGoods/hazardousChemicalGoodsManage/requisitionRegistrationManage/addRequisitionRegistration.vue'),
            meta: {
              title: '新建领用登记',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/hazardousChemicalGoodsManage/requisitionRegistrationManage'
            }
          },
          {
            path: 'requisitionRegistrationView',
            name: 'requisitionRegistrationView',
            component: () => import('@/views/hazardousChemicalGoods/hazardousChemicalGoodsManage/requisitionRegistrationManage/requisitionRegistrationDetail.vue'),
            meta: {
              title: '领用登记详情',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/hazardousChemicalGoodsManage/requisitionRegistrationManage'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/hazardousChemicalGoods_config',
    component: Layout,
    name: 'hazardousChemicalGoods_config',
    meta: {
      title: '危化品管理配置',
      menuAuth: '/hazardousChemicalGoods_config'
    },
    children: [
      {
        path: 'unitsManage',
        component: EmptyLayout,
        meta: {
          title: '单位管理',
          menuAuth: '/hazardousChemicalGoods_config/unitsManage'
        },
        children: [
          {
            path: '',
            name: 'unitsManage',
            component: () => import('@/views/hazardousChemicalGoods/hazardousChemicalGoods_config/unitsManage/index.vue'),
            meta: {
              title: '单位管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'addUnitsManage',
            name: 'addUnitsManage',
            component: () => import('@/views/hazardousChemicalGoods/hazardousChemicalGoods_config/unitsManage/addUnitsManage.vue'),
            meta: {
              title: '创建单位',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'functionConfig',
        component: EmptyLayout,
        meta: {
          title: '功能配置',
          menuAuth: '/hazardousChemicalGoods_config/functionConfig'
        },
        children: [
          {
            path: '',
            name: 'functionConfig',
            component: () => import('@/views/hazardousChemicalGoods/hazardousChemicalGoods_config/functionConfig/index.vue'),
            meta: {
              title: '功能配置',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'addFunctionConfig',
            name: 'addFunctionConfig',
            component: () => import('@/views/hazardousChemicalGoods/hazardousChemicalGoods_config/functionConfig/addFunctionConfig.vue'),
            meta: {
              title: '创建危化品分类',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      }
    ]
  },
  {
    path: '/hazardousChemicalGoods_safety',
    component: Layout,
    name: 'hazardousChemicalGoods_safety',
    redirect: '/hazardousChemicalGoodsOverview/safetyManageSystem',
    meta: {
      title: '库房安全管理',
      menuAuth: '/hazardousChemicalGoods_safety'
    },
    children: [
      {
        path: 'safetyManageSystem',
        component: EmptyLayout,
        redirect: { name: 'safetyManageSystem' },
        meta: {
          title: '安全管理制度',
          menuAuth: '/hazardousChemicalGoods_safety/safetyManageSystem'
        },
        children: [
          {
            path: '',
            name: 'safetyManageSystem',
            component: () => import('@/views/hazardousChemicalGoods/hazardousChemicalGoods_safety/safetyManageSystem/index.vue'),
            meta: {
              title: '安全管理制度',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/hazardousChemicalGoods_safety/safetyManageSystem',
              auth: ['safetyManageSystem:create', 'safetyManageSystem:edit', 'safetyManageSystem:delete', 'roleManagement:export']
            }
          },
          {
            path: 'safetyManageSystemDocAdd',
            name: 'safetyManageSystemDocAdd',
            component: () => import('@/views/hazardousChemicalGoods/hazardousChemicalGoods_safety/safetyManageSystem/addDoc.vue'),
            meta: {
              title: '创建文档',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/hazardousChemicalGoods_safety/safetyManageSystem'
            }
          },
          {
            path: 'safetyManageSystemDocView',
            name: 'safetyManageSystemDocView',
            component: () => import('@/views/hazardousChemicalGoods/hazardousChemicalGoods_safety/safetyManageSystem/DocumentDetails.vue'),
            meta: {
              title: '文档详情',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/hazardousChemicalGoods_safety/safetyManageSystem'
            }
          }
        ]
      },
      {
        path: 'educationArchives',
        component: EmptyLayout,
        redirect: { name: 'educationArchives' },
        meta: {
          title: '教育与培训档案',
          menuAuth: '/hazardousChemicalGoods_safety/educationArchives'
        },
        children: [
          {
            path: '',
            name: 'educationArchives',
            component: () => import('@/views/hazardousChemicalGoods/hazardousChemicalGoods_safety/educationArchives/index.vue'),
            meta: {
              title: '教育与培训档案',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/hazardousChemicalGoods_safety/educationArchives',
              auth: ['educationArchives:create', 'educationArchives:edit', 'educationArchives:delete', 'educationArchives:export']
            }
          },
          {
            path: 'educationArchivesDocAdd',
            name: 'educationArchivesDocAdd',
            component: () => import('@/views/hazardousChemicalGoods/hazardousChemicalGoods_safety/educationArchives/addDoc.vue'),
            meta: {
              title: '创建培训记录',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/hazardousChemicalGoods_safety/educationArchives'
            }
          },
          {
            path: 'educationArchivesDocView',
            name: 'educationArchivesDocView',
            component: () => import('@/views/hazardousChemicalGoods/hazardousChemicalGoods_safety/educationArchives/DocumentDetails.vue'),
            meta: {
              title: '培训记录详情',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/hazardousChemicalGoods_safety/educationArchives'
            }
          }
        ]
      },
      {
        path: 'riskIdentify',
        component: EmptyLayout,
        redirect: { name: 'riskIdentify' },
        meta: {
          title: '风险识别与分析',
          menuAuth: '/hazardousChemicalGoods_safety/riskIdentify'
        },
        children: [
          {
            path: '',
            name: 'riskIdentify',
            component: () => import('@/views/hazardousChemicalGoods/hazardousChemicalGoods_safety/riskIdentify/index.vue'),
            meta: {
              title: '风险识别与分析',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/hazardousChemicalGoods_safety/riskIdentify',
              auth: [
                'riskIdentificationAndAnalysis:add',
                'riskIdentificationAndAnalysis:del',
                'riskIdentificationAndAnalysis:edit',
                'riskIdentificationAndAnalysis:check',
              ]
            }
          },
          {
            path: 'riskIdentifyRecordAdd',
            name: 'riskIdentifyRecordAdd',
            component: () => import('@/views/hazardousChemicalGoods/hazardousChemicalGoods_safety/riskIdentify/addRiskIdentify.vue'),
            meta: {
              title: '风险识别与分析',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/hazardousChemicalGoods_safety/riskIdentify'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/hazardousChemicalGoods_inspection',
    component: Layout,
    name: 'hazardousChemicalGoods_inspection',
    meta: {
      title: '安全巡查管理',
      menuAuth: '/hazardousChemicalGoods_inspection'
    },
    children: [
      {
        path: 'templateManage',
        component: EmptyLayout,
        meta: {
          title: '日常巡查模版',
          menuAuth: '/hazardousChemicalGoods_inspection/templateManage'
        },
        children: [
          {
            path: '',
            name: 'hsc_templateManage',
            component: () => import('@/views/equipmentCenter/InspectionManagement/templateManagement.vue'),
            meta: {
              title: '日常巡查模版',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/hazardousChemicalGoods_inspection/templateManage',
              type: '4'
            }
          },
          {
            path: 'comInsAddTemplate',
            name: 'hsc_comInsAddTemplate',
            component: () => import('@/views/equipmentCenter/InspectionManagement/components/addTemplate.vue'),
            meta: {
              title: '新增模板',
              sidebar: false,
              activeMenu: '/hazardousChemicalGoods_inspection/templateManagement',
              type: '4'
            }
          },
          {
            path: 'comInsTemplateDetail',
            name: 'hsc_comInsTemplateDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/components/templateDetail.vue'),
            meta: {
              title: '模板详情',
              sidebar: false,
              activeMenu: '/hazardousChemicalGoods_inspection/templateManagement',
              type: '4'
            }
          }
        ]
      },
      {
        path: 'planManagement',
        component: EmptyLayout,
        redirect: { name: 'hsc_planManagement' },
        meta: {
          title: '日常巡查计划',
          menuAuth: '/hazardousChemicalGoods_inspection/planManagement'
        },
        children: [
          {
            path: '',
            name: 'hsc_planManagement',
            component: () => import('@/views/Inspection/InspectionManagement/planManagement.vue'),
            meta: {
              title: '日常巡查计划',
              sidebar: false,
              breadcrumb: false,
              type: '4'
            }
          },
          {
            path: 'progressDetail',
            name: 'hsc_progressDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/progressDetail.vue'),
            meta: {
              title: '计划进度详情',
              sidebar: false,
              activeMenu: '/hazardousChemicalGoods_inspection/planManagement',
              type: '4'
            }
          },
          {
            path: 'planProgressDetail',
            name: 'hsc_planProgressDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/planProgressDetail.vue'),
            meta: {
              title: '任务详情',
              sidebar: false,
              activeMenu: '/hazardousChemicalGoods_inspection/planManagement',
              type: '4'
            }
          },
          {
            path: 'InspectionPointDetail',
            name: 'hsc_InspectionPointDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/components/InspectionPointDetail.vue'),
            meta: {
              title: '巡检点详情',
              sidebar: false,
              activeMenu: '/hazardousChemicalGoods_inspection/planManagement',
              type: '4'
            }
          },
          {
            path: 'addPlans',
            name: 'hsc_addPlans',
            component: () => import('@/views/equipmentCenter/InspectionManagement/addPlans.vue'),
            meta: {
              title: '新增计划',
              sidebar: false,
              activeMenu: '/hazardousChemicalGoods_inspection/planManagement',
              type: '4'
            }
          }
        ]
      },
      {
        path: 'taskManagement',
        component: EmptyLayout,
        redirect: { name: 'hsc_taskManagement' },
        meta: {
          title: '日常巡查任务',
          menuAuth: '/hazardousChemicalGoods_inspection/taskManagement'
        },
        children: [
          {
            path: '',
            name: 'hsc_taskManagement',
            component: () => import('@/views/Inspection/InspectionManagement/taskManagement.vue'),
            meta: {
              title: '日常巡查任务',
              sidebar: false,
              breadcrumb: false,
              type: '4'
            }
          },
          {
            path: 'taskDetail',
            name: 'hsc_taskDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/taskDetail.vue'),
            meta: {
              title: '任务详情',
              sidebar: false,
              activeMenu: '/hazardousChemicalGoods_inspection/taskManagement',
              type: '4'
            }
          },
          {
            path: 'taskPointDetail',
            name: 'hsc_taskPointDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/components/InspectionPointDetail.vue'),
            meta: {
              title: '巡检点详情',
              sidebar: false,
              activeMenu: '/hazardousChemicalGoods_inspection/taskManagement',
              type: '4'
            }
          },
          {
            path: 'taskPointEdit',
            name: 'hsc_taskPointEdit',
            component: () => import('@/views/equipmentCenter/InspectionManagement/planProgressDetail.vue'),
            meta: {
              title: '巡检点编辑',
              sidebar: false,
              activeMenu: '/hazardousChemicalGoods_inspection/taskManagement',
              type: '4'
            }
          }
        ]
      },
      {
        path: 'specialInspList',
        component: EmptyLayout,
        redirect: { name: 'hsc_specialInspectionList' },
        meta: {
          title: '管理部门安全巡查记录',
          menuAuth: '/hazardousChemicalGoods_inspection/specialInspList'
        },
        children: [
          {
            path: '',
            name: 'hsc_specialInspectionList',
            component: () => import('@/views/hazardousChemicalGoods/hazardousChemicalGoods_inspection/index.vue'),
            meta: {
              title: '管理部门安全巡查记录',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/hazardousChemicalGoods_inspection/specialInspList'
            }
          },
          {
            path: 'checkTableDetail',
            name: 'hsc_checkTableDetail',
            component: () => import('@/views/hazardousChemicalGoods/hazardousChemicalGoods_inspection/checkDetail.vue'),
            meta: {
              title: '检查详情',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/hazardousChemicalGoods_inspection/specialInspList'
            }
          }
        ]
      }
    ]
  }
]
