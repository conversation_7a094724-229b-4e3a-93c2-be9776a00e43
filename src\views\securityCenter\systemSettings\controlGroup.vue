<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%;">
      <div class="role-content-left">
        <div class="toptip">
          <span class="green_line"></span>
          管控小组
        </div>
        <div class="left_content">
          <el-input v-model.trim="filterText" clearable placeholder="输入关键字进行过滤"></el-input>
          <div class="tree">
            <el-tree
              ref="tree"
              v-loading
              :data="treeData"
              :props="defaultProps"
              node-key="id"
              :filter-node-method="filterNode"
              highlight-current
              @node-click="handleNodeClick"
              @check="treeChecked"
            ></el-tree>
          </div>
        </div>
      </div>
      <div class="role-content-right">
        <div style="height: 100%;">
          <div class="search-from">
            <el-button type="primary" :disabled="multipleSelection.length != 1" @click="deletePerson">修改权限</el-button>
          </div>
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table
                ref="multipleTable"
                v-loading="tableLoading"
                highlight-current-row
                :data="tableData"
                border
                stripe
                style="width: 100%;"
                :height="tableHeight"
                @row-click="rowHandleClick"
                @selection-change="handleSelectionChange"
              >
                <!-- <template slot="empty">
                  <p class="tableEmptyLabel">{{ $store.state.tableLabel }}</p>
                </template> -->
                <el-table-column type="selection" width="55" align="center"></el-table-column>
                <el-table-column type="index" width="65" label="序号" align="center">
                  <template slot-scope="scope">
                    <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <!-- <el-table-column prop="username" show-overflow-tooltip label="登录名"></el-table-column> -->
                <el-table-column prop="name" show-overflow-tooltip label="姓名"></el-table-column>
                <el-table-column prop="mobilePhone" show-overflow-tooltip label="手机号"></el-table-column>
                <el-table-column prop="controlTeamName" show-overflow-tooltip label="所属部门"></el-table-column>
                <el-table-column prop="teamTypeName" show-overflow-tooltip label="部门类别"></el-table-column>
                <el-table-column prop="positionType" show-overflow-tooltip label="权限">
                  <template slot-scope="scope">
                    <span>
                      {{ scope.row.positionType == 1 ? '组长' : scope.row.positionType == 2 ? '组员' : '' }}
                    </span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="contentTable-footer">
              <el-pagination
                :current-page="paginationData.currentPage"
                :page-sizes="[15, 30, 50, 100]"
                :page-size="paginationData.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="paginationData.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              ></el-pagination>
            </div>
          </div>
        </div>
      </div>
      <el-dialog v-dialogDrag custom-class="model-dialog" title="修改权限" width="30%" :visible.sync="dialogVisibleRole" @close="closeDialog">
        <div v-loading style="min-height: 200px; width: 100%; background-color: #fff; padding: 10px;">
          <el-radio v-model="radio" label="1">组长</el-radio>
          <el-radio v-model="radio" label="2">组员</el-radio>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="dialogVisibleRole = false">取 消</el-button>
          <el-button type="primary" @click="closeDialogRole">确定</el-button>
        </span>
      </el-dialog>
    </div>
  </PageContainer>
</template>

<script>
import tableListMixin from '@/mixins/tableListMixin.js'
import { transData } from '@/util'
export default {
  name: 'controlGroup',
  components: {},
  mixins: [tableListMixin],
  data() {
    return {
      filterText: '',
      radio: '',
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      multipleSelection: [],
      tableData: [],
      advancClose: true,
      treeLoading: false,
      tableLoading: false,
      organizationTypeArr: [],
      defaultProps: {
        children: 'children',
        label: function (data, node) {
          return data.teamName
        },
        value: 'id'
      },
      treeData: [],
      checkedData: '',
      dialogVisible: false,
      dialogVisibleRole: false,
      // roleType: JSON.parse(sessionStorage.getItem('LOGINDATA')).moduleIdentity || ''
      roleType: ''
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    rowHandleClick(row, column, event) {
      // this.$refs.multipleTable.toggleRowSelection(row)
    },
    deletePerson(row) {
      this.dialogVisibleRole = true
      if (this.multipleSelection[0].permissionName == '组长') {
        this.radio = '1'
      } else if (this.multipleSelection[0].permissionName == '组员') {
        this.radio = '2'
      }
    },
    // 修改权限
    closeDialogRole(val) {
      let params = {
        ids: this.multipleSelection[0].id,
        ...this.multipleSelection[0],
        positionType: this.radio
      }
      this.$api.ipsmUpdateControlTeamUser(params).then((res) => {
        if (res.code == 200) {
          this.$message.success(res.message)
          this.dialogVisibleRole = false
        } else {
          this.$message.error(res.message)
        }
        this.getStaffList(this.checkedData.id)
        // this.$refs.tree.setCurrentKey(this.treeData[0].id);
      })
    },
    closeDialog(val) {
      this.dialogVisible = false
      // this.init();
    },
    treeChecked(data, checked) {
      this.checkedData = data
      this.$refs.tree.setCheckedNodes([data])
    },

    init() {
      this.treeLoading = true
      // 获取角色列表
      this.$api.ipsmGetControlGroupInfoList({ roleType: this.roleType }).then((res) => {
        this.treeLoading = false
        this.treeData = transData(res.data.list, 'id', 'parentId', 'children')
        this.$nextTick(() => {
          if (!this.checkedData && this.treeData.length) {
            this.handleNodeClick(this.treeData[0])
            this.$refs.tree.setCurrentKey(this.treeData[0].id)
          }
        })
        this.tableData = []
      })
      // this.getStaffList()
    },
    getStaffList(val) {
      this.tableLoading = true
      let data = {
        controlTeamId: this.checkedData.id == '#' ? '' : this.checkedData.id, // 选第一级传空
        currentPage: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize
      }
      this.$api
        .ipsmGetControlTeamUserList(data)
        .then((res) => {
          if (res.code == 200) {
            this.tableLoading = false
            this.tableData = res.data.list
            this.paginationData.total = parseInt(res.data.sum)
          }
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    filterNode(value, data) {
      if (!value) return true
      return data.teamName.indexOf(value) !== -1
    },

    //  ----------------------------------------------------分页相关------------------------------------------------------------
    handleSizeChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      this.getStaffList(this.checkedData.id)
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getStaffList(this.checkedData.id)
    },
    //  ----------------------------------------------------树状结构相关---------------------------------------------------------
    /**
     * 树状图点击
     */
    handleNodeClick(data, checked) {
      this.paginationData.currentPage = 1
      // this.$store.commit('changeTableLabel', 'search')
      this.checkedData = data
      this.$refs.tree.setCheckedNodes([data])
      this.getStaffList(data.id)
    },
    //  ------------------------------------------------操作表格中数据------------------------------------------------------------
    handleSelectionChange(val) {
      this.multipleSelection = val
    }
  }
}
</script>
<style lang="scss" scoped>
// el-tree-node is-expanded is-focusable
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}

::v-deep .el-tree-node__content {
  height: 32px;
}

.role-content {
  height: 100%;
  display: flex;

  .role-content-left {
    width: 246px;
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    margin-right: 12px;

    .left_content {
      height: calc(100% - 60px);
      padding: 10px;
      // overflow: auto;
      .tree {
        margin-top: 20px;
        height: calc(100% - 40px);
        overflow: auto;
      }
    }
  }

  .role-content-right {
    flex: 1;
    height: 100%;
    min-width: 0;
    padding: 10px;
    background: #fff;
    border-radius: 4px;

    .search-from {
      padding-bottom: 12px;

      & > div {
        margin-right: 10px;
      }

      & > button {
        margin-top: 12px;
      }
    }

    .contentTable {
      height: calc(100% - 50px);
      display: flex;
      flex-direction: column;

      .contentTable-main {
        flex: 1;
        overflow: auto;
      }

      .contentTable-footer {
        padding: 10px 0 0;
      }
    }
  }
}
</style>
