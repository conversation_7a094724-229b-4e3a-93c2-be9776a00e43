<template>
  <PageContainer>
    <div slot="header">
      <div class="search-header">
        <el-tabs v-model="activeWarnType" @tab-click="handleWarnTypeClick">
          <el-tab-pane label="待处理" name="wait" />
          <el-tab-pane label="全部" name="all" />
        </el-tabs>
        <div class="search-from">
          <el-select
            v-model="searchFrom.projectCode"
            placeholder="报警来源"
            multiple
            filterable
            collapse-tags
            clearable
            @change="getIncidentAndSourceGroup"
            @clear="
              () => {
                searchFrom.incidentType = ''
              }
            "
          >
            <el-option v-for="item in alarmSourceOptions" :key="item.projectCode" :label="item.projectName" :value="item.projectCode"> </el-option>
          </el-select>
          <el-select v-model="searchFrom.incidentType" placeholder="事件类型" clearable :disabled="!(searchFrom.projectCode.length == 1)">
            <el-option v-for="item in eventTypeOptions" :key="item.incidentName" :label="item.incidentName" :value="item.incidentType"> </el-option>
          </el-select>
          <el-select v-model="searchFrom.alarmLevel" placeholder="报警等级" clearable>
            <el-option v-for="item in alarmLevelOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
          <el-select ref="treeSelect" v-model="searchFrom.alarmSpaceId" clearable placeholder="空间位置" @clear="handleClear">
            <el-option hidden :value="searchFrom.alarmSpaceId" :label="areaName"> </el-option>
            <el-tree
              :data="serverSpaces"
              :props="serverDefaultProps"
              :load="serverLoadNode"
              lazy
              :expand-on-click-node="false"
              :check-on-click-node="true"
              @node-click="handleNodeClick"
            >
            </el-tree>
          </el-select>
          <el-date-picker
            v-model="searchFrom.dataRange"
            type="daterange"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions"
          >
          </el-date-picker>
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="searchForm">查询</el-button>
        </div>
        <div class="batch-control">
          <el-button v-for="(item, index) in batchControlList" :key="index" :disabled="!selectAlarmList.length" type="primary" @click="batchControlEvent(item, selectAlarmList)">{{
            item.label
          }}</el-button>
        </div>
      </div>
    </div>
    <div slot="content" style="height: 100%">
      <template v-if="alarmDetailShow">
        <AlarmDetailDialog ref="alarmDetail" :alarmId="selectAlarmItem.alarmId" :visible.sync="alarmDetailShow" @operating="operating" />
      </template>
      <WorkOrderDetailDialog v-if="workOrderDetailShow" :visible.sync="workOrderDetailShow" :alarmDetail="selectAlarmItem" :dialogDetail="workOderDialogData" />
      <unhandledTable v-if="checkActive" ref="unhandledTable" :params="searchFrom" @getSelectList="getSelectList" @operating="operating" />
      <wholeTable v-else ref="wholeTable" :params="searchFrom" @getSelectList="getSelectList" @operating="operating" />
      <screenDialog v-if="scrDialog" :selectItems="screenSelectItems" :visible.sync="scrDialog" />
      <template v-if="remrDialog">
        <remarkDialog :visible.sync="remrDialog" :item="selectAlarmItem"></remarkDialog>
      </template>
      <template v-if="showConfirmAlarm">
        <confirmAlarmDialog :visible.sync="showConfirmAlarm" :item="selectAlarmItem" :isBatch="isBatch" :batchList="selectAlarmList"></confirmAlarmDialog>
      </template>
    </div>
  </PageContainer>
</template>
<script>
import { transData, ListTree } from '@/util'
import { mapGetters } from 'vuex'
import moment from 'moment'
export default {
  name: 'AlarmRecordIndex',
  components: {
    unhandledTable: () => import('./components/unhandledTable'),
    wholeTable: () => import('./components/wholeTable'),
    screenDialog: () => import('./components/screenDialog'),
    remarkDialog: () => import('./components/remarkDialog'),
    confirmAlarmDialog: () => import('./components/confirmAlarmDialog')
  },
  data() {
    return {
      activeWarnType: 'wait', // 选中tabs切换
      alarmSourceOptions: [], // 报警来源
      eventTypeOptions: [], // 事件类型
      alarmLevelOptions: [
        // 报警等级
        {
          value: '0',
          label: '通知'
        },
        {
          value: '1',
          label: '一般'
        },
        {
          value: '2',
          label: '紧急'
        },
        {
          value: '3',
          label: '重要'
        }
      ],
      serverSpaces: [], // 空间位置
      spaces: [], // 空间位置
      serverDefaultProps: {
        label: 'ssmName',
        isLeaf: 'leaf',
        children: 'children'
      },
      areaName: '', // 选中 下拉树的name
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      searchFrom: {
        // 搜索条件
        projectCode: [], // 报警来源
        incidentType: '', // 事件类型
        alarmLevel: '', // 报警等级
        alarmSpaceId: '', // 空间位置
        dataRange: [], // 时间范围
        objectId: '' // 对象id
      },
      batchControlList: [
        // 批量控制
        {
          state: 1,
          label: '关闭'
        },
        {
          state: 2,
          label: '真实报警'
        },
        {
          state: 3,
          label: '误报'
        },
        {
          state: 4,
          label: '演练'
        },
        {
          state: 5,
          label: '调试'
        },
        {
          state: 6,
          label: '屏蔽'
        }
      ],
      isBatch: false,
      selectAlarmList: [], // 选中的报警列表
      scrDialog: false, // 屏蔽弹窗
      workOrderDetailShow: false, // 工单详情
      remrDialog: false, // 备注弹窗
      showConfirmAlarm: false, // 确警弹窗
      workOderDialogData: [], // 工单传递给子组件的数据
      alarmDetailShow: false, // 报警详情弹窗
      selectAlarmItem: {}, // 选中报警项
      screenSelectItems: [] // 屏蔽选中数据
    }
  },
  computed: {
    checkActive() {
      return this.activeWarnType === 'wait'
    },
    ...mapGetters({
      socketMsgs: 'socket/socketMsgs'
    })
  },
  watch: {
    scrDialog(val) {
      if (!val) {
        this.initComponentData()
      }
    },
    workOrderDetailShow(val) {
      if (!val) {
        this.initComponentData()
      }
    },
    remrDialog(val) {
      if (!val) {
        this.initComponentData()
      }
    },
    showConfirmAlarm(val) {
      if (!val) {
        this.isBatch = false
        this.initComponentData()
      }
    },
    alarmDetailShow(val) {
      if (!val) {
        this.initComponentData()
      }
    },
    socketMsgs(data) {
      this.initComponentData(true, 1)
    }
  },
  created() {
    const queryData = this.$route.query
    // 从其他页面跳转过来
    if (Object.keys(queryData).length) {
      for (let key in queryData) {
        switch (key) {
          // tab切换 待处理及全部
          case 'activeWarnType':
            this.activeWarnType = queryData[key]
            break
          // 时间过滤 目前直接入查询当日
          case 'dataRange':
            if (queryData[key]) {
              if (queryData[key] == 'today') {
                this.searchFrom[key] = [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
              } else {
                this.searchFrom[key] = queryData[key]
              }
            }
            break
          // 告警状态 0未处理 1已处理
          case 'alarmStatus':
            this.searchFrom[key] = queryData[key]
            break
          // 报警来源都好拼接
          case 'projectCode':
            this.searchFrom[key] = queryData[key].split(',') || []
            this.getIncidentAndSourceGroup(this.searchFrom[key])
            break
          // 报警对象id（监测实体id）
          case 'objectId':
            this.searchFrom[key] = queryData[key]
            break
        }
      }
    }
  },
  mounted() {
    this.getTreelist()
    this.getAlarmSource()
  },
  methods: {
    // 一键派单
    oneKeyDispatch(row) {
      const userInfo = this.$store.state.user.userInfo.user
      let param = {
        alarmId: row.alarmId,
        alarmLevel: row.alarmLevel,
        alarmSourceName: row.alarmSource,
        incidentName: row.incidentName,
        spaceId: row.alarmSpaceId,
        userName: userInfo.staffName,
        userId: userInfo.staffId,
        projectCode: row.projectCode
      }
      this.$api.OneKeyDispatch(param).then((res) => {
        if (res.code == 200) {
          this.$message({
            message: '已派单',
            type: 'success'
          })
          this.initComponentData()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 获取报警来源
    getAlarmSource() {
      this.$api.getSourceByEmpty().then((res) => {
        if (res.code == 200) {
          this.alarmSourceOptions = res.data
        }
      })
    },
    // 获取事件类型以及报警来源分组
    getIncidentAndSourceGroup(val) {
      this.searchFrom.incidentType = ''
      this.$api.getIncidentGroupByProjectCode({ projectCode: val.toString() }).then((res) => {
        if (res.code == 200) {
          // this.alarmSourceOptions = res.data
          // this.alarmSourceOptions = res.data.source
          this.eventTypeOptions = res.data
        }
      })
    },
    // 子/孙组件流程操作
    operating(type, selectItem) {
      // dispatch(派单) confirmAlarm(确警) remark(备注) shielded(屏蔽) close(关闭) viewDetails(查看详情)
      this.selectAlarmItem = selectItem
      console.log(type, selectItem)
      if (type == 'dispatch') {
        // 派单
        this.$confirm('是否确认派发确警工单?', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.oneKeyDispatch(selectItem)
        })
      }
      // 确警
      if (type == 'confirmAlarm') {
        this.showConfirmAlarm = !this.showConfirmAlarm
      }
      // 备注
      if (type == 'remark') {
        this.remrDialog = !this.remrDialog
      }
      // 屏蔽
      if (type == 'shielded') {
        if (selectItem.shield == 1) {
          this.$confirm('是否取消屏蔽当前报警?', '提示', {
            cancelButtonClass: 'el-button--primary is-plain',
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$api
              .shield({
                alarmId: selectItem.alarmId,
                alarmObjectId: selectItem.alarmObjectId,
                incidentType: selectItem.incidentType,
                shield: false
              })
              .then((res) => {
                if (res.code == 200) {
                  this.$message({
                    message: '已取消屏蔽',
                    type: 'success'
                  })
                  this.initComponentData()
                }
              })
          })
        } else {
          this.batchControlEvent({ state: 6, label: '屏蔽' }, [selectItem])
        }
      }
      // 关闭
      // 未处理关闭前先走警情确认流程
      if (type == 'close') {
        if (selectItem.alarmStatus == 0) {
          this.$confirm('当前报警未处理，是否确警后再进行关闭？', '提示', {
            cancelButtonClass: 'el-button--primary is-plain',
            confirmButtonText: '确警',
            cancelButtonText: '取消关闭',
            type: 'warning'
          }).then(() => {
            this.operating('confirmAlarm', selectItem)
          })
        } else {
          this.$confirm('是否关闭当前报警记录?', '提示', {
            cancelButtonClass: 'el-button--primary is-plain',
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.closeAlarmRecord(selectItem.alarmId)
          })
        }
      }
      // 报警详情
      if (type == 'alarmDetails') {
        this.alarmDetailShow = !this.alarmDetailShow
      }
      // 工单详情
      if (type == 'workOrderDetails') {
        this.workOderDialogData = []
        // 获取报警详情
        this.getAlarmDetails(selectItem.alarmId)
      }
      // 存为经典案例
      if (type == 'collect') {
        this.collectAlarmRecords()
      }
    },
    // 报警详情
    getAlarmDetails(alarmId) {
      this.$api.GetAlarmDetails({ alarmId: alarmId }).then((res) => {
        if (res.code == 200) {
          // 根据报警详情获取工单详情
          const workOrderList =
            res.data?.workInfo?.map((e) => {
              return {
                workTypeName: e.workTypeName,
                id: e.workNum
              }
            }) ?? []
          if (!workOrderList.length) {
            this.$message.warning('该报警暂无关联工单！')
          } else {
            this.workOderDialogData = workOrderList
            this.workOrderDetailShow = !this.workOrderDetailShow
          }
        }
      })
    },
    // 关闭
    closeAlarmRecord(alarmId) {
      this.$api.CloseAlarmRecord({ alarmId }).then((res) => {
        if (res.code == 200) {
          this.initComponentData()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 确警
    alarmAffirm(status, list) {
      let params = {
        alarmAffirm: status,
        alarmId: list.map((item) => item.alarmId).join(','),
        projectCode: list.map((item) => item.projectCode).join(',')
      }
      this.$api.AlarmAffirm(params).then((res) => {
        if (res.code == 200) {
          this.initComponentData()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 存为经典案例
    collectAlarmRecords() {
      let param = {
        alarmId: this.selectAlarmItem.alarmId,
        classic: this.selectAlarmItem.classic == 1 ? '0' : '1'
      }
      this.$api.CollectAlarmRecords(param).then((res) => {
        if (res.code == 200) {
          this.initComponentData()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 初始化组件数据
    initComponentData(tab = false, page) {
      this.selectAlarmList = []
      this.selectAlarmItem = {}
      // 初始化报警详情
      if (this.alarmDetailShow && !tab) {
        this.$refs.alarmDetail.getAlarmDetails()
        return
      }
      // 初始化待处理页面
      if (this.activeWarnType === 'wait' && this.$refs.unhandledTable) {
        this.$refs.unhandledTable.clearSelect()
        this.$refs.unhandledTable.getAlarmRecord('shielded', 1)
        this.$refs.unhandledTable.getAlarmRecord('unhandled', 1)
        this.$refs.unhandledTable.getAlarmRecord('processing', 1)
        return
      }
      if (this.activeWarnType === 'all' && this.$refs.wholeTable) {
        this.$refs.wholeTable.getDataList(page)
        this.$refs.wholeTable.clearSelect()
      }
    },
    // 子组件选中事件
    getSelectList(data) {
      this.selectAlarmList = data
    },
    // 空间数据清除
    handleClear() {
      this.searchFrom.alarmSpaceId = ''
      this.areaName = ''
    },
    // 选择下拉树 数据
    handleNodeClick(data) {
      this.searchFrom.alarmSpaceId = data.id
      this.areaName = data.ssmName
      this.$refs.treeSelect.blur()
    },
    // tabs切换
    handleWarnTypeClick() {
      this.selectAlarmList = []
      this.initComponentData()
      // this.resetForm()
    },
    // 批量处置按钮
    batchControlEvent(item, arr = this.selectAlarmList) {
      console.log(item.state, '处置状态')
      // 如果状态为关闭报警
      if (item.state == '1') {
        // 如果选择的报警中有已关闭的报警
        if (this.selectAlarmList.map((item) => item.alarmStatus).includes(2)) {
          this.$message.error('当前选择报警已被关闭，请重新选择')
          return
          // 如果选择的报警中有未警情确认的报警
        } else if (this.selectAlarmList.some((item) => ![1, 2, 3, 4].includes(item.alarmAffirm))) {
          // 如果选择的报警都是未确警的
          if (this.selectAlarmList.filter((item) => ![1, 2, 3, 4].includes(item.alarmAffirm)).length == this.selectAlarmList.length) {
            this.$confirm('当前报警未确警，是否确警后再进行关闭？', '提示', {
              cancelButtonClass: 'el-button--primary is-plain',
              confirmButtonText: '确警',
              cancelButtonText: '取消关闭',
              type: 'warning'
            }).then(() => {
              this.isBatch = true
              this.showConfirmAlarm = !this.showConfirmAlarm
            })
          } else {
            this.$message.error('当前选择报警已有警情确认，请重新选择')
            return
          }
        } else {
          // 没有已关闭或未警情确认的报警 进行关闭操作
          this.$confirm(`是否关闭${arr.length}条报警记录?`, '提示', {
            cancelButtonClass: 'el-button--primary is-plain',
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.closeAlarmRecord(arr.map((item) => item.alarmId).join(','))
          })
        }
        // this.$confirm('当前报警未处理，是否确警后再进行关闭？', '提示', {
        //     cancelButtonClass: 'el-button--primary is-plain',
        //     confirmButtonText: '确警',
        //     cancelButtonText: '取消关闭',
        //     type: 'warning'
        //   }).then(() => {
        //     this.operating('confirmAlarm', selectItem)
        //   })
      } else if (item.state == '6') {
        if (this.selectAlarmList.map((item) => item.shield).includes(1)) {
          this.$message.error('当前选择报警已被屏蔽，请重新选择')
          return
        }
        this.scrDialog = !this.scrDialog
        this.screenSelectItems = arr
      } else if ([2, 3, 4, 5].includes(item.state)) {
        if (this.selectAlarmList.map((item) => item.alarmAffirm).includes(1 || 2 || 3 || 4)) {
          this.$message.error('当前选择报警已有警情确认，请重新选择')
          return
        }
        this.$confirm(`是否将${arr.length}条报警记录确警为${item.label}?`, '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.alarmAffirm(item.state - 1, arr)
        })
      }
    },
    // 重置查询
    resetForm() {
      this.handleClear()
      Object.assign(this.searchFrom, {
        projectCode: [], // 报警来源
        incidentType: '', // 事件类型
        alarmLevel: '', // 报警等级
        dataRange: [], // 时间范围
        objectId: '' // 报警对象
      })
      this.initComponentData(true, 1)
    },
    // 查询
    searchForm() {
      this.initComponentData(true, 1)
    },
    // 获取字典项
    getDictionaryList() {
      this.$api
        .getDictionaryList({
          dictType: 0
        })
        .then((res) => {
          this.jibielist = res.data
        })
    },
    // 下拉树懒加载
    serverLoadNode(node, resolve) {
      if (node.level == 0) {
        return resolve(node.data)
      } else if (node.level > 0 && node.level < 4) {
        return resolve(node.data.children)
      } else {
        return this.spaceTreeChange(node.data, resolve)
      }
    },
    // 获取服务空间树形结构
    getTreelist() {
      // this.$api.getStructureTree({}, __PATH.USER_CODE).then((res) => {
      this.$api.getStructureTree({}).then((res) => {
        if (res.code == 200) {
          this.spaces = res.data
          // 增加 懒加载节点
          res.data.map((e) => {
            e.leaf = false
          })
          // 转换为树形结构数据
          this.serverSpaces = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    // 服务空间树结构点击获取自己及父节点ID并请求数据实现懒加载
    spaceTreeChange(value, resolve) {
      const child = ListTree(this.spaces, value.pid)
      child.push(value.id)
      let treeId = child.toString()
      let data = {
        current: 1,
        functionDictId: '',
        simCode: treeId,
        size: 999
      }
      // this.$api.getSpaceInfoList(data, __PATH.USER_CODE).then((res) => {
      this.$api.getSpaceInfoList(data).then((res) => {
        if (res.code == 200) {
          if (typeof resolve === 'function') {
            let treeNodeData = JSON.parse(JSON.stringify(res.data.records))
            treeNodeData.map((e) => {
              e.leaf = true
            })
            resolve(treeNodeData)
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.search-header {
  padding: 0 8px 10px;
  .search-from {
    padding-bottom: 12px;
    & > div {
      margin-top: 12px;
      margin-right: 10px;
    }
  }
}
</style>
