<!-- 运行率占比弹窗 -->
<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="运行报警"
    width="50%"
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="content">
      <TablePage
        ref="table"
        v-loading="loading"
        :showPage="true"
        :tableColumn="tableColumn"
        :data="tableData"
        height="calc(100% - 40px)"
        :pageData="pageData"
        @pagination="paginationChange"
      />
      <alarm-details-dialog v-if="alarmDetailsDialog" :visible.sync="alarmDetailsDialog" :alarmId="alarmDetailsId" />
    </div>
    <span slot="footer">
      <el-button plain @click="closeDialog">关 闭</el-button>
    </span>
  </el-dialog>
</template>
<script lang="jsx">
import alarmDetailsDialog from './alarmDetailsDialog'
export default {
  name: 'alarmDialog',
  components: {
    alarmDetailsDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    alarmTypeItem: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      loading: false,
      alarmDetailsDialog: false,
      alarmDetailsId: '',
      tableData: [],
      pageData: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      tableColumn: [
        {
          prop: 'serialNumber',
          label: '序号',
          formatter: (scope) => {
            return (this.pageData.page - 1) * this.pageData.pageSize + scope.$index + 1
          },
          width: 50
        },
        {
          prop: 'alarmStartTime',
          label: '报警开始时间'
        },
        {
          prop: 'projectName',
          label: '报警系统'
        },
        {
          prop: 'alarmEntityTypeName',
          label: '报警实体类型'
        },
        {
          prop: 'alarmObjectName',
          label: '报警实体对象'
        },
        {
          prop: 'incidentName',
          label: '报警名称'
        },
        {
          prop: 'alarmRule',
          label: '报警规则'
        },
        {
          width: 50,
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span style="color: #3562db" onClick={() => this.alarmDetails(row.row.id)}>查看</span>
              </div>
            )
          }
        }
      ]
    }
  },
  mounted() {
    this.getDetectionItemAlarmList()
  },
  methods: {
    // 查看报警详情
    alarmDetails(id) {
      this.alarmDetailsId = id
      this.alarmDetailsDialog = true
    },
    // 获取检测项报警记录
    getDetectionItemAlarmList() {
      let params = {
        projectCode: this.alarmTypeItem.projectCode,
        surveyCode: this.alarmTypeItem.alarmObjectId,
        alarmLevel: this.alarmTypeItem.alarmLevel,
        page: this.pageData.page,
        pageSize: this.pageData.pageSize
      }
      this.loading = true
      this.$api.GetDetectionItemAlarmList(params).then((res) => {
        this.loading = false
        if (res.code == 200) {
          this.tableData = res.data.records
          this.pageData.total = res.data.total
        }
      })
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getDetectionItemAlarmList()
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__body {
  /* padding: 0; */
  height: calc(78vh - 110px);
}

.content {
  width: 100%;
  height: 100%;
}

.model-dialog {
  padding: 0 !important;
}

::v-deep .el-pagination {
  .btn-next,
  .btn-prev {
    background: transparent;
  }

  .el-pager li {
    background: transparent;
  }
}
</style>
