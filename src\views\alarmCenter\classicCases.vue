<template>
  <PageContainer>
    <div slot="header">
      <div class="search-header">
        <div class="search-from">
          <el-select v-model="searchForm.alarmLevel" placeholder="全部报警等级" clearable>
            <el-option v-for="item in alarmLevelOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
          <el-input v-model="searchForm.alarmId" placeholder="搜索报警ID" suffix-icon="el-icon-search" clearable></el-input>
          <el-select
            v-model="searchForm.projectCode"
            placeholder="全部报警系统"
            filterable
            clearable
            @change="getIncidentAndSourceGroup"
            @clear="
              () => {
                searchForm.alarmType = []
              }
            "
          >
            <el-option v-for="item in alarmSourceOptions" :key="item.thirdSystemCode" :label="item.thirdSystemName" :value="item.thirdSystemCode"> </el-option>
          </el-select>
          <el-select v-model="searchForm.alarmType" placeholder="全部报警类型" multiple collapse-tags clearable :disabled="!searchForm.projectCode">
            <el-option v-for="item in eventTypeOptions" :key="item.id" :label="item.alarmDictName" :value="item.id"> </el-option>
          </el-select>
          <el-date-picker
            v-model="searchForm.dataRange"
            type="daterange"
            unlink-panels
            range-separator="至"
            start-placeholder="报警开始时间"
            end-placeholder="报警结束时间"
            value-format="yyyy-MM-dd"
            suffix-icon="el-icon-date"
            :picker-options="pickerOptions"
          >
          </el-date-picker>
          <el-select ref="treeSelect" v-model="searchForm.alarmSpaceId" placeholder="报警位置" clearable @clear="handleClear">
            <el-option hidden :value="searchForm.alarmSpaceId" :label="areaName"> </el-option>
            <el-tree
              :data="serverSpaces"
              :props="serverDefaultProps"
              :load="serverLoadNode"
              lazy
              :expand-on-click-node="false"
              :check-on-click-node="true"
              @node-click="handleNodeClick"
            >
            </el-tree>
          </el-select>
          <el-input v-model="searchForm.deviceId" placeholder="搜索报警设备或设备SN" suffix-icon="el-icon-search" clearable />
          <el-select v-model="searchForm.alarmAffirm" placeholder="全部警情类型" clearable>
            <el-option v-for="item in alarmAffirmList" :key="item.value" :label="item.name" :value="item.value"> </el-option>
          </el-select>
          <el-select v-model="searchForm.haveWork" placeholder="是否关联工单" clearable>
            <el-option v-for="item in relevanceWorkOrderList" :key="item.value" :label="item.name" :value="item.value"> </el-option>
          </el-select>
        </div>
        <div class="batch-control">
          <el-button type="primary" plain @click="reset">重置</el-button>
          <el-button type="primary" @click="search">查询</el-button>
          <el-button type="primary" :disabled="multipleSelection.length < 1" @click="uncollect">取消收藏</el-button>
        </div>
      </div>
    </div>
    <div slot="content" style="height: 100%">
      <div class="contentTable">
        <div class="contentTable-main table-content">
          <el-table
            ref="table"
            v-loading="loading"
            border
            :resizable="false"
            :data="tableData"
            height="100%"
            style="width: 100%"
            @sort-change="tableSortChange"
            @selection-change="selectionChange"
            @row-dblclick="(row) => operating('details', row)"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column prop="alarmLevel" label="报警等级" sortable="custom" width="110">
              <template slot-scope="scope">
                <span class="alarmLevel" :style="{ background: alarmLevelItem[scope.row.alarmLevel].color }">
                  {{ alarmLevelItem[scope.row.alarmLevel].text }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="projectName" label="报警系统" show-overflow-tooltip></el-table-column>
            <el-table-column prop="alarmType" label="报警类型" show-overflow-tooltip></el-table-column>
            <el-table-column prop="alarmStartTime" label="报警时间" sortable="custom" width="170"> </el-table-column>
            <el-table-column prop="alarmSpaceName" label="报警位置" show-overflow-tooltip>
              <template slot-scope="scope">
                <el-tooltip effect="dark" :content="scope.row.alarmSpaceName" placement="top">
                  <span class="alarmSpace">{{ showSpaceName(scope.row.alarmSpaceName) }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="alarmObjectName" label="报警对象" show-overflow-tooltip></el-table-column>
            <el-table-column prop="alarmDetails" label="报警描述" show-overflow-tooltip></el-table-column>
            <el-table-column prop="alarmStatus" label="处理状态" width="120">
              <div slot-scope="scope" class="alarmStatus" :style="{ color: scope.row.alarmStatus == 0 ? '#FA403C' : scope.row.alarmStatus == 1 ? '#fd9434' : '#999' }">
                <span class="alarmStatusIcon" :style="{ background: scope.row.alarmStatus == 0 ? '#FA403C' : scope.row.alarmStatus == 1 ? '#ff9435' : '#999' }"></span>
                {{ scope.row.alarmStatus == 0 ? '未处理' : scope.row.alarmStatus == 1 ? '处理中' : '已处理' }}
              </div>
            </el-table-column>
            <el-table-column prop="alarmAffirm" label="警情类型" show-overflow-tooltip>
              <template slot-scope="scope">
                <span v-if="scope.row.alarmAffirm == '1'">真实报警</span>
                <span v-if="scope.row.alarmAffirm == '2'">误报</span>
                <span v-if="scope.row.alarmAffirm == '3'">演练</span>
                <span v-if="scope.row.alarmAffirm == '4'">调试</span>
              </template>
            </el-table-column>
            <el-table-column label="关联工单" show-overflow-tooltip>
              <template slot-scope="scope">
                <span v-if="!scope.row.workList.length">无</span>
                <span v-else-if="scope.row.workList.length && scope.row.workList.length > 1" style="color: #3562db; cursor: pointer" @click="operating('details', scope.row, 3)">
                  已关联（{{ scope.row.workList.length }}）
                </span>
                <span v-else style="color: #3562db; cursor: pointer" @click="showWorkOrder(scope.row.workList[0])">
                  {{ scope.row.workList[0].flowtype }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="alarmId" label="报警ID" show-overflow-tooltip></el-table-column>
            <el-table-column prop="classic" label="收藏案例" width="80">
              <template slot-scope="scope">
                <span style="color: #3562db; cursor: pointer" @click="handleClass(scope.row)">
                  <i :class="`el-icon-star-${scope.row.classic ? 'on' : 'off'}`"></i>
                  {{ scope.row.classic ? '已存' : '未存' }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-button type="text" @click="operating('details', scope.row)">查看</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="contentTable-footer">
          <el-pagination
            :current-page="pagination.pageNo"
            :page-sizes="pagination.pageSizeOptions"
            :page-size="pagination.pageSize"
            :layout="'total, sizes, ->, prev, pager, next, jumper'"
            :total="pagination.total"
            @size-change="paginationSizeChange"
            @current-change="paginationCurrentChange"
          >
          </el-pagination>
        </div>
      </div>
      <WorkOrderDetailDialog v-if="workOrderDetailShow" sourceType="alarm" :visible.sync="workOrderDetailShow" :alarmDetail="alarmDetail" :dialogDetail="workOderDetailData" />
    </div>
  </PageContainer>
</template>
<script>
import { transData, ListTree } from '@/util'
export default {
  name: 'ClassicCasesIndex',
  beforeRouteEnter(to, from, next) {
    // 进入页面时，先将当前页面的 name 信息存入 keep-alive 全局状态（当前页面的name需要和路由的name一致）
    next((vm) => {
      // 二级页面存储当前级，多级页面存储多级
      vm.$store.commit('keepAlive/add', 'ClassicCasesIndex')
    })
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['alarmDetail'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      alarmSourceOptions: [], // 报警来源
      searchForm: {
        alarmId: '',
        alarmType: [],
        alarmAffirm: '',
        classic: 1, // 经典案例
        // 搜索条件
        projectCode: '', // 报警来源
        alarmLevel: '', // 报警等级
        alarmSpaceId: '', // 空间位置
        dataRange: [], // 时间范围
        deviceId: '', // 对象id
        haveWork: '' // 是否关联工单
      },
      alarmLevelOptions: [
        // 报警等级
        {
          value: '0',
          label: '通知'
        },
        {
          value: '1',
          label: '一般'
        },
        {
          value: '2',
          label: '紧急'
        },
        {
          value: '3',
          label: '重要'
        }
      ],
      relevanceWorkOrderList: [
        {
          name: '是',
          value: 1
        },
        {
          name: '否',
          value: 0
        }
      ],
      alarmAffirmList: [
        {
          name: '真实报警',
          value: '1'
        },
        {
          name: '误报',
          value: '2'
        },
        {
          name: '演练',
          value: '3'
        },
        {
          name: '调试',
          value: '4'
        }
      ],
      pagination: {
        pageSizeOptions: [15, 30, 50, 100],
        pageNo: 1,
        pageSize: 15,
        total: 0
      },
      serverSpaces: [], // 空间位置
      spaces: [], // 空间位置
      serverDefaultProps: {
        label: 'ssmName',
        isLeaf: 'leaf',
        children: 'children'
      },
      areaName: '', // 选中 下拉树的name
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      eventTypeOptions: [], // 事件类型
      slelectAlarmItem: {}, // 选中报警项
      alarmLevelItem: {
        0: { text: '通知', color: '#3562DB' },
        1: { text: '一般', color: '#3562DB' },
        2: { text: '紧急', color: '#FF9435' },
        3: { text: '重要', color: '#FA403C' }
      },
      loading: false,
      tableData: [],
      timeOrType: '', // 排序 0.按紧急降序  1.按紧急升序  2.按时间降序  3.按时间升序
      multipleSelection: [],
      workOderDetailData: [],
      workOrderDetailShow: false,
      alarmDetail: {}
    }
  },
  activated() {
    this.getDataList()
  },
  mounted() {
    this.getTreelist()
    this.getAlarmSource()
    this.getDataList()
  },
  methods: {
    // 获取报警来源
    getAlarmSource() {
      this.$api.getAlarmThirdSystemData().then((res) => {
        if (res.code == 200) {
          this.alarmSourceOptions = res.data
        }
      })
    },
    showSpaceName(val) {
      let str = val.split('').reverse().splice(0, 10).reverse().join('')
      return `...${str}`
    },
    // 获取事件类型以及报警来源分组
    getIncidentAndSourceGroup(thirdSystemCode) {
      this.searchForm.alarmType = []
      this.$api.getAlarmThirdTypeData({ thirdSystemCode: thirdSystemCode }).then((res) => {
        if (res.code == 200) {
          this.eventTypeOptions = res.data
        }
      })
    },
    // 获取服务空间树形结构
    getTreelist() {
      // this.$api.getStructureTree({}, __PATH.USER_CODE).then((res) => {
      this.$api.getStructureTree({}).then((res) => {
        if (res.code == 200) {
          this.spaces = res.data
          // 增加 懒加载节点
          res.data.map((e) => {
            e.leaf = false
          })
          // 转换为树形结构数据
          this.serverSpaces = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    // 子/孙组件流程操作
    operating(type, selectItem, tabType) {
      this.$refs.table.clearSelection()
      // 报警详情
      if (type == 'details') {
        this.$router.push({
          path: '/allAlarm/alarmDetail',
          query: {
            tabType: tabType,
            alarmId: selectItem.alarmId
          }
        })
      }
    },
    // 打开关联工单
    showWorkOrder(row) {
      this.alarmDetail = {
        projectCode: row.projectCode,
        alarmSpaceId: row.alarmSpaceId,
        alarmId: row.alarmId
      }
      this.workOderDetailData = [
        {
          workTypeName: row.workTypeName,
          id: row.workNum,
          active: true
        }
      ]
      this.workOrderDetailShow = true
    },
    // 下拉树懒加载
    serverLoadNode(node, resolve) {
      if (node.level == 0) {
        return resolve(node.data)
      } else if (node.level > 0 && node.level < 4) {
        return resolve(node.data.children)
      } else {
        return this.spaceTreeChange(node.data, resolve)
      }
    },
    // 空间数据清除
    handleClear() {
      this.searchForm.alarmSpaceId = ''
      this.areaName = ''
    },
    // 选择下拉树 数据
    handleNodeClick(data) {
      this.searchForm.alarmSpaceId = data.id
      this.areaName = data.ssmName
      this.$refs.treeSelect.blur()
    },
    // 服务空间树结构点击获取自己及父节点ID并请求数据实现懒加载
    spaceTreeChange(value, resolve) {
      const child = ListTree(this.spaces, value.pid)
      child.push(value.id)
      let treeId = child.toString()
      let data = {
        current: 1,
        functionDictId: '',
        simCode: treeId,
        size: 999
      }
      this.$api.getSpaceInfoList(data).then((res) => {
        if (res.code == 200) {
          if (typeof resolve === 'function') {
            let treeNodeData = JSON.parse(JSON.stringify(res.data.records))
            treeNodeData.map((e) => {
              e.leaf = true
            })
            resolve(treeNodeData)
          }
        }
      })
    },
    // table排序
    tableSortChange(column) {
      if (column.prop === 'alarmStartTime') {
        this.timeOrType = column.order ? (column.order == 'ascending' ? 3 : 2) : ''
      } else {
        this.timeOrType = column.order ? (column.order == 'ascending' ? 1 : 0) : ''
      }
      this.getDataList()
    },
    // 批量处置按钮
    reset() {
      this.searchForm = {
        alarmLevel: '', // 报警等级
        alarmId: '',
        projectCode: '', // 报警系统
        alarmType: [],
        dataRange: [], // 时间范围
        alarmSpaceId: '', // 空间位置
        deviceId: '',
        alarmAffirm: '',
        haveWork: '' // 是否关联工单
      }
      this.areaName = ''
      this.search()
    },
    search() {
      this.pagination.pageNo = 1
      this.getDataList()
    },
    // 取消收藏
    uncollect() {
      let arr = []
      this.multipleSelection.forEach((item) => {
        arr.push(item.alarmId)
      })
      let param = {
        alarmId: arr.join(','),
        classic: '0'
      }
      this.$api.CollectAlarmRecords(param).then((res) => {
        if (res.code == 200) {
          this.getDataList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 收藏取消
    handleClass(row) {
      this.$api
        .CollectAlarmRecords({
          alarmId: row.alarmId,
          classic: '0'
        })
        .then((res) => {
          if (res.code == 200) {
            this.getDataList()
          } else {
            this.$message.error(res.msg)
          }
        })
    },
    // 选中框
    selectionChange(val) {
      this.multipleSelection = val
      this.$nextTick(() => {
        this.$forceUpdate()
      })
    },
    paginationSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.pageNo = 1
      this.getDataList()
    },
    paginationCurrentChange(val) {
      this.pagination.pageNo = val
      this.getDataList()
    },
    // 获取全部报警记录
    getDataList() {
      let { alarmId, projectCode, alarmType, alarmLevel, alarmSpaceId, dataRange, deviceId, alarmAffirm, haveWork } = this.searchForm
      let params = {
        alarmId,
        timeOrType: this.timeOrType,
        pageNo: this.pagination.pageNo,
        pageSize: this.pagination.pageSize,
        projectCode,
        deviceId,
        alarmType: alarmType.join(','),
        alarmLevel,
        alarmSpaceId,
        startTime: dataRange.length ? dataRange[0] : '',
        endTime: dataRange.length ? dataRange[1] : '',
        alarmAffirm,
        haveWork
      }
      this.loading = true
      this.$api
        .GetAlarmRecordClassics(params)
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            this.tableData = res.data ? res.data.records : []
            this.pagination.total = res.data ? res.data.total : 0
          } else {
            this.tableData = []
            this.pagination.total = 0
          }
        })
        .catch((err) => {
          this.loading = false
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.search-header {
  padding: 0 8px 10px;
  .search-from {
    padding-bottom: 12px;
    & > div {
      margin-top: 12px;
      margin-right: 10px;
    }
    .el-input {
      width: 200px;
    }
  }
}
.contentTable {
  height: calc(100% - 16px);
  background: #fff;
  border-radius: 4px;
  padding: 16px;
  display: flex;
  margin-top: 16px;
  flex-direction: column;
  overflow: auto;
  .contentTable-main {
    flex: 1;
    overflow: auto;
  }
  .contentTable-footer {
    padding: 10px 0 0;
  }
  .alarmLevel {
    padding: 3px 6px;
    border-radius: 4px;
    color: #fff;
    line-height: 14px;
  }
  .alarmStatus {
    position: relative;
    display: inline-block;
    padding-left: 12px;
    .alarmStatusIcon {
      display: inline-block;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      border-radius: 100%;
    }
  }
}
</style>
