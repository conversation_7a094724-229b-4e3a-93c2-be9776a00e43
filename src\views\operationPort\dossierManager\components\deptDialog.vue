<template>
  <el-dialog v-if="visible" v-dialogDrag :modal="false" :close-on-click-modal="false" :title="title" width="40%"
    :visible="visible" custom-class="model-dialog" :before-close="closeDialog">
    <div class="dialog-content">
      <div class="content-left">
        <el-input v-model="treeFilter" placeholder="请输入关键字" clearable></el-input>
        <div v-loading="treeLoading" class="left-tree">
          <el-tree ref="tree" :data="deptTreeData" node-key="id" size="small"
            :default-expanded-keys="deptTreeData.map((v) => v.id)" :highlight-current="true" show-checkbox
            :check-strictly="true" :props="treeProps" :filter-node-method="filterNode" @check="checkData"
            @check-change="handleCheckChange" :default-checked-keys="checkedId">
          </el-tree>
        </div>
      </div>
      <div class="content-right">
        <div class="right-title">
          <p class="title">
            已选：<span>{{ selectDept.length }}</span>个科室
          </p>
          <p class="clear" @click="clear">清空</p>
        </div>
        <div class="right-list">
          <div v-for="(item, index) in selectDept" :key="item.id" class="list-item">
            <span>{{ item.deptName }}</span>
            <i class="el-icon-close" @click="remove(item, index)"></i>
          </div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { transData } from '@/util'
export default {
  name: 'deptDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '所属科室'
    },
    isRadio: {
      type: Boolean,
      default: false
    },
    nature: {
      type: String,
      default: '1'
    },
    isNotmultiSector: {
      type: Boolean,
      default: false
    },
    disabledId: {
      type: [String, Array],
      default: ''
    },
    //回显
    selectedDept: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      treeFilter: '',
      treeLoading: false,
      deptTreeData: [],
      treeProps: {
        label: 'deptName',
        children: 'children'
      },
      selectDept: [],
      checkedId: [],//默认选中的数据
      lastTreeParentId: ''
    }
  },
  watch: {
    treeFilter(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    if (this.selectedDept && this.selectedDept.length > 0) {
      this.selectDept = this.selectedDept
      this.checkedId = this.selectedDept.map(item => item.id);
    }
    this.getUnitListFn()
  },
  methods: {
    // 移除
    remove(node, index) {
      this.$refs.tree.setChecked(node.id, false)
      this.selectDept.splice(index, 1)
    },
    // 清空
    clear() {
      this.selectDept = []
      this.$nextTick(() => {
        this.$refs.tree.setCheckedKeys([])
      })
    },
    filterNode(value, data) {
      if (!value) return true
      return data.deptName.indexOf(value) !== -1
    },
    checkData(data, checked, indeterminate) {
      if (this.isRadio) {
        this.selectDept = [data]
        this.$refs.tree.setCheckedKeys([data.id])
      } else {
        this.selectDept = this.$refs.tree.getCheckedNodes()
      }
    },
    setDisabledById(treeData, targetId) {
      // 递归函数，处理每个节点
      function processNode(node) {
        // 创建新节点对象，避免修改原对象
        const newNode = { ...node }
        // 如果当前节点的 id 等于目标 id
        const isTrue = targetId instanceof Array ? targetId.includes(newNode.id) : targetId === newNode.id
        if (isTrue) {
          newNode.disabled = false
        } else {
          newNode.disabled = true
          // 递归处理子节点
          if (newNode.children && newNode.children.length > 0) {
            newNode.children = newNode.children.map(processNode)
          }
        }
        return newNode
      }
      // 对整个树进行递归处理
      return treeData.map(processNode)
    }, // 单位树
    getUnitTree(unitList) {
      unitList.forEach(async (v) => {
        let { data: deptList } = await this.$api.getDeptList({ unitId: v.umId })
        v.id = v.umId
        v.deptName = v.unitComName
        v.children = transData(deptList, 'id', 'pid', 'children')
        this.setDisable(v.children)
      })
      let timeout = setTimeout(() => {
        if (this.disabledId) {
          this.deptTreeData = this.setDisabledById(unitList, this.disabledId)
        } else {
          this.deptTreeData = unitList
        }
        this.treeLoading = false
        clearTimeout(timeout)
      }, 1000)
    },
    setDisable(data) {
      data.forEach((v) => {
        if (v.children && v.children.length) {
          this.setDisable(v.children) // 子级循环时把这一层数据的count传入
        }
      })
    },
    // 获取单位列表
    getUnitListFn() {
      this.treeLoading = true
      this.$api.getUnitList({}).then((res) => {
        if (res.code == 200) {
          // 单位仅为本院
          if (this.nature === '1') {
            this.getUnitTree(res.data.filter((item) => item.nature == 1))
          } else {
            // 查所有
            this.getUnitTree(res.data)
          }
        }
      })
    },
    confirm() {
      if (!this.selectDept.length) {
        this.$message({ message: '请选择科室', type: 'error' })
        return
      }
      this.$emit('selectDept', this.selectDept)
      this.closeDialog()
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    },
    handleCheckChange(node, check, childCheck) {
      if (this.isNotmultiSector) {
        let checkedArr = this.$refs.tree.getCheckedNodes()
        if (check) {
          if (checkedArr.length > 1) {
            for (let i = 0; i < checkedArr.length; i++) {
              if (node.id != checkedArr[i].id) {
                this.lastTreeParentId = checkedArr[i].umId
              }
            }
          }
        } else {
          if (checkedArr.length == 0) {
            this.lastTreeParentId = null
          }
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  background: #fff;
  padding: 24px 0px;
  display: flex;
  p {
    margin: 0;
  }
  .content-left {
    height: 480px;
    flex: 1;
    padding: 0px 24px;
    border-right: 1px solid #e4e7ed;
    display: flex;
    flex-direction: column;
    .left-tree {
      flex: 1;
      margin-top: 16px;
      overflow: auto;
    }
    ::v-deep .el-tree-node {
      .is-leaf + .el-checkbox .el-checkbox__inner {
        display: inline-block;
      }
    }
  }
  .content-right {
    height: 480px;
    flex: 1;
    padding: 0px 24px;
    display: flex;
    flex-direction: column;
    .right-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 10px;
      .title {
        font-size: 14px;
        color: #7f848c;
        line-height: 22px;
        span {
          color: #333333;
          margin: 0 8px;
        }
      }
      .clear {
        cursor: pointer;
        font-size: 12px;
        color: #3562db;
      }
    }
    .right-list {
      flex: 1;
      overflow: auto;
      .list-item {
        transition: all 0.3s;
        padding: 8px 12px;
        border-radius: 4px;
        color: #333333;
        margin-top: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        i {
          cursor: pointer;
          color: #666666;
          font-size: 16px;
        }
      }
      .list-item:hover {
        background: #e6effc;
      }
    }
  }
}
</style>
