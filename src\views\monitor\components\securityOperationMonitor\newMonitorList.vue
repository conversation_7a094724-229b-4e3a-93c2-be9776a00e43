<!--
 * @Author: hedd heding<PERSON>@sinomis.com
 * @Date: 2025-03-11 21:47:11
 * @LastEditors: hedd heding<PERSON>@sinomis.com
 * @LastEditTime: 2025-03-13 17:52:50
 * @FilePath: \ihcrs_pc\src\views\monitor\components\securityOperationMonitor\newMonitorList.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-row :gutter="16">
    <el-col v-for="item in listData" :key="item.surveyEntityCode" :xs="12" :sm="12" :md="8" :xl="6">
      <div class="monitor-item">
        <div class="item-heade">
          <div v-if="item.status == '1' || item.status == '2'" class="entity-status status-error"><svg-icon name="icon-error" /><span>异常</span></div>
          <div v-if="item.status == '6'" class="entity-status status-offline"><svg-icon name="icon-offline" /><span>离线</span></div>
          <div v-if="item.status == '0'" class="entity-status status-online"><svg-icon name="icon-online" /><span>在线</span></div>
          <div class="header-title">
            <span @click="jumpMonitorDetail(item)">{{ item.surveyEntityName }}</span>
            <img v-if="item.unDisposePolice" class="jrbjs-icon" :src="jrbjsIcon" alt="" @click="goToAlarm({ objectName: item.surveyEntityName, alarmStatus: '0' })" />
          </div>
          <div v-if="item.parameterList.length > 2" class="title-right" @click="openMoreParameter(item)">更多</div>
        </div>
        <div class="item-main">
          <img class="main-img" :src="monitorItemImg[projectCode][item.status == 0 ? 'on' : 'off']" :alt="item.surveyEntityName" />
          <div class="main-item">
            <div v-for="parameter in item.parameterList.slice(0, 2)" :key="parameter.parameterId" class="main-item-box">
              <p class="item-title">{{ parameter.parameterName }}</p>
              <p class="item-value">{{ parameter.parameterValue || '-' }} {{ parameter.parameterUnit || '' }}</p>
            </div>
          </div>
        </div>
        <div class="item-footer">
          <div class="footer-item" @click="goToAlarm({ objectName: item.surveyEntityName, dataRange: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')] })">
            <p>
              今日报警：<span>{{ item.allPolice }}</span>
            </p>
          </div>
          <div class="footer-item" @click="goToAlarm({ objectName: item.surveyEntityName, alarmStatus: '0' })">
            <p>
              未处理报警：<span>{{ item.unDisposePolice }}</span>
            </p>
          </div>
        </div>
      </div>
    </el-col>
    <parameterListDialog v-if="parameterListDialogShow" :cardData="selectCardData" :visible.sync="parameterListDialogShow" />
  </el-row>
</template>
<script>
import { monitorItemImg } from '@/util/dict.js'
import moment from 'moment'
import jrbjsIcon from '@/assets/images/monitor/jrbjs_icon.png'
import parameterListDialog from '../../commonPage/elecRealWarning/components/parameterListDialog.vue'
export default {
  name: 'newMonitorList',
  components: { parameterListDialog },
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    listData: {
      type: Array,
      default: () => []
    },
    projectCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      moment,
      monitorItemImg,
      jrbjsIcon,
      parameterListDialogShow: false,
      selectCardData: {}
    }
  },
  methods: {
    // 跳转监测历史趋势
    jumpMonitorDetail(item) {
      this.$emit('jumpDetail', item)
    },
    openMoreParameter(item) {
      this.selectCardData = item
      this.parameterListDialogShow = true
    },
    // 跳转报警
    goToAlarm(params) {
      this.$emit('goToAlarm', params)
    }
  }
}
</script>
<style lang="scss" scoped>
.monitor-item {
  margin-top: 16px;
  width: 100%;
  padding: 16px;
  background: #faf9fc;
  border-radius: 4px;
  .item-heade {
    padding: 0 0 14px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .entity-status {
      width: 65px;
      height: 24px;
      border-radius: 4px;
      text-align: center;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      margin-right: 5px;
      flex-shrink: 0;
      span {
        font-size: 14px;
      }
    }
    .header-title {
      font-size: 14px;
      font-weight: 500;
      color: #121f3e;
      line-height: 14px;
      word-break: break-all;
      text-align: left;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      span {
        cursor: pointer;
      }
    }
    .jrbjs-icon {
      width: 22px;
      height: 22px;
      margin: 0 10px;
      cursor: pointer;
    }
    .title-right {
      flex-shrink: 0;
      text-align: center;
      font-size: 14px;
      font-family: 'PingFang SC-Medium', 'PingFang SC';
      font-weight: 500;
      color: #fff;
      cursor: pointer;
      display: inline-block;
      width: 48px;
      height: 24px;
      line-height: 24px;
      background: #3562db;
      border-radius: 4px;
    }
    .status-error {
      background: #ffece8;
      color: #cb2634;
    }
    .status-offline {
      background: #f2f4f9;
      color: #86909c;
    }
    .status-online {
      background: #e8ffea;
      color: #009a29;
    }
  }
  .item-main {
    display: flex;
    padding-top: 14px;
    padding-bottom: 14px;
    border-top: 1px solid #dcdfe6;
    border-bottom: 1px solid #dcdfe6;
    .main-img {
      width: 80px;
      height: 80px;
      flex-shrink: 0;
    }
    .main-item {
      display: flex;
      flex: 1;
      padding: 6px 0 6px 20px;
      .main-item-box {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-around;
        width: 50%;
        p {
          margin: 0px;
          padding: 0px;
        }
      }
    }
    .item-title {
      font-size: 14px;
      font-weight: 400;
      color: #414653;
      line-height: 14px;
    }
    .item-value {
      font-size: 16px;
      font-weight: bold;
      color: #121f3e;
      line-height: 20px;
    }
  }
  .item-footer {
    padding: 10px 0 0;
    display: flex;
    .footer-item {
      width: 50%;
      text-align: center;
      cursor: pointer;
      p {
        font-size: 14px;
        font-weight: 400;
        color: #414653;
        line-height: 18px;
        padding: 0px;
        margin: 0px;
        span {
          font-size: 18px;
          color: #121f3e;
        }
      }
    }
    .footer-item:last-child {
      border-left: 1px solid #dcdfe6;
    }
  }
}
.tooltip-content {
  font-size: 14px;
  font-weight: 400;
  color: #fff;
  line-height: 22px;
  p {
    margin: 0;
  }
  .tooltip-content-value {
    display: flex;
    flex-wrap: wrap;
    max-width: 500px;
    p {
      margin-left: 15px;
    }
  }
}
</style>
