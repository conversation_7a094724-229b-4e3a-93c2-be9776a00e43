<!--
 * @Author: hedd
 * @Date: 2023-11-09 15:12:38
 * @LastEditTime: 2023-11-10 10:39:24
 * @FilePath: \ihcrs_pc\src\views\oauth.vue
 * @Description:
-->
<template>
  <div v-loading="loading" class="oauth-content">
    <el-button type="primary" size="default" class="authButton" @click.native.prevent="handleLogin">授权登录</el-button>
  </div>
</template>

<script>
export default {
  name: 'OAuth',
  data() {
    return {
      loading: true,
      routeData: {},
      jumpPath: ''
    }
  },
  mounted() {
    this.routeData = this.$route.query
    this.getEnergyToken()
  },
  methods: {
    // 获取能耗单点登录token
    getEnergyToken() {
      const params = {
        grant_type: 'authorization_code',
        code: this.routeData.code,
        redirect_uri: ''
      }
      const headers = {
        Authorization: 'Basic ' + oAuthData.clientSecret
      }
      this.$api.getOauthToken(params, headers).then(res => {
        this.loading = false
        this.jumpPath = process.env.VUE_APP_ENERGY_API + '#/home?token=' + res.access_token
        this.handleLogin()
      })
    },
    handleLogin() {
      // 重定向到能耗系统免密登录
      const handelUp = window.open(this.jumpPath)
      console.log(handelUp)
      if (handelUp) {
        setTimeout(() => {
          this.closeDialog()
        }, 1000)
      } else {
        this.$message({ message: '请允许浏览器弹窗', type: 'warning' })
      }
    },
    // 关闭弹窗
    closeDialog() {
      window.opener = null
      window.open('', '_self')
      window.close()
    }
  }
}
</script>

<style lang="scss" scoped>
.oauth-content {
  width: 100%;
  height: 100%;
  display: flex;
  .authButton {
    width: 300px;
    height: 48px;
    margin: auto;
  }
}
</style>>
