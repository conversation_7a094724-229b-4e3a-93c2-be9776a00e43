/*
 * @Author: hedd
 * @Date: 2023-02-27 14:35:18
 * @LastEditTime: 2023-03-16 11:29:17
 * @FilePath: \IHCRS_alarm\src\router\modules\drag.js
 * @Description:
 */
import Layout from '@/layout'

export default [
  {
    path: '/drag',
    component: Layout,
    redirect: '/drag/index',
    name: 'drag',
    meta: {
      title: '工作台',
      menuAuth: '/drag/index'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'index',
        name: 'dragIndex',
        component: () => import('@/views/drag/index'),
        meta: {
          title: '工作台',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/drag'
        }
      },
      {
        path: 'warnRecord',
        name: 'warnRecord',
        component: () => import('@/views/drag/navigationMore/warnRecord'),
        meta: {
          title: '报警管理',
          sidebar: false,
          activeMenu: '/drag'
        }
      },
      {
        path: 'todoAndmsgRecord',
        name: 'todoAndmsgRecord',
        component: () => import('@/views/drag/navigationMore/todoAndmsgRecord'),
        meta: {
          title: '消息管理',
          sidebar: false,
          activeMenu: '/drag'
        }
      },
      // {
      //   path: 'todoRecord',
      //   name: 'todoRecord',
      //   component: () => import('@/views/drag/navigationMore/todoRecord'),
      //   meta: {
      //     title: '待办管理',
      //     sidebar: false,
      //     activeMenu: '/drag'
      //   }
      // },
      {
        path: 'noticeDetail',
        name: 'noticeDetail',
        component: () => import('@/views/drag/navigationMore/noticeDetail'),
        meta: {
          title: '通知公告详情',
          sidebar: false,
          activeMenu: '/drag'
        }
      }
    ]
  }
]
