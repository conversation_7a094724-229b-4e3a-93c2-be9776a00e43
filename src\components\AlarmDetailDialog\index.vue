<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="报警详情"
    width="60%"
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="dialog-content">
      <el-row :gutter="20" :type="$store.state.settings.mode == 'mobile' ? '' : 'flex'" style="align-items: stretch">
        <el-col :md="12" style="display: flex; flex-direction: column">
          <ContentCard title="报警信息" :showOpen="true" :cstyle="{ 'margin-bottom': '10px', padding: '10px 0 10px 10px' }">
            <svg-icon
              slot="title-right"
              class="title-right-svg"
              :name="alarmDetail.alarmStatus == 0 ? 'alarm-unhandled' : alarmDetail.alarmStatus == 1 ? 'alarm-processing' : 'alarm-closed'"
            />
            <table slot="content" class="card-content-table">
              <tr>
                <td>报警类型 :</td>
                <td>{{ alarmDetail.incidentName || '-' }}</td>
              </tr>
              <tr>
                <td>报警位置 :</td>
                <td>{{ alarmDetail.alarmSpaceName || '-' }}</td>
              </tr>
              <tr>
                <td>报警对象 :</td>
                <td>{{ alarmDetail.alarmObjectName || '-' }}</td>
              </tr>
              <tr>
                <td>报警时间 :</td>
                <td>{{ alarmDetail.alarmStartTime || '-' }}</td>
              </tr>
              <tr>
                <td>报警级别 :</td>
                <td :style="{ color: alarmLevelItem[alarmDetail.alarmLevel].color || '' }">{{ alarmLevelItem[alarmDetail.alarmLevel].text || '-' }}</td>
              </tr>
              <tr>
                <td>报警数值 :</td>
                <td>{{ alarmDetail.alarmValue || '-' }}</td>
              </tr>
              <tr>
                <td>报警描述 :</td>
                <td>{{ (alarmDetail.alarmDetails || '') + '(' + (alarmDetail.alarmRule || '-') + ')' }}</td>
              </tr>
              <tr>
                <td>报警来源 :</td>
                <td>{{ alarmDetail.alarmSource || '-' }}</td>
              </tr>
              <tr>
                <td>报警ID :</td>
                <td>{{ alarmDetail.alarmId || '-' }}</td>
              </tr>
            </table>
          </ContentCard>
          <ContentCard title="工单信息" :cstyle="{ flex: 1, overflow: 'hidden' }">
            <el-tooltip slot="title-right" popper-class="tooltip" effect="light" placement="bottom">
              <div slot="content" class="work-order-type">
                <div v-for="item in workOrderList" :key="item.id" @click="selectWorkOrderType(item.id, item.workTypeName)">{{ item.workTypeName }}</div>
              </div>
              <el-button v-if="alarmDetail.alarmStatus != 2" class="title-right-btn" type="primary">新建工单</el-button>
            </el-tooltip>
            <div v-for="item in workInfoList" :key="item.workNum" slot="content" class="work-order-box">
              <div class="box-left-detail">
                <div style="margin-bottom: 15px">
                  <div class="work-order-label">工单号 :</div>
                  <div
                    class="work-order-value active-ID"
                    :style="{ cursor: sourceType != 'alarm' ? 'pointer' : 'default' }"
                    @click="sourceType != 'workOrder' ? jumpToWorkOrderDetail(item.workNum) : ''"
                  >
                    {{ item.workNum }}
                  </div>
                </div>
                <div>
                  <div class="work-order-label">工单类型 :</div>
                  <div class="work-order-value">{{ item.workTypeName }}</div>
                </div>
              </div>
              <div class="box-right-tag" :style="{ backgroundColor: getFlowtypeBgColor(item.flowcode) }">{{ item.flowtype ?? '' }}</div>
            </div>
          </ContentCard>
        </el-col>
        <el-col :md="12">
          <div class="right-content">
            <ContentCard title="报警处置流程" :cstyle="{ height: '100%' }">
              <div slot="content" class="time-line">
                <div v-for="item in operationRecord" :key="item.id" class="time-line-box">
                  <div class="top-date">
                    <div class="data-ymd">{{ item.createdTime.slice(0, 10) }}</div>
                    <div class="defaule-icon"></div>
                    <div class="data-hms">{{ item.createdTime.slice(10, 19) }}</div>
                  </div>
                  <!-- 0:创建报警记录 1:真实报警 2:误报 3:演练 4:调试 5: 未确认 6:备注 7:关闭 8:设置为经典案例 9:取消为经典案例 10:派单 11:新建工单 12:已屏蔽 13:取消屏蔽 -->
                  <div class="box-content">
                    <div v-if="item.status == 0" class="time-work-order">
                      <div class="time-work-order-event">{{ operationType[item.status] }}</div>
                      <div class="work-order-detail">
                        <span>报警ID :</span>
                        <span style="padding-left: 10px">{{ alarmDetail.alarmId }}</span>
                      </div>
                    </div>
                    <div v-if="[1, 2, 3, 4].includes(item.status)" class="time-work-order">
                      <div class="time-work-order-event">{{ operationType[item.status] }}</div>
                      <div class="work-order-detail">
                        <span>确警人 :</span>
                        <span style="padding-left: 10px">{{ item.operationPersonName + '（' + item.operationPersonId + '）' }}</span>
                      </div>
                    </div>
                    <div v-if="item.status == 5" class="time-work-order">
                      <div class="time-work-order-event">{{ operationType[item.status] }}</div>
                    </div>
                    <div v-if="item.status == 6" class="time-work-order">
                      <div class="time-work-order-event">{{ operationType[item.status] }}</div>
                      <div class="work-order-detail">
                        <span>备注人 :</span>
                        <span style="padding-left: 10px">{{ item.operationPersonName + '（' + item.operationPersonId + '）' }}</span>
                      </div>
                      <div class="work-order-detail">
                        <span>备注 :</span>
                        <span style="padding-left: 10px">{{ item.remark || '-' }}</span>
                      </div>
                    </div>
                    <div v-if="item.status == 7" class="time-work-order">
                      <div class="time-work-order-event" style="color: #414653">{{ operationType[item.status] }}</div>
                      <div class="work-order-detail">
                        <span>关闭人 :</span>
                        <span style="padding-left: 10px">{{ item.operationPersonName + '（' + item.operationPersonId + '）' }}</span>
                      </div>
                    </div>
                    <div v-if="[8, 9, 13].includes(item.status)" class="time-work-order">
                      <div class="time-work-order-event">{{ operationType[item.status] }}</div>
                      <div class="work-order-detail">
                        <span>操作人 :</span>
                        <span style="padding-left: 10px">{{ item.operationPersonName + '（' + item.operationPersonId + '）' }}</span>
                      </div>
                    </div>
                    <div v-if="item.status == 12" class="time-work-order">
                      <div class="time-work-order-event">{{ operationType[item.status] }}</div>
                      <div class="work-order-detail">
                        <span>操作人 :</span>
                        <span style="padding-left: 10px">{{ item.operationPersonName + '（' + item.operationPersonId + '）' }}</span>
                      </div>
                      <div class="work-order-detail">
                        <span>备注 :</span>
                        <span style="padding-left: 10px">{{ item.remark || '-' }}</span>
                      </div>
                    </div>
                    <div v-if="item.status == 10" class="time-work-order">
                      <div class="time-work-order-event">{{ operationType[item.status] }}</div>
                      <div class="work-order-detail">
                        <span>派单人 :</span>
                        <span style="padding-left: 10px">{{ item.operationPersonName + '（' + item.operationPersonId + '）' }}</span>
                      </div>
                      <div class="work-order-detail">
                        <span>工单号 :</span>
                        <span style="padding-left: 10px; color: #0379f1; cursor: pointer" @click="jumpToWorkOrderDetail(item.workNum)">{{ item.workNum || '-' }}</span>
                      </div>
                    </div>
                    <div v-if="item.status == 11" class="time-work-order">
                      <div class="time-work-order-event">{{ item.operationRemark }}</div>
                      <div class="work-order-detail">
                        <span>创建人 :</span>
                        <span style="padding-left: 10px">{{ item.operationPersonName + '（' + item.operationPersonId + '）' }}</span>
                      </div>
                      <div class="work-order-detail">
                        <span>工单号 :</span>
                        <span style="padding-left: 10px; color: #0379f1; cursor: pointer" @click="jumpToWorkOrderDetail(item.workNum)">{{ item.workNum || '-' }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </ContentCard>
          </div>
        </el-col>
      </el-row>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button v-if="alarmDetail.classic == 0" type="primary" plain @click="collectAlarmRecords"
      ><i class="el-rate__icon el-icon-star-off" style="color: #3562db; font-size: 14px"></i> 存为经典案例</el-button
      >
      <el-button v-else type="primary" plain @click="collectAlarmRecords"
      ><i class="el-rate__icon el-icon-star-on" style="color: #3562db; font-size: 14px"></i> 已存为经典案例</el-button
      >
      <el-button v-if="alarmDetail.alarmStatus != 2" type="primary" plain @click="closeAlarmRecords">关 闭</el-button>
      <el-button type="primary" plain @click="shieldedAlarmRecords">{{ alarmDetail.shield == 1 ? '取消屏蔽' : '屏 蔽' }}</el-button>
      <el-button v-if="!alarmDetail.workNum" type="primary" @click="oneKeyDispatch">派 单</el-button>
      <el-button v-if="![1, 2, 3, 4].includes(alarmDetail.alarmAffirm)" type="primary" @click="confirmAlarm">确 警</el-button>
    </span>
    <template v-if="workOrderDealShow">
      <CreatedWorkOrder
        :workOrderDealShow.sync="workOrderDealShow"
        :workTypeCode="olgTaskManagement.workTypeCode"
        :workTypeName="olgTaskManagement.workTypeName"
        :alarmId="alarmId"
        :projectCode="alarmDetail.projectCode"
        :spaceId="alarmDetail.alarmSpaceId"
        dealType="add"
        @workOrderSure="workOrderSure"
      />
    </template>
    <!-- 确警 -->
    <confirmAlarmDialog v-if="showConfirmAlarm" :visible.sync="showConfirmAlarm" :item="alarmDetail"></confirmAlarmDialog>
    <!-- 屏蔽 -->
    <screenDialog v-if="scrDialog" :selectItems="screenSelectItems" :visible.sync="scrDialog" />
    <!-- 工单详情 -->
    <WorkOrderDetailDialog v-if="workOrderDetailShow" sourceType="alarm" :visible.sync="workOrderDetailShow" :alarmDetail="alarmDetail" :dialogDetail="workOderDialogData" />
  </el-dialog>
</template>
<script>
// import moment from 'moment'
export default {
  name: 'AlarmDetailDialog',
  components: {
    confirmAlarmDialog: () => import('@/views/alarmCenter/alarmRecord/components/confirmAlarmDialog.vue'),
    screenDialog: () => import('@/views/alarmCenter/alarmRecord/components/screenDialog.vue')
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    dialogDetail: {
      type: Object,
      default: () => {}
    },
    alarmId: {
      type: String,
      default: ''
    },
    sourceType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      alarmDetail: {
        alarmLevel: 0
      },
      operationRecord: [], // 操作记录列表
      workInfoList: [], // 工单信息列表
      workOrderList: [
        // 工单类型列表
        {
          workTypeName: '综合维修',
          id: '1'
        }
        // ,
        // {
        //   workTypeName: '确警',
        //   id: '16'
        // }
      ],
      flowTypeList: [
        {
          id: '3',
          backgroundColor: '#3562DB'
        },
        {
          id: '5',
          backgroundColor: '#08CB83'
        }
      ],
      workOrderDealShow: false, // 新增工单弹窗
      olgTaskManagement: {
        // 新增工单弹窗数据
        workTypeCode: '',
        workTypeName: ''
      },
      alarmLevelItem: {
        0: { text: '通知', color: '#3562DB' },
        1: { text: '一般', color: '#3562DB' },
        2: { text: '紧急', color: '#FF9435' },
        3: { text: '重要', color: '#FA403C' }
      },
      operationType: {
        0: '产生报警',
        1: '真实报警',
        2: '误报',
        3: '演练',
        4: '调试',
        5: '未确认', // 不显示信息
        6: '备注',
        7: '报警关闭',
        8: '设置为经典案例',
        9: '取消设置为经典案例',
        10: '派单',
        11: '新建工单',
        12: '已屏蔽',
        13: '取消屏蔽'
      },
      workOrderDetailShow: false, // 工单详情弹窗
      workOderDialogData: [], // 工单详情弹窗数据
      showConfirmAlarm: false, // 确警弹窗
      scrDialog: false, // 屏蔽弹窗
      screenSelectItems: [] // 屏蔽选中数据
    }
  },
  watch: {
    scrDialog(val) {
      if (!val) {
        this.getAlarmDetails()
      }
    },
    workOrderDetailShow(val) {
      if (!val) {
        this.getAlarmDetails()
      }
    },
    showConfirmAlarm(val) {
      if (!val) {
        this.getAlarmDetails()
      }
    },
    workOrderDealShow(val) {
      if (!val) {
        this.getAlarmDetails()
      }
    }
  },
  mounted() {
    this.getAlarmDetails()
  },
  methods: {
    // 一键派单
    oneKeyDispatch() {
      const userInfo = this.$store.state.user.userInfo.user
      let param = {
        alarmId: this.alarmDetail.alarmId,
        alarmLevel: this.alarmDetail.alarmLevel,
        alarmSourceName: this.alarmDetail.alarmSource,
        incidentName: this.alarmDetail.incidentName,
        spaceId: this.alarmDetail.alarmSpaceId,
        userName: userInfo.staffName,
        userId: userInfo.staffId,
        operationSource: 0,
        projectCode: this.alarmDetail.projectCode
      }
      this.$confirm('是否确认派发确警工单?', '提示', {
        cancelButtonClass: 'el-button--primary is-plain',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.OneKeyDispatch(param).then((res) => {
          if (res.code == 200) {
            this.getAlarmDetails()
            this.$message({
              message: '已派单',
              type: 'success'
            })
          } else {
            this.$message.error(res.msg)
          }
        })
      })
    },
    // 查看工单
    workOrderDetails() {
      this.workOrderDetailShow = !this.workOrderDetailShow
    },
    // 确警
    confirmAlarm() {
      this.showConfirmAlarm = !this.showConfirmAlarm
    },
    // 屏蔽报警
    shieldedAlarmRecords() {
      if (this.alarmDetail.shield == 1) {
        this.$confirm('是否取消屏蔽当前报警?', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api
            .shield({
              alarmId: this.alarmDetail.alarmId,
              alarmObjectId: this.alarmDetail.alarmObjectId,
              incidentType: this.alarmDetail.incidentType,
              shield: false
            })
            .then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: '已取消屏蔽',
                  type: 'success'
                })
                this.getAlarmDetails()
              }
            })
        })
      } else {
        this.scrDialog = !this.scrDialog
        this.screenSelectItems = [this.alarmDetail]
      }
    },
    // 关闭报警
    closeAlarmRecords() {
      // 未处理关闭前先走警情确认流程
      if (this.alarmDetail.alarmStatus == 0) {
        this.$confirm('当前报警未处理，是否确警后再进行关闭？', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确警',
          cancelButtonText: '取消关闭',
          type: 'warning'
        }).then(() => {
          this.confirmAlarm()
        })
      } else {
        this.$confirm('是否关闭当前报警记录?', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 关闭
          this.$api.CloseAlarmRecord({ alarmId: this.alarmDetail.alarmId }).then((res) => {
            if (res.code == 200) {
              this.getAlarmDetails()
            } else {
              this.$message.error(res.msg)
            }
          })
        })
      }
    },
    // 存为经典案例
    collectAlarmRecords() {
      let param = {
        alarmId: this.alarmDetail.alarmId,
        classic: this.alarmDetail.classic == 1 ? 0 : 1
      }
      this.$api.CollectAlarmRecords(param).then((res) => {
        if (res.code == 200) {
          this.getAlarmDetails()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 获取报警详情
    getAlarmDetails() {
      this.$api.GetAlarmDetails({ alarmId: this.alarmId }).then((res) => {
        if (res.code == 200) {
          this.alarmDetail = res.data.record
          console.log(this.alarmDetail, 'ssssssssssssssssssss')
          this.workInfoList = res.data.workInfo
          // this.operationRecord = res.data.detail
          // moment 时间倒序
          // operationRecord.sort((a, b) => {
          //   return moment(b.createdTime).valueOf() - moment(a.createdTime).valueOf()
          // })
          // 倒序
          const operationRecord = res.data.detail.reverse()
          this.operationRecord = operationRecord
        }
      })
    },
    // 工单状态颜色处理
    getFlowtypeBgColor(flowcode) {
      const flowType = this.flowTypeList.find((item) => item.id == flowcode)
      return flowType ? flowType.backgroundColor : '#FFC000'
    },
    // 查看工单详情页
    jumpToWorkOrderDetail(workNum) {
      this.workOderDialogData = []
      const workOrderList = this.workInfoList.map((e) => {
        return {
          workTypeName: e.workTypeName,
          id: e.workNum,
          active: e.workNum == workNum
        }
      })
      this.workOderDialogData = workOrderList
      this.workOrderDetailShow = !this.workOrderDetailShow
    },
    // 新建工单 打开弹窗
    selectWorkOrderType(id, name) {
      this.olgTaskManagement = {
        workTypeCode: id,
        workTypeName: name
      }
      this.workOrderDealShow = true
    },
    // 新建工单提交
    workOrderSure(item) {
      this.workOrderDealShow = false
      this.getAlarmDetails()
      // console.log(item, '提交工单信息')
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
      this.$emit('closeAlarmDialog')
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .model-dialog {
  // min-height: 60vh;
}
.dialog-content {
  .title-right-svg,
  .title-right-btn {
    position: absolute;
    right: 0;
    top: 0;
  }
  .title-right-svg {
    width: 74px;
    height: 25px;
    line-height: 25px;
  }
  .active-ID {
    color: #0379f1 !important;
  }
  .card-content-table tr {
    td {
      padding-bottom: 15px;
      font-size: 14px;
      font-family: 'PingFang SC-Regular', 'PingFang SC';
      &:first-child {
        font-weight: 400;
        color: #414653;
        text-align: right;
        white-space: nowrap;
      }
      &:last-child {
        padding-left: 10px;
        color: #121f3e;
        font-weight: 600;
      }
    }
  }
  .work-order-box {
    background: rgb(246 245 250 / 60%);
    border-radius: 4px;
    padding: 15px 10px;
    display: flex;
    .box-left-detail {
      flex: 1;
      & > div {
        display: flex;
        font-family: 'PingFang SC-Regular', 'PingFang SC';
        .work-order-label {
          width: 80px;
          color: #414653;
          text-align: right;
        }
        .work-order-value {
          flex: 1;
          padding-left: 10px;
          color: #121f3e;
          font-weight: 600;
        }
      }
    }
    .box-right-tag {
      width: 66px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      font-size: 14px;
      font-family: 'PingFang SC-Medium', 'PingFang SC';
      color: #fff;
      background: #ff9435;
      border-radius: 24px;
    }
  }
  .right-content {
    height: 100%;
    background: #fff;
    overflow: auto;
    .time-line {
      .time-line-box {
        margin-bottom: 6px;
        .top-date {
          height: 24px;
          line-height: 24px;
          display: flex;
          .data-ymd {
            min-width: 85px;
            font-size: 14px;
            font-family: 'PingFang SC-Medium', 'PingFang SC';
            color: #121f3e;
            font-weight: 600;
          }
          .defaule-icon {
            width: 12px;
            height: 12px;
            margin: auto 10px;
            background: #fff;
            border: 2px solid #3562db;
            border-radius: 50%;
          }
          .data-hms {
            font-size: 14px;
            font-family: 'PingFang SC-Regular', 'PingFang SC';
            color: #414653;
          }
        }
        .box-content {
          margin-left: 100px;
          padding-left: 20px;
          border-left: 2px solid #f6f5fa;
          .time-work-order {
            background: rgb(246 245 250 / 60%);
            border-radius: 4px;
            padding: 15px 10px 10px;
            font-family: 'PingFang SC-Regular', 'PingFang SC';
            .time-work-order-event {
              font-size: 16px;
              color: #3562db;
              margin-bottom: 8px;
            }
            .work-order-detail {
              color: #121f3e;
              padding-bottom: 5px;
            }
          }
        }
      }
    }
  }
}
</style>
