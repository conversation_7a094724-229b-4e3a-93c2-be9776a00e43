<template>
  <PageContainer>
    <div slot="header">
      <div class="search-header">
        <div class="search-from">
          <el-input v-model="formInline.questionCode" placeholder="请输入隐患编号" style="width: 200px;"></el-input>
          <el-input v-model="formInline.dutyDeptName" placeholder="请输入责任部门" style="width: 200px;"></el-input>
          <el-select v-model="formInline.riskCode" placeholder="请选择隐患等级">
            <el-option v-for="item in hiddenLevelList" :key="item.id" :label="item.dictName" :value="item.dictCode"></el-option>
          </el-select>
          <el-cascader
            v-model="formInline.questionDetailCode"
            :options="hiddenClassifyList"
            :show-all-levels="false"
            :props="{ expandTrigger: 'hover', checkStrictly: true, value: 'id', emitPath: false }"
            placeholder="请选择隐患分类"
          ></el-cascader>
          <el-input v-model="formInline.createByDeptName" placeholder="请输入反馈部门" style="width: 200px;"></el-input>
          <el-date-picker v-model="timeLine" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"> </el-date-picker>
        </div>
        <div>
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="search">查询</el-button>
          <el-button class="el-icon-download" type="primary" @click="exportFile">导出Excel</el-button>
        </div>
      </div>
    </div>
    <div slot="content" style="height: 100%; margin-top: 16px; background-color: #fff;">
      <div class="table-content">
        <el-table :data="tableData" :height="tableHeight" border stripe @row-dblclick="watchDetail">
          <el-table-column label="序号" width="100" align="center">
            <template slot-scope="scope">
              <span>{{ (paginationData.pageNo - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="questionCode" label="编号" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="questionDetailType" label="隐患分类" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="questionAddress" label="隐患区域" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="createByDeptName" label="反馈部门" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="createPersonName" label="反馈人员" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="createTime" label="反馈时间" width="200" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="riskName" label="隐患等级" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="rectificationPlanTime" label="要求整改完成时间" width="200" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="finishTime" label="实际整改完成时间" width="200" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="taskTeamName" label="操作" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="bths">
                <span @click="openAuditsDialogVisible(scope.row)">审核</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          style="margin-top: 3px;"
          :current-page="paginationData.pageNo"
          layout="total, sizes, prev, pager, next, jumper"
          :total="paginationData.total"
          :page-size="paginationData.pageSize"
          :page-sizes="[15, 30, 50]"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
      <el-dialog title="隐患审核" :visible.sync="auditsDialogVisible" width="30%" :before-close="handleClose" custom-class="model-dialog">
        <el-form :model="auditsForm"  label-width="120px" style="background-color: #fff; width: 100%;">
          <el-form-item label="审核结果:" prop="resource" >
            <el-radio-group v-model="auditsForm.auditType">
              <el-radio label="1">通过</el-radio>
              <el-radio label="2">驳回</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审核评价:" prop="desc">
            <el-input v-model="auditsForm.rectificatioEvaluate" type="textarea" maxlength="120" show-word-limit :autosize="{ minRows: 4, maxRows: 5 }"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="audit">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </PageContainer>
</template>

<script>
import axios from 'axios'
export default {
  // props: ['type', 'isActive'],
  data() {
    return {
      loading: true,
      formInline: {
        questionCode: '', // 隐患单号
        dutyDeptName: '', // 责任部门
        riskCode: '', // 隐患等级
        questionDetailCode: '', // 隐患分类
        createByDeptName: '', // 反馈部门
        startTime: '', // 开始日期
        endTime: '' // 结束日期
      },
      tableData: [],
      tableLoading: true,
      paginationData: {
        pageNo: 1,
        pageSize: 15,
        total: 0
      },
      auditsDialogVisible: false, // 审核框显示隐藏
      auditsForm: {},
      timeLine: [],
      hiddenLevelList: [],
      hiddenClassifyList: [],
      auditData: '',
      hospitalList: []
    }
  },
  computed: {
    tableHeight() {
      return document.body.clientHeight - 270
    }
    // // eslint-disable-next-line vue/return-in-computed-property
    // auditTitle() {
    //   if (!this.type) {
    //     return '隐患审核'
    //   } else if (this.isActive == '1') {
    //     return '隐患整改审核'
    //   } else if (this.isActive == '2') {
    //     return '隐患挂帐审核'
    //   }
    // }
  },
  watch: {
    timeLine(val) {
      if (val.length == 2) {
        this.formInline.startTime = val[0]
        this.formInline.endTime = val[1]
      }
    }
    // isActive(val) {
    //   this.getData()
    //   this.getDictData()
    // }
  },
  mounted() {
    this.loading = false
    this.getData()
    this.getDictData()
  },
  methods: {
    search() {
      this.paginationData.pageNo = 1
      this.getData()
    },
    resetForm() {
      this.formInline.questionCode = ''
      this.formInline.dutyDeptName = ''
      this.formInline.riskCode = ''
      this.formInline.questionDetailCode = ''
      this.formInline.createByDeptName = ''
      this.formInline.startTime = ''
      this.formInline.endTime = ''
      this.timeLine = []
      this.paginationData.pageNo = 1
      this.getData()
    },
    handleSizeChange(val) {
      this.paginationData.pageNo = 1
      this.paginationData.pageSize = val
      this.search()
    },
    handleCurrentChange(val) {
      this.paginationData.pageNo = val
      this.search()
    },
    getData() {
      // let httpname = this.type && this.type == 'pro' ? 'getAuditsListPro' : 'getAuditsList'
      // if (this.isActive) {
      //   this.formInline.tabType = this.isActive
      // }
      this.$api.ipsmGetAuditsList({ ...this.paginationData, ...this.formInline }).then((res) => {
        if (res.code == 200) {
          this.tableLoading = false
          this.tableData = res.data.list
          this.paginationData.total = res.data.sum
        } else {
          this.$message.error(res.message)
        }
      })
    },
    getDictData() {
      this.$api.ipsmGetDictList({ dictType: 'hidden_trouble_grade_type' }).then((res) => {
        if (res.code == 200) {
          this.hiddenLevelList = res.data
        } else {
          this.$message.error(res.message)
        }
      })
      this.$api.ipsmGetHiddenClassifyList().then((res) => {
        if (res.code == 200) {
          this.hiddenClassifyList = res.data
        } else {
          this.$message.error(res.message)
        }
      })
    },
    openAuditsDialogVisible(val) {
      this.auditsDialogVisible = true
      this.auditData = val
    },
    audit() {
      if (!this.auditsForm.auditType) {
        return this.$message.error('审核结果不能为空!')
      }
      if (!this.auditsForm.rectificatioEvaluate) {
        return this.$message.error('审核评价不能为空!')
      }
      let loginData = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      let params = {}
      params.id = this.auditData.id
      params.rectificatioEvaluate = this.auditsForm.rectificatioEvaluate
      params.checkPersonCode = loginData.id
      params.checkPersonName = loginData.name
      params.flowCode = this.auditData.flowCode
      params.isHospital = loginData.isHospital
      params.auditType = this.auditsForm.auditType
      // let httpname = this.type && this.type == 'pro' ? 'auditQuestionPro' : 'auditQuestion'
      this.$api.ipsmAuditQuestion(params).then((res) => {
        if (res.code == 200) {
          this.$message.success(res.message)
        } else {
          this.$message.error(res.message)
        }
        this.auditsDialogVisible = false
        this.auditData = ''
        this.auditsForm = {}
        this.getData()
      })
    },
    handleClose(done) {
      this.auditData = ''
      this.auditsForm = {}
      done()
    },
    closeDialog() {
      this.auditsDialogVisible = false
      this.auditData = ''
      this.auditsForm = {}
    },
    exportFile() {
      let baseInfo = sessionStorage.getItem('LOGINDATA') ? JSON.parse(sessionStorage.getItem('LOGINDATA')) : ''
      let httpname = 'safetyManager/hospitalQuestion/exportAuditList'
      let params = {
        // unitCode: 'BJSYGJ',
        // hospitalCode: 'BJSJTYY',
        // userId: 'a16425c49f86969f4cd3f174e9fbe86e',
        // userName: '王海龙',
        // hospitalName: '世纪坛医院',
        // unitName: '朝阳医管局',
        // roleCode: 'BJSJTYY-YYGLY0-20221117173504',
        // userTeamId: '1581497000007970816',
        // positionType: '1',
        // controlGroupIds: '1581497000007970816',
        // platformFlag: 1,
        // officeName: '生产安全管理办公室',
        // officeId: '1581497000007970816'
        ...baseInfo
      }
      axios({
        method: 'post',
        url: __PATH.VUE_AQ_URL + httpname,
        data: { ...params, ...this.formInline },
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then(function (res) {
          let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          console.log(name)
          let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
          let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          // 释放这个临时的对象url
          window.URL.revokeObjectURL(url)
        })
        .catch(function (res) {
          console.log('error', res)
        })
    },
    watchDetail(row) {
      this.$router.push({
        name: 'hiddenManagementDetails',
        query: { id: row.id }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.search-header {
  padding: 10px 16px;

  .search-from {
    padding-bottom: 12px;

    & > div {
      margin-top: 12px;
      margin-right: 10px;
    }
  }
}

.table-content {
  padding: 16px;
  height: calc(100% - 80px);
}

.bths {
  > span {
    cursor: pointer;
  }

  > span:nth-child(1) {
    color: #558bf9;
    margin-right: 5px;
  }
}

::v-deep .el-textarea {
  width: 65% !important;
}
</style>
