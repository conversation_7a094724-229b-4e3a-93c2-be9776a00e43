<!--
 * @Author: hedd
 * @Date: 2023-03-15 15:04:55
 * @LastEditTime: 2024-03-14 11:16:31
 * @FilePath: \ihcrs_pc\src\views\drag\components\msgReminder.vue
 * @Description:
-->
<template>
  <ContentCard
    :title="item.componentTitle"
    :scrollbarHover="true"
    :cstyle="{ height: '100%' }"
    class="drag_class"
    :hasMoreOper="['more', 'edit']"
    @more-oper-event="(val) => $emit('all-more-Oper', val, 'msgReminder')"
  >
    <div id="getMsgScrollTop" slot="content" class="infinite-list-wrapper msg-content">
      <ul v-infinite-scroll="recordLoad" class="list" infinite-scroll-disabled="disabled">
        <li v-for="(item, i) in msgDataList" :key="i" class="list-item">
          <svg-icon :name="msgIconList[item.msgCatId]?.icon ?? 'announcement'" />
          <div v-if="item.readStatus == 0" class="red-round"></div>
          <div class="msg-title" @click="navigationTo(item)">
            <div class="msg-type">【{{ item.msgSysTypeName }}】</div>
            <div class="msg-text">{{ item.msgTitle }}</div>
          </div>
          <div class="msg-time">{{ item.publishTime }}</div>
        </li>
      </ul>
      <p v-if="loading">加载中...</p>
      <p v-if="noMore">没有更多了</p>
      <template v-if="workOrderDetailCenterShow">
        <el-dialog :visible.sync="workOrderDetailCenterShow" custom-class="detailDialog main" :close-on-click-modal="false" :before-close="workOrderDetailCloseDialog">
          <template slot="title">
            <span class="dialog-title">{{ dialogTitle }}</span>
          </template>
          <component :is="iomsComponent" :rowData="detailObj" />
        </el-dialog>
      </template>
      <template v-if="alarmDetailShow">
        <AlarmDetailDialog ref="alarmDetail" :alarmId="alarmDetail.alarmId" :visible.sync="alarmDetailShow" @update:visible="closeAlarmDialog" />
      </template>
      <WorkOrderDetailDialog v-if="workOrderDetailShow" sourceType="alarm" :visible.sync="workOrderDetailShow" :alarmDetail="alarmDetail" :dialogDetail="workOderDialogData" />
    </div>
  </ContentCard>
</template>
<script>
import { msgIconList } from '@/util/dict.js'
export default {
  name: 'msgReminder',
  components: {
    workOrderDetailYS: () => import('@/views/serviceQuality/transport/components/workOrderDetailList.vue'),
    workOrderDetailWX: () => import('@/views/serviceQuality/maintenance/components/workOrderDetailList.vue'),
    workOrderDetailBJ: () => import('@/views/serviceQuality/cleaning/components/workOrderDetailList.vue')
  },
  props: {
    item: {
      type: Object,
      default: () => {}
    },
    setSocketMsgs: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      alarmDetailShow: false,
      alarmDetail: {},
      workOrderDetailShow: false, // 工单详情弹窗
      workOderDialogData: [], // 工单详情传递数据
      total: 20,
      page: 1,
      loading: false,
      msgIconList: Object.freeze(msgIconList),
      msgDataList: [],
      workOrderDetailCenterShow: false, // 工单详情弹窗
      dialogTitle: '', // 弹窗标题
      detailObj: {}, // 工单详情传递数据
      iomsComponent: 'workOrderDetailWX', // 工单详情组件(维修)
      pageSize: 20
    }
  },
  computed: {
    noMore() {
      return this.msgDataList.length >= this.total
    },
    disabled() {
      return this.loading || this.noMore
    }
  },
  watch: {
    setSocketMsgs: {
      handler(item) {
        // console.log('setSocketMsgs', item)
        // if (item) {
        //   if (item.msgType == 0) {
        this.messageInit()
        //   }
        // }
      },
      deep: true // 深度监听
    }
  },
  mounted() {
    this.messageInit()
  },
  methods: {
    messageInit() {
      // 查看sessionStorage是否有存储的分页数据 与 滚动条位置
      var msgDataPosition = JSON.parse(sessionStorage.getItem('msgDataPosition'))
      if (msgDataPosition) {
        this.page = 1
        this.total = 0
        this.msgDataList = []
        this.getMessageByUserId(msgDataPosition)
      } else {
        this.page = 1
        this.total = 0
        this.msgDataList = []
        this.getMessageByUserId()
      }
    },
    // 获取消息列表
    getMessageByUserId(msgDataPosition) {
      // this.loading = true
      const params = {
        userId: this.$store.state.user.userInfo.user.staffId,
        msgType: 0, // 0:消息 1:待办 2:todolist
        msgSysId: 0,
        page: this.page,
        // 有存储的分页数据则先请求之前历史数据
        pageSize: msgDataPosition ? msgDataPosition.page * msgDataPosition.pageSize : this.pageSize
      }
      this.$api
        .getMessageByUserId(params)
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            this.msgDataList = res.data.list.length ? this.msgDataList.concat(res.data.list) : this.msgDataList
            this.total = res.data.total
            if (msgDataPosition) {
              // 历史数据渲染加载完成 将历史的分页页数放入当前的分页页数中  下次请求接着走之前的分页  将滚动条跳至上次位置
              this.$nextTick(() => {
                this.page = msgDataPosition.page
                document.querySelector('#getMsgScrollTop').parentNode.scrollTop = msgDataPosition.scrollTop
              })
              // 清除缓存的位置  之后不在加载历史数据
              sessionStorage.removeItem('msgDataPosition')
            }
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    recordLoad() {
      this.page++
      this.loading = true
      this.getMessageByUserId()
    },
    // 跳转查看详情
    navigationTo(item) {
      // 跳转前记录当前分页页数 与请求每页数量  记录滚动条位置  存在sessionStorage
      var scrollTop = document.querySelector('#getMsgScrollTop').parentNode.scrollTop
      let msgDataPosition = {
        page: this.page,
        pageSize: this.pageSize,
        scrollTop
      }
      sessionStorage.setItem('msgDataPosition', JSON.stringify(msgDataPosition))
      console.log('-------------------')
      const selectTypeData = msgIconList[item.msgCatId]
      // 通知公告
      if (selectTypeData.type == 'system') {
        this.$router.push({
          path: '/drag/noticeDetail',
          query: {
            msgId: item.msgId,
            msgSysId: item.msgSysId
          }
        })
        return
      }
      const params = {
        userId: this.$store.state.user.userInfo.user.staffId,
        msgId: item.msgId,
        msgSysId: item.msgSysId
      }
      this.$api.getImserverMsgInfo(params).then((res) => {
        if (res.code == 200) {
          const data = res.data
          let msgBody = {}
          try {
            msgBody = JSON.parse(data.msgBody)
          } catch (error) {
            return this.$message.error('消息体解析失败，请联系管理员')
          }
          console.log(msgBody)
          if (!msgBody) {
            return this.$message.warning('此消息暂无详情数据')
          }
          const selectTypeData = msgIconList[data.msgCatId]
          const elements = selectTypeData?.elements?.split(',') ?? []
          // 遍历msgBody对象保留elements中的属性
          for (const key in msgBody) {
            if (!elements.includes(key)) {
              delete msgBody[key]
            }
          }
          // msgBody的元素数量不等于elements的元素数量，说明有查询属性缺失
          if (Object.keys(msgBody).length != elements.length) {
            return this.$message.warning('消息数据缺失，请联系管理员')
          }

          if (selectTypeData.type == 'ioms') {
            // 确警工单
            if (selectTypeData.typeCode == 16) {
              this.workOderDialogData = [
                {
                  workTypeName: msgBody.type,
                  id: msgBody.workNum,
                  active: true
                }
              ]
              this.workOrderDetailShow = !this.workOrderDetailShow
            } else {
              this.detailObj = {
                id: msgBody.workNum
              }
              this.dialogTitle = `${msgBody.type}（${msgBody.flowType}）`
              if (selectTypeData?.component) {
                this.iomsComponent = selectTypeData?.component
                this.workOrderDetailCenterShow = true
              } else {
                this.$message.warning('该工单类型暂未开放')
              }
            }
          } else if (selectTypeData.type == 'icis') {
            this.$router.push({
              path: '/InspectionManagement/taskManagement/taskDetail',
              query: {
                taskId: msgBody.id,
                type: 'detail',
                systemType: selectTypeData.typeCode
              }
            })
          } else if (selectTypeData.type == 'warn') {
            this.alarmDetail = msgBody
            this.alarmDetailShow = true
          } else {
            this.$message.warning('该消息类型暂未开放')
          }
          console.log(msgBody, elements, selectTypeData)
        }
      })
    },
    // 工单详情弹窗关闭
    workOrderDetailCloseDialog() {
      this.workOrderDetailCenterShow = false
      this.messageInit()
    },
    // 报警详情弹窗关闭
    closeAlarmDialog() {
      this.messageInit()
    }
  }
}
</script>
<style lang="scss" scoped>
.msg-content {
  height: 100%;

  ul {
    list-style-type: none;
    padding: 10px 0;
    margin: 0;

    li {
      margin: 3px 0;
      padding: 5px;
      display: flex;
      justify-content: space-between;
      overflow: hidden;
      cursor: pointer;
      position: relative;

      svg {
        width: 18px;
        height: 18px;
      }

      .red-round {
        position: absolute;
        width: 5px;
        height: 5px;
        background: #fa403c;
        border-radius: 50%;
        box-shadow: 0 0 2px 3px rgba($color: #fff, $alpha: 100%);
        left: 18px;
      }

      .msg-title {
        flex: 1;
        display: flex;
        align-items: center;
        font-size: 14px;
        font-family: "PingFang SC-Regular", "PingFang SC";
        font-weight: 400;
        color: #121f3e;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        .msg-type {
          width: 110px;
          white-space: nowrap;
        }

        .msg-text {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .msg-time {
        font-size: 13px;
        font-family: "PingFang SC-Regular", "PingFang SC";
        color: #7f848c;
        white-space: nowrap;
      }
    }
  }

  & > p {
    text-align: center;
    padding: 0 0 5px;
    margin: 0 0 10px;
    font-size: 14px;
    color: #999;
    font-family: PingFangSC-Regular;
  }
}
</style>
