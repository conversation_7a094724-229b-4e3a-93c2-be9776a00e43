<!--
 * @Description:
-->
<template>
  <div class="warn-history">
    <div class="wholeTable-main">
      <el-table ref="table" v-loading="loading" :resizable="false" border :data="tableData" height="100%" style="width: 100%">
        <el-table-column prop="surveyName" label="监测参数">
          <template slot-scope="scope">
            <span>{{ scope.row.surveyName }} - {{ scope.row.paramName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="totalTime" label="运行时长" show-overflow-tooltip></el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
export default {
  name: 'warnHistory',
  props: {
    searchForm: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      loading: false,
      tableData: []
    }
  },
  mounted() {
    this.getDetails()
  },
  methods: {
    // 初始化组件数据
    getDetails() {
      let { projectCode, surveyCode, dataRange } = this.searchForm
      let params = {
        projectCode: projectCode,
        surveyCode: surveyCode,
        queryStartTime: dataRange[0],
        queryEndTime: dataRange[1]
      }
      this.loading = true
      this.$api
        .workDuration(params)
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            this.tableData = res.data ? res.data : []
          } else {
            this.tableData = []
          }
        })
        .catch((err) => {
          this.loading = false
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.warn-history {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 0 16px 10px 16px;
  .wholeTable-main {
    height: 100%;
    overflow: auto;
  }
  .wholeTable-footer {
    padding: 10px 0;
  }
}
</style>
