<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-width="auto" class="formRef">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="班次名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入" :disabled="queryParams.type === 'view'" maxlength="50"> </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="22">
          <el-form-item label="考勤时间" class="is-required">
            <el-table :data="form.tableData" border row-key="id">
              <el-table-column type="index" width="80" align="center" label="时段"> </el-table-column>
              <el-table-column label="上班时间" prop="" width="300">
                <template slot-scope="{ row, $index }">
                  <div style="display: flex">
                    <el-form-item>
                      <el-select
                        v-model="form.tableData[$index].workType"
                        placeholder="请选择"
                        class="width100"
                        :disabled="isStartDayTypeDisabled($index)"
                        @change="onStartDayTypeChange($index, row)"
                      >
                        <el-option v-for="item in todayTomorrowList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item :prop="'tableData.' + $index + '.workTime'" :rules="dynamicRules.workTime">
                      <el-time-picker
                        v-model="form.tableData[$index].workTime"
                        format="HH:mm"
                        class="width100 ml-8"
                        value-format="HH:mm"
                        placeholder="任意时间点"
                        :disabled="queryParams.type === 'view'"
                        :clearable="false"
                        @blur="onUpperBlur($index, row)"
                      >
                      </el-time-picker>
                    </el-form-item>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="" label="下班时间" width="300">
                <template slot-scope="{ row, $index }">
                  <div style="display: flex">
                    <el-form-item>
                      <el-select
                        v-model="form.tableData[$index].offWorkType"
                        placeholder="请选择"
                        class="width100"
                        :disabled="isEndDayTypeDisabled($index)"
                        @change="onEndDayTypeChange($index, row)"
                      >
                        <el-option v-for="item in todayTomorrowList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item :prop="'tableData.' + $index + '.offWorkTime'" :rules="dynamicRules.workTime">
                      <el-time-picker
                        v-model="form.tableData[$index].offWorkTime"
                        format="HH:mm"
                        class="ml-8 width100"
                        value-format="HH:mm"
                        placeholder="任意时间点"
                        :disabled="queryParams.type === 'view'"
                        @blur="onLowerBlur($index, row)"
                      >
                      </el-time-picker>
                    </el-form-item>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="basicUnitName" label="上班打卡规则" width="320">
                <template #default="{ row }">
                  <div>
                    最早可提前
                    <el-input-number
                      v-model="row.workAdvanceCheck"
                      :min="0"
                      :max="720"
                      class="width100"
                      :disabled="queryParams.type === 'view'"
                      size="small"
                      controls-position="right"
                    >
                    </el-input-number>
                    分钟进行打卡
                  </div>
                  <div style="margin-top: 10px">
                    上班时间后
                    <el-input-number
                      v-model="row.workDelayCheck"
                      :min="0"
                      :max="720"
                      class="width100"
                      :disabled="queryParams.type === 'view'"
                      size="small"
                      controls-position="right"
                    >
                    </el-input-number>
                    分钟内打卡按满勤计算
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="basicUnitName" label="下班打卡规则" width="320">
                <template #default="{ row }">
                  <div style="display: flex">
                    <el-form-item>
                      最晚可延后
                      <el-input-number
                        v-model="row.offWorkDelayCheck"
                        :min="0"
                        :max="720"
                        class="width100"
                        :disabled="queryParams.type === 'view'"
                        controls-position="right"
                        size="small"
                      ></el-input-number>
                      分钟进行打卡
                    </el-form-item>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template slot-scope="{ $index }">
                  <el-button type="text" class="text-red" :disabled="$index === 0 || queryParams.type === 'view'" @click="handleDelete($index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-col>
      </el-row>
      <el-button v-if="queryParams.type !== 'view'" type="primary" :disabled="isAddTimePeriodDisabled" icon="el-icon-plus" @click="addTimePeriod"> 添加时段 </el-button>
      <div class="totalTime">总工作时长 ：{{ totalWorkHours }} 小时</div>
    </el-form>
  </div>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin'
export default {
  name: 'classConfig',
  mixins: [tableListMixin],
  props: {
    parameter: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data() {
    return {
      // 正常表单
      form: {
        name: '', // 班次名称
        tableData: []
      },
      formLoading: false, // 表单loading
      rules: {
        name: [{ required: true, message: '请输入班次名称', trigger: 'blur' }]
      },
      todayTomorrowList: [
        {
          label: '当日',
          value: 1
        },
        {
          label: '次日',
          value: 2
        }
      ],
      dynamicRules: {
        workTime: [
          { required: true, message: '请选择时间', trigger: 'change' },
          { validator: this.validateWorkTime, trigger: 'change' }
        ]
      },
      tableData: [], // 表格数据
      queryParams: {}
    }
  },
  computed: {
    totalWorkHours() {
      let total = 0
      let time = 0
      let hasAdded24Hours = false // 标志变量
      // 遍历所有时段，检查是否有次日
      this.form.tableData.forEach((item) => {
        if (item.offWorkType === 2 && !hasAdded24Hours) {
          time += 24 * 60 // 如果是次日，加24小时
          hasAdded24Hours = true // 设置标志为已加
        }
        let startMinutes = this.timeToMinutes(item.workTime)
        let endMinutes = this.timeToMinutes(item.offWorkTime)
        if (item.workType === 2) {
          startMinutes += time
        }
        if (item.offWorkType === 2) {
          endMinutes += time
        }
        let diff = endMinutes - startMinutes
        total += diff / 60 // 将分钟转换为小时
      })
      // this.totalWorkHourSum = total.toFixed(1)
      return total.toFixed(1)
    },
    isAddTimePeriodDisabled() {
      // 如果最后一个时段的下班时间是次日并且下班时间等于时段一得上班时间，则禁用添加时段按钮
      const lastRow = this.form.tableData[this.form.tableData.length - 1]
      let isFlag = lastRow ? lastRow.offWorkType === 2 && lastRow.offWorkTime === this.form.tableData[0].workTime : false
      return parseFloat(this.totalWorkHours) >= 24 || this.queryParams.type === 'view' || isFlag
    }
  },
  mounted() {
    this.queryParams = this.parameter
    this.init()
  },
  methods: {
    // 上班时间blur
    onUpperBlur(index, row) {
      this.$nextTick(() => {
        this.$refs.form.validateField([`tableData.${index}.workTime`, `tableData.${index}.offWorkTime`])
      })
    },
    // 下班时间blur
    onLowerBlur(index, row) {
      this.$nextTick(() => {
        this.$refs.form.validateField([`tableData.${index}.workTime`, `tableData.${index}.offWorkTime`])
      })
      if (index === 0) {
        // 说明用户点击的是第一个时间段并重新编辑了下班时间，需要重新计算下面所有时段的上班时间和下班时间
        for (let i = 1; i < this.form.tableData.length; i++) {
          const previousPeriod = this.form.tableData[i - 1]
          const currentPeriod = this.form.tableData[i]
          // 计算新的上班时间，加1分钟
          const newStartTime = this.calculateNewStartTime(previousPeriod.offWorkType, previousPeriod.offWorkTime, 1)
          currentPeriod.workTime = newStartTime // 更新当前时段的上班时间
          // 计算新的下班时间
          if (previousPeriod.offWorkType === 2) {
            // 如果是次日，下班时间为时段一的上班时间
            currentPeriod.offWorkTime = this.form.tableData[0].workTime
          } else {
            // 如果是当日，下班时间为23:59
            currentPeriod.offWorkTime = '23:59'
          }
        }
      } else {
        // 说明用户重新编辑了除开时段一的下班时间，这个时候需要重新计算下面所有时段的上班时间和下班时间
        for (let i = index + 1; i < this.form.tableData.length; i++) {
          const previousPeriod = this.form.tableData[i - 1]
          const currentPeriod = this.form.tableData[i]
          // 计算新的上班时间，加1分钟
          const newStartTime = this.calculateNewStartTime(previousPeriod.offWorkType, previousPeriod.offWorkTime, 1)
          currentPeriod.workTime = newStartTime // 更新当前时段的上班时间
          // 如果下班时间为23:59，设置后续所有班次为次日
          if (previousPeriod.offWorkTime === '23:59') {
            currentPeriod.offWorkType = 2 // 设置为次日
            currentPeriod.workType = 2 // 设置为次日
            // 把下班时间重新在设置成时段一的上班时间
            currentPeriod.offWorkTime = this.form.tableData[0].workTime
            return
          }
          // 计算新的下班时间
          if (previousPeriod.offWorkType === 2) {
            // 如果是次日，下班时间为时段一的上班时间  最长工作时间不超过24小时，所以最后一时段的结束时间为第一时段的开始时间
            currentPeriod.offWorkTime = this.form.tableData[0].workTime
            currentPeriod.workType = 2 // 设置为次日
          } else {
            // 如果是当日，下班时间为23:59
            currentPeriod.offWorkTime = currentPeriod.offWorkTime ? currentPeriod.offWorkTime : '23:59'
            currentPeriod.offWorkType = 1 // 设置为当日
            currentPeriod.workType = 1 // 设置为当日
          }
        }
      }
    },
    // 添加时间段
    addTimePeriod() {
      if (this.form.tableData.length === 0) return // 如果没有时段，直接返回
      let lastPeriod = this.form.tableData[this.form.tableData.length - 1]
      const { offWorkType, offWorkTime } = lastPeriod
      // 计算新的上班时间，加1分钟
      let newStartTime = this.calculateNewStartTime(offWorkType, offWorkTime, 1)
      // 第一个时段的开始时间作为新时段的结束时间
      let firstPeriodStartTime = this.form.tableData[0].workTime
      let firstPeriodStartDayType = this.form.tableData[0].workType
      // 如果最后一条的下班时间是23:59且结束时间类型为当日，则新时段的开始和结束时间类型都应为次日
      let newEndDayType = firstPeriodStartDayType
      let newEndTime = firstPeriodStartTime // 默认新时段的下班时间为时段一的上班时间
      if (offWorkTime === '23:59' && offWorkType === 1) {
        newEndDayType = 2 // 设置为次日
      } else if (this.form.tableData[0].offWorkType === 2) {
        newEndDayType = 2 // 设置为次日
      } else {
        newEndTime = '23:59' // 如果是当日，设置下班时间为23:59
      }
      this.form.tableData.push({
        workType: newEndDayType, // 新时段的开始时间类型与上一时段的结束时间类型相同
        workTime: newStartTime,
        offWorkType: newEndDayType, // 根据第一个时段的结束时间类型设置
        offWorkTime: newEndTime // 使用新计算的下班时间
      })
    },
    calculateNewStartTime(offWorkType, offWorkTime, minuteOffset = 0) {
      let [hours, minutes] = offWorkTime.split(':').map(Number)
      minutes += minuteOffset // 加上偏移分钟数
      if (minutes >= 60) {
        minutes -= 60
        hours += 1
      }
      if (hours >= 24) {
        hours -= 24
        offWorkType = (offWorkType + 1) % 2 // 如果超过24小时，切换日类型
      }
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
    },
    // 删除
    handleDelete(index) {
      this.form.tableData.splice(index, 1)
      // 删除后重新校验剩余的所有时段
      this.$nextTick(() => {
        this.form.tableData.forEach((_, idx) => {
          this.$refs.form.validateField([`tableData.${idx}.workTime`, `tableData.${idx}.offWorkTime`])
        })
      })
    },
    // 新增：将时间字符串转换为分钟数的方法
    timeToMinutes(timeStr) {
      if (!timeStr) return 0
      const [hours, minutes] = timeStr.split(':').map(Number)
      return hours * 60 + minutes
    },
    // 下班选择change校验
    onEndDayTypeChange(index, item) {
      if (index === 0) {
        // 取消时段2以下所有禁用
        this.form.tableData.forEach((row, idx) => {
          if (idx > 0) {
            row.offWorkType = item.offWorkType // 重置为当日，取消禁用
            row.workType = item.offWorkType
          }
        })
      }
      // 触发该行的所有时间校验
      this.$nextTick(() => {
        this.$refs.form.validateField([`tableData.${index}.workTime`, `tableData.${index}.offWorkTime`])
      })
    },
    // 上班选择change校验
    onStartDayTypeChange(index, item) {
      // 后面所有时段禁用 除自己
      this.form.tableData.forEach((row, idx) => {
        if (idx >= index) {
          row.workType = item.workType
          row.offWorkType = item.workType
        }
      })
      // 触发该行的所有时间校验
      this.$nextTick(() => {
        this.$refs.form.validateField([`tableData.${index}.workTime`, `tableData.${index}.offWorkTime`])
      })
    },
    init() {
      if (!this.queryParams.id) {
        this.form.tableData.push({
          workType: 1,
          workTime: '09:00',
          offWorkType: 1,
          offWorkTime: '18:00'
        })
        return
      }
      // 编辑：获取数据
      if (!this.queryParams.id) return
      this.$api.supplierAssess
        .getShiftInfoById({ id: this.queryParams.id })
        .then((res) => {
          const { code, data } = res
          if (code == 200) {
            const { name, rule, id } = data
            this.form = {
              name,
              tableData: rule,
              id
            }
          } else {
            this.$message.error(res.msg || '获取数据失败!')
          }
        })
        .catch((err) => {
          this.$message.error(err.msg || '获取数据失败!')
        })
    },
    // 上班时间disabled
    isStartDayTypeDisabled(index) {
      // 时段一的上班时间始终禁用，时段二及后续时段排除自己禁用其他
      return index === 0 || (index > 0 && this.form.tableData[0].offWorkType === 2) || this.queryParams.type === 'view'
    },
    // 下班时间disabled
    isEndDayTypeDisabled(index) {
      // 只有时段一（index为0）禁用后续时段的选择
      // return (
      //   (index > 0 && this.form.tableData.some((day) => day.workType === 2)) ||
      //   this.queryParams.type === "view" ||
      //   (index === 1 && this.form.tableData[index - 1].offWorkType === 2)  // 如果时段1的下班时间为次日，禁用时段2的下班时间
      // );
      return (index > 0 && this.form.tableData[0].offWorkType === 2) || this.queryParams.type === 'view'
    },
    // 新增：自定义校验规则
    validateWorkTime(rule, value, callback) {
      const index = parseInt(rule.field.split('.')[1]) // 获取当前行的索引
      const currentRow = this.form.tableData[index]
      // 这个判断是防止没有选择上班时间或者下班时间
      if (!currentRow.workTime || !currentRow.offWorkTime) {
        callback()
        return
      }
      // 首先会有默认时段一得数据，我们要先校验当前行时间是否超过24小时，还会设计跨天问题，还要校验当前下班时间是不是大于上班时间
      let time = 24 * 60
      // let hasAdded24Hours = false; // 标志变量
      // // 遍历所有时段，检查是否有次日
      // this.form.tableData.forEach((row) => {
      //   if (row.offWorkType === 2 && !hasAdded24Hours) {
      //     time += 24 * 60; // 如果是次日，加24小时
      //     hasAdded24Hours = true; // 设置标志为已加
      //   }
      // });
      // 还要保存时段一的上班时间，用于校验后续时段的下班时间是否大于上班时间
      const firstRow = this.form.tableData[0]
      const firstRowStartMinutes = this.timeToMinutes(firstRow.workTime) // 时段一的上班时间
      // 循环校验
      for (let i = 0; i < this.form.tableData.length; i++) {
        // 时段一
        if (i === 0) {
          let currentRow = this.form.tableData[i]
          const startMinutes = this.timeToMinutes(currentRow.workTime) // 上班时间
          const endMinutes = this.timeToMinutes(currentRow.offWorkTime) // 下班时间
          // 01 校验单个时间段是否超过24小时
          let diffMinutes = endMinutes - startMinutes
          if (currentRow.offWorkType === 2) {
            // 如果是次日我们需要加上24小时,用开始时间减去结束时间加上24小时
            diffMinutes += time // 加上跨天时间
          }
          if (diffMinutes > 24 * 60) {
            callback(new Error('单个时间段不能超过24小时'))
            return
          }
          // 02 如果当前行是当日就要校验下班时间是否大于上班时间，如果是次日就就要校验下班时间是否大于次日上班时间
          if (currentRow.offWorkType === 1) {
            // 当日
            if (endMinutes <= startMinutes) {
              // 确保下班时间晚于上班时间
              callback(new Error('下班时间必须晚于上班时间'))
              return
            }
          } else if (currentRow.offWorkType === 2) {
            // 次日
            let startMinutesS = startMinutes + time
            if (startMinutesS < endMinutes) {
              // 确保下班时间晚于上班时间
              callback(new Error('次日下班时间必须晚于当日上班时间'))
              return
            }
          }
        } else {
          // 还要保存当前行的上一个时段得下班时间
          let previousRow = this.form.tableData[i - 1]
          const previousRowEndMinutes = this.timeToMinutes(previousRow.offWorkTime) // 上一个时段的下班时间
          // 其他时段
          let otherRow = this.form.tableData[i]
          let startMinutes = this.timeToMinutes(otherRow.workTime) // 上班时间
          let endMinutes = this.timeToMinutes(otherRow.offWorkTime) // 下班时间
          // 01 校验后面上班时段是当日还是次日，分为两种情况
          // 情况一：当日
          if (otherRow.workType === 1) {
            // 当前上班时间必须大于上一个时段下班时间
            if (startMinutes <= previousRowEndMinutes) {
              callback(new Error('当前上班时间必须大于上一个时段下班时间'))
              return
            }
            // 当前上班时间不能小于大于23:59
            if (startMinutes >= 24 * 60) {
              callback(new Error('当前上班时间不能大于23:59'))
              return
            }
          } else if (otherRow.workType === 2) {
            // 次日需要当前时间加上24小时
            let startMinutesS = startMinutes + time
            // 次日 上班时间不能小于上一个时段下班时间
            if (startMinutesS <= previousRowEndMinutes) {
              callback(new Error('次日上班时间必须大于上一个时段下班时间1'))
              return
            }
            // 次日 他的上班时间不能大于时段一得上班时间
            let firstRowStartMinutesS = firstRowStartMinutes + time
            if (startMinutesS >= firstRowStartMinutesS) {
              callback(new Error('次日上班时间不能大于时段一得上班时间'))
              return
            }
            if (otherRow.offWorkType === 1) {
              // 情况一：上班是次日，下班是当日
              // 下班时间不能小于当前上班时间
              if (endMinutes <= startMinutesS) {
                callback(new Error('下班时间必须大于上班时间'))
                return
              }
            } else if (otherRow.offWorkType === 2) {
              // 情况二：上班是次日，下班是次日
              let endMinutesS = endMinutes + time
              // 下班时间不能小于当前上班时间
              if (endMinutesS <= startMinutesS) {
                callback(new Error('下班时间必须大于上班时间'))
                return
              }
            }
          }
          // 02 校验后面下班时段是当日还是次日，分为两种情况
          // 情况一：当日
          if (otherRow.offWorkType === 1) {
            // 当前下班时间必须大于上班时间
            if (endMinutes <= startMinutes) {
              callback(new Error('下班时间必须晚于上班时间'))
              return
            }
            // 当前下班时间不能大于23:59
            if (endMinutes >= 24 * 60) {
              callback(new Error('下班时间不能大于23:59'))
              return
            }
          } else if (otherRow.offWorkType === 2) {
            // 次日加上24小时
            let endMinutesS = endMinutes + time
            // 他的下班时间不能大于时段一得上班时间
            let firstRowStartMinutesS = firstRowStartMinutes + time
            if (endMinutesS > firstRowStartMinutesS) {
              callback(new Error('下班时间不能大于时段一得上班时间'))
              return
            }
          }
        }
      }
      callback() // 校验通过
    }
  }
}
</script>
<style scoped lang="scss">
.totalTime {
  margin-top: 8px;
}
.width100 {
  width: 100px;
}
.ml-8 {
  margin-left: 8px;
}
</style>
