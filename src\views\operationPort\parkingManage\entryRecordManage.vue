<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <div class="search-from">
        <el-select v-model="searchFrom.querySection" placeholder="时间范围" clearable>
          <el-option v-for="item in timeTypeList" :key="item.id" :label="item.label" :value="item.id"> </el-option>
        </el-select>
        <el-date-picker v-model="dataRange" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd">
        </el-date-picker>
        <el-input v-model="searchFrom.proofNum" placeholder="凭证号" clearable style="width: 200px"></el-input>
        <el-input v-model="searchFrom.userName" placeholder="用户名称" clearable style="width: 200px"></el-input>
        <div style="display: flex">
          <el-button type="primary" @click="searchForm">查询</el-button>

          <el-button type="primary" plain @click="resetForm">重置</el-button>
        </div>
      </div>
      <el-dialog title="图片查看" :visible.sync="dialogVisible" width="900px" :before-close="handleClose" custom-class="model-dialog">
        <div class="dialogs">
          <div style="padding: 15px 20px; width: 49%">
            <div class="entrance">
              <div>
                <p style="color: #333; font-size: 18px; font-family: PingFang SC-Semibold, PingFang SC">入场 <br /><img src="@/assets/images/parkingLot/entrance.png" alt="" /></p>
              </div>
              <div>
                <div class="entranceTxt">
                  <p>入场凭证类型：</p>
                  <p>车牌号码</p>
                </div>
                <div class="entranceTxt">
                  <p>入场凭证：</p>
                  <p>{{ rowList.proofNum }}</p>
                </div>
                <div class="entranceTxt">
                  <p>入场套餐：</p>
                  <p>{{ rowList.setMealType }}</p>
                </div>
                <div class="entranceTxt">
                  <p>入场设备：</p>
                  <p>{{ rowList.inParkingEq }}</p>
                </div>
                <div class="entranceTxt">
                  <p>入场时间：</p>
                  <p>{{ rowList.inParkingTime }}</p>
                </div>
              </div>
            </div>
            <p style="margin: 15px 0">入场图片</p>
            <div class="admissionPictures">
              <img v-if="rowList.inParkingPhotos" :src="rowList.inParkingPhotos[0] || empty" alt="" />
            </div>
            <div class="admissionPictures">
              <img v-if="rowList.inParkingPhotos" :src="rowList.inParkingPhotos[1] || empty" alt="" />
            </div>
          </div>
          <div style="padding: 15px 20px; width: 49%">
            <div class="entrance" style="background-color: #fff7e8">
              <div>
                <p style="color: #333; font-size: 18px; font-family: PingFang SC-Semibold, PingFang SC">出场 <br /><img src="@/assets/images/parkingLot/enter.png" alt="" /></p>
              </div>
              <div>
                <div class="entranceTxt">
                  <p>出场凭证类型：</p>
                  <!-- <p>车牌号码</p> -->
                </div>
                <div class="entranceTxt">
                  <p>出场凭证：</p>
                  <!-- <p>粤BBT8379</p> -->
                </div>
                <div class="entranceTxt">
                  <p>出场套餐：</p>
                  <!-- <p>临时用户A</p> -->
                </div>
                <div class="entranceTxt">
                  <p>出场设备：</p>
                  <!-- <p>1号右入口</p> -->
                </div>
                <div class="entranceTxt">
                  <p>出场时间：</p>
                  <!-- <p>2023-10-19 08:00:00</p> -->
                </div>
              </div>
            </div>
            <p style="margin: 15px 0">出场图片</p>
            <div class="admissionPictures">
              <!-- <img :src="img" alt="" v-if="img" /> -->
              <img :src="empty" alt="" />
            </div>
            <div class="admissionPictures">
              <!-- <img src="../../../assets/images/parkingLot/暂无图片.png" alt="" v-if="img" /> -->
              <img :src="empty" alt="" />
            </div>
          </div>
        </div>
        <!-- <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="handleClose">取 消</el-button>
        </span> -->
      </el-dialog>
    </div>
    <div slot="content" class="table-content">
      <TablePage
        ref="table"
        v-loading="tableLoading"
        :showPage="true"
        :tableColumn="tableColumn"
        :data="tableData"
        height="calc(100% - 40px)"
        :pageData="pageData"
        :pageProps="pageProps"
        @pagination="paginationChange"
      />
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import empty from '@/assets/images/parkingLot/parking-null.png'

export default {
  name: 'entryRecordManage',
  components: {},
  data() {
    return {
      empty,
      dialogVisible: false,
      rowList: {},
      dataRange: [], // 时间范围
      searchFrom: {
        querySection: '', // 时间

        proofNum: '', // 凭证号
        userName: '' // 用户名称
      },
      timeTypeList: [
        {
          id: 1,
          label: '今天'
        },
        {
          id: 2,
          label: '昨天'
        },
        {
          id: 3,
          label: '上周'
        },
        {
          id: 4,
          label: '上个月'
        },
        {
          id: 5,
          label: '最近60天'
        }
      ],
      tableLoading: false,
      tableColumn: [
        // {
        //   type: 'selection',
        //   align: 'center',
        //   selectable: (row) => {
        //     return row.id !== 1
        //   }
        // },
        {
          prop: '',
          label: '序号',
          width: '60',
          formatter: (scope) => {
            // 当前分页重置
            // return scope.$index + 1
            // 跨分页序号
            return (this.pageData.current - 1) * this.pageData.size + scope.$index + 1
          }
        },
        {
          prop: 'proofNum',
          label: '凭证号'
        },
        {
          prop: 'userName',
          label: '用户名称',
          width: '90'
        },
        {
          prop: 'setMealType',
          label: '套餐类型'
        },
        {
          prop: 'inParkingTime',
          label: '入场时间'
        },
        {
          prop: 'inParkingEq',
          label: '入场设备'
        },
        {
          prop: '',
          label: '视频图片',
          width: '90',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" style="color: #5482ee" onClick={() => this.handleDetailEvent(row.row)}>
                  查看
                </span>
              </div>
            )
          }
        },
        {
          prop: 'recordType',
          label: '记录类型'
        },
        {
          prop: 'whether',
          label: '是否出场',
          width: '90'
        },
        {
          prop: 'parkingLotName',
          label: '车场名称'
        },

        {
          prop: 'operateTime',
          label: '操作时间'
        },

        {
          prop: 'operator',
          label: '操作员',
          width: '90'
        },

        {
          prop: 'operationModel',
          label: '操作方式'
        }
      ],
      tableData: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      pageProps: {
        page: 'current',
        pageSize: 'size',
        total: 'total'
      }
    }
  },
  mounted() {
    this.getRecordList()
  },
  methods: {
    // 获取人员列表
    getRecordList() {
      let param = {
        ...this.searchFrom,
        sectionBegin: this.dataRange[0] || '',
        sectionEnd: this.dataRange[1] || '',
        pageSize: this.pageData.size,
        page: this.pageData.current
      }
      this.tableLoading = true
      this.$api
        .getInParkingRecord(param)
        .then((res) => {
          if (res.code == 200) {
            res.data.forEach((i) => {
              i.whether = '否'
            })
            this.tableData = res.data
            this.pageData.total = res.total
          }
          this.tableLoading = false
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    // 查询
    searchForm() {
      this.pageData.current = 1
      this.getRecordList()
    },
    // 重置查询
    resetForm() {
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.dataRange = []
      this.searchForm()
    },
    // 查看详情
    handleDetailEvent(row) {
      this.rowList = row
      this.dialogVisible = true
    },
    handleClose() {
      this.dialogVisible = false
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getRecordList()
    },
    // 判断当前页是否是最后一页
    isLastPage(deleteNum) {
      let deleteAfterPage = Math.ceil((this.pageData.total - deleteNum) / this.pageData.size)
      let currentPage = this.pageData.current > deleteAfterPage ? deleteAfterPage : this.pageData.current
      this.pageData.current = currentPage < 1 ? 1 : currentPage
    }
  }
}
</script>
<style lang="scss" scoped>
.control-btn-header {
  padding: 10px !important;
  .search-from {
    display: flex;
    & > div {
      margin-right: 10px;
    }
  }
}

.table-content {
  margin-top: 15px;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 10px;
  background-color: #fff;
}
.dialogs {
  width: 100%;
  padding: 10px;
  display: flex;
  height: 725px;
  justify-content: space-between;
  > div {
    width: 49%;
    background-color: #fff;
  }
}
.entrance {
  display: flex;
  padding: 15px;
  background-color: #e6effc;
  > div:nth-child(1) {
    width: 30%;
    > p {
      width: 40px;
      text-align: center;
      margin: 54px auto;
    }
  }
  > div:nth-child(2) {
    width: 70%;
  }
}
.entranceTxt {
  display: flex;
  > p:nth-child(1) {
    width: 100px;
    font-size: 14px;
    color: #666666;
  }
  > p:nth-child(2) {
    text-align: left;
    font-size: 14px;
    color: #333333;
  }
}
.admissionPictures {
  width: 100%;
  height: 200px;
  margin-bottom: 15px;
  img {
    width: 100%;
    height: 200px;
  }
}
::v-deep .el-dialog {
  height: 800px;
}
::v-deep .model-dialog .el-dialog__body {
  overflow: hidden;
  max-height: 800px;
}
</style>
<style lang="scss">
.operationBtn-span {
  margin-right: 10px;
}
</style>
