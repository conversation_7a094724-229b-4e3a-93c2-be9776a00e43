<template>
  <el-drawer
    :visible.sync="drawerDialog"
    :with-header="true"
    size="55%"
    :show-close="true"
    :before-close="closeDrawer"
  >
    <div slot="title" class="coursrDrawer">选择试题</div>
    <div class="drawer_conter">
      <div class="content_left">
        <div class="title">试题分类</div>
        <div class="tree">
          <el-tree
            ref="typeTree"
            v-loading="treeLoading"
            :data="treeData"
            :props="defaultProps"
            :default-expand-all="true"
            :default-checked-keys="expanded"
            show-checkbox
            node-key="id"
            style="margin-top: 10px"
            highlight-current
            @node-click="handleNodeClick"
          >
              <span class="span-ellipsis" slot-scope="{ node, data }">
                <span :title="node.label">{{ node.label }}</span>
                <div v-if="data.disabled" class="disabled" @click.stop></div>
              </span>
          </el-tree>
        </div>
      </div>
      <div class="content_right">
        <div class="seachTop">
          <div>
            <el-select
              v-model="seachForm.quesTypeCode"
              filterable
              clearable
              placeholder="全部题型"
              style="width: 180px; margin-right: 16px;" 
              @change="getQuestionsList"
            >
              <el-option
                style="padding: 0px 35px;"
                v-for="item in quesTypeList"
                :key="item.id"
                :label="item.dictName"
                :value="item.id"
              >
              </el-option>
            </el-select>
            <el-input
              v-model="seachForm.topic"
              style="width: 200px; margin-right: 16px"
              placeholder="试题编号/试题关键词"
              maxlength="25"
            >
              <i slot="suffix" class="el-input__icon el-icon-search" @click="getQuestionsList"></i>
            </el-input>
            <el-checkbox  v-model="mySubmitFlag">我提交的 </el-checkbox>
          </div>
          <span class="allFoldClass" @click="allFold">{{
            isAllFold ? "全部展开" : "全部折叠"
          }}</span>
        </div>
        <div class="table">
          <div
            v-for="(item, index) in exercisesList"
            :key="item.id"
            :name="item.id"
            :ref="'exercisesItem' + index"
            :class="['exercisesItem', item.isExpand ? 'expand' : '']"
          >
            <div class="exercisesTop">
              <div class="left">
                <el-checkbox
                  v-model="item.checked"
                  @change="checkItem()"
                ></el-checkbox>
                <div class="exercisesType">
                  {{
                    item.type == "1"
                      ? "单选题"
                      : item.type == "2"
                      ? "多选题"
                      : "判断题"
                  }}
                </div>
                <span>({{ item.id }})</span>
              </div>
              <div class="right">
                <span @click="isExpandBtn(item, index)">{{
                  item.isExpand ? "折叠" : "展开"
                }}</span>
              </div>
            </div>
            <div :class="['exercisesName', item.isExpand ? '' : 'title']">
              {{ item.topic }}
            </div>
            <el-radio-group
              v-if="item.type == '1'"
              class="radio"
              v-model="item.answer"
              disabled
            >
              <el-radio
                v-for="(item, index) in item.options"
                :key="index"
                :label="item.id"
                >{{ item.id }}. {{ item.label }}</el-radio
              >
            </el-radio-group>
            <el-checkbox-group
              v-if="item.type == '2'"
              v-model="item.answer"
              class="radio"
              disabled
            >
              <el-checkbox
                v-for="(item, index) in item.options"
                :key="index"
                :label="item.id"
                >{{ item.id }}. {{ item.label }}</el-checkbox
              >
            </el-checkbox-group>
            <p>答案：{{ item | getAnswer }}</p>
            <p>
              解析：
              {{ item.analysis }}
            </p>
          </div>
        </div>
      </div>
    </div>
    <div class="drawer_footer">
      <div class="drawer_footerSum">
        已选择 <span class="sumNamber">{{checkDataList.length}}</span> 个课程
      </div>
      <div>
        <el-checkbox
          :indeterminate="isIndeterminate"
          v-model="checkAll"
          @change="checkAllBtn"
          style="margin-right: 16px;"
          >全选</el-checkbox
        >
        <el-button type="primary" plain @click="closeDrawer">取消</el-button>
        <el-button type="primary" plain @click="submit">添加</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
export default {
  props: {
    testQuestionsList: {
      type:Array,
      default:()=>{ return [] }    
    },
    drawerDialog: {
      type: Boolean,
      default: false,
    },
    subjectId: {
      type: Number,
      default: null,
    },
    courseName: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      treeLoading: false,
      treeData: [],
      defaultProps: {
        children: "childList",
        label: "name",
      },
      expanded: [],
      seachForm: {
        quesTypeCode: "",
        topic: "",
      },
      quesTypeList: [
        {
          id: 1,
          dictName: "单选题",
        },
        {
          id: 2,
          dictName: "多选题",
        },
        {
          id: 3,
          dictName: "判断题",
        },
      ],
      exercisesList: [],
      enterNum: "",
      radio: "",
      mySubmitFlag: false,
      paginationData: {
        pageNo: 1,
        pageSize: 15,
        total: 0,
      },
      routeInfo: {},
      isAllFold:true,
      isIndeterminate: false,
      checkAll:false,
      checkDataList:[]
    };
  },
  created() {
    this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
  },
  watch:{
    mySubmitFlag(val){
      this.getQuestionsList()
    },
    checkDataList(val) {
      this.checkAll = this.checkDataList.length === this.exercisesList.length;
      this.isIndeterminate = this.checkDataList.length > 0 && this.checkDataList.length < this.exercisesList.length;
    }
  },
  filters:{
    getAnswer(val){
      if(val.type=='3'){
        return val.answer=='1'?'正确':'错误'
      }else if(val.type =='2'){
        return val.answer.toString()
      }else{
        return val.answer
      }
    }
  },
  methods: {
    // 获取试题分类列表
    getTblleList() {
      this.treeLoading = true;
      let data = {
        pageNo: 1,
        pageSize: 9999,
      };
      this.$api.ipsmFicationlList(data).then((res) => {
        if (res.code == 200) {
          this.treeData = res.data.records;
          this.expanded = [];
          this.expanded.push(this.subjectId);
          this.$nextTick(() => {
            this.disableAllTree(this.treeData);
          });
          this.getQuestionsList();
        } else {
          this.$message.error(res.message);
        }
        this.treeLoading = false;
      });
    },
    disableAllTree(v) {
      v.forEach((item) => {
        if (item.childList) {
          this.disableAllTree(item.childList);
        }
        this.$set(item, "disabled", true); // :data="resourceList"中需要禁用的选中disabled=true
      });
    },
    handleNodeClick(data, checked) {},
    // 获取试题列表
    getQuestionsList(questionsList) {
      let data = {
        subjectId: this.subjectId,
        systemCode: this.routeInfo.systemCode,
        topic: this.seachForm.topic,
        type: this.seachForm.quesTypeCode,
        createBy:this.mySubmitFlag?this.routeInfo.userId:'',
        pageNo: 1,
        pageSize: 9999,
        moduleType: this.mySubmitFlag?"2":'1', // 1为公共题库2为我的试题
        courseName: this.courseName,
      };
      this.$api.getListByCourseName(data).then((res) => {
        if (res.code == "200") {
          res.data.list.forEach((i) => {
            i.options = JSON.parse(i.options);
            i.isExpand = false;
            i.checked = false;
            if(i.type=='2'){
              i.answer = i.answer.split(',')
            }
          });
          this.checkDataList = []
          if(this.testQuestionsList.length>0){
            this.testQuestionsList.forEach(k=>{
              res.data.list.forEach(j=>{
                if(j.id==(k.free1||k.id)){
                  this.checkDataList.push(j)
                  j.checked = true
                }
              })
            })
          }
          this.exercisesList = res.data.list;
        }
      });
    },
    // 全部展开
    allFold(){
      this.exercisesList.forEach(item=>item.isExpand=!item.isExpand)
      this.isAllFold=!this.isAllFold
    },
    // 全选
    checkAllBtn(val) {
      this.checkDataList = []
      if (val) {
        this.exercisesList.forEach((item,index) => {
          if(index < 50){
            item.checked = true;
            this.checkDataList.push(item)
          }
        });
      }else{
        this.exercisesList.forEach((item) => {
          item.checked = false;
        });
      }
      this.isIndeterminate = false;
    },
    checkItem() {
      this.checkDataList=[]
      this.exercisesList.forEach(i=>{
        if(i.checked){
          this.checkDataList.push(i)
        }
      })
      this.checkAll = this.checkDataList.length === this.exercisesList.length;
      this.isIndeterminate = this.checkDataList.length > 0 && this.checkDataList.length < this.exercisesList.length;
    },
    closeDrawer() {
      this.checkAll=false
      this.isIndeterminate=false
      this.exercisesList.forEach((item) => {
        item.checked = false;
      });
      this.checkDataList=[]
      this.$emit("closeDrawer", false);
    },
    submit(){
      this.$emit('testQuestions',this.checkDataList)
      this.closeDrawer()
    },
    isExpandBtn(item, index) {
      item.isExpand = !item.isExpand;
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}
.coursrDrawer {
  color: #333333;
  font-weight: 500;
  font-size: 18px;
}
//  .el-drawer__header {
//   height: 56px;
//   line-height: 56px !important;
//   margin-bottom: 0 !important;
//   border-bottom: 1px solid #dcdfe6;
//   box-shadow: -4px 0 4px 0 rgba(203, 205, 220, 0.24);
// }
// /deep/ .el-select-dropdown__item {
//   padding: 0px 30px !important;
// }
.drawer_conter {
  padding: 24px;
  height: calc(100% - 80px);
  display: flex;
  .content_left {
    width: 200px;
    border-radius: 4px;
    border: 1px solid #e5e6eb;
    .title {
      margin: 16px;
      font-size: 16px;
    }
    .tree {
      height: calc(100% - 50px);
      overflow: auto;
    }
  }
  .content_right {
    flex: 1;
    min-width: 0;
    margin-left: 16px;
    .seachTop {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      .expand {
        color: #3562db;
        font-size: 14px;
      }
    }
    .table {
      height: calc(100% - 48px);
      overflow: auto;
      background-color: #faf9fc;
      padding: 16px;
      font-size: 14px;
      .exercisesItem {
        height: 90px;
        overflow: hidden;
        background-color: #fff;
        margin-bottom: 26px;
        padding: 16px;
        .exercisesTop {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;
          .left {
            display: flex;
            align-items: center;
            justify-content: center;
            span {
              color: #7f848c;
            }
          }
          .right {
            color: #ccced3;
            span {
              color: #3562db;
              margin-left: 16px;
              cursor: pointer;
            }
          }
          .exercisesType {
            width: 58px;
            height: 22px;
            line-height: 22px;
            text-align: center;
            border-radius: 4px;
            color: #86909c;
          }
        }
        .exercisesName {
          line-height: 20px;
          margin-bottom: 26px;
          word-break: break-all;
        }
        .title {
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
        }
        .el-radio {
          margin-left: 38px;
          font-size: 14px;
          color: #7f848c !important;
          line-height: 18px;
          display: flex;
        }
        p {
          font-size: 14px;
          color: #7f848c !important;
          line-height: 20px;
          margin-bottom: 16px;
        }
      }
      .expand {
        height: auto;
      }
    }
  }
}
::v-deep .el-radio {
  display: block;
  margin: 10px 0;
  .el-radio__label {
    white-space: normal;
  }
}
.drawer_footer {
  padding: 20px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #7f848c;
  .sumNamber {
    color: #000;
  }
}
::v-deep .radio{
  .el-checkbox {
    display: block;
    margin: 10px 0;
    margin-left: 38px;
    font-size: 14px;
    color: #7f848c !important;
    line-height: 30px;
  }
}
.allFoldClass{
  font-size: 14px;
  color: #3562db;
  cursor: pointer;
}
</style>
