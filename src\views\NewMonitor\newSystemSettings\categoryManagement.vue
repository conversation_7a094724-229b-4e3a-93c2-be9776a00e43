<template>
  <PageContainer>
    <div slot="header">
      <div class="searchForm">
        <div class="search-box">
          <el-input
            v-model="searchForm.dictionaryName"
            clearable
            filterable
            placeholder="请输入系统名称"
            @blur="(event) => (searchForm.dictionaryName = event.target.value.replace(/\s+/g, ''))"
          ></el-input>
          <div class="ml-16">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button type="primary" plain @click="reset">重置</el-button>
          </div>
        </div>
      </div>
    </div>
    <div slot="content" ref="contentRef" class="table-box">
      <div class="tableContainer">
        <!-- <el-button type="primary" style="margin-bottom: 12px" @click="handleListEvent('add')">新建品类</el-button> -->
        <el-table
          :data="tableData"
          style="width: 100%; margin-bottom: 20px"
          row-key="dictionaryDetailsId"
          border
          height="250"
          default-expand-all
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        >
          <el-table-column prop="dictionaryDetailsName" label="类型名称"> </el-table-column>
          <el-table-column prop="dictionaryDetailsCode" label="类型标识"> </el-table-column>
          <el-table-column prop="dictionaryDetailsSort" label="排序"> </el-table-column>
          <el-table-column prop="isMainDevice" label="主设备">
            <template slot-scope="scope">
              <span v-if="scope.row.parentId != '-1'">
                {{ scope.row.isMainDevice === 0 ? '否' : '是' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template slot-scope="scope">
              <el-button v-if="scope.row.level === 1" type="text" @click="handleListEvent('look', scope.row)">查看</el-button>
              <el-button v-if="scope.row.level === 1" type="text" @click="handleListEvent('edit', scope.row)">系统配置</el-button>
              <el-button v-if="scope.row.level === 2" type="text" @click="handleListEvent('edit', scope.row)">编辑</el-button>
              <el-button v-if="scope.row.level === 2" type="text" @click="handleListEvent('del', scope.row)">删除</el-button>
              <el-button v-if="scope.row.level === 1" type="text" @click="handleListEvent('addChild', scope.row)">添加子分类</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="pagination.pageNo"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageTotal"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
      <!-- 查看详情 -->
      <el-dialog
        v-dialogDrag
        :visible.sync="lookVisible"
        :before-close="dialogClosed"
        :modal="false"
        :close-on-click-modal="false"
        title="查看详情"
        width="50%"
        custom-class="model-dialog"
      >
        <div class="diaContent" style="padding: 10px">
          <el-form ref="formLook" :model="formLook" label-width="130px" :rules="rules" label-position="right">
            <el-form-item label="模块名称：" class="form-item">
              {{ formLook.moduleName }}
            </el-form-item>
            <el-form-item label="系统类型：" class="form-item">
              {{ formLook.systemTypeName }}
            </el-form-item>
            <el-form-item label="通用功能：" class="form-item">
              <el-table border :data="formLook.publicMenuDetailList" height="200px" style="width: 690px">
                <el-table-column prop="menuName" label="功能名称" show-overflow-tooltip width="150"></el-table-column>
                <el-table-column prop="menuPath" label="路径 " show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span>
                      {{ scope.row.menuPath.replace('/runningMenu', `/${formLook.systemTypeCode}`) }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80">
                  <template slot-scope="scope">
                    <el-button type="text" @click="duplication(scope.row.menuPath.replace('/runningMenu', `/${formLook.systemTypeCode}`))">复制</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
            <el-form-item label="私有功能：" class="form-item">
              <el-table border :data="formLook.privateMenuList" height="200px" style="width: 690px">
                <el-table-column prop="menuName" label="功能名称" show-overflow-tooltip width="150"></el-table-column>
                <el-table-column prop="menuPath" label="路径 " show-overflow-tooltip></el-table-column>
                <el-table-column label="操作" width="80">
                  <template slot-scope="scope">
                    <el-button type="text" @click="duplication(scope.row.menuPath)">复制</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
            <el-form-item label="备注：" class="inputWidth">
              {{ formLook.remark }}
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="dialogClosed">取 消</el-button>
          <el-button type="primary" @click="dialogClosed">确 定</el-button>
        </span>
      </el-dialog>
      <!-- 系统配置 -->
      <el-dialog
        v-dialogDrag
        :visible.sync="dialogVisible"
        :before-close="dialogClosed"
        :modal="false"
        :close-on-click-modal="false"
        :title="title"
        width="50%"
        custom-class="model-dialog"
      >
        <div class="diaContent" style="padding: 10px">
          <el-form ref="formInline" :model="formInline" label-width="100px" :rules="rules" label-position="right">
            <el-form-item label="系统名称：" prop="systemTypeName" class="form-item">
              <el-input v-model.trim="formInline.systemTypeName" placeholder="请输入系统名称" :maxlength="20" readonly></el-input>
            </el-form-item>
            <el-form-item label="系统标识：" prop="systemTypeCode" class="form-item">
              <el-input v-model.trim="formInline.systemTypeCode" placeholder="请输入系统标识" :maxlength="20" readonly></el-input>
              <!-- <el-select v-model.trim="formInline.systemTypeCode" placeholder="请选择系统标识"
                                @change="systemTypeChange">
                                <el-option v-for="item in systemType" :key="item.dictionaryDetailsCode"
                                    :label="item.dictionaryDetailsName" :value="item.dictionaryDetailsCode"></el-option>
                            </el-select> -->
            </el-form-item>
            <el-form-item label="通用功能：" prop="publicMenuList" class="inputWidth">
              <el-checkbox-group v-model="checkedArr" @change="handleNameArrChange">
                <el-checkbox v-for="item in checkedList" :key="item.id" :label="item.id">{{ item.menuName }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="私有功能：" class="inputWidth">
              <span class="span-plus ml-16" @click="spanPlus">添加</span>
              <div v-for="(domain, index) in formInline.privateMenuList" :key="index">
                <div v-if="formInline.privateMenuList">
                  <el-form-item :prop="'privateMenuList.' + index + '.menuName'" :rules="getRules(index, 'menuName')" style="display: inline-block">
                    <el-input v-model.trim="domain.menuName" placeholder="请输入名称" :maxlength="20" style="width: 200px"></el-input>
                  </el-form-item>
                  <el-form-item :prop="'privateMenuList.' + index + '.menuTag'" :rules="getRules(index, 'menuTag')" style="display: inline-block">
                    <el-input v-model.trim="domain.menuTag" placeholder="请输入标识" style="width: 200px" class="ml-16"></el-input>
                  </el-form-item>
                  <el-form-item :prop="'privateMenuList.' + index + '.menuPath'" :rules="getRules(index, 'menuPath')" style="display: inline-block">
                    <el-input v-model.trim="domain.menuPath" placeholder="请输入路径" style="width: 200px" class="ml-16"></el-input>
                  </el-form-item>
                  <div v-if="formInline.privateMenuList[index].childList">
                    <div v-for="(subDomain, subIndex) in domain.childList" :key="subIndex">
                      <el-form-item
                        :prop="'privateMenuList.' + index + '.childList.' + subIndex + '.menuName'"
                        :rules="getRules(`${index}-${subIndex}`, 'menuName')"
                        style="display: inline-block"
                      >
                        <el-input v-model.trim="subDomain.menuName" placeholder="请输入名称" :maxlength="20" style="width: 160px"></el-input>
                      </el-form-item>
                      <el-form-item
                        :prop="'privateMenuList.' + index + '.childList.' + subIndex + '.menuTag'"
                        :rules="getRules(`${index}-${subIndex}`, 'menuTag')"
                        style="display: inline-block"
                      >
                        <el-input v-model.trim="subDomain.menuTag" placeholder="请输入标识" style="width: 160px" class="ml-16"></el-input>
                      </el-form-item>
                      <el-form-item
                        :prop="'privateMenuList.' + index + '.childList.' + subIndex + '.menuPath'"
                        :rules="getRules(`${index}-${subIndex}`, 'menuPath')"
                        style="display: inline-block"
                      >
                        <el-input v-model.trim="subDomain.menuPath" placeholder="请输入路径" style="width: 160px" class="ml-16"></el-input>
                      </el-form-item>
                      <span class="span-reduce ml-16" @click="delSubpage(index, subIndex)">删除</span>
                    </div>
                  </div>
                  <span class="span-plus ml-16" @click="addSubpage(index)">添加子页面</span>
                  <span class="span-reduce ml-16" @click="spanReduce(index)">删除</span>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="备注：" class="inputWidth">
              <el-input v-model.trim="formInline.remark" maxlength="200" show-word-limit type="textarea" placeholder="请输入模版说明，最多200字"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="dialogClosed">取 消</el-button>
          <el-button type="primary" @click="submit('formInline')">确 定</el-button>
        </span>
      </el-dialog>
      <!-- 新增子级 -->
      <el-dialog
        v-dialogDrag
        :visible.sync="addChildVisible"
        :before-close="dialogClosed"
        :modal="false"
        :close-on-click-modal="false"
        :title="title1"
        width="50%"
        custom-class="model-dialog"
      >
        <div class="diaContent" style="padding: 10px">
          <el-form ref="formChildren" :model="formChildren" label-width="130px" :rules="rules" label-position="right">
            <el-form-item label="名称：" prop="dictionaryDetailsName" class="form-item">
              <el-input v-model.trim="formChildren.dictionaryDetailsName" placeholder="请输入名称" :maxlength="20"></el-input>
            </el-form-item>
            <el-form-item label="类型标识：" prop="dictionaryDetailsCode" class="form-item">
              <el-input v-model.trim="formChildren.dictionaryDetailsCode" placeholder="请输入类型标识"> </el-input>
            </el-form-item>
            <el-form-item label="是否主检测设备:" prop="isMainDevice" class="form-item">
              <el-select ref="troopDeviceCode" v-model="formChildren.isMainDevice" placeholder="请选择是否主检测设备" clearable filterable>
                <el-option v-for="item in isMainDeviceList" :key="item.value" :label="item.name" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="排序：" prop="dictionaryDetailsSort" class="form-item">
              <el-input v-model.trim="formChildren.dictionaryDetailsSort" placeholder="请输入排序" :maxlength="20"></el-input>
            </el-form-item>
            <el-form-item label="类型图标：">
              <template>
                <div v-if="fileList && fileList.length != 0" class="isimg">
                  <img :src="assetsImgUrl || $tools.imgUrlTranslation(formChildren.dictionaryDetailsPicUrl)" alt="" style="width: 146px; height: 146px" />
                  <p class="hover"><i class="el-icon-delete" @click="deletImg"></i></p>
                </div>
                <el-upload
                  v-else
                  action=""
                  list-type="picture-card"
                  :file-list="fileList"
                  multiple
                  :limit="1"
                  accept=".png,.jpg,.jpeg,.gif"
                  :http-request="handleUpload"
                  :on-remove="deletImg"
                  :before-upload="beforeUpload"
                  :class="{ hide: hideUpload }"
                >
                  <i class="el-icon-plus"></i>
                </el-upload>
              </template>
              <el-dialog :visible.sync="imgVisible" :append-to-body="true">
                <img width="100%" :src="assetsImgUrl" alt="" />
              </el-dialog>
            </el-form-item>
            <el-form-item label="模型icon：">
              <template>
                <div v-if="fileList1 && fileList1.length != 0" class="isimg">
                  <img :src="assetsImgUrl1 || $tools.imgUrlTranslation(formChildren.dictionaryDetailsModelUrl)" alt="" style="width: 146px; height: 146px" />
                  <p class="hover"><i class="el-icon-delete" @click="deletImg1"></i></p>
                </div>
                <el-upload
                  v-else
                  action=""
                  list-type="picture-card"
                  :file-list="fileList1"
                  multiple
                  :limit="1"
                  accept=".png,.jpg,.jpeg,.gif"
                  :http-request="handleUpload1"
                  :on-remove="deletImg1"
                  :before-upload="beforeUpload1"
                  :class="{ hide: hideUpload1 }"
                >
                  <i class="el-icon-plus"></i>
                </el-upload>
              </template>
              <el-dialog :visible.sync="imgVisible1" :append-to-body="true">
                <img width="100%" :src="assetsImgUrl1" alt="" />
              </el-dialog>
            </el-form-item>
            <el-form-item label="备注：" prop="dictionaryDetailsRemake" class="inputWidth">
              <el-input v-model.trim="formChildren.dictionaryDetailsRemake" maxlength="200" show-word-limit type="textarea" placeholder="请输入备注"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="dialogClosed">取 消</el-button>
          <el-button type="primary" :disabled="isSubmitting" @click="submitChildren('formChildren')">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </PageContainer>
</template>
<script>
import { monitoringFacility } from '@/util/newDict.js'
import { newRoutingListChildren } from './routingList.js'
export default {
  name: 'categoryManagement',
  data() {
    return {
      isSubmitting: false, // 状态变量
      lastSubmissionTime: 0,
      imageUrl: '',
      imageUrl1: '',
      fileList: [],
      fileList1: [],
      tableData: [],
      title: '',
      title1: '添加子分类',
      addChildVisible: false,
      tableLoading: false,
      systemType: [],
      formInline: {
        id: '',
        systemTypeName: '', // 模块名称
        systemTypeCode: '', // 系统类型编码
        publicMenuList: [], // 通用功能
        privateMenuList: [], // 私有功能
        remark: '', // 备注
        routerJson: {} // 菜单路由
      },
      requiredFields: [],
      checkedArr: [],
      checkedList: [],
      dialogVisible: false,
      hideUpload: false,
      hideUpload1: false,
      searchForm: {
        dictionaryName: ''
      },
      troopCaptainList: [],
      troopDeviceList: [],
      pagination: {
        pageSize: 15,
        pageNo: 1
      },
      pageTotal: 0,
      formChildren: {
        dictionaryDetailsId: '',
        dictionaryDetailsName: '', // 名称
        dictionaryDetailsCode: '', // 类型标识
        dictionaryDetailsPicUrl: '', // 类型图标
        dictionaryDetailsModelUrl: '', // 模型图标
        isMainDevice: '', // 是否主监测设备
        dictionaryDetailsSort: '', // 排序
        dictionaryDetailsRemake: '', // 备注
        level: '',
        parentId: ''
      },
      isMainDeviceList: monitoringFacility, // 是否主监测设备
      assetsImgUrl: '',
      assetsImgUrl1: '',
      imgVisible: false,
      imgVisible1: false,
      rules: {
        systemTypeName: [{ required: true, message: '请输入模块名称', trigger: 'blur' }],
        systemTypeCode: [{ required: true, message: '请选择系统类型', trigger: 'change' }],
        publicMenuList: { required: true, message: '请至少选择一个通用功能', trigger: 'blur' },
        dictionaryDetailsName: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        dictionaryDetailsCode: [{ required: true, message: '请输入类型标识', trigger: 'blur' }],
        dictionaryDetailsSort: [{ required: true, message: '请输入排序', trigger: 'blur' }]
      },
      troopTypeList: [],
      lookVisible: false,
      formLook: {
        dictionaryDetailsName: '',
        dictionaryDetailsRemake: ''
      }
    }
  },
  mounted() {
    this.getQueryCategoryByCategoryId()
    this.getTableData()
    this.getSystemManagementList()
  },
  methods: {
    // 获取系统标识
    getQueryCategoryByCategoryId() {
      let data = {
        dictionaryCategoryId: 'PRODUCT_CATEGORY',
        level: 1
      }
      this.$api.getQueryCategoryByCategoryId(data).then((res) => {
        if (res.code === '200') {
          this.systemType = res.data
        }
      })
    },
    // 系统标识选择
    // systemTypeChange(val) {
    //     this.parentMenu = this.systemType.find(item => {
    //         return item.dictionaryDetailsCode == val
    //     })
    //     this.checkedArr = []
    //     this.formInline.routerJson = {
    //         path: `/${this.parentMenu.dictionaryDetailsCode}`,
    //         component: 'Layout',
    //         name: `${this.parentMenu.dictionaryDetailsCode}`,
    //         redirect: '',
    //         meta: {
    //             title: this.parentMenu.dictionaryDetailsName,
    //             menuAuth: `/${this.parentMenu.dictionaryDetailsCode}`
    //         },
    //         children: []
    //     }
    // },
    // 通用功能
    getSystemManagementList() {
      this.$api.getSystemManagementList().then((res) => {
        if (res.code === '200') {
          this.checkedList = res.data
        }
      })
    },
    // 通用功能多选
    handleNameArrChange(data) {
      this.checkedArr = data
      const matchedItems = newRoutingListChildren.filter((child) => data.some((item) => item === child.meta.id))
      this.formInline.publicMenuList = []
      if (this.parentMenu) {
        if (!this.formInline.routerJson) {
          this.formInline.routerJson = {}
        }
        this.formInline.routerJson.redirect = '/' + this.parentMenu.dictionaryDetailsCode + '/' + matchedItems[0].path
        matchedItems.forEach((item) => {
          item.meta.menuAuth = '/' + this.parentMenu.dictionaryDetailsCode + '/' + item.path
          this.formInline.publicMenuList.push(item.meta.id)
          item.children.forEach((child) => {
            child.meta.systemType = this.parentMenu.dictionaryDetailsCode
            child.meta.jumpAddress = '/' + this.parentMenu.dictionaryDetailsCode + '/' + item.path
          })
        })
      }
      this.formInline.routerJson.children = matchedItems
      if (this.formInline.publicMenuList.length > 0) {
        this.$refs.formInline.clearValidate('publicMenuList') // 清除验证
      } else {
        this.$refs.formInline.validateField('publicMenuList') // 重新验证
      }
    },
    // 私有功能增加
    spanPlus() {
      // 确保所有输入都有值
      this.formInline.privateMenuList.push({ menuName: '', menuPath: '', menuTag: '', childList: [] })
      // 将新项标记为必填
      this.requiredFields.push(this.formInline.privateMenuList.length - 1)
    },
    // 添加子页面
    addSubpage(index) {
      // 确保subMenuList数组存在
      if (!this.formInline.privateMenuList[index].childList) {
        this.formInline.privateMenuList[index].childList = []
      }
      // 获取当前subMenuList的长度作为subIndex
      const subIndex = this.formInline.privateMenuList[index].childList.length
      // 添加新的子页面
      this.formInline.privateMenuList[index].childList.push({ menuName: '', menuPath: '', menuTag: '' })
      // 将新添加的子页面标记为必填
      this.requiredFields.push(`${index}-${subIndex}`)
    },
    // 删除子页面
    delSubpage(index, subIndex) {
      this.formInline.privateMenuList[index].childList.splice(subIndex, 1)
      // 从requiredFields中移除对应的必填标记
      this.requiredFields = this.requiredFields.filter((field) => field !== `${index}-${subIndex}`)
    },
    // 私有功能删除
    spanReduce(index) {
      this.formInline.privateMenuList.splice(index, 1)
      // 移除对应的必填标记
      this.requiredFields = this.requiredFields.filter((i) => i !== index)
    },
    getRules(index, field) {
      // 返回验证规则
      return this.requiredFields.includes(index) ? [{ required: true, message: '该字段为必填', trigger: 'blur' }] : []
    },
    handleAvatarSuccess(res, file) {
      const params = new FormData()
      params.append('file', file.file)
      this.$api.newUploadFiles(params).then((res) => {
        if (res.code === '200') {
          this.$message({
            message: '上传成功',
            type: 'success'
          })
          this.hideUpload = true
          this.imageUrl = res.data
          this.formChildren.dictionaryDetailsPicUrl = res.data
        } else {
          this.$message.error(res.message)
        }
      })
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg'
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isJPG) {
        this.$message.error('上传头像图片只能是 JPG 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!')
      }
      return isJPG && isLt2M
    },
    formatTableData(data) {
      return data.map((item) => {
        if (Array.isArray(item.children) && item.children.length === 0) {
          // 如果 children 为空数组,则从对象中删除该字段
          delete item.children
        } else {
          // 递归处理子节点
          item.children = this.formatTableData(item.children)
        }
        return item
      })
    },
    // 品类table列表
    getTableData() {
      this.tableLoading = true
      let data = {
        ...this.pagination,
        ...this.searchForm,
        enable: 1
      }
      this.$api
        .getCategoryManagementList(data)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = this.formatTableData(res.data.records)
            this.pageTotal = res.data.total
          } else if (res.message) {
            this.tableData = []
            this.pagination.total = 0
            this.$message.error(res.message)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    // 限制图片大小
    beforeUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过10MB')
      }
      return isLt10M
    },
    beforeUpload1(file) {
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过10MB')
      }
      return isLt10M
    },
    // 图片上传
    handleUpload(file) {
      const params = new FormData()
      params.append('file', file.file)
      this.$api.newUploadFiles(params).then((res) => {
        if (res.code === '200') {
          this.$message({
            message: '上传成功',
            type: 'success'
          })
          this.hideUpload = true
          this.formChildren.dictionaryDetailsPicUrl = res.data
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleUpload1(file) {
      const params = new FormData()
      params.append('file', file.file)
      this.$api.newUploadFiles(params).then((res) => {
        if (res.code === '200') {
          this.$message({
            message: '上传成功',
            type: 'success'
          })
          this.hideUpload1 = true
          this.formChildren.dictionaryDetailsModelUrl = res.data
        } else {
          this.$message.error(res.message)
        }
      })
    },
    deletImg() {
      this.hideUpload = false
      this.formChildren.dictionaryDetailsPicUrl = ''
      this.assetsImgUrl = ''
      this.fileList = []
    },
    deletImg1() {
      this.hideUpload1 = false
      this.formChildren.dictionaryDetailsModelUrl = ''
      this.assetsImgUrl1 = ''
      this.fileList1 = []
    },
    // 分页器
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.pageNo = 1
      this.getTableData()
    },
    search() {
      this.pagination.pageNo = 1
      this.getTableData()
    },
    reset() {
      this.searchForm = {
        dictionaryName: ''
      }
      this.pagination.pageNo = 1
      this.pageTotal = 0
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.pagination.pageNo = val
      this.getTableData()
    },
    handleListEvent(type, row) {
      if (type == 'add') {
        this.title = '新建品类'
        this.formInline = {
          dictionaryDetailsId: '',
          dictionaryDetailsName: '', // 名称
          dictionaryDetailsCode: '', // 类型标识
          dictionaryDetailsSort: '', // 排序
          dictionaryDetailsRemake: '', // 备注
          level: 1,
          parentId: '-1'
        }
        this.dialogVisible = true
      }
      if (type == 'look') {
        this.title = '查看'
        let params = {
          id: row.sysSettingsId
        }
        this.$api.getSystemManagementQueryById(params).then((res) => {
          if (res.code === '200') {
            setTimeout(() => {
              this.formLook = res.data
            }, 0)
          } else {
            this.$message.error(res.message)
          }
        })
        this.lookVisible = true
      }
      if (type == 'edit') {
        if (row.level === 1) {
          this.title = '系统配置'
          let params = {
            id: row.sysSettingsId
          }
          this.$api.getSystemManagementQueryById(params).then((res) => {
            if (res.code === '200') {
              setTimeout(() => {
                this.formInline = {
                  id: res.data.id || '',
                  systemTypeName: res.data.systemTypeName,
                  systemTypeCode: res.data.systemTypeCode,
                  publicMenuList: res.data.publicMenuList,
                  privateMenuList: res.data.privateMenuList.map((item) => ({
                    menuName: item.menuName,
                    menuPath: item.menuPath,
                    menuTag: item.menuTag,
                    childList:
                      item.childList.map((child) => ({
                        menuName: child.menuName, // 从子菜单中提取
                        menuPath: child.menuPath, // 从子菜单中提取
                        menuTag: child.menuTag // 从子菜单中提取
                      })) || [] // 如果没有子菜单，返回一个空数组
                  })),
                  remark: res.data.remark
                }
                this.formInline.routerJson = {
                  path: `/${res.data.systemTypeCode}`,
                  component: 'Layout',
                  name: `${res.data.systemTypeCode}`,
                  redirect: '',
                  meta: {
                    title: res.data.systemTypeName,
                    menuAuth: `/${res.data.systemTypeCode}`
                  },
                  children: []
                }
                this.parentMenu = this.systemType.find((item) => {
                  return item.dictionaryDetailsCode == res.data.systemTypeCode
                })
                this.checkedArr = res.data.publicMenuList
              }, 0)
            } else {
              this.$message.error(res.message)
            }
          })
          this.dialogVisible = true
        } else {
          this.title1 = '编辑子分类'
          let params = {
            dictionaryId: row.dictionaryDetailsId
          }
          this.fileList = []
          this.fileList1 = []
          this.$api.getCategoryManagementDetailById(params).then((res) => {
            if (res.code === '200') {
              this.hideUpload = true
              this.formChildren = res.data
              if (this.formChildren.dictionaryDetailsPicUrl) {
                const absoluteUrl = this.$tools.imgUrlTranslation(this.formChildren.dictionaryDetailsPicUrl)
                this.fileList.push({
                  name: 'image',
                  url: absoluteUrl
                })
              } else {
                this.fileList = []
                this.hideUpload = false
              }
              if (this.formChildren.dictionaryDetailsModelUrl) {
                const absoluteUrl1 = this.$tools.imgUrlTranslation(this.formChildren.dictionaryDetailsModelUrl)
                this.fileList1.push({
                  name: 'image',
                  url: absoluteUrl1
                })
              } else {
                this.fileList1 = []
                this.hideUpload1 = false
              }
              this.addChildVisible = true
            } else {
              this.$message.error(res.message)
            }
          })
        }
      }
      if (type == 'del') {
        this.$confirm('此操作将永久删除, 是否继续?', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let params = {
            id: row.dictionaryDetailsId
          }
          this.$api.deleteCategoryManagementData(params).then((res) => {
            if (res.code === '200') {
              this.$message.success(res.message)
              this.getTableData()
            } else {
              this.$message.error(res.message)
            }
          })
        })
      }
      if (type == 'addChild') {
        this.fileList = []
        this.fileList.length = 0
        this.fileList1 = []
        this.fileList1.length = 0
        this.title1 = '添加子分类'
        this.formChildren = {
          dictionaryDetailsId: '',
          dictionaryDetailsName: '', // 名称
          dictionaryDetailsCode: '', // 类型标识
          dictionaryDetailsPicUrl: '', // 类型图标
          dictionaryDetailsModelUrl: '', // 模型图标
          isMainDevice: '', // 是否主监测设备
          dictionaryDetailsSort: '', // 排序
          dictionaryDetailsRemake: '', // 备注
          level: row.level + 1,
          parentId: row.dictionaryDetailsId
        }
        this.addChildVisible = true
        this.deletImg()
        this.deletImg1()
      }
    },
    // 子级编辑保存
    submitChildren(formName) {
      const now = Date.now()
      const debounceTime = 1000
      if (this.isSubmitting || now - this.lastSubmissionTime < debounceTime) return
      this.isSubmitting = true // 设置为正在提交状态
      this.lastSubmissionTime = now // 更新上一次提交时间
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let data = {
            ...this.formChildren
          }
          const apiCall = data.dictionaryDetailsId ? this.$api.getCategoryEditDetail(data) : this.$api.addDetailCategoryManagement(data)
          apiCall
            .then((res) => {
              if (res.code === '200') {
                this.$message.success(res.message)
                this.getTableData()
                this.addChildVisible = false
                this.$refs[formName].resetFields()
                this.isSubmitting = false // 请求完成后重置状态
              } else {
                this.$message.error(res.message) // 提示错误消息
                this.isSubmitting = false // 处理请求错误
              }
            })
            .catch(() => {
              this.isSubmitting = false // 处理请求错误
            })
        } else {
          this.isSubmitting = false // 验证失败，重置状态
        }
      })
    },
    // 系统配置保存
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (typeof this.formInline.routerJson === 'string') {
            this.formInline.routerJson = JSON.parse(this.formInline.routerJson)
          }
          this.formInline.routerJson.children = []
          const matchedItems = newRoutingListChildren.filter((child) => this.checkedArr.some((item) => item === child.meta.id))
          this.formInline.publicMenuList = []
          if (this.parentMenu) {
            this.formInline.routerJson.redirect = '/' + this.parentMenu.dictionaryDetailsCode + '/' + matchedItems[0].path
            matchedItems.forEach((item) => {
              item.meta.menuAuth = '/' + this.parentMenu.dictionaryDetailsCode + '/' + item.path
              this.formInline.publicMenuList.push(item.meta.id)
              item.children.forEach((child) => {
                child.meta.systemType = this.parentMenu.dictionaryDetailsCode
                child.meta.jumpAddress = '/' + this.parentMenu.dictionaryDetailsCode + '/' + item.path
              })
            })
          }
          const newRoute = this.formInline.privateMenuList
            .map((item) => {
              if (this.parentMenu) {
                // 构建基础路由对象
                const route = {
                  path: item.menuTag,
                  name: item.menuTag,
                  component: 'EmptyLayout',
                  redirect: { name: item.menuTag },
                  meta: {
                    title: item.menuName,
                    menuAuth: item.menuPath
                  },
                  children: [
                    {
                      path: '',
                      name: item.menuTag,
                      component: item.menuTag,
                      meta: {
                        title: item.menuName,
                        sidebar: false,
                        breadcrumb: false,
                        systemType: this.parentMenu.dictionaryDetailsCode,
                        jumpAddress: '/' + this.parentMenu.dictionaryDetailsCode + '/' + item.menuTag
                      }
                    }
                  ]
                }
                // 添加 childList 中的子路由
                if (item.childList && Array.isArray(item.childList)) {
                  const childRoutes = item.childList.map((child) => ({
                    path: child.menuTag,
                    name: child.menuTag,
                    component: child.menuTag,
                    meta: {
                      title: child.menuName,
                      sidebar: false,
                      breadcrumb: false,
                      systemType: this.parentMenu.dictionaryDetailsCode,
                      jumpAddress: '/' + this.parentMenu.dictionaryDetailsCode + '/' + child.menuTag
                    }
                  }))
                  // 将子路由添加到 children 数组中
                  route.children.push(...childRoutes)
                }
                return route // 返回构建的路由对象
              }
              return null // 若 parentMenu 不存在，返回 null
            })
            .filter((route) => route !== null) // 过滤掉无效项
          this.formInline.routerJson.children.push(...matchedItems, ...newRoute)
          if (typeof this.formInline.routerJson !== 'string') {
            this.formInline.routerJson = JSON.stringify(this.formInline.routerJson)
          }
          this.$api.getSystemManagementSave({ ...this.formInline }).then((res) => {
            if (res.code === '200') {
              this.$message.success(res.message)
              this.getTableData()
              this.$refs.formInline.resetFields()
              this.dialogVisible = false
            } else {
              this.$message.error(res.message)
            }
          })
        } else {
          return false
        }
      })
    },
    // 取消
    dialogClosed() {
      this.dialogVisible = false
      this.addChildVisible = false
      this.lookVisible = false
      if (this.$refs.formInline) {
        this.$refs.formInline.resetFields()
      }
      if (this.$refs.formChildren) {
        this.$refs.formChildren.resetFields()
      }
    },
    // 复制
    duplication(row) {
      if (row && typeof row === 'string') {
        // 创建一个临时输入框
        const input = document.createElement('input')
        input.value = row // 将要复制的文本设置为输入框的值
        document.body.appendChild(input) // 将输入框添加到 DOM 中
        input.select() // 选中输入框中的文本
        // 执行复制操作
        document.execCommand('copy')
        document.body.removeChild(input) // 复制后移除输入框
        this.$message.success('复制成功') // 提示用户
      } else {
        this.$message.error('没有检测到内容，复制失败')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
/*修改展开按钮的样式 start*/
/*1.取消原本展开的旋转动效*/
::v-deep .el-table .el-table__expand-icon {
  -webkit-transform: rotate(0deg);
  transform: rotate(0deg);
  margin-right: 10px;
}
/*2.展开按钮未点击的样式是加号带边框*/
::v-deep .el-table__expand-icon .el-icon-arrow-right:before {
  content: '\e6d9';
  border: 1px solid #ccc;
  padding: 2px;
}
/*3.展开按钮点击后的样式是减号带边框*/
::v-deep .el-table__expand-icon--expanded .el-icon-arrow-right:before {
  content: '\e6d8';
}
.searchForm {
  display: flex;
  height: 80px;
  padding: 0 16px;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  .el-input {
    width: 200px;
    margin-left: 16px;
  }
  > div {
    margin-right: 20px;
  }
}
.hide {
  width: 300px;
  height: 146px;
  ::v-deep .el-upload--picture-card {
    width: 0;
    height: 0;
    display: none;
  }
}
.search-box {
  display: flex;
  align-items: center;
}
.container-content > div {
  height: 100%;
  margin-top: 16px;
}
.el-table {
  margin-bottom: 12px;
}
::v-deep .el-table__row:hover {
  cursor: pointer;
}
.table-box {
  background-color: #fff;
  height: 100%;
  padding: 16px;
  .tableContainer {
    height: 100%;
    .el-table {
      height: calc(100% - 96px) !important;
    }
  }
}
.diaContent {
  width: 100%;
  max-height: 550px !important;
  overflow: auto;
  background-color: #fff !important;
  .el-input,
  .el-select,
  .el-cascader {
    width: 300px;
  }
}
.span-plus {
  color: #3562db;
}
.span-reduce {
  color: #d9001b;
}
.span-plus,
.span-reduce {
  cursor: pointer;
}
.form-item {
  display: inline-block;
  margin-right: 20px;
}
.inputWidth {
  width: 820px;
}
.ml-16 {
  margin-left: 16px;
}
.dialog .el-dialog {
  width: 60% !important;
}
.isimg {
  position: relative;
  width: 146px;
  height: 146px;
  img {
    width: 146px;
    height: 146px;
  }
  .hover {
    position: absolute;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.5);
    width: 100%;
    height: 100%;
    border-radius: 6px;
    margin: 0 auto;
    line-height: 146px;
    text-align: center;
    display: none;
    i {
      font-size: 20px;
      color: #fff;
      padding: 0 8px;
    }
  }
}
.isimg:hover .hover {
  display: block;
  /* 悬停时显示 */
}
</style>
