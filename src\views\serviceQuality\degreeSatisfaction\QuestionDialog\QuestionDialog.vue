<template>
  <div>
    <el-dialog v-if="dialogVisiable" :visible.sync="dialogVisiable" :title="childName" class="main-container" custom-class="model-dialog">
      <slot ref="myComponent" name="question-dialog"></slot>
      <div slot="footer">
        <el-button type="primary" plain @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import notice from '../utils/notice'
import utils from '../utils/utils'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      childName: '', // 子组件名称
      childComponent: '' // 子 组件指引
    }
  },
  computed: {
    dialogVisiable: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  destroyed() {
    notice.$off('closeQuestionDialog')
    notice.$off('initChildComponent')
  },
  mounted() {
    notice.$on('initChildComponent', (data, res) => {
      this.childName = res
      this.childComponent = data
    })
    notice.$on('closeQuestionDialog', (data) => {
      if (this.childComponent === data) {
        notice.$emit('getCurrentQuestionAllSubject')
        this.dialogVisiable = false
      }
    })
  },
  methods: {
    handleSubmit() {
      const that = this
      notice.$emit('handleSubmit', this.childComponent)
      setTimeout(() => {
        this.$parent.$parent.getPaperQuestions()
      }, 300)
    },
    handleCancel() {
      this.dialogVisiable = false
    }
  }
}
</script>
<style lang="scss" type="text/css" scoped>
.main-container {
  .el-dialog__footer {
    box-shadow: 0 -4px 0 0 #f3f4f8;
    box-shadow: 0 -4px 0 0 #f3f4f8;
    padding: 10px 20px;
    width: 100%;
  }
}
</style>
